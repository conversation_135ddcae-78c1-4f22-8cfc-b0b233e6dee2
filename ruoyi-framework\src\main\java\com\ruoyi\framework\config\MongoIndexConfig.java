package com.ruoyi.framework.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.CompoundIndexDefinition;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.domain.Sort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.UUID;
import java.util.List;

import javax.annotation.PostConstruct;
import org.bson.Document;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.CertVersion;

/**
 * MongoDB 索引配置
 * 
 * <AUTHOR>
 */
@Configuration
public class MongoIndexConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(MongoIndexConfig.class);
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @PostConstruct
    public void initIndexes() {
        // 为 document_samples 集合创建索引
        IndexOperations indexOps = mongoTemplate.indexOps("document_samples");
        
        // 版本ID索引
        indexOps.ensureIndex(new Index().on("versionId", org.springframework.data.domain.Sort.Direction.ASC));
        
        // 版本ID和状态复合索引
        Document compoundIndex = new Document();
        compoundIndex.put("versionId", 1);
        compoundIndex.put("status", 1);
        indexOps.ensureIndex(new CompoundIndexDefinition(compoundIndex));
        
        // 样本名称索引（支持模糊查询）
        indexOps.ensureIndex(new Index().on("sampleName", org.springframework.data.domain.Sort.Direction.ASC).named("sampleName_idx"));
        
        // 光源类型索引
        indexOps.ensureIndex(new Index().on("images.lightType", org.springframework.data.domain.Sort.Direction.ASC).named("lightType_idx"));
        
        // 防伪特征类型索引
        indexOps.ensureIndex(new Index().on("images.parts.antiFeatureType", org.springframework.data.domain.Sort.Direction.ASC).named("antiFeatureType_idx"));
        
        // 创建时间索引
        indexOps.ensureIndex(new Index().on("createTime", org.springframework.data.domain.Sort.Direction.DESC).named("createTime_idx"));
        
        // greenfox - 新增：为 image_repository 集合创建增强索引 - 2025-01-15
        IndexOperations imageIndexOps = mongoTemplate.indexOps("image_repository");
        
        // 基础查询索引
        imageIndexOps.ensureIndex(new Index().on("taskId", org.springframework.data.domain.Sort.Direction.ASC));
        imageIndexOps.ensureIndex(new Index().on("lightType", org.springframework.data.domain.Sort.Direction.ASC));
        imageIndexOps.ensureIndex(new Index().on("processStatus", org.springframework.data.domain.Sort.Direction.ASC));
        
        // greenfox - 新增：部门信息相关索引 - 2025-01-15
        imageIndexOps.ensureIndex(new Index().on("deptInfo.deptId", org.springframework.data.domain.Sort.Direction.ASC).named("deptId_idx"));
        imageIndexOps.ensureIndex(new Index().on("deptInfo.deptName", org.springframework.data.domain.Sort.Direction.ASC).named("deptName_idx"));
        imageIndexOps.ensureIndex(new Index().on("deptInfo.userId", org.springframework.data.domain.Sort.Direction.ASC).named("userId_idx"));
        
        // greenfox - 新增：时间相关索引 - 2025-01-15
        imageIndexOps.ensureIndex(new Index().on("uploadTime", org.springframework.data.domain.Sort.Direction.DESC).named("uploadTime_idx"));
        imageIndexOps.ensureIndex(new Index().on("createTime", org.springframework.data.domain.Sort.Direction.DESC).named("createTime_idx"));
        imageIndexOps.ensureIndex(new Index().on("updateTime", org.springframework.data.domain.Sort.Direction.DESC).named("updateTime_idx"));
        
        // greenfox - 新增：组合索引，用于增强查询性能 - 2025-01-15
        Document imageCompoundIndex1 = new Document();
        imageCompoundIndex1.put("deptInfo.deptName", 1);
        imageCompoundIndex1.put("uploadTime", -1);
        imageIndexOps.ensureIndex(new CompoundIndexDefinition(imageCompoundIndex1).named("dept_uploadtime_idx"));

        // greenfox - 修复：处理enhanced_query_idx索引冲突 - 2025-01-15
        try {
            // 先尝试删除可能存在的冲突索引
            try {
                imageIndexOps.dropIndex("enhanced_query_idx");
                logger.info("已删除冲突的enhanced_query_idx索引");
            } catch (Exception e) {
                logger.debug("enhanced_query_idx索引不存在或删除失败，继续创建新索引: {}", e.getMessage());
            }

            // 创建正确的索引
            Document imageCompoundIndex2 = new Document();
            imageCompoundIndex2.put("taskId", 1);
            imageCompoundIndex2.put("lightType", 1);
            imageCompoundIndex2.put("processStatus", 1);
            imageCompoundIndex2.put("deptInfo.deptName", 1);
            imageIndexOps.ensureIndex(new CompoundIndexDefinition(imageCompoundIndex2).named("enhanced_query_idx"));
            logger.info("enhanced_query_idx索引创建成功");

        } catch (Exception e) {
            logger.error("创建enhanced_query_idx索引失败: {}", e.getMessage());
            // 使用不同的索引名称作为备选方案
            try {
                Document imageCompoundIndex2 = new Document();
                imageCompoundIndex2.put("taskId", 1);
                imageCompoundIndex2.put("lightType", 1);
                imageCompoundIndex2.put("processStatus", 1);
                imageCompoundIndex2.put("deptInfo.deptName", 1);
                imageIndexOps.ensureIndex(new CompoundIndexDefinition(imageCompoundIndex2).named("enhanced_query_v2_idx"));
                logger.info("使用备选名称enhanced_query_v2_idx创建索引成功");
            } catch (Exception ex) {
                logger.error("创建备选索引也失败: {}", ex.getMessage());
            }
        }
        
        // greenfox - 新增：标注相关索引 - 2025-01-15
        imageIndexOps.ensureIndex(new Index().on("fileInfoId", Sort.Direction.ASC).named("fileInfoId_idx"));
        imageIndexOps.ensureIndex(new Index().on("isAnnotated", Sort.Direction.ASC).named("isAnnotated_idx"));
        imageIndexOps.ensureIndex(new Index().on("imageType", Sort.Direction.ASC).named("imageType_idx"));
        imageIndexOps.ensureIndex(new Index().on("shootingAngle", Sort.Direction.ASC).named("shootingAngle_idx"));
        imageIndexOps.ensureIndex(new Index().on("relatedMainImageId", Sort.Direction.ASC).named("relatedMainImageId_idx"));
        imageIndexOps.ensureIndex(new Index().on("qualityScore", Sort.Direction.ASC).named("qualityScore_idx"));
        imageIndexOps.ensureIndex(new Index().on("annotatedBy", Sort.Direction.ASC).named("annotatedBy_idx"));
        imageIndexOps.ensureIndex(new Index().on("annotatedAt", Sort.Direction.DESC).named("annotatedAt_idx"));
        
        // greenfox - 新增：标注相关组合索引 - 2025-01-15
        Document annotationIndex1 = new Document();
        annotationIndex1.put("fileInfoId", 1);
        annotationIndex1.put("isAnnotated", 1);
        imageIndexOps.ensureIndex(new CompoundIndexDefinition(annotationIndex1).named("fileInfo_annotation_idx"));
        
        Document annotationIndex2 = new Document();
        annotationIndex2.put("fileInfoId", 1);
        annotationIndex2.put("lightType", 1);
        annotationIndex2.put("isAnnotated", 1);
        imageIndexOps.ensureIndex(new CompoundIndexDefinition(annotationIndex2).named("fileInfo_light_annotation_idx"));
        
        Document annotationIndex3 = new Document();
        annotationIndex3.put("isMainImage", 1);
        annotationIndex3.put("fileInfoId", 1);
        imageIndexOps.ensureIndex(new CompoundIndexDefinition(annotationIndex3).named("mainImage_fileInfo_idx"));
        
        // greenfox - 新增：为 folder_info 集合创建增强索引 - 2025-01-15
        IndexOperations folderIndexOps = mongoTemplate.indexOps("folder_info");
        
        // 基础查询索引
        folderIndexOps.ensureIndex(new Index().on("taskId", org.springframework.data.domain.Sort.Direction.ASC));
        folderIndexOps.ensureIndex(new Index().on("folderId", org.springframework.data.domain.Sort.Direction.ASC));
        
        // greenfox - 新增：上传者信息相关索引 - 2025-01-15
        folderIndexOps.ensureIndex(new Index().on("uploaderInfo.deptId", org.springframework.data.domain.Sort.Direction.ASC).named("uploader_deptId_idx"));
        folderIndexOps.ensureIndex(new Index().on("uploaderInfo.deptName", org.springframework.data.domain.Sort.Direction.ASC).named("uploader_deptName_idx"));
        folderIndexOps.ensureIndex(new Index().on("uploaderInfo.userId", org.springframework.data.domain.Sort.Direction.ASC).named("uploader_userId_idx"));
        folderIndexOps.ensureIndex(new Index().on("uploaderInfo.userName", org.springframework.data.domain.Sort.Direction.ASC).named("uploader_userName_idx"));
        
        // greenfox - 新增：时间相关索引 - 2025-01-15
        folderIndexOps.ensureIndex(new Index().on("createTime", org.springframework.data.domain.Sort.Direction.DESC).named("folder_createTime_idx"));
        folderIndexOps.ensureIndex(new Index().on("updateTime", org.springframework.data.domain.Sort.Direction.DESC).named("folder_updateTime_idx"));
        
        // greenfox - 新增：解析信息索引 - 2025-01-15
        folderIndexOps.ensureIndex(new Index().on("parsedInfo.country", org.springframework.data.domain.Sort.Direction.ASC).named("country_idx"));
        folderIndexOps.ensureIndex(new Index().on("parsedInfo.documentType", org.springframework.data.domain.Sort.Direction.ASC).named("documentType_idx"));
        folderIndexOps.ensureIndex(new Index().on("parsedInfo.year", org.springframework.data.domain.Sort.Direction.ASC).named("year_idx"));
        
        // greenfox - 新增：组合索引，用于部门和时间范围查询 - 2025-01-15
        Document folderCompoundIndex = new Document();
        folderCompoundIndex.put("uploaderInfo.deptName", 1);
        folderCompoundIndex.put("createTime", -1);
        folderIndexOps.ensureIndex(new CompoundIndexDefinition(folderCompoundIndex).named("dept_createtime_idx"));
        
        // greenfox - 新增：标注相关索引 - 2025-01-15
        // 先为现有记录生成fileInfoId，然后创建唯一索引
        try {
            // 查找所有fileInfoId为null的记录并生成唯一ID
            Query nullFileInfoQuery = new Query(Criteria.where("fileInfoId").exists(false));
            List<FolderInfo> foldersWithoutFileInfoId = mongoTemplate.find(nullFileInfoQuery, FolderInfo.class, "folder_info");
            
            if (!foldersWithoutFileInfoId.isEmpty()) {
                logger.info("发现{}个文件夹记录缺少fileInfoId，正在生成...", foldersWithoutFileInfoId.size());
                
                for (FolderInfo folder : foldersWithoutFileInfoId) {
                    // 生成唯一的fileInfoId
                    String fileInfoId = UUID.randomUUID().toString().replace("-", "");
                    
                    // 更新记录
                    Query updateQuery = new Query(Criteria.where("id").is(folder.getId()));
                    Update update = new Update().set("fileInfoId", fileInfoId);
                    mongoTemplate.updateFirst(updateQuery, update, FolderInfo.class, "folder_info");
                }
                
                logger.info("已为{}个文件夹记录生成fileInfoId", foldersWithoutFileInfoId.size());
            }
            
            // 现在创建唯一索引
            folderIndexOps.ensureIndex(new Index().on("fileInfoId", Sort.Direction.ASC).unique().named("fileInfoId_unique_idx"));
            logger.info("fileInfoId唯一索引创建成功");
            
        } catch (Exception e) {
            logger.error("创建fileInfoId索引失败，将使用普通索引: {}", e.getMessage());
            // 如果唯一索引创建失败，使用普通索引
            try {
                folderIndexOps.ensureIndex(new Index().on("fileInfoId", Sort.Direction.ASC).named("fileInfoId_idx"));
            } catch (Exception ex) {
                logger.error("创建fileInfoId普通索引也失败: {}", ex.getMessage());
            }
        }
        
        folderIndexOps.ensureIndex(new Index().on("annotationStatus", Sort.Direction.ASC).named("annotationStatus_idx"));
        folderIndexOps.ensureIndex(new Index().on("reviewStatus", Sort.Direction.ASC).named("reviewStatus_idx"));
        folderIndexOps.ensureIndex(new Index().on("submittedBy", Sort.Direction.ASC).named("submittedBy_idx"));
        folderIndexOps.ensureIndex(new Index().on("reviewedBy", Sort.Direction.ASC).named("reviewedBy_idx"));
        folderIndexOps.ensureIndex(new Index().on("submittedAt", Sort.Direction.DESC).named("submittedAt_idx"));
        folderIndexOps.ensureIndex(new Index().on("reviewedAt", Sort.Direction.DESC).named("reviewedAt_idx"));
        folderIndexOps.ensureIndex(new Index().on("qualityRating", Sort.Direction.ASC).named("qualityRating_idx"));
        
        // greenfox - 新增：标注相关组合索引 - 2025-01-15
        Document annotationCompoundIndex1 = new Document();
        annotationCompoundIndex1.put("taskId", 1);
        annotationCompoundIndex1.put("annotationStatus", 1);
        folderIndexOps.ensureIndex(new CompoundIndexDefinition(annotationCompoundIndex1).named("task_annotation_status_idx"));
        
        Document annotationCompoundIndex2 = new Document();
        annotationCompoundIndex2.put("annotationStatus", 1);
        annotationCompoundIndex2.put("submittedAt", -1);
        folderIndexOps.ensureIndex(new CompoundIndexDefinition(annotationCompoundIndex2).named("annotation_submitted_idx"));
        
        Document annotationCompoundIndex3 = new Document();
        annotationCompoundIndex3.put("reviewStatus", 1);
        annotationCompoundIndex3.put("reviewedAt", -1);
        folderIndexOps.ensureIndex(new CompoundIndexDefinition(annotationCompoundIndex3).named("review_status_time_idx"));
        
        // greenfox - 新增：为 batch_upload_task 集合创建索引 - 2025-01-15
        IndexOperations taskIndexOps = mongoTemplate.indexOps("batch_upload_task");
        
        // 基础查询索引
        taskIndexOps.ensureIndex(new Index().on("taskId", org.springframework.data.domain.Sort.Direction.ASC));
        taskIndexOps.ensureIndex(new Index().on("status", org.springframework.data.domain.Sort.Direction.ASC));
        taskIndexOps.ensureIndex(new Index().on("createdBy", org.springframework.data.domain.Sort.Direction.ASC));
        taskIndexOps.ensureIndex(new Index().on("createTime", org.springframework.data.domain.Sort.Direction.DESC));
        taskIndexOps.ensureIndex(new Index().on("updateTime", org.springframework.data.domain.Sort.Direction.DESC));
        
        // 文本搜索索引
        taskIndexOps.ensureIndex(new Index().on("folderName", org.springframework.data.domain.Sort.Direction.ASC).named("folderName_text_idx"));
        
        // greenfox - 新增：为自动创建version功能添加索引 - 2025-01-15
        
        // CertVersion集合索引
        try {
            // 文件夹名称索引（用于避免重复创建）

            
            // 联合查询索引（证件类型 + 发行年份 + 国家代码）
            mongoTemplate.indexOps(CertVersion.class)
                .ensureIndex(new Index()
                    .on("certType", Sort.Direction.ASC)
                    .on("issueYear", Sort.Direction.ASC)
                    .on("countryInfo.code", Sort.Direction.ASC))
                    ;
            
            // 国家代码索引
            mongoTemplate.indexOps(CertVersion.class)
                .ensureIndex(new Index().on("countryInfo.code", Sort.Direction.ASC));
            
            // 需要人工确认状态索引
            mongoTemplate.indexOps(CertVersion.class)
                .ensureIndex(new Index().on("needManualCheck", Sort.Direction.ASC));
            
            // 状态索引
            mongoTemplate.indexOps(CertVersion.class)
                .ensureIndex(new Index().on("status", Sort.Direction.ASC));
            
            logger.info("CertVersion自动创建功能索引创建完成");
            
        } catch (Exception e) {
            logger.error("创建CertVersion索引失败: {}", e.getMessage(), e);
        }
        
        // ImageRepository集合新增索引
        try {
            // versionId索引（用于关联version）
            mongoTemplate.indexOps(ImageRepository.class)
                .ensureIndex(new Index().on("versionId", Sort.Direction.ASC));
            
            logger.info("ImageRepository versionId索引创建完成");
            
        } catch (Exception e) {
            logger.error("创建ImageRepository versionId索引失败: {}", e.getMessage(), e);
        }
    }
} 