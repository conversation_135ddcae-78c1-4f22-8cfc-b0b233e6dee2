package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 国家信息对象 country
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class Country extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 国家ID */
    private Long id;

    /** 国家名称(中文) */
    @Excel(name = "国家名称(中文)")
    @NotBlank(message = "中文名称不能为空")
    @Size(max = 50, message = "中文名称长度不能超过50个字符")
    private String name;

    /** 国家名称(英文) */
    @Excel(name = "国家名称(英文)")
    @NotBlank(message = "英文名称不能为空")
    @Size(max = 100, message = "英文名称长度不能超过100个字符")
    private String nameEn;

    /** 国家代码(三位字母) */
    @Excel(name = "国家代码(三位字母)")
    @NotBlank(message = "国家代码不能为空")
    @Size(min = 3, max = 3, message = "国家代码必须为3位字母")
    @Pattern(regexp = "^[A-Z]{3}$", message = "国家代码必须为3位大写字母")
    private String code;

    /** 国旗图标路径 */
    @Excel(name = "国旗图标路径")
    @Size(max = 200, message = "国旗图标路径长度不能超过200个字符")
    private String flagIcon;

    /** 查询参数：首字母筛选 */
    private String firstLetter;

    /** 查询参数：关键词搜索 */
    private String keyword;

    // greenfox - 新增：辅助方法 - 2025-01-15
    
    /**
     * 获取首字母
     */
    public String getFirstLetter() {
        if (nameEn != null && !nameEn.isEmpty()) {
            return nameEn.substring(0, 1).toUpperCase();
        }
        return "";
    }

    /**
     * 获取显示名称（中英文组合）
     */
    public String getDisplayName() {
        if (name != null && nameEn != null) {
            return name + " (" + nameEn + ")";
        } else if (name != null) {
            return name;
        } else if (nameEn != null) {
            return nameEn;
        }
        return "";
    }

    /**
     * 获取默认国旗路径
     */
    public String getDefaultFlagPath() {
        if (code != null && !code.isEmpty()) {
            return "/static/flags/" + code.toUpperCase() + ".png";
        }
        return null;
    }

    /**
     * 检查是否有国旗图标
     */
    public boolean hasFlagIcon() {
        return flagIcon != null && !flagIcon.trim().isEmpty();
    }

    // getter 和 setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code != null ? code.toUpperCase() : null;
    }

    public String getFlagIcon() {
        return flagIcon;
    }

    public void setFlagIcon(String flagIcon) {
        this.flagIcon = flagIcon;
    }

    public void setFirstLetter(String firstLetter) {
        this.firstLetter = firstLetter;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("nameEn", getNameEn())
            .append("code", getCode())
            .append("flagIcon", getFlagIcon())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
