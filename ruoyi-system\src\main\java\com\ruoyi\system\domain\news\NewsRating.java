package com.ruoyi.system.domain.news;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文章评分对象 news_rating
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
public class NewsRating extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 雪花算法ID */
    private Long ratingId;

    /** 关联文章ID */
    @Excel(name = "关联文章ID")
    private Long newsId;

    /** 评分用户ID */
    @Excel(name = "评分用户ID")
    private Long userId;

    /** 1-5分 */
    @Excel(name = "1-5分")
    private Long score;

    /** $column.columnComment */
    @Excel(name = "评分时间", readConverterExp = "$column.readConverterExp()")
    private Date createdAt;

    /** 评分用户    */
    @Excel(name = "评分用户 ")
    private String userName;

    /** 文章标题    */
    @Excel(name = "文章标题 ")
    private String newsTitle;

    public void setRatingId(Long ratingId) 
    {
        this.ratingId = ratingId;
    }

    public Long getRatingId() 
    {
        return ratingId;
    }

    public void setNewsId(Long newsId) 
    {
        this.newsId = newsId;
    }

    public Long getNewsId() 
    {
        return newsId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setScore(Long score) 
    {
        this.score = score;
    }

    public Long getScore() 
    {
        return score;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getNewsTitle() {
        return newsTitle;
    }

    public void setNewsTitle(String newsTitle) {
        this.newsTitle = newsTitle;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ratingId", getRatingId())
            .append("newsId", getNewsId())
            .append("userId", getUserId())
            .append("score", getScore())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
