package com.ruoyi.common.utils.image;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片处理工具类
 * 
 * <AUTHOR>
 */
public class ImageUtils {
    
    /**
     * 生成缩略图
     *
     * @param input 输入流
     * @param width 宽度
     * @param height 高度
     * @param format 格式（jpg, png等）
     * @return 缩略图输入流
     */
    public static InputStream generateThumbnail(InputStream input, int width, int height, String format) throws IOException {
        BufferedImage originalImage = ImageIO.read(input);
        
        // 计算缩放比例
        double scale = calculateScale(originalImage.getWidth(), originalImage.getHeight(), width, height);
        
        // 计算新尺寸
        int newWidth = (int) (originalImage.getWidth() * scale);
        int newHeight = (int) (originalImage.getHeight() * scale);
        
        // 创建缩略图
        BufferedImage thumbnail = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = thumbnail.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g.dispose();
        
        // 转换为输入流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(thumbnail, format, outputStream);
        return new ByteArrayInputStream(outputStream.toByteArray());
    }
    
    /**
     * 计算缩放比例
     */
    private static double calculateScale(int originalWidth, int originalHeight, int targetWidth, int targetHeight) {
        double widthScale = (double) targetWidth / originalWidth;
        double heightScale = (double) targetHeight / originalHeight;
        
        // 取较小的缩放比例，保持图片比例
        return Math.min(widthScale, heightScale);
    }
    
    /**
     * 裁剪图片
     *
     * @param input 输入流
     * @param x 起始X坐标
     * @param y 起始Y坐标
     * @param width 宽度
     * @param height 高度
     * @param format 格式（jpg, png等）
     * @return 裁剪后的图片输入流
     */
    public static InputStream cropImage(InputStream input, int x, int y, int width, int height, String format) throws IOException {
        BufferedImage originalImage = ImageIO.read(input);
        
        // 确保裁剪区域在图片范围内
        if (x + width > originalImage.getWidth()) {
            width = originalImage.getWidth() - x;
        }
        if (y + height > originalImage.getHeight()) {
            height = originalImage.getHeight() - y;
        }
        
        // 裁剪图片
        BufferedImage croppedImage = originalImage.getSubimage(x, y, width, height);
        
        // 转换为输入流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(croppedImage, format, outputStream);
        return new ByteArrayInputStream(outputStream.toByteArray());
    }
} 