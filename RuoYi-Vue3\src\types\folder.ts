/**
 * 文件夹相关的TypeScript类型定义
 */

import type { Country, CertType } from './common'

/** 文件夹状态枚举 */
export enum FolderStatus {
  UNASSOCIATED = 'unassociated',
  ASSOCIATED = 'associated',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

/** 文件夹查询参数 */
export interface FolderQueryParams {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 文件夹名称 */
  folderName?: string
  /** 国家代码 */
  countryCode?: string
  /** 证件类型代码 */
  certTypeCode?: string
  /** 签发年份 */
  issueYear?: string
  /** 状态 */
  status?: FolderStatus
  /** 任务ID */
  taskId?: string
  /** 版本ID */
  versionId?: string
  /** 是否为标准样本 */
  isStandardSample?: boolean
}

/** 文件夹信息VO */
export interface FolderInfoVO {
  /** 文件夹ID */
  folderId: string
  /** 文件夹名称 */
  folderName: string
  /** 文件夹路径 */
  folderPath: string
  /** 状态 */
  status: FolderStatus
  /** 图片数量 */
  imageCount: number
  /** 国家信息 */
  countryInfo?: Country
  /** 证件类型信息 */
  certTypeInfo?: CertType
  /** 签发年份 */
  issueYear?: string
  /** 签发地 */
  issuePlace?: string
  /** 是否为标准样本 */
  isStandardSample: boolean
  /** 关联的版本ID */
  versionId?: string
  /** 关联的任务ID */
  taskId?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 创建者 */
  createdBy: string
  /** 更新者 */
  updatedBy?: string
}

/** 文件夹详情VO */
export interface FolderDetailVO extends FolderInfoVO {
  /** 图片列表 */
  images?: ImageInfo[]
  /** 标注信息 */
  annotationInfo?: AnnotationInfo
  /** 统计信息 */
  statistics?: FolderStatistics
}

/** 图片信息 */
export interface ImageInfo {
  /** 图片ID */
  imageId: string
  /** 图片名称 */
  imageName: string
  /** 图片类型 */
  imageType: string
  /** 图片路径 */
  imagePath: string
  /** 图片大小 */
  fileSize: number
  /** 图片格式 */
  fileFormat: string
  /** 宽度 */
  width?: number
  /** 高度 */
  height?: number
  /** 是否已标注 */
  isAnnotated: boolean
  /** 创建时间 */
  createTime: string
}

/** 标注信息 */
export interface AnnotationInfo {
  /** 是否可编辑 */
  canEdit: boolean
  /** 是否可查看 */
  canView: boolean
  /** 权限说明 */
  reason: string
  /** 已标注图片数 */
  annotatedCount: number
  /** 总图片数 */
  totalCount: number
  /** 标注进度 */
  progress: number
}

/** 文件夹统计信息 */
export interface FolderStatistics {
  /** 总图片数 */
  totalImages: number
  /** 已标注图片数 */
  annotatedImages: number
  /** 未标注图片数 */
  unannotatedImages: number
  /** 标注进度百分比 */
  annotationProgress: number
  /** 图片类型统计 */
  imageTypeStats: ImageTypeStat[]
}

/** 图片类型统计 */
export interface ImageTypeStat {
  /** 图片类型 */
  imageType: string
  /** 图片类型显示名称 */
  imageTypeDisplayName: string
  /** 数量 */
  count: number
  /** 已标注数量 */
  annotatedCount: number
}

/** 文件夹类型更新DTO */
export interface FolderTypeUpdateDTO {
  /** 文件夹ID */
  folderId: string
  /** 国家ID */
  countryId: number
  /** 证件类型ID */
  certTypeId: number
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
}

/** 文件夹批量操作DTO */
export interface FolderBatchOperationDTO {
  /** 文件夹ID列表 */
  folderIds: string[]
  /** 操作类型 */
  operation: 'delete' | 'move' | 'update' | 'associate'
  /** 目标版本ID（用于关联操作） */
  targetVersionId?: string
  /** 更新数据（用于更新操作） */
  updateData?: Partial<FolderTypeUpdateDTO>
} 