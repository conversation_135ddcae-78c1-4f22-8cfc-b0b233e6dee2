/**
 * 标注相关的TypeScript类型定义
 */

/** 坐标信息 */
export interface AnnotationCoordinate {
  /** X坐标（百分比） */
  x: number
  /** Y坐标（百分比） */
  y: number
  /** 宽度（百分比） */
  width: number
  /** 高度（百分比） */
  height: number
}

/** 标注项 */
export interface AnnotationItem {
  /** 标注项ID */
  annotationId?: string
  /** 标注类型 */
  annotationType: string
  /** 标注名称 */
  annotationName: string
  /** 坐标信息 */
  coordinate?: AnnotationCoordinate
  /** 标注值 */
  annotationValue?: string
  /** 是否必填 */
  required?: boolean
  /** 显示顺序 */
  displayOrder?: number
}

/** 标注模板请求DTO */
export interface AnnotationTemplateDTO {
  /** 模板ID */
  templateId?: string
  /** 版本ID */
  versionId: string
  /** 图片类型 */
  imageType: string
  /** 证件类型 */
  certType?: string
  /** 国家代码 */
  countryCode?: string
  /** 标注项列表 */
  annotations: AnnotationItem[]
  /** 创建此模板的标准文件夹ID */
  standardFolderId: string
}

/** 版本标注模板 */
export interface VersionAnnotationTemplate {
  /** MongoDB主键 */
  id: string
  /** 模板ID */
  templateId: string
  /** 版本ID */
  versionId: string
  /** 图片类型 */
  imageType: string
  /** 证件类型 */
  certType?: string
  /** 国家代码 */
  countryCode?: string
  /** 标注项列表 */
  annotations: AnnotationItem[]
  /** 创建此模板的标准文件夹ID */
  standardFolderId: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createdBy: string
  /** 更新人 */
  updatedBy: string
}

/** 标注权限信息 */
export interface AnnotationPermissionInfo {
  /** 是否可编辑 */
  canEdit: boolean
  /** 是否可查看 */
  canView: boolean
  /** 权限说明 */
  reason: string
  /** 模板信息 */
  template?: VersionAnnotationTemplate
  /** 图片类型 */
  imageType?: string
  /** 是否为标准样本 */
  isStandardSample: boolean
  /** 是否为可标注类型 */
  isAnnotatableType: boolean
}

/** 模板统计信息 */
export interface TemplateStatistics {
  /** 总模板数 */
  totalTemplates: number
  /** 已标注类型数 */
  annotatedTypes: number
  /** 可用类型列表 */
  availableTypes: string[]
  /** 标准文件夹ID */
  standardFolderId?: string
  /** 是否有标准文件夹 */
  hasStandardFolder: boolean
}

/** 图片标注请求DTO */
export interface ImageAnnotationRequestDTO {
  /** 图片ID */
  imageId: string
  /** 标注项列表 */
  annotations: AnnotationItem[]
  /** 操作类型 */
  operation?: string
}

/** 图片标注信息VO */
export interface ImageAnnotationVO {
  /** 图片ID */
  imageId: string
  /** 图片类型 */
  imageType: string
  /** 图片类型显示名称 */
  imageTypeDisplayName?: string
  /** 是否可编辑标注 */
  canEdit: boolean
  /** 是否可查看标注 */
  canView: boolean
  /** 权限说明 */
  reason: string
  /** 标注项列表 */
  annotations: AnnotationItem[]
  /** 模板来源 */
  templateSource?: string
  /** 是否为标准样本 */
  isStandardSample: boolean
  /** 是否为可标注类型 */
  isAnnotatableType: boolean
} 