import os
import requests
import uuid
from minio import Minio
from minio.error import S3Error
import logging
from datetime import datetime
import concurrent.futures
import time
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置
API_URL = 'http://localhost:8000/api/v1/cert/version/unified'
FOLDER_ROOT = r'C:\Users\<USER>\Documents\工作文档\2025研发项目\证研平台\configs_upd\configs\personal'

# MinIO配置
MINIO_ENDPOINT = 'localhost:9000'
MINIO_ACCESS_KEY = 'minioadmin'
MINIO_SECRET_KEY = 'minioadmin'
MINIO_BUCKET_NAME = 'xjlfiles'
MINIO_SECURE = False

# 性能优化配置 - 大幅提升并发数
MAX_CONCURRENT_UPLOADS = 10   # 图片上传并发数
MAX_CONCURRENT_REQUESTS = 8   # API请求并发数

# 初始化MinIO客户端
minio_client = Minio(MINIO_ENDPOINT, access_key=MINIO_ACCESS_KEY, secret_key=MINIO_SECRET_KEY, secure=MINIO_SECURE)

def ensure_bucket_exists():
    """确保MinIO bucket存在"""
    try:
        if not minio_client.bucket_exists(MINIO_BUCKET_NAME):
            minio_client.make_bucket(MINIO_BUCKET_NAME)
        return True
    except Exception as e:
        logger.error(f"MinIO bucket检查失败: {e}")
        return False

def upload_training_image_to_minio(local_path: str, folder_name: str) -> str:
    """上传训练图片到MinIO"""
    try:
        if not os.path.exists(local_path):
            return None
        
        file_extension = os.path.splitext(local_path)[1].lower()
        minio_path = f"{folder_name}/training/PER{file_extension}"
        
        # 检查文件是否已存在
        try:
            minio_client.stat_object(MINIO_BUCKET_NAME, minio_path)
            return f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
        except S3Error:
            pass
        
        # 上传文件
        minio_client.fput_object(
            bucket_name=MINIO_BUCKET_NAME,
            object_name=minio_path,
            file_path=local_path,
            content_type=f"image/{file_extension[1:]}" if file_extension in ['.jpg', '.jpeg', '.png'] else 'application/octet-stream'
        )
        
        return f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
        
    except Exception as e:
        logger.error(f"上传训练图片异常: {folder_name}, {e}")
        return None

def parse_folder_name(folder_name: str) -> Dict[str, str]:
    """解析文件夹名称"""
    parts = folder_name.split('_')
    if len(parts) >= 4:
        return {'countryCode': parts[0], 'certType': parts[1], 'startNo': parts[2], 'issueYear': parts[3]}
    return None

def find_training_image(root_dir: str, country_code: str, folder_name: str) -> str:
    """查找训练图片"""
    possible_paths = [
        os.path.join(root_dir, country_code, folder_name, "PER.JPG"),
        os.path.join(root_dir, country_code, folder_name, "per.jpg"),
        os.path.join(root_dir, country_code, folder_name, "PER.jpg"),
        os.path.join(root_dir, country_code, folder_name, "per.JPG")
    ]
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def find_cert_folders(root_dir: str) -> List[Dict[str, str]]:
    """递归查找符合格式的文件夹"""
    result = []
    for country_code in os.listdir(root_dir):
        country_path = os.path.join(root_dir, country_code)
        if os.path.isdir(country_path):
            for folder in os.listdir(country_path):
                folder_path = os.path.join(country_path, folder)
                if os.path.isdir(folder_path) and '_' in folder:
                    parts = folder.split('_')
                    if len(parts) == 4:
                        result.append({'folderName': folder, 'countryCode': country_code, 'folderPath': folder_path})
    return result

def generate_version_code(country_code: str, cert_type: str, issue_year: str) -> str:
    """生成版本代码"""
    timestamp = datetime.now().strftime("%Y%m%d")
    return f"{country_code}_{cert_type}_{issue_year}_{timestamp}"

def process_single_folder(folder_info: Dict[str, str]) -> Dict[str, Any]:
    """处理单个文件夹"""
    try:
        folder_name = folder_info['folderName']
        country_code = folder_info['countryCode']
        
        parsed = parse_folder_name(folder_name)
        if not parsed:
            return {'folder_info': folder_info, 'status': 'error', 'error': f'无法解析文件夹名称: {folder_name}'}
        
        version_code = generate_version_code(country_code, parsed['certType'], parsed['issueYear'])
        
        # 查找并上传训练图片
        training_image_path = find_training_image(FOLDER_ROOT, country_code, folder_name)
        training_image_url = None
        if training_image_path:
            training_image_url = upload_training_image_to_minio(training_image_path, folder_name)
            if not training_image_url:
                training_image_url = f"local://{training_image_path}"
        
        # 构建请求数据
        request_data = {
            "creationMode": "STANDARD_SAMPLE",
            "folderName": folder_name,
            "certType": parsed['certType'],
            "issueYear": parsed['issueYear'],
            "countryCode": country_code,
            "startNo": parsed['startNo'],
            "endNo": "D9999",
            "numberFormat": "D{4}",
            "securityFeatures": ["水印", "防伪线", "全息图"],
            "width": 85.6,
            "height": 53.98,
            "thickness": 0.76,
            "material": "PVC",
            "versionCode": version_code,
            "trainingImageUrl": training_image_url,
            "trainingImageLightType": "visible",
            "trainingImageDescription": f"标准样本训练图片 - {version_code}",
            "imageCount": 1,
            "lightTypes": ["visible"]
        }
        
        return {'folder_info': folder_info, 'request_data': request_data, 'training_image_url': training_image_url, 'status': 'ready'}
        
    except Exception as e:
        return {'folder_info': folder_info, 'status': 'error', 'error': str(e)}

def send_api_request(processed_folder: Dict[str, Any]) -> Dict[str, Any]:
    """发送API请求创建证件版本"""
    try:
        if processed_folder['status'] != 'ready':
            return {**processed_folder, 'api_status': 'skipped'}
        
        request_data = processed_folder['request_data']
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(API_URL, json=request_data, headers=headers, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return {
                    **processed_folder,
                    'api_status': 'success',
                    'version_id': result.get('data', {}).get('versionId'),
                    'version_code': result.get('data', {}).get('versionCode')
                }
            else:
                return {**processed_folder, 'api_status': 'failed', 'error': result.get('msg', '未知错误')}
        else:
            return {**processed_folder, 'api_status': 'http_error', 'error': f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {**processed_folder, 'api_status': 'exception', 'error': str(e)}

def main():
    """主函数 - 真正的并发处理"""
    print("🚀 证件版本统一创建工具 (真正并发版)")
    print("=" * 60)
    
    # 检查MinIO连接
    if not ensure_bucket_exists():
        print("❌ MinIO连接失败")
        return
    
    # 获取文件夹列表
    folder_infos = find_cert_folders(FOLDER_ROOT)
    if not folder_infos:
        print("未找到符合格式的文件夹")
        return

    print(f"📁 找到 {len(folder_infos)} 个证件文件夹")
    print(f"⚙️  并发配置: 上传{MAX_CONCURRENT_UPLOADS}线程, API{MAX_CONCURRENT_REQUESTS}线程")
    
    confirm = input("\n是否继续处理? (y/n): ")
    if confirm.lower() != 'y':
        return

    start_time = time.time()
    
    # 阶段1：并发处理文件夹
    print(f"\n🔄 阶段1: 并发处理文件夹 (最大并发: {MAX_CONCURRENT_UPLOADS})")
    processed_folders = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_UPLOADS) as executor:
        future_to_folder = {executor.submit(process_single_folder, folder_info): folder_info for folder_info in folder_infos}
        
        completed = 0
        for future in concurrent.futures.as_completed(future_to_folder):
            completed += 1
            result = future.result()
            processed_folders.append(result)
            
            if completed % 100 == 0 or completed == len(folder_infos):
                print(f"  进度: [{completed}/{len(folder_infos)}]")

    stage1_time = time.time() - start_time
    ready_count = sum(1 for p in processed_folders if p['status'] == 'ready')
    print(f"  ✅ 阶段1完成: {ready_count}/{len(folder_infos)} 准备就绪, 用时: {stage1_time:.1f}秒")

    # 阶段2：并发发送API请求
    print(f"\n🔄 阶段2: 并发API请求 (最大并发: {MAX_CONCURRENT_REQUESTS})")
    final_results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_REQUESTS) as executor:
        future_to_processed = {executor.submit(send_api_request, processed): processed for processed in processed_folders}
        
        completed = 0
        for future in concurrent.futures.as_completed(future_to_processed):
            completed += 1
            result = future.result()
            final_results.append(result)
            
            if completed % 100 == 0 or completed == len(processed_folders):
                print(f"  进度: [{completed}/{len(processed_folders)}]")

    # 统计结果
    end_time = time.time()
    total_time = end_time - start_time
    
    success_count = sum(1 for r in final_results if r.get('api_status') == 'success')
    failed_count = len(final_results) - success_count

    print("\n" + "="*60)
    print("🎉 批量创建证件版本结果 (真正并发版):")
    print(f"📊 总数: {len(folder_infos)}")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {failed_count}")
    print(f"📈 成功率: {success_count/len(folder_infos)*100:.1f}%")
    print(f"⏱️  总处理时间: {total_time:.1f} 秒")
    print(f"🚀 平均速度: {len(folder_infos)/total_time:.2f} 个/秒")
    print(f"💡 相比之前提升: {(len(folder_infos)/total_time)/0.37:.1f} 倍")

if __name__ == "__main__":
    main() 