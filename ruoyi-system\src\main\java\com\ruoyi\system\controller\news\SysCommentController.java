package com.ruoyi.system.controller.news;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.news.SysComment;
import com.ruoyi.system.service.news.ISysCommentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.news.SysNews;
import com.ruoyi.system.service.news.ISysNewsService;
import com.ruoyi.system.service.news.ISysNotificationService;
import com.ruoyi.common.annotation.Anonymous;

/**
 * 新闻评论Controller
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
@RestController
@RequestMapping("/system/comment")
public class SysCommentController extends BaseController
{
    @Autowired
    private ISysCommentService sysCommentService;

    @Autowired
    private ISysNewsService newsService;

    @Autowired
    private ISysNotificationService notificationService;

    /**
     * 查询新闻评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysComment sysComment)
    {
        startPage();
        List<SysComment> list = sysCommentService.selectSysCommentList(sysComment);
        return getDataTable(list);
    }

    /**
     * 导出新闻评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:comment:export')")
    @Log(title = "新闻评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysComment sysComment)
    {
        List<SysComment> list = sysCommentService.selectSysCommentList(sysComment);
        ExcelUtil<SysComment> util = new ExcelUtil<SysComment>(SysComment.class);
        util.exportExcel(response, list, "新闻评论数据");
    }

    /**
     * 获取新闻评论详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:comment:query')")
    @Anonymous
    @GetMapping(value = "/{commentId}")
    public AjaxResult getInfo(@PathVariable("commentId") Long commentId)
    {
        return success(sysCommentService.selectSysCommentByCommentId(commentId));
    }

    /**
     * 新增新闻评论
     */
    //@PreAuthorize("@ss.hasPermi('system:comment:add')")
    @Anonymous
    @Log(title = "新闻评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysComment comment)
    {
        comment.setUserId(getUserId());
        comment.setCreateBy(getUsername());
        comment.setStatus("0"); // 默认状态为正常
        
        int rows = sysCommentService.insertSysComment(comment);
        
        // 获取文章信息，用于发送通知
        SysNews news = newsService.selectSysNewsByNewsId(comment.getNewsId());
        if (news != null && rows > 0) {
            // 增加判断：如果评论用户不是文章作者，才发送通知
            if (!comment.getUserId().equals(news.getUserId())) {
                // 发送评论通知给文章作者
                notificationService.sendCommentNotification(
                    comment.getNewsId(),
                    news.getNewsTitle(),
                    getUserId(),
                    getUsername(),
                    news.getUserId()
                );
            }
        }
        
        return toAjax(rows);
    }

    /**
     * 修改新闻评论
     */
    @PreAuthorize("@ss.hasPermi('system:comment:edit')")
    @Log(title = "新闻评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysComment sysComment)
    {
        return toAjax(sysCommentService.updateSysComment(sysComment));
    }

    /**
     * 删除新闻评论
     */
    @PreAuthorize("@ss.hasPermi('system:comment:remove')")
    @Log(title = "新闻评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{commentIds}")
    public AjaxResult remove(@PathVariable Long[] commentIds)
    {
        return toAjax(sysCommentService.deleteSysCommentByCommentIds(commentIds));
    }
    
    /**
     * 根据新闻ID获取评论列表
     */
    @GetMapping("/news/{newsId}")
    @Anonymous
    public AjaxResult getCommentsByNewsId(@PathVariable("newsId") Long newsId)
    {
        List<SysComment> comments = sysCommentService.selectSysCommentByNewsId(newsId);
        return success(comments);
    }
    
    /**
     * 添加评论（前台用户）
     */
    @PostMapping("/add")
    @Anonymous
    public AjaxResult addComment(@RequestBody SysComment sysComment)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null)
        {
            return AjaxResult.error("请先登录");
        }
        SysUser user = loginUser.getUser();
        sysComment.setUserId(user.getUserId());
        sysComment.setCreateBy(user.getUserName());
        sysComment.setStatus("0"); // 默认正常状态
        return toAjax(sysCommentService.insertSysComment(sysComment));
    }
}
