package com.ruoyi.system.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;

/**
 * TUS上传状态清理工具
 * 
 * 用于清理过期的TUS上传临时文件和状态，防止offset错误等问题
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Component
public class TusCleanupUtil {

    private static final Logger log = LoggerFactory.getLogger(TusCleanupUtil.class);

    @Value("${tus.upload.storage-path:/tmp/tus-uploads}")
    private String tusStoragePath;

    @Value("${tus.upload.expiration-period:86400000}")
    private Long expirationPeriod;

    @Value("${tus.upload.cleanup-on-startup:true}")
    private Boolean cleanupOnStartup;

    /**
     * 应用启动时清理过期文件
     */
    @PostConstruct
    public void cleanupOnStartup() {
        if (cleanupOnStartup) {
            log.info("应用启动，开始清理过期的TUS上传文件...");
            cleanupExpiredUploads();
        }
    }

    /**
     * 清理过期的上传文件
     */
    public void cleanupExpiredUploads() {
        try {
            File storageDir = new File(tusStoragePath);
            if (!storageDir.exists()) {
                log.info("TUS存储目录不存在，无需清理: {}", tusStoragePath);
                return;
            }

            long currentTime = System.currentTimeMillis();
            long expiredThreshold = currentTime - expirationPeriod;

            File[] files = storageDir.listFiles();
            if (files == null) {
                log.info("TUS存储目录为空，无需清理");
                return;
            }

            int deletedCount = 0;
            int totalCount = files.length;

            for (File file : files) {
                try {
                    // 检查文件修改时间
                    long lastModified = file.lastModified();
                    
                    if (lastModified < expiredThreshold) {
                        // 文件已过期，删除
                        if (file.isDirectory()) {
                            deleteDirectory(file);
                        } else {
                            file.delete();
                        }
                        
                        deletedCount++;
                        
                        LocalDateTime expiredTime = LocalDateTime.ofInstant(
                            Instant.ofEpochMilli(lastModified), 
                            ZoneId.systemDefault()
                        );
                        
                        log.debug("删除过期TUS文件: {}, 过期时间: {}", file.getName(), expiredTime);
                    }
                } catch (Exception e) {
                    log.error("删除TUS文件失败: {}", file.getName(), e);
                }
            }

            log.info("TUS过期文件清理完成: 删除 {} 个文件，共 {} 个文件", deletedCount, totalCount);

        } catch (Exception e) {
            log.error("清理TUS过期文件失败", e);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        try {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        } catch (Exception e) {
            log.error("删除目录失败: {}", directory.getPath(), e);
        }
    }

    /**
     * 强制清理所有TUS文件（危险操作，仅在必要时使用）
     */
    public void forceCleanupAll() {
        try {
            log.warn("执行强制清理所有TUS文件...");
            
            File storageDir = new File(tusStoragePath);
            if (!storageDir.exists()) {
                log.info("TUS存储目录不存在: {}", tusStoragePath);
                return;
            }

            File[] files = storageDir.listFiles();
            if (files == null) {
                log.info("TUS存储目录为空");
                return;
            }

            int deletedCount = 0;
            for (File file : files) {
                try {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                    deletedCount++;
                } catch (Exception e) {
                    log.error("删除TUS文件失败: {}", file.getName(), e);
                }
            }

            log.warn("强制清理完成: 删除 {} 个文件/目录", deletedCount);

        } catch (Exception e) {
            log.error("强制清理TUS文件失败", e);
            throw new RuntimeException("TUS文件强制清理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取TUS存储目录状态
     */
    public String getStorageStatus() {
        try {
            File storageDir = new File(tusStoragePath);
            if (!storageDir.exists()) {
                return String.format("存储目录不存在: %s", tusStoragePath);
            }

            File[] files = storageDir.listFiles();
            int fileCount = files != null ? files.length : 0;

            long totalSize = 0;
            if (files != null) {
                totalSize = Arrays.stream(files)
                    .mapToLong(file -> file.length())
                    .sum();
            }

            return String.format("存储目录: %s, 文件数量: %d, 总大小: %d bytes", 
                    tusStoragePath, fileCount, totalSize);

        } catch (Exception e) {
            return String.format("获取存储状态失败: %s", e.getMessage());
        }
    }
} 