<template>
  <div class="annotation-toolbar">
    <el-card shadow="never" class="toolbar-card">
      <template #header>
        <div class="toolbar-header">
          <span class="toolbar-title">标注工具栏</span>
          <div class="toolbar-actions">
            <el-button
              type="primary"
              size="small"
              :disabled="!canEdit"
              @click="handleSave"
              :loading="saving"
            >
              <el-icon><DocumentAdd /></el-icon>
              保存标注
            </el-button>
            <el-button
              size="small"
              @click="handleReset"
              :disabled="!hasAnnotations"
            >
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 权限状态显示 -->
      <div class="permission-status">
        <el-tag
          :type="permissionColor"
          size="small"
          class="permission-tag"
        >
          {{ permissionText }}
        </el-tag>
        <span class="permission-reason">{{ reason }}</span>
      </div>

      <!-- 标注工具 -->
      <div class="annotation-tools" v-if="canView">
        <div class="tool-group">
          <span class="tool-label">标注类型：</span>
          <el-select
            v-model="selectedAnnotationType"
            placeholder="选择标注类型"
            size="small"
            style="width: 150px"
            :disabled="!canEdit"
          >
            <el-option
              v-for="type in annotationTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>

        <div class="tool-group">
          <span class="tool-label">标注名称：</span>
          <el-input
            v-model="annotationName"
            placeholder="输入标注名称"
            size="small"
            style="width: 150px"
            :disabled="!canEdit"
          />
        </div>

        <div class="tool-group">
          <span class="tool-label">标注值：</span>
          <el-input
            v-model="annotationValue"
            placeholder="输入标注值"
            size="small"
            style="width: 150px"
            :disabled="!canEdit"
          />
        </div>

        <div class="tool-group">
          <el-checkbox
            v-model="isRequired"
            :disabled="!canEdit"
          >
            必填项
          </el-checkbox>
        </div>

        <div class="tool-group">
          <el-button
            type="success"
            size="small"
            :disabled="!canEdit || !canAddAnnotation"
            @click="handleAddAnnotation"
          >
            <el-icon><Plus /></el-icon>
            添加标注
          </el-button>
        </div>
      </div>

      <!-- 图片类型信息 -->
      <div class="image-info" v-if="imageType">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="图片类型">
            <el-tag size="small">{{ imageTypeDisplayName || imageType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否可标注">
            <el-tag :type="isAnnotatableType ? 'success' : 'danger'" size="small">
              {{ isAnnotatableType ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="样本类型">
            <el-tag :type="isStandardSample ? 'warning' : 'info'" size="small">
              {{ isStandardSample ? '标准样本' : '普通样本' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标注数量">
            <span>{{ annotationCount }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentAdd, RefreshLeft, Plus } from '@element-plus/icons-vue'
import type { AnnotationItem } from '@/api/cert/annotation'

// Props
interface Props {
  /** 是否可编辑 */
  canEdit?: boolean
  /** 是否可查看 */
  canView?: boolean
  /** 权限说明 */
  reason?: string
  /** 图片类型 */
  imageType?: string
  /** 图片类型显示名称 */
  imageTypeDisplayName?: string
  /** 是否为标准样本 */
  isStandardSample?: boolean
  /** 是否为可标注类型 */
  isAnnotatableType?: boolean
  /** 标注列表 */
  annotations?: AnnotationItem[]
  /** 保存中状态 */
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: false,
  canView: false,
  reason: '',
  imageType: '',
  imageTypeDisplayName: '',
  isStandardSample: false,
  isAnnotatableType: false,
  annotations: () => [],
  saving: false
})

// Emits
const emit = defineEmits<{
  save: [annotations: AnnotationItem[]]
  reset: []
  addAnnotation: [annotation: AnnotationItem]
}>()

// 响应式数据
const selectedAnnotationType = ref('')
const annotationName = ref('')
const annotationValue = ref('')
const isRequired = ref(false)

// 标注类型选项
const annotationTypes = [
  { label: '文本', value: 'TEXT' },
  { label: '数字', value: 'NUMBER' },
  { label: '日期', value: 'DATE' },
  { label: '区域', value: 'REGION' },
  { label: '其他', value: 'OTHER' }
]

// 计算属性
const permissionColor = computed(() => {
  if (props.canEdit) return 'success'
  if (props.canView) return 'warning'
  return 'danger'
})

const permissionText = computed(() => {
  if (props.canEdit) return '可编辑'
  if (props.canView) return '只读'
  return '无权限'
})

const annotationCount = computed(() => props.annotations.length)

const hasAnnotations = computed(() => props.annotations.length > 0)

const canAddAnnotation = computed(() => {
  return selectedAnnotationType.value && annotationName.value.trim()
})

// 方法
const handleSave = () => {
  if (!props.canEdit) {
    ElMessage.warning('当前无编辑权限')
    return
  }

  emit('save', props.annotations)
}

const handleReset = () => {
  emit('reset')
  resetForm()
}

const handleAddAnnotation = () => {
  if (!canAddAnnotation.value) {
    ElMessage.warning('请填写标注类型和名称')
    return
  }

  const annotation: AnnotationItem = {
    annotationType: selectedAnnotationType.value,
    annotationName: annotationName.value.trim(),
    annotationValue: annotationValue.value.trim(),
    required: isRequired.value,
    displayOrder: props.annotations.length + 1
  }

  emit('addAnnotation', annotation)
  resetForm()
}

const resetForm = () => {
  selectedAnnotationType.value = ''
  annotationName.value = ''
  annotationValue.value = ''
  isRequired.value = false
}

// 监听权限变化
watch(() => props.canEdit, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped lang="scss">
.annotation-toolbar {
  .toolbar-card {
    margin-bottom: 16px;
  }

  .toolbar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toolbar-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .toolbar-actions {
      display: flex;
      gap: 8px;
    }
  }

  .permission-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;

    .permission-tag {
      flex-shrink: 0;
    }

    .permission-reason {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  .annotation-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    margin-bottom: 16px;

    .tool-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .tool-label {
        font-size: 12px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
  }

  .image-info {
    margin-top: 16px;
  }
}
</style>
