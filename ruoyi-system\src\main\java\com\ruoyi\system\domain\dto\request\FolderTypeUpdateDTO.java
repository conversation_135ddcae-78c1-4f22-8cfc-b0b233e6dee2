package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 文件夹类型更新请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class FolderTypeUpdateDTO {
    
    /** 新类型 ("standard" 或 "regular") */
    @NotBlank(message = "文件夹类型不能为空")
    @Pattern(regexp = "^(standard|regular)$", message = "文件夹类型只能是standard或regular")
    private String newType;
}
