/**
 * 文件夹相关工具函数
 */

/**
 * 判断文件夹是否为标准样本
 * @param {Object} folder 文件夹对象
 * @returns {boolean} 是否为标准样本
 */
export function isStandardSampleFolder(folder) {
  return folder && folder.folderType === 'standard'
}

/**
 * 通过版本信息判断文件夹是否为标准样本
 * @param {Object} folder 文件夹对象
 * @returns {boolean} 是否为标准样本
 */
export function isStandardSampleByVersion(folder) {
  return folder && 
         folder.versionInfo && 
         folder.versionInfo.standardFolderId === folder.folderId &&
         folder.status === 'associated'
}

/**
 * 判断图片是否可标注
 * @param {Object} image 图片对象
 * @param {Object} folder 文件夹对象
 * @returns {boolean} 是否可标注
 */
export function canAnnotateImage(image, folder) {
  return isStandardSampleFolder(folder) && 
         image && 
         image.isAnnotatableType === true
}

/**
 * 获取文件夹类型显示名称
 * @param {string} folderType 文件夹类型
 * @returns {string} 显示名称
 */
export function getFolderTypeDisplayName(folderType) {
  switch (folderType) {
    case 'standard':
      return '标准样本'
    case 'regular':
      return '普通样本'
    default:
      return '未知类型'
  }
}

/**
 * 获取文件夹类型对应的标签类型
 * @param {string} folderType 文件夹类型
 * @returns {string} Element Plus 标签类型
 */
export function getFolderTypeTagType(folderType) {
  switch (folderType) {
    case 'standard':
      return 'success'
    case 'regular':
      return 'info'
    default:
      return 'warning'
  }
}
