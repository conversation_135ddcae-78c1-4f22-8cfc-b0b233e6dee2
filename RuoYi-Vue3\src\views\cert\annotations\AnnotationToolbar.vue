<template>
  <div class="annotation-toolbar">
    <el-button-group>
      <el-tooltip content="选择和移动已有标注" placement="top">
        <el-button
          :type="activeTool === 'select' ? 'primary' : 'default'"
          :icon="Pointer"
          @click="selectTool('select')"
        >
          移动/选择
        </el-button>
      </el-tooltip>

      <el-tooltip content="绘制矩形标注" placement="top">
        <el-button
          :type="activeTool === 'rectangle' ? 'primary' : 'default'"
          :icon="Grid"
          @click="selectTool('rectangle')"
        >
          矩形
        </el-button>
      </el-tooltip>

      <el-tooltip content="绘制多边形标注" placement="top">
        <el-button
          :type="activeTool === 'polygon' ? 'primary' : 'default'"
          :icon="Connection"
          @click="selectTool('polygon')"
        >
          多边形
        </el-button>
      </el-tooltip>
    </el-button-group>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Pointer, Grid, Connection } from '@element-plus/icons-vue'

// 定义 emits
const emit = defineEmits(['tool-selected'])

// 本地状态：追踪当前激活的工具
const activeTool = ref('select')

// 选择工具的方法
const selectTool = (toolName) => {
  activeTool.value = toolName
  emit('tool-selected', toolName)
}
</script>

<style scoped>
.annotation-toolbar {
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fff;
}
</style>
