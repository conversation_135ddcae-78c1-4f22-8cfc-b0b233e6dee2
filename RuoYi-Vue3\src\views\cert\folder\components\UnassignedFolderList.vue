<template>
  <div class="unassigned-folder-list">
    <div v-loading="loading" class="folder-list">
      <!-- 未关联文件夹 -->
      <div v-if="unassignedFolders.length > 0" class="folder-section">
        <div class="section-title">未关联文件夹</div>
        <FolderListItem
          v-for="folder in unassignedFolders"
          :key="folder.folderId"
          :folder-name="folder.folderName"
          :thumbnail-url="getMainPicUrl(folder.mainPicPath)"
          :show-checkbox="false"
          :show-radio="true"
          :is-selected="selectedFolderId === folder.folderId"
          :show-compare-button="true"
          @radio-change="handleFolderSelect(folder.folderId)"
          @folder-clicked="handleFolderSelect(folder.folderId)"
          @compare-clicked="handleCompareClicked(folder)"
        />
      </div>

      <!-- 已关联文件夹 -->
      <div v-if="associatedFolders.length > 0" class="folder-section">
        <div class="section-title">已关联文件夹</div>
        <AssociatedFolderItem
          v-for="folder in associatedFolders"
          :key="folder.folderId"
          :folder="folder"
          :thumbnail-url="getMainPicUrl(folder.mainPicPath)"
          @edit-version-clicked="handleEditVersionClicked"
          @reassociate-clicked="handleReassociateClicked"
          @cancel-association-clicked="handleCancelAssociationClicked"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import FolderListItem from './FolderListItem/'
import AssociatedFolderItem from './AssociatedFolderItem.vue'
import { getFolderList, type FolderInfoVO } from '@/api/cert/folder'

// 定义Props
interface Props {
  taskId: string
}

const props = defineProps<Props>()

// 定义Emits
const emit = defineEmits<{
  'selection-change': [selection: { folderIds: string[], folders: FolderInfoVO[] }]
  'folder-selected': [folder: FolderInfoVO | null]
  'compare-clicked': [folder: FolderInfoVO]
  'edit-version-clicked': [folder: FolderInfoVO]      // 新增：编辑版本
  'reassociate-clicked': [folder: FolderInfoVO]       // 新增：重新关联
  'cancel-association-clicked': [folder: FolderInfoVO] // 新增：取消关联
}>()

// 响应式数据
const loading = ref(false)
const allFolders = ref<FolderInfoVO[]>([]) // 所有文件夹
const unassignedFolders = ref<FolderInfoVO[]>([]) // 未关联文件夹
const associatedFolders = ref<FolderInfoVO[]>([]) // 已关联文件夹
const selectedFolders = ref<string[]>([])
const selectedFolderId = ref<string>('') // 单选模式：当前选中的文件夹ID

// 添加比对事件
const handleCompareClicked = (folder: FolderInfoVO) => {
  console.log('点击比对按钮:', folder)
  emit('compare-clicked', folder)
}

// 处理编辑版本事件
const handleEditVersionClicked = (folder: FolderInfoVO) => {
  console.log('点击编辑版本按钮:', folder)
  emit('edit-version-clicked', folder)
}

// 处理重新关联事件
const handleReassociateClicked = (folder: FolderInfoVO) => {
  console.log('点击重新关联按钮:', folder)
  emit('reassociate-clicked', folder)
}

// 处理取消关联事件
const handleCancelAssociationClicked = (folder: FolderInfoVO) => {
  console.log('点击取消关联按钮:', folder)
  emit('cancel-association-clicked', folder)
}

// 获取所有文件夹数据并分组
const loadUnassignedFolders = async () => {
  loading.value = true
  try {
    const response = await getFolderList({
      taskId: props.taskId
    })
    if (response.code === 200) {
      allFolders.value = response.rows || []

      // 按关联状态分组（使用status字段）
      unassignedFolders.value = allFolders.value.filter(folder => folder.status === 'unassociated')
      associatedFolders.value = allFolders.value.filter(folder => folder.status === 'associated')

      console.log('未关联文件夹数量:', unassignedFolders.value.length)
      console.log('已关联文件夹数量:', associatedFolders.value.length)
    } else {
      ElMessage.error(response.msg || '加载文件夹失败')
    }
  } catch (error) {
    console.error('加载文件夹失败:', error)
    ElMessage.error('加载文件夹失败')
  } finally {
    loading.value = false
  }
}

// 处理文件夹选择（保留多选功能用于兼容）
const handleSelection = (folderId: string) => {
  const index = selectedFolders.value.indexOf(folderId)
  if (index > -1) {
    selectedFolders.value.splice(index, 1)
  } else {
    selectedFolders.value.push(folderId)
  }

  // 触发选择变化事件
  const selectedFolderObjects = unassignedFolders.value.filter(folder =>
    selectedFolders.value.includes(folder.folderId)
  )

  emit('selection-change', {
    folderIds: selectedFolders.value,
    folders: selectedFolderObjects
  })
}

// 处理文件夹单选
const handleFolderSelect = (folderId: string) => {
  console.log('选择文件夹:', folderId)

  // 如果点击的是已选中的文件夹，则取消选择
  if (selectedFolderId.value === folderId) {
    selectedFolderId.value = ''
    emit('folder-selected', null)
  } else {
    selectedFolderId.value = folderId
    const selectedFolder = unassignedFolders.value.find(folder => folder.folderId === folderId)
    emit('folder-selected', selectedFolder || null)
  }
}

// 构建主图URL
const getMainPicUrl = (mainPicPath: string | undefined) => {
  if (!mainPicPath) {
    console.log('mainPicPath 为空，返回空字符串')
    return ''
  }

  console.log('构建主图URL，原始路径:', mainPicPath)

  // 如果已经是完整URL，直接返回
  if (mainPicPath.startsWith('http://') || mainPicPath.startsWith('https://')) {
    console.log('已是完整URL，直接返回:', mainPicPath)
    return mainPicPath
  }

  // 构建MinIO访问URL
  const directMinioUrl = `http://localhost:9000/xjlfiles/${mainPicPath}`
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`

  console.log('生成的代理URL:', proxyUrl)
  return proxyUrl
}

// 刷新数据方法（供父组件调用）
const refreshData = async () => {
  await loadUnassignedFolders()
  // 清空选择
  selectedFolders.value = []
  selectedFolderId.value = ''
  emit('selection-change', { folderIds: [], folders: [] })
  emit('folder-selected', null)
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 生命周期
onMounted(() => {
  if (props.taskId) {
    loadUnassignedFolders()
  }
})
</script>

<style scoped>
.unassigned-folder-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  height: 100%;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.folder-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 8px;
}
</style>
