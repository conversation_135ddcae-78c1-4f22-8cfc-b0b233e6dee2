# 标注组件路径更新说明

## 概述

标注组件已从全局目录移动到 `src/views/samples/components/annotations/` 目录，并更新了所有相关的依赖引用。

## 迁移详情

### 删除的文件
- `src/views/samples/components/AnnotationToolbar.vue` (旧版本)
- `src/views/samples/components/AnnotationList.vue` (旧版本)  
- `src/views/samples/components/AnnotationCanvas.vue` (旧版本)

### 保留的文件
- `src/views/samples/components/annotations/AnnotationToolbar.vue` ✅
- `src/views/samples/components/annotations/AnnotationList.vue` ✅
- `src/views/samples/components/annotations/AnnotationCanvas.vue` ✅
- `src/views/samples/components/annotations/ExampleUsage.vue` ✅
- `src/views/samples/components/annotations/README.md` ✅

## 主要变化

### 1. 组件导入路径更新
```javascript
// 旧路径
import AnnotationToolbar from './components/AnnotationToolbar.vue'

// 新路径
import AnnotationToolbar from './components/annotations/AnnotationToolbar.vue'
```

### 2. 组件接口简化

#### AnnotationToolbar
**旧接口**:
```vue
<AnnotationToolbar
  @save-annotation="handleSaveAnnotation"
  @clear-annotation="handleClearAnnotation"
  @tool-change="handleToolChange"
  @category-change="handleCategoryChange"
  @color-change="handleColorChange"
  @undo="handleUndo"
  :can-save="hasAnnotationChanges"
  :saving="saving"
  :can-undo="canUndo"
/>
```

**新接口**:
```vue
<AnnotationToolbar
  @tool-selected="handleToolChange"
/>
```

#### AnnotationCanvas
**旧接口**:
```vue
<AnnotationCanvas
  :image-data="selectedImage"
  :annotations="currentAnnotations"
  :current-tool="currentTool"
  :selected-category="selectedCategory"
  :annotation-color="annotationColor"
  @annotation-change="handleAnnotationChange"
  @annotation-select="handleAnnotationSelect"
/>
```

**新接口**:
```vue
<AnnotationCanvas
  :imageUrl="selectedImage?.imageUrl"
  :tool="currentTool"
  v-model="currentAnnotations"
  @select-annotation="handleAnnotationSelect"
/>
```

#### AnnotationList
**旧接口**:
```vue
<AnnotationList
  :annotations="currentAnnotations"
  :selected-annotation="selectedAnnotation"
  @annotation-select="handleAnnotationSelect"
  @annotation-delete="handleAnnotationDelete"
  @annotation-update="handleAnnotationUpdate"
  @clear-all="handleClearAll"
/>
```

**新接口**:
```vue
<AnnotationList
  v-model="currentAnnotations"
  @select-annotation="handleAnnotationSelect"
/>
```

### 3. 新增功能

#### 工具栏操作按钮
由于新的 AnnotationToolbar 只提供工具选择，额外的操作按钮被移到了独立的区域：

```vue
<div class="toolbar-actions">
  <el-button type="primary" @click="handleSaveAnnotation" :loading="saving">
    保存标注
  </el-button>
  <el-button type="danger" @click="handleClearAnnotation">
    清空标注
  </el-button>
  <el-button @click="handleUndo" :disabled="!canUndo">
    撤销
  </el-button>
</div>
```

#### 自动数据同步
新组件使用 v-model 模式，添加了监听器自动处理数据变化：

```javascript
watch(currentAnnotations, (newAnnotations, oldAnnotations) => {
  if (oldAnnotations && newAnnotations !== oldAnnotations) {
    hasAnnotationChanges.value = true
  }
}, { deep: true })
```

## 技术优势

### 1. 基于成熟库
- 使用 `@recogito/annotorious` 专业标注库
- 减少自定义代码维护成本
- 提供更稳定的标注功能

### 2. 更好的复用性
- 组件位于 `src/views/samples/components/annotations/` 目录
- 可在多个页面中复用
- 标准化的接口设计

### 3. 简化的API
- 使用 v-model 双向绑定
- 减少事件处理复杂度
- 更符合 Vue 3 最佳实践

## 兼容性说明

### 保留的方法
为了保持向后兼容，以下方法仍然保留：
- `handleAnnotationDelete`
- `handleAnnotationUpdate` 
- `handleClearAll`

### 新增依赖
- `@recogito/annotorious`: 图片标注库

## 使用建议

1. **新项目**: 直接使用 `src/views/samples/components/annotations/` 下的组件
2. **现有项目**: 参考 `FolderDetailView.vue` 的迁移方式
3. **自定义需求**: 基于新组件进行扩展，而不是修改核心组件

## 测试建议

1. 测试基本标注功能（矩形、多边形、点标注）
2. 测试标注数据的保存和加载
3. 测试组件间的数据同步
4. 测试响应式布局

## 后续优化

1. 可以考虑移除不再使用的旧方法
2. 添加更多标注工具（如圆形、箭头等）
3. 增强标注样式自定义功能
4. 添加标注数据导入导出功能
