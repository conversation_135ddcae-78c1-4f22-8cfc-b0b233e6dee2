<template>
  <TopNavbar />
  <div class="app-container">

    <h1 class="news-list-title">信息发布</h1>
    <el-row :gutter="20" style="display: flex;">
      <el-col :xs="24" :sm="24" :md="8" :lg="8" style="flex-shrink: 0;">
        <SearchForm
          :queryParams="queryParams"
          v-model:dateRange="dateRange"
          :categroyList="categroyList"
          :deptOptions="deptOptions"
          @query="handleQuery"
          @reset="resetQuery"
        />
        <el-col :xs="24" :sm="24" :md="24" :lg="24">
          <CountNews />
        </el-col>
      </el-col>
      <el-col :xs="24" :sm="24" :md="16" :lg="16" style="flex-grow: 1;">
      <el-card class="box-card">
        <template #header>
          <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
            <span class="title-large">{{ receivedTitle || '新闻列表'}}</span>
            <!-- 可以根据需求添加更多按钮 -->
          </div>
        </template>
        <div v-loading="loading" class="news-list">
          <!-- 修改表格为列表项 -->
          <div v-for="(news, index) in newsList" :key="index" class="news-item" @click="goToNewsDetail(news.newsId)">
            <div class="news-header">
              <span class="news-title">{{ news.newsTitle }}</span>
              <span class="news-time">{{ parseTime(news.uploadTime, '{y}-{m}-{d}') }}</span>
            </div>
            <div class="news-footer">
              <span class="news-dept">{{ news.deptName }}</span>
              <span class="news-author">{{ news.author }}</span>
            </div>
          </div>
          <!-- 当 newsList 为空时，显示提示信息 -->
          <div v-if="newsList.length === 0" class="news-item">
            <div class="news-header">
              <span class="news-title">暂无文章数据</span>
            </div>
          </div>
        </div>
      </el-card>
      <pagination
      v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleSizeChange"
      />
      </el-col>
    </el-row>
    <FooterComponent />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, toRefs, watch } from 'vue';
import { listNews } from "@/api/news/news";
import { PublicDeptList } from "@/api/system/dept";
import { listCategroy } from '@/api/news/categroy';
import CountNews from '@/views/news/components/CountNews.vue';
import { useRouter, useRoute } from 'vue-router';
import SearchForm from '@/views/news/components/SearchForm.vue';
import FooterComponent from '@/views/news/components/FooterComponent.vue';
import TopNavbar from '@/views/news/components/TopNavbar.vue';

const receivedTitle = ref('');
const { proxy } = getCurrentInstance();
const newsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const deptOptions = ref([]); // 部门树数据
const categroyList = ref([]);
const router = useRouter();
const route = useRoute();
const dateRange = ref([]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    newsTitle: null,
    newsType: null,
    newsContent: null,
    status: null,
    reviewer: null,
    reviewTime: null,
    author: null,
    uploadTime: null,
    deptName: null,
    headlinePic: null,
    deptId: null,
    startTime: null,
    endTime: null
  },
});
const { queryParams } = toRefs(data);
// 新增重置查询参数的函数
const resetQueryParams = () => {
  data.queryParams = {
    pageNum: 1,
    pageSize: 10,
    newsTitle: null,
    newsType: undefined,
    newsContent: null,
    status: null,
    reviewer: null,
    reviewTime: null,
    author: null,
    uploadTime: null,
    deptName: null,
    headlinePic: null,
    deptId: null,
    startTime: null,
    endTime: null
  };
};

// 监听日期范围变化
watch(dateRange, (newValue) => {
    if (newValue && newValue.length === 2) {
        queryParams.value.startTime = newValue[0];
        queryParams.value.endTime = newValue[1];
    } else {
        queryParams.value.startTime = null;
        queryParams.value.endTime = null;
    }
});

function getList() {
  loading.value = true;
  listNews(queryParams.value).then(response => {
    newsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  const searchFields = [
    'newsTitle',
    'newsContent',  // 添加这行
    'author',
    'newsType',
    'deptId',
    'startTime',
    'endTime'
  ];

  const hasSearchParams = searchFields.some((field) => {
    const value = queryParams.value[field];
    if (typeof value === 'string') {
      return value.trim() !== '';
    }
    return value !== null && value !== undefined;
  });

  if (hasSearchParams) {
    queryParams.value.pageNum = 1;
    receivedTitle.value = '新闻列表';
    getList();
  }
}

//文章条数变化时触发
function handleSizeChange(val) {
  if (receivedTitle.value === '新闻列表') {
    getList();
  } else {
    const selectedCategory = categroyList.value.find(item => item.categroyName === receivedTitle.value);
    if (selectedCategory) {
      queryParams.value.newsType = selectedCategory.categroyID;
    }
    getList();
    resetQueryParams();
  }
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dateRange.value = [];
  resetQueryParams();
}
/** 查询部门列表 */
function getDeptList() {
  PublicDeptList().then(response => {
    deptOptions.value = response.data;
  });
}
const goToNewsDetail = (newsId) => {
  router.push({ name: 'NewsDetail', params: { newsId } });
};
// 获取文章类型列表
const getCategroyList = async () => {
  try {
    const response = await listCategroy({ pageNum: 1, pageSize: 100 });
    if (response && response.rows) {
      categroyList.value = response.rows;
      receivedTitle.value = router.currentRoute.value.query.title || '新闻列表';
      const selectedCategory = categroyList.value.find(item => item.categroyName === receivedTitle.value);
      if (selectedCategory) {
        queryParams.value.newsType = selectedCategory.categroyID;
      }
    } else {
      proxy.$modal.msgError('文章类型列表数据格式错误，请联系管理员');
    }
  } catch (error) {
    proxy.$modal.msgError('获取文章类型列表失败，请检查网络连接或联系管理员');
  }
};

// 在组件挂载时获取文章类型列表，并检查路由参数
onMounted(async () => {
  await getDeptList();
  await getCategroyList();

  // 检查路由参数并更新查询参数
  if (route.query) {
    Object.assign(queryParams.value, route.query);
    if (route.query.dateRange) {
      dateRange.value = route.query.dateRange.split('-');
      queryParams.value.startTime = dateRange.value[0];
      queryParams.value.endTime = dateRange.value[1];
    }
  }
  await getList();
  resetQueryParams();
});

</script>

<style scoped lang="scss">
.news-list-title {
  font-size: 28px;
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-weight: bold;
}

.app-container {
  padding: 20px 10%;
  background-color: #f5f9fd;
}

.news-list {
  .news-item {
    margin-bottom: 20px;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    min-height: 40px;
    transition: all 0.3s ease;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #f0f7ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }

  .news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .news-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    transition: color 0.3s;

    &:hover {
      color: #409EFF;
    }
  }

  .news-time {
    font-size: 16px;
    color: #666;
  }

  .news-footer {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 15px;
  }

  .news-dept, .news-author {
    font-size: 14px;
    color: #666;
    line-height: 1;
    vertical-align: middle;

    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 4px;
      background-color: #999;
      border-radius: 50%;
      margin-right: 5px;
      vertical-align: middle;
    }
  }
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .card-header {
    padding: 15px 0;

    .title-large {
      font-size: 22px;
      font-weight: bold;
      color: #333;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background-color: #409EFF;
        border-radius: 2px;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 15px 5%;
  }
}
</style>
