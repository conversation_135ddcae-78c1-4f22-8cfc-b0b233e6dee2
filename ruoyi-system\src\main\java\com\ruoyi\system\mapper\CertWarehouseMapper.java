package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CertWarehouse;

/**
 * 证件仓库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface CertWarehouseMapper 
{
    /**
     * 查询证件仓库
     * 
     * @param id 证件仓库主键
     * @return 证件仓库
     */
    public CertWarehouse selectCertWarehouseById(Long id);

    /**
     * 查询证件仓库列表
     * 
     * @param certWarehouse 证件仓库
     * @return 证件仓库集合
     */
    public List<CertWarehouse> selectCertWarehouseList(CertWarehouse certWarehouse);

    /**
     * 新增证件仓库
     * 
     * @param certWarehouse 证件仓库
     * @return 结果
     */
    public int insertCertWarehouse(CertWarehouse certWarehouse);

    /**
     * 修改证件仓库
     * 
     * @param certWarehouse 证件仓库
     * @return 结果
     */
    public int updateCertWarehouse(CertWarehouse certWarehouse);

    /**
     * 删除证件仓库
     * 
     * @param id 证件仓库主键
     * @return 结果
     */
    public int deleteCertWarehouseById(Long id);

    /**
     * 批量删除证件仓库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCertWarehouseByIds(Long[] ids);
}
