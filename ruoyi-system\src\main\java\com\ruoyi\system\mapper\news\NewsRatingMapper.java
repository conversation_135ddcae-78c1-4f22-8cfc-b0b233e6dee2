package com.ruoyi.system.mapper.news;

import java.util.List;
import com.ruoyi.system.domain.news.NewsRating;

import org.apache.ibatis.annotations.Param;

/**
 * 文章评分Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface NewsRatingMapper 
{
    /**
     * 查询文章评分
     * 
     * @param ratingId 文章评分主键
     * @return 文章评分
     */
    public NewsRating selectNewsRatingByRatingId(Long ratingId);

    /**
     * 查询文章评分列表
     * 
     * @param newsRating 文章评分
     * @return 文章评分集合
     */
    public List<NewsRating> selectNewsRatingList(NewsRating newsRating);

    /**
     * 新增文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    public int insertNewsRating(NewsRating newsRating);

    /**
     * 修改文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    public int updateNewsRating(NewsRating newsRating);

    /**
     * 删除文章评分
     * 
     * @param ratingId 文章评分主键
     * @return 结果
     */
    public int deleteNewsRatingByRatingId(Long ratingId);

    /**
     * 批量删除文章评分
     * 
     * @param ratingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewsRatingByRatingIds(Long[] ratingIds);

    /**
     * 查询文章平均评分
     * 
     * @param newsId 文章ID
     * @return 平均评分
     */
    public Double selectAvgRatingByNewsId(@Param("newsId") Long newsId);
    
    /**
     * 查询文章评分人数
     * 
     * @param newsId 文章ID
     * @return 评分人数
     */
    public Integer selectRatingCountByNewsId(@Param("newsId") Long newsId);
    
    /**
     * 查询用户对文章的评分
     * 
     * @param newsId 文章ID
     * @param userId 用户ID
     * @return 评分信息
     */
    public NewsRating selectRatingByNewsIdAndUserId(@Param("newsId") Long newsId, @Param("userId") Long userId);
}
