<template>
  <div class="compare-area">
    <!-- 比对区域标题 -->
    <div class="compare-header">
      <h3 class="compare-title">图片比对</h3>
      <div class="compare-actions">
        <el-button
          v-if="selectedVersions.length > 1"
          size="small"
          :icon="ArrowLeft"
          :disabled="currentVersionIndex === 0"
          @click="handlePreviousVersion"
        />
        <span v-if="selectedVersions.length > 1" class="version-indicator">
          {{ currentVersionIndex + 1 }} / {{ selectedVersions.length }}
        </span>
        <el-button
          v-if="selectedVersions.length > 1"
          size="small"
          :icon="ArrowRight"
          :disabled="currentVersionIndex === selectedVersions.length - 1"
          @click="handleNextVersion"
        />
        <!-- 移除关闭按钮，因为现在是弹窗 -->
      </div>
    </div>

    <!-- 比对内容区域 -->
    <div class="compare-container">
      <!-- 左栏：选中文件夹 -->
      <div class="left-panel">
        <h4 class="panel-title">待处理文件夹</h4>
        <div class="image-container">
          <el-image
            v-if="selectedFolder?.mainPicPath"
            :src="getMainPicUrl(selectedFolder.mainPicPath)"
            :preview-src-list="[getMainPicUrl(selectedFolder.mainPicPath)]"
            fit="contain"
            class="compare-image"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无主图</span>
          </div>
        </div>
        <div class="folder-info">
          <p class="folder-name">{{ selectedFolder?.folderName || '未选择文件夹' }}</p>
        </div>
      </div>

      <!-- 右栏：选中版本 -->
      <div class="right-panel">
        <h4 class="panel-title">
          对比版本
          <span v-if="currentVersion" class="version-code">
            ({{ currentVersion.versionCode }})
          </span>
        </h4>
        <div class="image-container">
          <el-image
            v-if="currentVersion?.mainPicPath"
            :src="getMainPicUrl(currentVersion.mainPicPath)"
            :preview-src-list="[getMainPicUrl(currentVersion.mainPicPath)]"
            fit="contain"
            class="compare-image"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无主图</span>
          </div>
        </div>
        <div v-if="currentVersion" class="version-info">
          <p class="version-description">{{ currentVersion.description || '暂无描述' }}</p>
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="compare-footer">
      <div class="footer-info">
        <span v-if="selectedVersions.length > 0" class="selected-info">
          已选择 {{ selectedVersions.length }} 个版本进行比对
        </span>
      </div>
      <div class="footer-actions">
        <el-button
          v-if="selectedFolder && currentVersion"
          type="primary"
          @click="handleAssociate"
        >
          关联到当前版本
        </el-button>
        <el-button
          v-if="selectedFolder && selectedFolder.reviewStatus === 'pending'"
          type="success"
          @click="handleApprove"
        >
          审核通过
        </el-button>
        <el-button
          v-if="selectedFolder && selectedFolder.reviewStatus === 'pending'"
          type="danger"
          @click="handleReject"
        >
          审核驳回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, ArrowLeft, ArrowRight, Close } from '@element-plus/icons-vue'
import type { FolderInfoVO } from '@/api/cert/folder'
import type { CertVersionVO } from '@/api/cert/version'

// Props
interface Props {
  selectedFolder: FolderInfoVO | null
  selectedVersions: CertVersionVO[]
  currentVersionIndex: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'version-change': [index: number]
  'associate-folder': [folder: FolderInfoVO, version: CertVersionVO]
  'approve-folder': [folder: FolderInfoVO]
  'reject-folder': [folder: FolderInfoVO]
  // 移除 'close-compare': []
}>()

// 计算属性
const currentVersion = computed(() => {
  if (props.selectedVersions.length > 0 && props.currentVersionIndex >= 0) {
    return props.selectedVersions[props.currentVersionIndex]
  }
  return null
})

// 方法
const getMainPicUrl = (mainPicPath: string | undefined) => {
  if (!mainPicPath) {
    return ''
  }
  
  // 如果已经是完整URL，直接返回
  if (mainPicPath.startsWith('http://') || mainPicPath.startsWith('https://')) {
    return mainPicPath
  }
  
  // 构建MinIO访问URL
  const directMinioUrl = `http://localhost:9000/xjlfiles/${mainPicPath}`
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`
  
  return proxyUrl
}

const handlePreviousVersion = () => {
  if (props.currentVersionIndex > 0) {
    emit('version-change', props.currentVersionIndex - 1)
  }
}

const handleNextVersion = () => {
  if (props.currentVersionIndex < props.selectedVersions.length - 1) {
    emit('version-change', props.currentVersionIndex + 1)
  }
}

const handleAssociate = () => {
  if (props.selectedFolder && currentVersion.value) {
    emit('associate-folder', props.selectedFolder, currentVersion.value)
  }
}

const handleApprove = () => {
  if (props.selectedFolder) {
    emit('approve-folder', props.selectedFolder)
  }
}

const handleReject = () => {
  if (props.selectedFolder) {
    emit('reject-folder', props.selectedFolder)
  }
}

// 移除 handleClose 方法
</script>

<style scoped lang="scss">


.compare-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f8f9fa;
}

.compare-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.compare-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-indicator {
  font-size: 14px;
  color: #606266;
  margin: 0 8px;
}

.compare-container {
  display: flex;
  min-height: 400px;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.left-panel {
  border-right: 1px solid #e4e7ed;
}

.panel-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.version-code {
  font-weight: normal;
  color: #409eff;
}

.image-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #fafafa;
  margin-bottom: 16px;
}

.compare-image {
  width: 100%;
  height: 280px;
  object-fit: contain;
  border-radius: 6px;
}

.image-placeholder,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 8px;
  }
}

.image-error {
  color: #f56c6c;
}

.folder-info,
.version-info {
  text-align: center;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.folder-name {
  margin: 0;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.version-description {
  margin: 0;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.compare-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #f8f9fa;
}

.footer-info {
  flex: 1;
}

.selected-info {
  font-size: 14px;
  color: #606266;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .compare-container {
    flex-direction: column;
  }
  
  .left-panel {
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .image-container {
    min-height: 200px;
  }
  
  .compare-image {
    height: 180px;
  }
}

/* 调整弹窗内容的样式 */
:deep(.el-dialog__body) {
  padding: 0;
}
</style>
