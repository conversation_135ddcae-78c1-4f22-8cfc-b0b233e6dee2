/**
 * 连接测试工具
 * 用于诊断网络连接、检查登录状态等
 */

import { getToken } from '@/utils/auth'
import useUserStore from '@/store/modules/user'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'

/**
 * 检查登录状态
 * @returns {Promise<{isLoggedIn: boolean, token: string|null, userInfo: Object|null}>}
 */
export async function checkLoginStatus() {
  try {
    const token = getToken()
    if (!token) {
      return {
        isLoggedIn: false,
        token: null,
        userInfo: null
      }
    }

    // 尝试获取用户信息来验证token有效性
    const userStore = useUserStore()
    
    // 如果store中已有用户信息，直接返回
    if (userStore.name && userStore.id) {
      return {
        isLoggedIn: true,
        token: token,
        userInfo: {
          userId: userStore.id,
          userName: userStore.name,
          avatar: userStore.avatar,
          roles: userStore.roles,
          permissions: userStore.permissions
        }
      }
    }

    // 否则尝试获取用户信息
    const userInfo = await userStore.getInfo()
    return {
      isLoggedIn: true,
      token: token,
      userInfo: {
        userId: userStore.id,
        userName: userStore.name,
        avatar: userStore.avatar,
        roles: userStore.roles,
        permissions: userStore.permissions
      }
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    return {
      isLoggedIn: false,
      token: getToken(),
      userInfo: null,
      error: error.message
    }
  }
}

/**
 * 重定向到登录页
 */
export function redirectToLogin() {
  try {
    // 清除当前用户状态
    const userStore = useUserStore()
    userStore.logOut().then(() => {
      // 重定向到登录页面
      const currentPath = window.location.pathname
      const loginUrl = `/login${currentPath !== '/' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`
      window.location.href = loginUrl
    }).catch(error => {
      console.error('退出登录失败:', error)
      // 强制跳转到登录页
      window.location.href = '/login'
    })
  } catch (error) {
    console.error('重定向到登录页失败:', error)
    // 直接跳转
    window.location.href = '/login'
  }
}

/**
 * 基本连接测试
 * @returns {Promise<Array>}
 */
async function performBasicTests() {
  const tests = []
  const baseURL = import.meta.env.VITE_APP_BASE_API

  // 测试1: 基本API连通性
  try {
    const response = await fetch(`${baseURL}/system/user/getInfo`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    })
    
    tests.push({
      name: '基本API连通性',
      success: response.ok,
      status: response.status,
      statusText: response.statusText
    })
  } catch (error) {
    tests.push({
      name: '基本API连通性',
      success: false,
      error: error.message
    })
  }

  // 测试2: 静态资源访问
  try {
    const response = await fetch('/favicon.ico', {
      method: 'HEAD',
      timeout: 3000
    })
    
    tests.push({
      name: '静态资源访问',
      success: response.ok,
      status: response.status,
      statusText: response.statusText
    })
  } catch (error) {
    tests.push({
      name: '静态资源访问',
      success: false,
      error: error.message
    })
  }

  return tests
}

/**
 * TUS协议测试
 * @returns {Promise<Object>}
 */
async function performTusTests() {
  try {
    const baseURL = import.meta.env.VITE_APP_BASE_API
    const tusEndpoint = `${baseURL}/tus/upload/`

    // 测试OPTIONS预检请求
    const optionsResponse = await fetch(tusEndpoint, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Tus-Resumable': '1.0.0'
      },
      timeout: 5000
    })

    if (!optionsResponse.ok) {
      return {
        success: false,
        error: `OPTIONS请求失败: ${optionsResponse.status} ${optionsResponse.statusText}`,
        optionsResponse: {
          status: optionsResponse.status,
          statusText: optionsResponse.statusText
        }
      }
    }

    // 测试创建上传
    const createResponse = await fetch(tusEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Tus-Resumable': '1.0.0',
        'Upload-Length': '100',
        'Upload-Metadata': 'filename dGVzdC50eHQ=,filetype dGV4dC9wbGFpbg=='
      },
      timeout: 5000
    })

    return {
      success: createResponse.status === 201,
      optionsResponse: {
        status: optionsResponse.status,
        statusText: optionsResponse.statusText,
        headers: Object.fromEntries(optionsResponse.headers.entries())
      },
      createResponse: {
        status: createResponse.status,
        statusText: createResponse.statusText,
        location: createResponse.headers.get('Location')
      }
    }

  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 生成诊断建议
 * @param {Object} loginStatus 
 * @param {Array} basicTests 
 * @param {Object} tusTests 
 * @returns {Array}
 */
function generateRecommendations(loginStatus, basicTests, tusTests) {
  const recommendations = []

  // 登录状态建议
  if (!loginStatus.isLoggedIn) {
    recommendations.push('请先登录系统')
  }

  // 基本连接建议
  const failedBasicTests = basicTests.filter(test => !test.success)
  if (failedBasicTests.length > 0) {
    recommendations.push('检查网络连接和服务器状态')
    if (failedBasicTests.some(test => test.error && test.error.includes('timeout'))) {
      recommendations.push('网络超时，请检查网络稳定性')
    }
  }

  // TUS协议建议
  if (tusTests && !tusTests.success) {
    recommendations.push('TUS上传服务异常，请联系管理员')
    if (tusTests.error && tusTests.error.includes('CORS')) {
      recommendations.push('可能存在跨域问题，请检查服务器CORS配置')
    }
  }

  // 通用建议
  if (recommendations.length === 0) {
    recommendations.push('网络连接正常，如仍有问题请尝试刷新页面')
  } else {
    recommendations.push('如问题持续存在，请联系技术支持')
  }

  return recommendations
}

/**
 * 运行连接诊断
 * @returns {Promise<Object>}
 */
export async function runConnectionDiagnostics() {
  console.log('🔍 开始运行连接诊断...')
  
  try {
    // 并行执行各项测试
    const [loginStatus, basicTests, tusTests] = await Promise.all([
      checkLoginStatus(),
      performBasicTests(),
      performTusTests()
    ])

    // 生成诊断建议
    const recommendations = generateRecommendations(loginStatus, basicTests, tusTests)

    const result = {
      timestamp: new Date().toISOString(),
      loginStatus,
      basicTests,
      tusTests,
      recommendations,
      success: loginStatus.isLoggedIn && basicTests.every(test => test.success) && tusTests.success
    }

    console.log('🔍 连接诊断完成:', result)
    return result

  } catch (error) {
    console.error('💥 连接诊断异常:', error)
    
    return {
      timestamp: new Date().toISOString(),
      loginStatus: { isLoggedIn: false, error: error.message },
      basicTests: [],
      tusTests: { success: false, error: error.message },
      recommendations: ['诊断过程发生异常，请刷新页面重试', '如问题持续存在，请联系技术支持'],
      success: false,
      error: error.message
    }
  }
}

/**
 * 快速连接检查
 * @returns {Promise<boolean>}
 */
export async function quickConnectionCheck() {
  try {
    const loginStatus = await checkLoginStatus()
    return loginStatus.isLoggedIn
  } catch (error) {
    console.error('快速连接检查失败:', error)
    return false
  }
}

export default {
  runConnectionDiagnostics,
  checkLoginStatus,
  redirectToLogin,
  quickConnectionCheck
} 