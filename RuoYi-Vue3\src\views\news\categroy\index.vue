<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="栏目名称" prop="categroyName">
        <el-input
          v-model="queryParams.categroyName"
          placeholder="请输入栏目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="栏目状态" prop="categroyStatus">
        <el-select v-model="queryParams.categroyStatus" placeholder="请选择栏目状态" clearable style="width: 100px">
          <el-option
            v-for="dict in categroy_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="栏目地址" prop="categroyAddress">
        <el-input
          v-model="queryParams.categroyAddress"
          placeholder="请输入栏目地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="图片需求" prop="pictureStatus">
        <el-select v-model="queryParams.pictureStatus" placeholder="请选择图片需求" clearable style="width: 100px">
          <el-option
            v-for="dict in categroy_picture_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['categroy:categroy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['categroy:categroy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['categroy:categroy:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['categroy:categroy:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="categroyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="栏目编号" align="center" prop="categroyID" />
      <el-table-column label="栏目名称" align="center" prop="categroyName" />
      <el-table-column label="栏目状态" align="center" prop="categroyStatus">
        <template #default="scope">
          <dict-tag :options="categroy_status" :value="scope.row.categroyStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="栏目地址" align="center" prop="categroyAddress" />
      <el-table-column label="图片需求" align="center" prop="pictureStatus">
        <template #default="scope">
          <dict-tag :options="categroy_picture_status" :value="scope.row.pictureStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="显示顺序" align="center" prop="displayOrder" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['categroy:categroy:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['categroy:categroy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改栏目信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="categroyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="栏目名称" prop="categroyName">
          <el-input v-model="form.categroyName" placeholder="请输入栏目名称" />
        </el-form-item>
        <el-form-item label="栏目状态" prop="categroyStatus">
          <el-radio-group v-model="form.categroyStatus">
            <el-radio
              v-for="dict in categroy_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="栏目地址" prop="categroyAddress">
          <el-input v-model="form.categroyAddress" placeholder="请输入栏目地址" />
        </el-form-item>
        <el-form-item label="图片需求" prop="pictureStatus">
          <el-radio-group v-model="form.pictureStatus">
            <el-radio
              v-for="dict in categroy_picture_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="显示顺序" prop="displayOrder">
          <el-input-number v-model="form.displayOrder" :min="1" :max="10" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Categroy">
import { listCategroy, getCategroy, delCategroy, addCategroy, updateCategroy } from "@/api/news/categroy"

const { proxy } = getCurrentInstance()
const { categroy_picture_status, categroy_status } = proxy.useDict('categroy_picture_status', 'categroy_status')

const categroyList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
// 定义每页显示的数量为常量
const PAGE_SIZE = 10;
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: PAGE_SIZE, // 使用常量设置每页显示数量
    categroyName: null,
    categroyStatus: null,
    categroyAddress: null,
    pictureStatus: null
  },
  rules: {
    categroyName: [
      { required: true, message: "栏目名称不能为空", trigger: "blur" }
    ],
    displayOrder: [
      { required: true, message: "显示顺序不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询栏目信息列表 */
function getList() {
  loading.value = true
  listCategroy(queryParams.value).then(response => {
    console.log(response)
    categroyList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    categroyID: null,
    categroyName: null,
    categroyStatus: '1',
    categroyAddress: null,
    pictureStatus: '0',
    displayOrder: 1
  }
  proxy.resetForm("categroyRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.categroyID)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加栏目信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _categroyID = row.categroyID || ids.value
  getCategroy(_categroyID).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改栏目信息"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["categroyRef"].validate(valid => {
    if (valid) {
      if (form.value.categroyID != null) {
        updateCategroy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCategroy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _categroyIDs = row.categroyID || ids.value
  proxy.$modal.confirm('是否确认删除栏目信息编号为"' + _categroyIDs + '"的数据项？').then(function() {
    return delCategroy(_categroyIDs)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('categroy/categroy/export', {
    ...queryParams.value
  }, `categroy_${new Date().getTime()}.xlsx`)
}

getList()
</script>
