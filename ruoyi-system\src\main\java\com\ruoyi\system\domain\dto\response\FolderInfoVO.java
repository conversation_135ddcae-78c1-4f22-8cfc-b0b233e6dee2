package com.ruoyi.system.domain.dto.response;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.mongo.FolderInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文件夹信息响应VO
 * 用于展示样本文件夹的详情
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class FolderInfoVO {
    
    /** MongoDB主键 */
    private String id;
    
    /** 业务文件夹ID */
    private String folderId;
    
    /** 关联任务ID */
    private String taskId;
    
    /** 文件夹类型 */
    private String folderType;
    
    /** 文件夹名称 */
    private String folderName;

    /** 主图路径 */
    private String mainPicPath;

    /** 国家信息 */
    private Country countryInfo;
    
    /** 证件类型信息 */
    private CertType certInfo;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 文件数量 */
    private Integer fileCount;
    
    /** 已处理文件数量 */
    private Integer processedFileCount;
    
    /** 状态 */
    private String status;

    /** 审核状态 */
    private String reviewStatus;

    /** 版本关联方式 */
    private String versionAssociationMethod;

    /** 版本信息 */
    private AssociationVersionInfo associationVersionInfo;

    /** 预解析版本信息 */
    private PreParseVersionInfo preParseVersionInfo;

    /** 部门信息 */
    private SysDept deptInfo;
    
    /** 上传者信息 */
    private SysUser uploaderInfo;
    
    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /**
     * 关联版本信息内嵌类
     * 与 FolderInfo.java 中的 AssociationVersionInfo 保持一致
     */
    @Data
    public static class AssociationVersionInfo {
        /** 关联的版本ID */
        private String versionId;

        /** 关联的版本编码 */
        private String versionCode;

        /** 关联类型：CREATED_NEW（新建版本关联）、LINKED_EXISTING（关联已有版本） */
        private String associationType;

        /** 关联操作的用户ID */
        private Long userId;

        /** 用户名 */
        private String userName;

        /** 部门ID */
        private Long deptId;

        /** 部门名称 */
        private String deptName;

        /** 首次关联时间 */
        private Date associationTime;

        /** 最后修改人ID */
        private Long lastModifiedBy;

        /** 最后修改人姓名 */
        private String lastModifiedByName;

        /** 最后修改时间 */
        private Date lastModifiedTime;
    }

    /**
     * 预解析版本信息内嵌类
     */
    @Data
    public static class PreParseVersionInfo {
        /** 原始文件夹名称 */
        private String originalFolderName;

        /** 解析后的版本代码 */
        private String parsedVersionCode;

        /** 解析的国家名称 */
        private String countryName;

        /** 匹配的国家代码 */
        private String countryCode;

        /** 解析的证件类型名称 */
        private String certTypeName;

        /** 匹配的证件类型代码 */
        private String certTypeCode;

        /** 签发年份 */
        private String issueYear;

        /** 证件号前缀 */
        private String certNumberPrefix;

        /** 签发地 */
        private String issuePlace;

        /** 解析状态: SUCCESS, PARTIAL, FAILED */
        private String parseStatus;

        /** 解析错误信息 */
        private List<String> parseErrors;

        /** 解析时间 */
        private Date parseTime;
    }
}
