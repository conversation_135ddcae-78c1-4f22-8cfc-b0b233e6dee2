package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.dto.request.FolderQueryDTO;
import com.ruoyi.system.domain.dto.request.FolderUpdateDTO;
import com.ruoyi.system.domain.dto.request.ReviewActionDTO;

import com.ruoyi.system.domain.dto.response.FolderInfoVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 文件夹信息Service接口
 * 职责: 管理样本文件夹的业务逻辑，包括查询、根据任务ID删除，以及与图片服务的交互
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IFolderInfoService {
    
    /**
     * 创建文件夹信息
     * 
     * @param folderInfo 文件夹信息
     * @return 创建的文件夹信息
     */
    FolderInfo createFolder(FolderInfo folderInfo);
    
    /**
     * 根据文件夹ID获取文件夹信息
     *
     * @param folderId 文件夹ID
     * @return 文件夹信息VO
     */
    FolderInfoVO getFolderById(String folderId);

    /**
     * 根据业务文件夹ID获取文件夹实体
     *
     * @param folderId 业务文件夹ID
     * @return 文件夹实体
     */
    FolderInfo getFolderByFolderId(String folderId);

    /**
     * 根据MongoDB主键获取文件夹信息
     *
     * @param id MongoDB主键
     * @return 文件夹实体
     */
    FolderInfo getFolderByMongoId(String id);
    
    /**
     * 根据任务ID获取文件夹列表
     * 
     * @param taskId 任务ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByTaskId(String taskId);
    
    /**
     * 根据版本ID获取文件夹列表
     * 
     * @param versionId 版本ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByVersionId(String versionId);
    
    /**
     * 根据文件夹类型获取文件夹列表
     * 
     * @param folderType 文件夹类型
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByType(String folderType);
    
    /**
     * 根据国家ID获取文件夹列表
     * 
     * @param countryId 国家ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByCountryId(Long countryId);
    
    /**
     * 根据证件类型ID获取文件夹列表
     * 
     * @param certTypeId 证件类型ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByCertTypeId(Long certTypeId);
    
    /**
     * 根据发行年份获取文件夹列表
     * 
     * @param issueYear 发行年份
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByIssueYear(String issueYear);
    
    /**
     * 根据状态获取文件夹列表
     * 
     * @param status 状态
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByStatus(String status);
    
    /**
     * 根据部门ID获取文件夹列表
     * 
     * @param deptId 部门ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByDeptId(Long deptId);
    
    /**
     * 根据上传者ID获取文件夹列表
     * 
     * @param userId 用户ID
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByUploaderId(Long userId);
    
    /**
     * 根据任务ID和文件夹名称获取文件夹
     * 
     * @param taskId 任务ID
     * @param folderName 文件夹名称
     * @return 文件夹信息
     */
    FolderInfoVO getFolderByTaskIdAndName(String taskId, String folderName);
    
    /**
     * 根据创建时间范围获取文件夹列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByCreateTimeRange(Date startTime, Date endTime);
    
    /**
     * 根据复合条件获取文件夹列表
     * 
     * @param countryId 国家ID
     * @param certTypeId 证件类型ID
     * @param issueYear 发行年份
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByComplexCondition(Long countryId, Long certTypeId, String issueYear);
    
    /**
     * 更新文件夹信息
     * 
     * @param folderInfo 文件夹信息
     * @return 更新后的文件夹信息
     */
    FolderInfo updateFolder(FolderInfo folderInfo);
    
    /**
     * 更新文件夹状态
     * 
     * @param folderId 文件夹ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateFolderStatus(String folderId, String status);
    
    /**
     * 删除文件夹信息（级联删除图片）
     * 
     * @param folderId 文件夹ID
     * @return 是否删除成功
     */
    boolean deleteFolder(String folderId);
    
    /**
     * 根据任务ID删除所有文件夹（级联删除图片）
     * 
     * @param taskId 任务ID
     * @return 删除的文件夹数量
     */
    int deleteFoldersByTaskId(String taskId);
    
    /**
     * 获取文件夹统计信息
     * 
     * @param folderId 文件夹ID
     * @return 统计信息Map（包含图片数量等）
     */
    Map<String, Object> getFolderStatistics(String folderId);
    
    /**
     * 批量创建文件夹
     * 
     * @param folderInfos 文件夹信息列表
     * @return 创建的文件夹列表
     */
    List<FolderInfo> batchCreateFolders(List<FolderInfo> folderInfos);
    
    /**
     * 检查文件夹是否存在
     * 
     * @param taskId 任务ID
     * @param folderName 文件夹名称
     * @return 是否存在
     */
    boolean existsByTaskIdAndName(String taskId, String folderName);
    
    /**
     * 获取任务的文件夹统计
     * 
     * @param taskId 任务ID
     * @return 统计信息Map
     */
    Map<String, Object> getTaskFolderStatistics(String taskId);
    
    /**
     * 设置文件夹为标准样本
     * 
     * @param folderId 文件夹ID
     * @param versionId 版本ID
     * @return 是否设置成功
     */
    boolean setAsStandardSample(String folderId, String versionId);
    
    /**
     * 取消文件夹的标准样本状态
     *
     * @param folderId 文件夹ID
     * @return 是否取消成功
     */
    boolean unsetStandardSample(String folderId);

    /**
     * 关联文件夹到已有版本
     * 将一个上传后处于"待处理"状态的文件夹，关联到一个已经存在的证件版本
     *
     * @param folderId 文件夹ID
     * @param versionId 版本ID
     * @param associationType 关联类型：CREATED_NEW（新建版本关联）、LINKED_EXISTING（关联已有版本）
     * @return 是否成功
     */
    boolean associateFolderToVersion(String folderId, String versionId, String associationType);

    /**
     * 重新关联文件夹到其他版本
     * 修改已关联文件夹的版本关联
     *
     * @param folderId 文件夹ID
     * @param newVersionId 新版本ID
     * @return 是否成功
     */
    boolean reassociateFolder(String folderId, String newVersionId);

    /**
     * 取消文件夹关联
     * 将文件夹的关联信息清空，恢复为未关联状态
     *
     * @param folderId 文件夹ID
     * @return 是否成功
     */
    boolean cancelFolderAssociation(String folderId);

    /**
     * 根据关键词搜索文件夹
     *
     * @param keyword 关键词
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByKeyword(String keyword);

    /**
     * 获取所有文件夹列表
     *
     * @return 所有文件夹列表
     */
    List<FolderInfoVO> getAllFolders();

    /**
     * 根据国家代码获取文件夹列表
     *
     * @param countryCode 国家代码
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByCountryCode(String countryCode);

    /**
     * 根据证件类型代码获取文件夹列表
     *
     * @param certTypeCode 证件类型代码
     * @return 文件夹列表
     */
    List<FolderInfoVO> getFoldersByCertTypeCode(String certTypeCode);

    /**
     * 获取文件夹详情 (getFolderById的别名)
     *
     * @param folderId 文件夹ID
     * @return 文件夹信息VO
     */
    default FolderInfoVO getFolderDetails(String folderId) {
        return getFolderById(folderId);
    }

    /**
     * 更新文件夹信息
     * 支持更新文件夹的各种属性，包括基本信息、证件信息、状态等
     *
     * @param folderId 文件夹ID
     * @param updateDTO 更新数据DTO
     * @return 是否更新成功
     */
    boolean updateFolderInfo(String folderId, FolderUpdateDTO updateDTO);

    // ==================== 版本审核和样本分类相关方法 ====================

    /**
     * 第三方版本检测
     * 通过第三方服务检测文件夹中的样本图片，自动识别可能匹配的证件版本
     *
     * @param folderId 文件夹ID
     * @return 第三方检测结果
     */
    FolderInfo.ThirdPartyDetectionResult detectVersionByThirdParty(String folderId);

    /**
     * 获取待审核文件夹列表
     * 查询需要人工审核的文件夹，支持按条件筛选
     *
     * @param queryDTO 查询条件DTO
     * @return 待审核文件夹列表
     */
    List<FolderInfo> getPendingReviewFolders(FolderQueryDTO queryDTO);

    /**
     * 审核文件夹版本关联
     * 对文件夹的版本关联进行人工审核，可以批准或驳回
     *
     * @param folderId 文件夹ID
     * @param dto 审核操作DTO
     * @return 是否审核成功
     */
    boolean reviewFolderVersion(String folderId, ReviewActionDTO dto);



    /**
     * 修复文件夹状态
     * 将已完成上传但状态仍为PROCESSING的文件夹状态修复为unassociated
     *
     * @param folderId 文件夹ID
     * @return 是否修复成功
     */
    boolean fixFolderStatus(String folderId);

    /**
     * 准备继续上传
     * 为中断的上传任务准备继续上传所需的信息
     *
     * @param folderId 文件夹ID
     * @return 包含任务信息和上传配置的Map
     */
    Map<String, Object> prepareResumeUpload(String folderId);

    // ==================== 标准样本管理方法 ====================

    /**
     * 设置标准样本文件夹
     */
    boolean setStandardFolder(String versionId, String folderId);

    /**
     * 替换标准样本文件夹
     */
    boolean replaceStandardFolder(String versionId, String newFolderId, boolean keepAnnotations);

    /**
     * 取消标准样本文件夹
     */
    boolean removeStandardFolder(String versionId, String folderId);

    /**
     * 检查版本是否已有标准样本文件夹
     */
    boolean hasStandardFolder(String versionId);

    /**
     * 获取版本的标准样本文件夹
     */
    Optional<FolderInfo> getStandardFolder(String versionId);

    /**
     * 获取文件夹的标准样本状态信息
     */
    StandardSampleInfo getStandardSampleInfo(String folderId);

    /**
     * 判断文件夹是否为标准样本
     */
    boolean isStandardSampleFolder(String folderId);

    /**
     * 判断图片是否可标注
     */
    boolean canAnnotateImage(String imageId);

    /**
     * 标准样本信息DTO
     */
    class StandardSampleInfo {
        private boolean isStandardSample;
        private String versionId;
        private boolean canSetAsStandard;
        private boolean canRemoveStandard;
        private String reason;
        private int templateCount;

        // 构造函数
        public StandardSampleInfo() {}

        public StandardSampleInfo(boolean isStandardSample, String reason) {
            this.isStandardSample = isStandardSample;
            this.reason = reason;
        }

        // getters and setters
        public boolean isStandardSample() { return isStandardSample; }
        public void setStandardSample(boolean standardSample) { isStandardSample = standardSample; }

        public String getVersionId() { return versionId; }
        public void setVersionId(String versionId) { this.versionId = versionId; }

        public boolean isCanSetAsStandard() { return canSetAsStandard; }
        public void setCanSetAsStandard(boolean canSetAsStandard) { this.canSetAsStandard = canSetAsStandard; }

        public boolean isCanRemoveStandard() { return canRemoveStandard; }
        public void setCanRemoveStandard(boolean canRemoveStandard) { this.canRemoveStandard = canRemoveStandard; }

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }

        public int getTemplateCount() { return templateCount; }
        public void setTemplateCount(int templateCount) { this.templateCount = templateCount; }
    }
}
