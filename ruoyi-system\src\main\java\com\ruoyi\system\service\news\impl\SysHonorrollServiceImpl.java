package com.ruoyi.system.service.news.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.SysHonorrollMapper;
import com.ruoyi.system.domain.news.SysHonorroll;
import com.ruoyi.system.service.news.ISysHonorrollService;

/**
 * 光荣榜信息表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
@Service
public class SysHonorrollServiceImpl implements ISysHonorrollService 
{
    @Autowired
    private SysHonorrollMapper sysHonorrollMapper;

    /**
     * 查询光荣榜信息表
     * 
     * @param honorId 光荣榜信息表主键
     * @return 光荣榜信息表
     */
    @Override
    public SysHonorroll selectSysHonorrollByHonorId(Long honorId)
    {
        return sysHonorrollMapper.selectSysHonorrollByHonorId(honorId);
    }

    /**
     * 查询光荣榜信息表列表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 光荣榜信息表
     */
    @Override
    public List<SysHonorroll> selectSysHonorrollList(SysHonorroll sysHonorroll)
    {
        return sysHonorrollMapper.selectSysHonorrollList(sysHonorroll);
    }

    /**
     * 新增光荣榜信息表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 结果
     */
    @Override
    public int insertSysHonorroll(SysHonorroll sysHonorroll)
    {
        return sysHonorrollMapper.insertSysHonorroll(sysHonorroll);
    }

    /**
     * 修改光荣榜信息表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 结果
     */
    @Override
    public int updateSysHonorroll(SysHonorroll sysHonorroll)
    {
        return sysHonorrollMapper.updateSysHonorroll(sysHonorroll);
    }

    /**
     * 批量删除光荣榜信息表
     * 
     * @param honorIds 需要删除的光荣榜信息表主键
     * @return 结果
     */
    @Override
    public int deleteSysHonorrollByHonorIds(Long[] honorIds)
    {
        return sysHonorrollMapper.deleteSysHonorrollByHonorIds(honorIds);
    }

    /**
     * 删除光荣榜信息表信息
     * 
     * @param honorId 光荣榜信息表主键
     * @return 结果
     */
    @Override
    public int deleteSysHonorrollByHonorId(Long honorId)
    {
        return sysHonorrollMapper.deleteSysHonorrollByHonorId(honorId);
    }
}
