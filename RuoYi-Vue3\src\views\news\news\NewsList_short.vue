<template>
      <el-card class="box-card">
        <template #header>
          <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
            <span class="title-large">{{ title }}</span>
            <el-button 
              :link="true"
              @click="handleMore"
              class="more-btn"
            >
              更多
            </el-button>
          </div>
        </template>
        <div v-loading="loading" class="news-list">
          <!-- 修改 v-for 循环，直接使用 newsList 中的元素 -->
          <div v-for="(news, index) in newsList" :key="index" class="news-item"
            @click="goToNewsDetail(news.newsId)">
            <div class="news-header">
              <span class="news-title">{{ news.newsTitle }}</span>
              <span class="news-time">{{ formatDate(news.uploadTime) }}</span>
            </div>
            <div class="news-footer">
              <span class="news-dept">{{ news.deptName }}</span>
              <span class="news-author">{{ news.author }}</span>
            </div>
          </div>
          <!-- 当 newsList 为空时，显示提示信息 -->
          <div v-if="newsList.length === 0" class="news-item">
            <div class="news-header">
              <span class="news-title">暂无文章数据</span>
            </div>
          </div>
        </div>
      </el-card>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue';
  import { listNews } from '@/api/news/news';
  import { listCategroy } from '@/api/news/categroy';
  import { useRouter } from 'vue-router';
  const { proxy } = getCurrentInstance();
  const newsList = ref([]);
  const loading = ref(true);
  const router = useRouter();

  const props = defineProps({
    title: {
      type: String,
      default: '通知通报'
    }
  });
  // 格式化日期
  const formatDate = (date) => {
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
  };
  
  const getCategroyList = async () => {
    try {
      const response = await listCategroy({ pageNum: 1, pageSize: 100 });
      if (response && response.rows) {
        return response.rows;
      } else {
        proxy.$modal.msgError('文章类型列表数据格式错误，请联系管理员');
      }
    } catch (error) {
      proxy.$modal.msgError('获取文章类型列表失败，请检查网络连接或联系管理员');
    }
  };


  const goToNewsDetail = (newsId) => {
    router.push({ name: 'NewsDetail', params: { newsId } });
  };
  // 获取对应文章栏目文章
  const getNewsByCategroy = async () => {
    loading.value = true;
    try {
      const categroyList = await getCategroyList();
      const selectCategory = categroyList?.find(item => item.categroyName === props.title);
      if (selectCategory) {
        const newsType = selectCategory.categroyID;
        const response = await listNews({ newsType });
        if (response && response.rows) {
          newsList.value = response.rows.slice(0, 5);
        } else {
          proxy.$modal.msgError('获取文章列表失败，请检查网络连接或联系管理员');
        }
      } else {
        proxy.$modal.msgError('未找到预警速递类型，请联系管理员');
      }
    } catch (error) {
      proxy.$modal.msgError('获取预警速递列表失败，请检查网络连接或联系管理员');
    } finally {
      loading.value = false;
    }
  };
  const handleMore = () => {
    router.push({ 
      name: 'NewsList', 
      query: { 
        title: props.title 
      } 
    });
  };
  
  onMounted(async () => {
    await getNewsByCategroy();
  });
 
  </script>
  
  <style scoped lang="scss">
  .news-list {
    .news-item {
      margin-bottom: 15px;
      padding: 12px;
      border-bottom: 1px solid #eaeaea;
      min-height: 60px;
      transition: all 0.3s ease;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background-color: #f0f7ff;
        transform: translateY(-2px);
      }
    }
  
    .news-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
  
    .news-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      transition: color 0.3s;
      position: relative;
      padding-left: 15px;
      
      &:hover {
        color: #409EFF;
      }
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        background-color: #409EFF;
        border-radius: 50%;
      }
    }
  
    .news-time {
      font-size: 14px;
      color: #999;
    }
  
    .news-footer {
      display: flex;
      gap: 15px;
      align-items: center; 
      margin-top: 12px;
    }
  
    .news-dept, .news-author {
      font-size: 14px;
      color: #666;
      line-height: 1; 
      vertical-align: middle;
    }
  }
  .title-large {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 15px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background-color: #409EFF;
      border-radius: 2px;
    }
  }
  .more-btn {
    padding: 0;
    color: #409EFF;
    font-size: 16px;
    font-weight: normal;
    transition: all 0.3s ease;
    
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
      transform: translateX(3px);
    }
  }
  .box-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 530px;
    
    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }
  }
  </style>