package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 第三方检测请求DTO
 * 用于第三方版本检测功能的参数传递
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
public class ThirdPartyDetectionDTO {

    /** 文件夹ID */
    @NotBlank(message = "文件夹ID不能为空")
    private String folderId;

    /** 图片ID列表 */
    @NotEmpty(message = "图片ID列表不能为空")
    private List<String> imageIds;

    /** 检测参数 */
    private Map<String, Object> detectionParams;

    // 手动添加getter/setter方法以确保编译通过
    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public List<String> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<String> imageIds) {
        this.imageIds = imageIds;
    }

    public Map<String, Object> getDetectionParams() {
        return detectionParams;
    }

    public void setDetectionParams(Map<String, Object> detectionParams) {
        this.detectionParams = detectionParams;
    }
}
