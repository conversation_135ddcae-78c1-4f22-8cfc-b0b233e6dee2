import request from '@/utils/request'

// 查询文章管理列表
export function listNews(query) {
  return request({
    url: '/news/news/list',
    method: 'get',
    params: query
  })
}

// 查询文章管理详细
export function getNews(newsId) {
  return request({
    url: '/news/news/' + newsId,
    method: 'get'
  })
}

// 查询管理员文章管理列表
export function adminListNews(query) {
  return request({
    url: '/news/news/adminList',
    method: 'get',
    params: query
  })
}

// 新增文章管理
export function addNews(data) {
  return request({
    url: '/news/news',
    method: 'post',
    data: data
  })
}

// 修改文章管理
export function updateNews(data) {
  return request({
    url: '/news/news',
    method: 'put',
    data: data
  })
}

// 删除文章管理
export function delNews(newsId) {
  return request({
    url: '/news/news/' + newsId,
    method: 'delete'
  })
}

// 审核文章
export function auditNews(data) {
  return request({
    url: '/news/news/audit/' + data.newsId,
    method: 'put',
    params: { auditComment: data.auditComment }
  })
}

// 审核不通过文章
export function rejectNews(data) {
  return request({
    url: '/news/news/reject/' + data.newsId,
    method: 'put',
    params: { auditComment: data.auditComment }
  })
}

// 按年、季度、月统计部门和个人发布的文章数量
export function countNewsByTimeAndType(type) {
  return request({
    url: '/news/news/statistics',
    method: 'get',
    params: { type }
  })
}
