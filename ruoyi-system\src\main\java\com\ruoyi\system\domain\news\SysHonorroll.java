package com.ruoyi.system.domain.news;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 光荣榜信息表对象 sys_honorroll
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
public class SysHonorroll extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long honorId;

    /** 单位/姓名 */
    @Excel(name = "单位/姓名")
    private String honorName;

    /** 图片链接 */
    @Excel(name = "图片链接")
    private String honorImaglUrl;

    /** 状态 */
    @Excel(name = "状态")
    private String honorStatus;

    /** 类型：1为默认，1为个人，2为集体 */
    @Excel(name = "类型：1为默认，1为个人，2为集体")
    private Long honorType;

    public void setHonorId(Long honorId) 
    {
        this.honorId = honorId;
    }

    public Long getHonorId() 
    {
        return honorId;
    }

    public void setHonorName(String honorName) 
    {
        this.honorName = honorName;
    }

    public String getHonorName() 
    {
        return honorName;
    }

    public void setHonorImaglUrl(String honorImaglUrl) 
    {
        this.honorImaglUrl = honorImaglUrl;
    }

    public String getHonorImaglUrl() 
    {
        return honorImaglUrl;
    }

    public void setHonorStatus(String honorStatus) 
    {
        this.honorStatus = honorStatus;
    }

    public String getHonorStatus() 
    {
        return honorStatus;
    }

    public void setHonorType(Long honorType) 
    {
        this.honorType = honorType;
    }

    public Long getHonorType() 
    {
        return honorType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("honorId", getHonorId())
            .append("honorName", getHonorName())
            .append("honorImaglUrl", getHonorImaglUrl())
            .append("honorStatus", getHonorStatus())
            .append("honorType", getHonorType())
            .toString();
    }
}
