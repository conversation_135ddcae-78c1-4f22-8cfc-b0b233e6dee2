<template>
  <el-card class="version-info-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>版本信息</span>
        <el-tag :type="getVersionStatusType(versionInfo?.status)">
          {{ getVersionStatusText(versionInfo?.status) }}
        </el-tag>
      </div>
    </template>

    <el-descriptions 
      :column="responsiveColumns" 
      border 
      size="small"
      class="version-descriptions"
    >
      <el-descriptions-item label="版本代码" :span="2">
        <el-text type="primary" size="large" class="version-code">
          {{ versionInfo?.versionCode || '-' }}
        </el-text>
      </el-descriptions-item>
      
      <el-descriptions-item label="国家">
        <div class="country-info">
          <span>{{ versionInfo?.countryInfo?.name || '-' }}</span>
          <el-text type="info" size="small">{{ versionInfo?.countryInfo?.code || '-' }}</el-text>
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="证件类型">
        <div class="cert-type-info">
          <span>{{ versionInfo?.certInfo?.zjlbmc || '-' }}</span>
          <el-text type="info" size="small">{{ versionInfo?.certInfo?.zjlbdm || '-' }}</el-text>
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="发行年份">
        {{ versionInfo?.issueYear || '-' }}
      </el-descriptions-item>
      
      <el-descriptions-item label="标准样本状态" :span="2">
        <div class="standard-status">
          <div v-if="versionInfo?.standardFolderId" class="standard-folder-info">
            <el-tag type="success" size="large" effect="dark">
              <el-icon><Star /></el-icon>
              已设置
            </el-tag>
            <span v-if="standardFolderName" class="standard-folder-name">
              {{ standardFolderName }}
            </span>
          </div>
          <el-tag v-else type="warning" size="large">
            <el-icon><Warning /></el-icon>
            未设置
          </el-tag>
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="文件夹数量">
        <el-tag type="info" effect="plain">
          {{ folderCount || 0 }} 个
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="创建时间">
        {{ formatTime(versionInfo?.createTime) }}
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Star, Warning } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  versionInfo: {
    type: Object,
    default: null
  },
  folderCount: {
    type: Number,
    default: 0
  },
  standardFolderName: {
    type: String,
    default: ''
  }
})

// 响应式列数
const responsiveColumns = computed(() => {
  // 根据屏幕宽度调整列数
  if (window.innerWidth < 768) return 2
  if (window.innerWidth < 1200) return 4
  return 8
})

// 版本状态相关方法
const getVersionStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getVersionStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'inactive': '停用',
    'pending': '待审核',
    'disabled': '禁用'
  }
  return statusMap[status] || '未知'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.version-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-descriptions {
  /* 控制整体高度，保持紧凑 */
  max-height: 80px;
}

.version-descriptions :deep(.el-descriptions__body) {
  /* 减少内边距 */
  padding: 8px 12px;
}

.version-descriptions :deep(.el-descriptions__label) {
  /* 标签样式优化 */
  font-weight: 600;
  color: #606266;
  width: auto;
  min-width: 80px;
}

.version-descriptions :deep(.el-descriptions__content) {
  /* 内容样式优化 */
  color: #303133;
}

.version-code {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.country-info,
.cert-type-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.country-info span,
.cert-type-info span {
  font-weight: 500;
}

.standard-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.standard-folder-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.standard-folder-name {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
  background: rgba(103, 194, 58, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .version-descriptions {
    max-height: none;
  }
  
  .standard-folder-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 768px) {
  .version-descriptions :deep(.el-descriptions__body) {
    padding: 6px 8px;
  }
  
  .version-descriptions :deep(.el-descriptions__label) {
    min-width: 60px;
    font-size: 12px;
  }
  
  .version-descriptions :deep(.el-descriptions__content) {
    font-size: 12px;
  }
  
  .country-info,
  .cert-type-info {
    font-size: 12px;
  }
  
  .standard-folder-name {
    font-size: 10px;
  }
}

/* 紧凑模式样式 */
.version-info-card.compact {
  margin-bottom: 12px;
}

.version-info-card.compact .version-descriptions {
  max-height: 60px;
}

.version-info-card.compact .version-descriptions :deep(.el-descriptions__body) {
  padding: 4px 8px;
}
</style>
