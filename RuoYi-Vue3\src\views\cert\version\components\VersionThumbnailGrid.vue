<template>
  <el-card class="thumbnail-grid-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>图片列表 ({{ filteredImages.length }})</span>
        <div class="header-filters">
          <!-- 图片类型筛选 -->
          <el-select
            v-model="filterType"
            placeholder="图片类型"
            size="small"
            style="width: 120px; margin-right: 8px;"
            clearable
          >
            <el-option label="全部类型" value="" />
            <el-option label="可见光" value="VISIBLE" />
            <el-option label="红外" value="INFRARED" />
            <el-option label="紫外" value="ULTRAVIOLET" />
            <el-option label="其他" value="OTHER" />
          </el-select>
          
          <!-- 标注状态筛选 -->
          <el-select
            v-model="filterAnnotation"
            placeholder="标注状态"
            size="small"
            style="width: 120px; margin-right: 8px;"
            clearable
          >
            <el-option label="全部状态" value="" />
            <el-option label="已标注" value="annotated" />
            <el-option label="未标注" value="unannotated" />
          </el-select>
          
          <!-- 仅显示可标注图片 -->
          <el-checkbox v-model="showAnnotatableOnly" size="small">
            仅可标注
          </el-checkbox>
        </div>
      </div>
    </template>

    <div class="image-list" v-loading="loading">
      <div
        v-for="(image, index) in filteredImages"
        :key="image.imageId"
        class="image-item"
        :class="{
          active: selectedImage && selectedImage.imageId === image.imageId,
          'annotatable-image': isAnnotatableImage(image)
        }"
        @click="handleImageSelect(image, index)"
      >
        <div class="image-thumbnail">
          <img
            :src="getImageThumbnail(image)"
            :alt="image.imageName"
            @error="handleImageError"
          />
          <div class="image-overlay">
            <!-- 标注状态标识 -->
            <div class="image-status">
              <el-tag
                v-if="image.isAnnotated"
                type="success"
                size="small"
                effect="dark"
              >
                <el-icon><EditPen /></el-icon>
              </el-tag>
              <el-tag
                v-else-if="isAnnotatableImage(image)"
                type="warning"
                size="small"
                effect="plain"
              >
                <el-icon><Edit /></el-icon>
              </el-tag>
            </div>
            
            <!-- 可标注图片特殊标识 -->
            <div v-if="isAnnotatableImage(image)" class="annotatable-indicator">
              <el-icon class="annotatable-icon"><Star /></el-icon>
            </div>
          </div>
        </div>
        
        <div class="image-info">
          <div class="image-actions">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleImageEdit(image)"
              :icon="Edit"
            >
              编辑
            </el-button>
          </div>
          
          <!-- 图片类型标签 -->
          <div class="image-type-tag">
            <el-tag :type="getImageTypeTagType(image.imageType)" size="small">
              {{ getImageTypeText(image.imageType) }}
            </el-tag>
          </div>
          
          <div class="image-name" :title="image.imageName">
            {{ image.imageName }}
          </div>
          <div class="image-meta">
            {{ formatFileSize(image.fileSize) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredImages.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无图片">
        <template v-if="hasFilters">
          <el-button @click="clearFilters">清除筛选条件</el-button>
        </template>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Edit, EditPen, Star } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  images: {
    type: Array,
    default: () => []
  },
  selectedImage: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['image-select', 'image-edit'])

// 筛选状态
const filterType = ref('')
const filterAnnotation = ref('')
const showAnnotatableOnly = ref(false)

// 判断是否为可标注图片
const isAnnotatableImage = (image) => {
  if (!image) return false
  const annotatableNames = ['可见光主图.jpg', '红外光主图.jpg', '紫外光主图.jpg']
  return annotatableNames.includes(image.imageName)
}

// 筛选后的图片列表
const filteredImages = computed(() => {
  let filtered = props.images

  // 图片类型筛选
  if (filterType.value) {
    filtered = filtered.filter(image => image.imageType === filterType.value)
  }

  // 标注状态筛选
  if (filterAnnotation.value) {
    if (filterAnnotation.value === 'annotated') {
      filtered = filtered.filter(image => image.isAnnotated)
    } else if (filterAnnotation.value === 'unannotated') {
      filtered = filtered.filter(image => !image.isAnnotated)
    }
  }

  // 仅显示可标注图片
  if (showAnnotatableOnly.value) {
    filtered = filtered.filter(image => isAnnotatableImage(image))
  }

  return filtered
})

// 是否有筛选条件
const hasFilters = computed(() => {
  return filterType.value || filterAnnotation.value || showAnnotatableOnly.value
})

// 清除筛选条件
const clearFilters = () => {
  filterType.value = ''
  filterAnnotation.value = ''
  showAnnotatableOnly.value = false
}

// 事件处理
const handleImageSelect = (image, index) => {
  emit('image-select', image, index)
}

const handleImageEdit = (image) => {
  emit('image-edit', image)
}

// 工具方法 - 从 FolderDetailView.vue 抽取
const getImageThumbnail = (image) => {
  return getImageUrl(image) || ''
}

const getImageUrl = (image) => {
  if (!image || !image.minioPath) {
    console.warn('图片对象或minioPath为空:', image)
    return ''
  }

  const directMinioUrl = `http://localhost:9000/xjlfiles/${image.minioPath}`
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`
  
  return proxyUrl
}

const handleImageError = (event) => {
  const img = event.target
  const currentSrc = img.src

  console.error('图片加载失败:', currentSrc)

  if (currentSrc.includes('/dev-api/common/image/proxy')) {
    const urlMatch = currentSrc.match(/url=([^&]+)/)
    if (urlMatch) {
      const originalUrl = decodeURIComponent(urlMatch[1])
      console.log('尝试直接访问MinIO:', originalUrl)
      img.src = originalUrl
      return
    }
  }

  console.log('图片加载失败，不显示默认图片')
  img.style.display = 'none'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 图片类型相关方法
const getImageTypeText = (imageType) => {
  const typeMap = {
    'VISIBLE': '可见光',
    'INFRARED': '红外',
    'ULTRAVIOLET': '紫外',
    'OTHER': '其他'
  }
  return typeMap[imageType] || '未知'
}

const getImageTypeTagType = (imageType) => {
  const typeMap = {
    'VISIBLE': 'primary',
    'INFRARED': 'danger',
    'ULTRAVIOLET': 'warning',
    'OTHER': 'info'
  }
  return typeMap[imageType] || 'info'
}
</script>

<style scoped>
.thumbnail-grid-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.header-filters {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.image-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.image-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.image-item:hover {
  background-color: #f5f7fa;
}

.image-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

/* 可标注图片特殊样式 */
.image-item.annotatable-image {
  border-left: 2px solid #67c23a;
}

.image-item.annotatable-image:hover {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
}

.image-item.annotatable-image.active {
  background-color: #e6f7ff;
  border-left: 3px solid #67c23a;
}

.image-thumbnail {
  position: relative;
  width: 60px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2px;
}

.image-status {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.annotatable-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.annotatable-icon {
  color: #67c23a;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 2px;
}

.image-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.image-type-tag {
  margin-bottom: 4px;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-meta {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 - 从 FolderDetailView.vue 抽取 */
@media (max-width: 1200px) {
  .image-list {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    max-height: 150px;
  }

  .image-item {
    flex-direction: column;
    min-width: 120px;
    border-bottom: none;
    border-right: 1px solid #f0f0f0;
  }

  .image-thumbnail {
    width: 100px;
    height: 80px;
    margin-right: 0;
    margin-bottom: 5px;
  }
}

@media (max-width: 768px) {
  .header-filters {
    width: 100%;
    justify-content: flex-start;
  }

  .image-item {
    min-width: 100px;
  }

  .image-thumbnail {
    width: 80px;
    height: 60px;
  }
}
</style>
