package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.IVersionNameParseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 版本解析管理控制器
 * 
 * 提供版本名称解析相关的REST API接口，包括：
 * 1. 重新解析文件夹版本信息
 * 2. 批量重新解析任务下的所有文件夹
 * 3. 获取解析统计信息
 * 4. 手动触发解析任务
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/system/version-parse")
public class VersionParseController extends BaseController {

    @Autowired
    private IVersionNameParseService versionNameParseService;

    /**
     * 重新解析单个文件夹的版本信息
     *
     * @param folderId 文件夹ID
     * @return 解析结果
     */
    @Log(title = "版本解析", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:folder:edit')")
    @PostMapping("/reparse-folder/{folderId}")
    public AjaxResult reparseFolder(@PathVariable String folderId) {
        try {
            return versionNameParseService.reparseFolder(folderId);
        } catch (Exception e) {
            logger.error("重新解析文件夹失败，folderId: {}", folderId, e);
            return AjaxResult.error("重新解析文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 批量重新解析任务下的所有文件夹
     * 
     * @param taskId 任务ID
     * @return 解析结果
     */
    @Log(title = "版本解析", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:batch-task:edit')")
    @PostMapping("/reparse-task/{taskId}")
    public AjaxResult reparseAllFolders(@PathVariable String taskId) {
        try {
            return versionNameParseService.reparseAllFolders(taskId);
        } catch (Exception e) {
            logger.error("批量重新解析任务文件夹失败，taskId: {}", taskId, e);
            return AjaxResult.error("批量重新解析失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务的解析统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:batch-task:query')")
    @GetMapping("/statistics/{taskId}")
    public AjaxResult getParseStatistics(@PathVariable String taskId) {
        try {
            Map<String, Object> statistics = versionNameParseService.getParseStatistics(taskId);
            return AjaxResult.success("获取统计信息成功", statistics);
        } catch (Exception e) {
            logger.error("获取解析统计信息失败，taskId: {}", taskId, e);
            return AjaxResult.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发任务的版本解析
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    @Log(title = "版本解析", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:batch-task:edit')")
    @PostMapping("/trigger-parse/{taskId}")
    public AjaxResult triggerParse(@PathVariable String taskId) {
        try {
            // 异步触发解析
            versionNameParseService.parseAllFoldersAsync(taskId);
            return AjaxResult.success("版本解析已触发，请稍后查看解析结果");
        } catch (Exception e) {
            logger.error("手动触发版本解析失败，taskId: {}", taskId, e);
            return AjaxResult.error("触发解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析单个文件夹名称（测试接口）
     * 
     * @param folderName 文件夹名称
     * @return 解析结果
     */
    @PreAuthorize("@ss.hasPermi('system:folder:query')")
    @GetMapping("/test-parse")
    public AjaxResult testParseFolderName(@RequestParam String folderName) {
        try {
            Object parseResult = versionNameParseService.parseFolderNameToVersionInfo(folderName);
            return AjaxResult.success("解析成功", parseResult);
        } catch (Exception e) {
            logger.error("测试解析文件夹名称失败，folderName: {}", folderName, e);
            return AjaxResult.error("解析失败: " + e.getMessage());
        }
    }

    /**
     * 生成任务名称（测试接口）
     *
     * @param deptName 部门名称
     * @return 生成的任务名称
     */
    @PreAuthorize("@ss.hasPermi('system:batch-task:query')")
    @GetMapping("/test-task-name")
    public AjaxResult testGenerateTaskName(@RequestParam String deptName) {
        try {
            String taskName = versionNameParseService.generateTaskName(deptName);
            return AjaxResult.success("生成成功", taskName);
        } catch (Exception e) {
            logger.error("测试生成任务名称失败，deptName: {}", deptName, e);
            return AjaxResult.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 测试国家名称查找（调试接口）
     *
     * @param countryName 国家名称
     * @return 查找结果
     */
    @PreAuthorize("@ss.hasPermi('system:folder:query')")
    @GetMapping("/test-country-search")
    public AjaxResult testCountrySearch(@RequestParam String countryName) {
        try {
            String countryCode = versionNameParseService.findCountryCodeByNameCn(countryName);
            Map<String, Object> result = new HashMap<>();
            result.put("inputName", countryName);
            result.put("foundCode", countryCode);
            return AjaxResult.success("查找完成", result);
        } catch (Exception e) {
            logger.error("测试国家名称查找失败，countryName: {}", countryName, e);
            return AjaxResult.error("查找失败: " + e.getMessage());
        }
    }
}
