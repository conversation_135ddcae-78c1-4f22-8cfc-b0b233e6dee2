<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="国家代码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入三位国家代码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中文名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入中文名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="英文名称" prop="nameEn">
        <el-input
          v-model="queryParams.nameEn"
          placeholder="请输入英文名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="首字母" prop="firstLetter">
        <el-select v-model="queryParams.firstLetter" placeholder="选择首字母" clearable style="width: 120px">
          <el-option
            v-for="letter in alphabetLetters"
            :key="letter"
            :label="letter"
            :value="letter"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['cert:country:add']"
        >新增国家</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cert:country:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cert:country:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['cert:country:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Refresh"
          @click="handleSyncFlags"
        >同步国旗</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 字母导航 -->
    <div class="letter-nav mb8" v-if="letterCounts && Object.keys(letterCounts).length > 0">
      <el-button-group>
        <el-button
          :type="currentLetter === '' ? 'primary' : ''"
          size="small"
          @click="handleLetterFilter('')"
        >全部({{ totalCount }})</el-button>
        <el-button
          v-for="(count, letter) in letterCounts"
          :key="letter"
          :type="currentLetter === letter ? 'primary' : ''"
          size="small"
          @click="handleLetterFilter(letter)"
        >{{ letter }}({{ count }})</el-button>
      </el-button-group>
    </div>

    <!-- 国家列表表格 -->
    <el-table
      v-loading="loading"
      :data="countryList"
      @selection-change="handleSelectionChange"
      :default-sort="{prop: 'nameEn', order: 'ascending'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="国旗" align="center" width="80">
        <template #default="scope">
          <div class="flag-container">
            <img
              :src="getFlagUrl(scope.row)"
              :alt="scope.row.name"
              class="flag-icon"
              @error="handleFlagError($event, scope.row)"
              v-if="scope.row.flagIcon"
            />
            <el-icon v-else class="no-flag-icon"><Picture /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="国家代码" align="center" prop="code" width="120">
        <template #default="scope">
          <el-tag type="primary" size="small">{{ scope.row.code }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="中文名称" align="center" prop="name" min-width="150" />
      <el-table-column label="英文名称" align="center" prop="nameEn" min-width="150" show-overflow-tooltip />
      <el-table-column label="国旗路径" align="center" prop="flagIcon" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <el-text type="info" size="small">{{ scope.row.flagIcon || '未设置' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cert:country:edit']"
          >修改</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cert:country:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改国家对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="countryRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="国家代码" prop="code">
              <el-input
                v-model="form.code"
                placeholder="请输入三位国家代码(如: CHN)"
                maxlength="3"
                style="text-transform: uppercase"
                @input="form.code = form.code.toUpperCase()"
              />
              <div class="form-tip">
                <el-text type="info" size="small">ISO 3166-1 alpha-3 标准代码</el-text>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中文名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入中文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="英文名称" prop="nameEn">
              <el-input v-model="form.nameEn" placeholder="请输入英文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="国旗图标" prop="flagIcon">
              <el-input v-model="form.flagIcon" placeholder="国旗图标路径(可选，留空将自动生成)" />
              <div class="form-tip">
                <el-text type="info" size="small">
                  建议格式: /static/flags/国家代码.png，留空将根据国家代码自动生成
                </el-text>
              </div>
            </el-form-item>
          </el-col>
          <!-- 国旗预览 -->
          <el-col :span="24" v-if="form.flagIcon || form.code">
            <el-form-item label="国旗预览">
              <div class="flag-preview">
                <img
                  :src="getPreviewFlagUrl()"
                  :alt="form.name"
                  class="flag-preview-img"
                  @error="handlePreviewError"
                />
                <div class="preview-info">
                  <el-text size="small">{{ getPreviewFlagUrl() }}</el-text>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 国家详情弹窗 -->
    <el-dialog title="国家详情" v-model="detailVisible" width="500px">
      <div class="country-detail" v-if="currentCountry">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="国家代码">
            <el-tag type="primary">{{ currentCountry.code }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="国旗">
            <img
              :src="getFlagUrl(currentCountry)"
              :alt="currentCountry.name"
              class="detail-flag"
              @error="handleFlagError($event, currentCountry)"
              v-if="currentCountry.flagIcon"
            />
            <el-text type="info" v-else>未设置</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="中文名称" :span="2">
            {{ currentCountry.name }}
          </el-descriptions-item>
          <el-descriptions-item label="英文名称" :span="2">
            {{ currentCountry.nameEn }}
          </el-descriptions-item>
          <el-descriptions-item label="国旗路径" :span="2">
            <el-text type="info" size="small">{{ currentCountry.flagIcon || '未设置' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ parseTime(currentCountry.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ parseTime(currentCountry.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Country">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import {
  getCountryList,
  deleteCountries,
  createCountry,
  updateCountry,
  getCountryLetters,
  listCountryByLetter, getCountryDetails
} from "@/api/cert/country";
import { parseTime } from "@/utils/ruoyi";
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, Plus, Edit, Delete, Download, View, Picture } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 数据定义
const countryList = ref([]);
const open = ref(false);
const detailVisible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const submitting = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref(0);
const title = ref("");
const currentCountry = ref(null);
const currentLetter = ref('');
const letterCounts = ref({});

// 字母表
const alphabetLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: null,
    name: null,
    nameEn: null,
    firstLetter: null,
  },
  rules: {
    code: [
      { required: true, message: "国家代码不能为空", trigger: "blur" },
      { min: 3, max: 3, message: "国家代码必须为3位字母", trigger: "blur" },
      { pattern: /^[A-Z]{3}$/, message: "国家代码必须为3位大写字母", trigger: "blur" }
    ],
    name: [
      { required: true, message: "中文名称不能为空", trigger: "blur" }
    ],
    nameEn: [
      { required: true, message: "英文名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询国家表列表 */
function getList() {
  loading.value = true;
  getCountryList(queryParams.value).then(response => {
    countryList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 获取字母统计 */
function getLetterCounts() {
  getCountryLetters().then(response => {
    letterCounts.value = response.data || {};
    // 计算总数
    totalCount.value = Object.values(letterCounts.value).reduce((sum, count) => sum + count, 0);
  }).catch(error => {
    console.error('获取字母统计失败:', error);
  });
}

/** 字母筛选 */
function handleLetterFilter(letter) {
  currentLetter.value = letter;
  if (letter === '') {
    // 显示全部
    queryParams.value.firstLetter = null;
    getList();
  } else {
    // 按字母筛选
    listCountryByLetter(letter).then(response => {
      countryList.value = response.data || [];
      total.value = countryList.value.length;
    });
  }
}

/** 获取国旗URL */
function getFlagUrl(country) {
  if (!country) return '';

  if (country.flagIcon) {
    // 如果是相对路径，添加前缀
    if (country.flagIcon.startsWith('/static/')) {
      return country.flagIcon;
    }
    return country.flagIcon;
  }

  // 默认根据国家代码生成
  return `/static/flags/${country.code}.png`;
}

/** 获取预览国旗URL */
function getPreviewFlagUrl() {
  if (form.value.flagIcon) {
    return form.value.flagIcon;
  }

  if (form.value.code) {
    return `/static/flags/${form.value.code.toUpperCase()}.png`;
  }

  return '';
}

/** 国旗加载错误处理 */
function handleFlagError(event, country) {
  // 使用默认图标
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAzMiAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMCAxMEwyMiAxME0xMCAxNEwyMiAxNCIgc3Ryb2tlPSIjQ0NDQ0NDIiBzdHJva2Utd2lkdGg9IjIiLz4KPHN2Zz4K';
  event.target.onerror = null; // 防止循环
}

/** 预览图片错误处理 */
function handlePreviewError(event) {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA2NCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1IiBzdHJva2U9IiNEREREREQiLz4KPHRleHQgeD0iMzIiIHk9IjI0IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTk5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuaXoOWbvueJhzwvdGV4dD4KPHN2Zz4K';
}

/** 查看详情 */
function handleViewDetail(row) {
  currentCountry.value = row;
  detailVisible.value = true;
}

/** 同步国旗 */
function handleSyncFlags() {
  ElMessageBox.confirm('确认要为所有国家同步国旗图标路径吗？', '确认同步', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里可以调用后端API来批量更新国旗路径
    ElMessage.success('国旗同步功能开发中...');
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    code: null,
    name: null,
    nameEn: null,
    flagIcon: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("countryRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  currentLetter.value = ''; // 重置字母筛选
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  currentLetter.value = '';
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加国家";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value[0];
  getCountryDetails(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改国家";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["countryRef"].validate(valid => {
    if (valid) {
      submitting.value = true;

      // 如果没有设置国旗路径，自动生成
      if (!form.value.flagIcon && form.value.code) {
        form.value.flagIcon = `/static/flags/${form.value.code.toUpperCase()}.png`;
      }

      if (form.value.id != null) {
        updateCountry(form.value).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          getList();
          getLetterCounts(); // 刷新字母统计
        }).finally(() => {
          submitting.value = false;
        });
      } else {
        createCountry(form.value).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          getList();
          getLetterCounts(); // 刷新字母统计
        }).finally(() => {
          submitting.value = false;
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  const names = row.name ? [row.name] : countryList.value.filter(item => ids.value.includes(item.id)).map(item => item.name);

  ElMessageBox.confirm(`是否确认删除国家"${names.join('、')}"？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(function() {
    return deleteCountries(_ids);
  }).then(() => {
    getList();
    getLetterCounts(); // 刷新字母统计
    ElMessage.success("删除成功");
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('cert/country/export', {
    ...queryParams.value
  }, `country_${new Date().getTime()}.xlsx`);
}

// 生命周期
onMounted(() => {
  getList();
  getLetterCounts();
});
</script>

<style lang="scss" scoped>
.letter-nav {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;

  .el-button-group {
    .el-button {
      margin-bottom: 5px;
    }
  }
}

.flag-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .flag-icon {
    width: 32px;
    height: 24px;
    border: 1px solid #e4e7ed;
    border-radius: 2px;
    object-fit: cover;
  }

  .no-flag-icon {
    font-size: 24px;
    color: #c0c4cc;
  }
}

.flag-preview {
  display: flex;
  align-items: center;
  gap: 10px;

  .flag-preview-img {
    width: 64px;
    height: 48px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    object-fit: cover;
  }

  .preview-info {
    flex: 1;

    .el-text {
      word-break: break-all;
    }
  }
}

.detail-flag {
  width: 48px;
  height: 36px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  object-fit: cover;
}

.form-tip {
  margin-top: 5px;
}

.country-detail {
  .el-descriptions {
    .el-descriptions-item__cell {
      .el-tag {
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .letter-nav {
    .el-button-group {
      .el-button {
        margin: 2px;
        font-size: 12px;
        padding: 5px 8px;
      }
    }
  }

  .flag-preview {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
