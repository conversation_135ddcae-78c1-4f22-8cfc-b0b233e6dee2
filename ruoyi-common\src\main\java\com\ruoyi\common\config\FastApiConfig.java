package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * FastAPI配置类
 * greenfox - 新增FastAPI服务配置 - 2025-01-15
 */
@Configuration
@ConfigurationProperties(prefix = "fastapi")
public class FastApiConfig {
    
    private Base base = new Base();
    private Api api = new Api();
    private Service service = new Service();
    private Retry retry = new Retry();
    private CircuitBreaker circuitBreaker = new CircuitBreaker();
    private Endpoints endpoints = new Endpoints();
    private Logging logging = new Logging();
    
    // 基础配置
    public static class Base {
        private String url = "http://localhost:8000";
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
    }
    
    // API认证配置
    public static class Api {
        private String key;
        private String headerName = "X-API-Key";
        
        public String getKey() { return key; }
        public void setKey(String key) { this.key = key; }
        public String getHeaderName() { return headerName; }
        public void setHeaderName(String headerName) { this.headerName = headerName; }
    }
    
    // 服务配置
    public static class Service {
        private boolean enabled = true;
        private int timeout = 30000;
        private int connectTimeout = 5000;
        private int readTimeout = 25000;
        
        // getters and setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public int getTimeout() { return timeout; }
        public void setTimeout(int timeout) { this.timeout = timeout; }
        public int getConnectTimeout() { return connectTimeout; }
        public void setConnectTimeout(int connectTimeout) { this.connectTimeout = connectTimeout; }
        public int getReadTimeout() { return readTimeout; }
        public void setReadTimeout(int readTimeout) { this.readTimeout = readTimeout; }
    }
    
    // 重试配置
    public static class Retry {
        private int maxAttempts = 3;
        private long delay = 1000;
        private double backoffMultiplier = 2.0;
        
        // getters and setters
        public int getMaxAttempts() { return maxAttempts; }
        public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
        public long getDelay() { return delay; }
        public void setDelay(long delay) { this.delay = delay; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public void setBackoffMultiplier(double backoffMultiplier) { this.backoffMultiplier = backoffMultiplier; }
    }
    
    // 熔断器配置
    public static class CircuitBreaker {
        private boolean enabled = true;
        private int failureThreshold = 5;
        private long recoveryTimeout = 30000;
        
        // getters and setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public int getFailureThreshold() { return failureThreshold; }
        public void setFailureThreshold(int failureThreshold) { this.failureThreshold = failureThreshold; }
        public long getRecoveryTimeout() { return recoveryTimeout; }
        public void setRecoveryTimeout(long recoveryTimeout) { this.recoveryTimeout = recoveryTimeout; }
    }
    
    // 接口路径配置
    public static class Endpoints {
        private String folderInfo = "/api/v1/cert/folder-info";
        private String folderDetail = "/api/v1/cert/folder-info/{id}";
        private String folderStats = "/api/v1/cert/folder-info/stats";
        private String healthCheck = "/health";
        
        // getters and setters
        public String getFolderInfo() { return folderInfo; }
        public void setFolderInfo(String folderInfo) { this.folderInfo = folderInfo; }
        public String getFolderDetail() { return folderDetail; }
        public void setFolderDetail(String folderDetail) { this.folderDetail = folderDetail; }
        public String getFolderStats() { return folderStats; }
        public void setFolderStats(String folderStats) { this.folderStats = folderStats; }
        public String getHealthCheck() { return healthCheck; }
        public void setHealthCheck(String healthCheck) { this.healthCheck = healthCheck; }
    }
    
    // 日志配置
    public static class Logging {
        private boolean enabled = true;
        private String level = "INFO";
        private boolean includeRequestBody = false;
        private boolean includeResponseBody = false;
        
        // getters and setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }
        public boolean isIncludeRequestBody() { return includeRequestBody; }
        public void setIncludeRequestBody(boolean includeRequestBody) { this.includeRequestBody = includeRequestBody; }
        public boolean isIncludeResponseBody() { return includeResponseBody; }
        public void setIncludeResponseBody(boolean includeResponseBody) { this.includeResponseBody = includeResponseBody; }
    }
    
    // 所有属性的getters and setters
    public Base getBase() { return base; }
    public void setBase(Base base) { this.base = base; }
    public Api getApi() { return api; }
    public void setApi(Api api) { this.api = api; }
    public Service getService() { return service; }
    public void setService(Service service) { this.service = service; }
    public Retry getRetry() { return retry; }
    public void setRetry(Retry retry) { this.retry = retry; }
    public CircuitBreaker getCircuitBreaker() { return circuitBreaker; }
    public void setCircuitBreaker(CircuitBreaker circuitBreaker) { this.circuitBreaker = circuitBreaker; }
    public Endpoints getEndpoints() { return endpoints; }
    public void setEndpoints(Endpoints endpoints) { this.endpoints = endpoints; }
    public Logging getLogging() { return logging; }
    public void setLogging(Logging logging) { this.logging = logging; }
}