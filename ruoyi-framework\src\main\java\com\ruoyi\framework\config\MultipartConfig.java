package com.ruoyi.framework.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;
import java.io.File;

/**
 * 文件上传配置
 * AI 助手 - 新增：解决临时文件删除问题 - 2025-01-15
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Configuration
public class MultipartConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置文件大小限制
        factory.setMaxFileSize(DataSize.ofMegabytes(100)); // 100MB
        factory.setMaxRequestSize(DataSize.ofMegabytes(500)); // 500MB
        
        // 设置临时文件目录
        String tmpDir = System.getProperty("java.io.tmpdir") + File.separator + "ruoyi-upload";
        File uploadTmpDir = new File(tmpDir);
        if (!uploadTmpDir.exists()) {
            uploadTmpDir.mkdirs();
        }
        factory.setLocation(tmpDir);
        
        // 设置文件写入磁盘的阈值
        factory.setFileSizeThreshold(DataSize.ofKilobytes(2)); // 2KB
        
        return factory.createMultipartConfig();
    }
} 