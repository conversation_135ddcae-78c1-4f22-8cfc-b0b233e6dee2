package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.BatchUploadTask;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.repository.BatchUploadTaskRepository;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.ImageRepositoryRepo;
import com.ruoyi.system.service.impl.BatchTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 多级文件夹上传功能测试
 * 
 * 测试场景：
 * 1. 文件夹路径映射功能
 * 2. 原始文件名保持功能
 * 3. MinIO路径格式验证
 * 4. 数据库记录创建验证
 */
@ExtendWith(MockitoExtension.class)
public class MultiLevelFolderUploadTest {

    private static final Logger log = LoggerFactory.getLogger(MultiLevelFolderUploadTest.class);

    @Mock
    private BatchUploadTaskRepository batchUploadTaskRepository;

    @Mock
    private FolderInfoRepository folderInfoRepository;

    @Mock
    private ImageRepositoryRepo imageRepositoryRepository;

    @InjectMocks
    private BatchTaskServiceImpl batchTaskService;

    private BatchUploadTask mockTask;
    private String testTaskId;
    private String testFolderId;

    @BeforeEach
    void setUp() {
        testTaskId = UUID.randomUUID().toString();
        testFolderId = UUID.randomUUID().toString();

        // 创建模拟的批量上传任务
        mockTask = new BatchUploadTask();
        mockTask.setTaskId(testTaskId);
        mockTask.setTaskName("2025-07-07_技术部_批量上传任务");
        mockTask.setIssuePlace("北京");
        mockTask.setTotalFiles(5);
        mockTask.setProcessedFiles(0);
        mockTask.setStatus("UPLOADING");

        // 设置国家和证件信息
        BatchUploadTask.Country countryInfo = new BatchUploadTask.Country();
        countryInfo.setId(1L);
        countryInfo.setName("中国");
        countryInfo.setCode("CHN");
        mockTask.setCountryInfo(countryInfo);

        BatchUploadTask.CertType certInfo = new BatchUploadTask.CertType();
        certInfo.setId(1L);
        certInfo.setZjlbmc("护照");
        certInfo.setZjlbdm("HZ");
        mockTask.setCertInfo(certInfo);

        mockTask.setIssueYear("2020");
    }

    /**
     * 测试单文件夹上传（向后兼容性）
     */
    @Test
    void testSingleFolderUpload() {
        log.info("开始测试单文件夹上传功能");

        // 准备测试数据
        String folderName = "中国_护照_2020_A1000_北京";
        String folderPath = folderName; // 单文件夹情况下，路径就是文件夹名
        String originalFileName = "passport_front.jpg";
        String expectedMinioPath = testTaskId + "/" + folderName + "/" + originalFileName;

        // Mock repository 行为
        when(batchUploadTaskRepository.findByTaskId(testTaskId)).thenReturn(Optional.of(mockTask));
        when(folderInfoRepository.findByFolderPath(folderPath)).thenReturn(null);
        when(folderInfoRepository.findByTaskIdAndFolderName(testTaskId, folderName)).thenReturn(java.util.Collections.emptyList());

        // 模拟 FolderInfo 创建
        FolderInfo mockFolderInfo = new FolderInfo();
        mockFolderInfo.setFolderId(testFolderId);
        mockFolderInfo.setTaskId(testTaskId);
        mockFolderInfo.setFolderName(folderName);
        mockFolderInfo.setFolderPath(folderPath);
        when(folderInfoRepository.save(any(FolderInfo.class))).thenReturn(mockFolderInfo);

        // 执行测试
        try {
            batchTaskService.handleUploadCompletion(
                testTaskId,
                "北京",
                "A1000",
                originalFileName,
                expectedMinioPath,
                folderName,
                folderPath
            );

            // 验证结果
            verify(batchUploadTaskRepository, times(1)).findByTaskId(testTaskId);
            verify(folderInfoRepository, times(1)).findByFolderPath(folderPath);
            verify(imageRepositoryRepository, times(1)).save(any(ImageRepository.class));

            log.info("单文件夹上传测试通过");

        } catch (Exception e) {
            log.error("单文件夹上传测试失败", e);
            fail("单文件夹上传测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试多级文件夹上传
     */
    @Test
    void testMultiLevelFolderUpload() {
        log.info("开始测试多级文件夹上传功能");

        // 准备测试数据 - 模拟多级文件夹结构
        String parentFolder = "证件样本集合";
        String subFolder = "西班牙_公务普通护照_2001_XDD85_哈瓦那";
        String folderPath = parentFolder + "/" + subFolder;
        String folderName = subFolder; // 实际的文件夹名称
        String originalFileName = "spain_passport_page1.jpg";
        String expectedMinioPath = testTaskId + "/" + folderName + "/" + originalFileName;

        // Mock repository 行为
        when(batchUploadTaskRepository.findByTaskId(testTaskId)).thenReturn(Optional.of(mockTask));
        when(folderInfoRepository.findByFolderPath(folderPath)).thenReturn(null);
        when(folderInfoRepository.findByTaskIdAndFolderName(testTaskId, folderName)).thenReturn(java.util.Collections.emptyList());

        // 模拟 FolderInfo 创建
        FolderInfo mockFolderInfo = new FolderInfo();
        mockFolderInfo.setFolderId(testFolderId);
        mockFolderInfo.setTaskId(testTaskId);
        mockFolderInfo.setFolderName(folderName);
        mockFolderInfo.setFolderPath(folderPath);
        when(folderInfoRepository.save(any(FolderInfo.class))).thenReturn(mockFolderInfo);

        // 执行测试
        try {
            batchTaskService.handleUploadCompletion(
                testTaskId,
                "哈瓦那",
                "XDD85",
                originalFileName,
                expectedMinioPath,
                folderName,
                folderPath
            );

            // 验证结果
            verify(batchUploadTaskRepository, times(1)).findByTaskId(testTaskId);
            verify(folderInfoRepository, times(1)).findByFolderPath(folderPath);
            verify(imageRepositoryRepository, times(1)).save(any(ImageRepository.class));

            log.info("多级文件夹上传测试通过");

        } catch (Exception e) {
            log.error("多级文件夹上传测试失败", e);
            fail("多级文件夹上传测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试特殊字符文件名处理
     */
    @Test
    void testSpecialCharacterHandling() {
        log.info("开始测试特殊字符文件名处理");

        // 准备测试数据 - 包含特殊字符的文件名
        String folderName = "美国_驾驶证_2019_DL123_洛杉矶";
        String folderPath = folderName;
        String originalFileName = "驾照(正面)_高清扫描.jpg";
        String expectedMinioPath = testTaskId + "/" + folderName + "/" + originalFileName;

        // Mock repository 行为
        when(batchUploadTaskRepository.findByTaskId(testTaskId)).thenReturn(Optional.of(mockTask));
        when(folderInfoRepository.findByFolderPath(folderPath)).thenReturn(null);
        when(folderInfoRepository.findByTaskIdAndFolderName(testTaskId, folderName)).thenReturn(java.util.Collections.emptyList());

        // 模拟 FolderInfo 创建
        FolderInfo mockFolderInfo = new FolderInfo();
        mockFolderInfo.setFolderId(testFolderId);
        when(folderInfoRepository.save(any(FolderInfo.class))).thenReturn(mockFolderInfo);

        // 执行测试
        try {
            batchTaskService.handleUploadCompletion(
                testTaskId,
                "洛杉矶",
                "DL123",
                originalFileName,
                expectedMinioPath,
                folderName,
                folderPath
            );

            // 验证 ImageRepository 保存时使用了原始文件名
            verify(imageRepositoryRepository, times(1)).save(argThat(imageRepo -> {
                ImageRepository repo = (ImageRepository) imageRepo;
                return originalFileName.equals(repo.getFileName()) && 
                       expectedMinioPath.equals(repo.getFilePath());
            }));

            log.info("特殊字符文件名处理测试通过");

        } catch (Exception e) {
            log.error("特殊字符文件名处理测试失败", e);
            fail("特殊字符文件名处理测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件夹路径映射功能
     */
    @Test
    void testFolderPathMapping() {
        log.info("开始测试文件夹路径映射功能");

        // 准备测试数据 - 已存在的文件夹路径
        String folderName = "法国_身份证_2018_ID789_巴黎";
        String folderPath = "欧洲证件/" + folderName;
        String originalFileName = "france_id_front.jpg";
        String expectedMinioPath = testTaskId + "/" + folderName + "/" + originalFileName;

        // 模拟已存在的 FolderInfo
        FolderInfo existingFolderInfo = new FolderInfo();
        existingFolderInfo.setFolderId(testFolderId);
        existingFolderInfo.setTaskId(testTaskId);
        existingFolderInfo.setFolderName(folderName);
        existingFolderInfo.setFolderPath(folderPath);

        // Mock repository 行为 - 根据路径找到已存在的文件夹
        when(batchUploadTaskRepository.findByTaskId(testTaskId)).thenReturn(Optional.of(mockTask));
        when(folderInfoRepository.findByFolderPath(folderPath)).thenReturn(existingFolderInfo);

        // 执行测试
        try {
            batchTaskService.handleUploadCompletion(
                testTaskId,
                "巴黎",
                "ID789",
                originalFileName,
                expectedMinioPath,
                folderName,
                folderPath
            );

            // 验证使用了已存在的文件夹，而不是创建新的
            verify(folderInfoRepository, times(1)).findByFolderPath(folderPath);
            verify(folderInfoRepository, never()).save(any(FolderInfo.class));
            verify(imageRepositoryRepository, times(1)).save(any(ImageRepository.class));

            log.info("文件夹路径映射功能测试通过");

        } catch (Exception e) {
            log.error("文件夹路径映射功能测试失败", e);
            fail("文件夹路径映射功能测试失败: " + e.getMessage());
        }
    }
}
