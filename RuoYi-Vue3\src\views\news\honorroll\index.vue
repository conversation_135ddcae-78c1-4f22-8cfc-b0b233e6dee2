<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="单位/姓名" prop="honorName">
        <el-input
          v-model="queryParams.honorName"
          placeholder="请输入单位/姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="honorType">
        <el-select v-model="queryParams.honorType" placeholder="请选择类型" clearable style="width: 100px">
          <el-option
            v-for="item in sys_honor_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['honorroll:honorroll:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['honorroll:honorroll:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['honorroll:honorroll:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['honorroll:honorroll:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="honorrollList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="honorId" />
      <el-table-column label="单位/姓名" align="center" prop="honorName" />
      <el-table-column label="展示图片" align="center" prop="honorImaglUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.honorImaglUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="honorStatus" >
        <template #default="scope">
          <dict-tag :options="sys_honor_status" :value="scope.row.honorStatus" />
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="honorType" >
      <template #default="scope">
          <dict-tag :options="sys_honor_type" :value="scope.row.honorType" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['honorroll:honorroll:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['honorroll:honorroll:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改光荣榜信息表对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="honorrollRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="单位/姓名" prop="honorName">
          <el-input v-model="form.honorName" placeholder="请输入单位/姓名" />
        </el-form-item>
        <el-form-item label="图片" prop="honorImaglUrl">
          <image-upload v-model="form.honorImaglUrl" :limit="1" />
        </el-form-item>
      </el-form>
      <el-form-item label="类型" prop="honorType">
        <el-select v-model="form.honorType" placeholder="请选择类型" clearable>
          <el-option
            v-for="item in sys_honor_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="honorStatus">
        <el-select v-model="form.honorStatus" placeholder="请选择是否展示" clearable>
          <el-option
            v-for="item in sys_honor_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Honorroll">
import { listHonorroll, getHonorroll, delHonorroll, addHonorroll, updateHonorroll } from "@/api/news/honorroll"

const { proxy } = getCurrentInstance()
const { sys_honor_status,sys_honor_type } = proxy.useDict("sys_honor_status","sys_honor_type");
const honorrollList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    honorName: null,
    honorImaglUrl: null,
    honorStatus: null,
    honorType: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询光荣榜信息表列表 */
function getList() {
  loading.value = true
  listHonorroll(queryParams.value).then(response => {
    honorrollList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    honorId: null,
    honorName: null,
    honorImaglUrl: null,
    honorStatus: null,
    honorType: null
  }
  proxy.resetForm("honorrollRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  queryParams.value.honorType = null; 
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.honorId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "新增光荣榜图片"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _honorId = row.honorId || ids.value
  getHonorroll(_honorId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改信息"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["honorrollRef"].validate(valid => {
    if (valid) {
      if (form.value.honorId != null) {
        updateHonorroll(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addHonorroll(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _honorIds = row.honorId || ids.value
  proxy.$modal.confirm('是否确认删除光荣榜信息表编号为"' + _honorIds + '"的数据项？').then(function() {
    return delHonorroll(_honorIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('honorroll/honorroll/export', {
    ...queryParams.value
  }, `honorroll_${new Date().getTime()}.xlsx`)
}

getList()
</script>
