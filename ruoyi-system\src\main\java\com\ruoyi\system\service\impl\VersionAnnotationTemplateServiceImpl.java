package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.domain.dto.request.AnnotationTemplateDTO;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.ImageRepositoryRepo;
import com.ruoyi.system.repository.VersionAnnotationTemplateRepository;
import com.ruoyi.system.service.IVersionAnnotationTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 版本标注模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class VersionAnnotationTemplateServiceImpl implements IVersionAnnotationTemplateService {
    
    private static final Logger log = LoggerFactory.getLogger(VersionAnnotationTemplateServiceImpl.class);
    
    @Autowired
    private VersionAnnotationTemplateRepository templateRepository;
    
    @Autowired
    private FolderInfoRepository folderInfoRepository;
    
    @Autowired
    private ImageRepositoryRepo imageRepositoryRepo;
    
    @Override
    public Optional<VersionAnnotationTemplate> getTemplate(String versionId, String imageType) {
        try {
            return templateRepository.findByVersionIdAndImageType(versionId, imageType);
        } catch (Exception e) {
            log.error("获取版本标注模板失败: versionId={}, imageType={}, error={}", versionId, imageType, e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    @Override
    @Transactional
    public VersionAnnotationTemplate saveTemplate(String versionId, String imageType, 
                                                 AnnotationTemplateDTO templateDTO, String standardFolderId) {
        try {
            log.info("保存版本标注模板: versionId={}, imageType={}, standardFolderId={}", versionId, imageType, standardFolderId);
            
            // 验证标准文件夹是否存在且为标准样本
            FolderInfo folder = folderInfoRepository.findByFolderId(standardFolderId);
            if (folder == null) {
                throw new RuntimeException("标准文件夹不存在: " + standardFolderId);
            }
            if (!"standard".equals(folder.getFolderType())) {
                throw new RuntimeException("文件夹不是标准样本: " + standardFolderId);
            }
            
            // 验证模板数据
            if (!validateTemplate(templateDTO)) {
                throw new RuntimeException("模板数据验证失败");
            }
            
            // 查找现有模板
            Optional<VersionAnnotationTemplate> existingTemplate = templateRepository.findByVersionIdAndImageType(versionId, imageType);
            
            VersionAnnotationTemplate template;
            if (existingTemplate.isPresent()) {
                // 更新现有模板
                template = existingTemplate.get();
                template.setAnnotations(templateDTO.toEntity().getAnnotations());
                template.setStandardFolderId(standardFolderId);
                template.setUpdateTime(new Date());
                template.setUpdatedBy(SecurityUtils.getUsername());
            } else {
                // 创建新模板
                template = templateDTO.toEntity();
                template.setTemplateId(IdUtils.simpleUUID());
                template.setVersionId(versionId);
                template.setImageType(imageType);
                template.setStandardFolderId(standardFolderId);
                template.setCreateTime(new Date());
                template.setUpdateTime(new Date());
                template.setCreatedBy(SecurityUtils.getUsername());
                template.setUpdatedBy(SecurityUtils.getUsername());
            }
            
            return templateRepository.save(template);
            
        } catch (Exception e) {
            log.error("保存版本标注模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存标注模板失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<VersionAnnotationTemplate> getVersionTemplates(String versionId) {
        try {
            return templateRepository.findByVersionId(versionId);
        } catch (Exception e) {
            log.error("获取版本所有标注模板失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public void deleteVersionTemplates(String versionId) {
        try {
            log.info("删除版本所有标注模板: versionId={}", versionId);
            templateRepository.deleteByVersionId(versionId);
        } catch (Exception e) {
            log.error("删除版本标注模板失败: versionId={}, error={}", versionId, e.getMessage(), e);
            throw new RuntimeException("删除版本标注模板失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean canAnnotateImage(String imageId) {
        try {
            AnnotationPermissionInfo permission = getAnnotationPermission(imageId);
            return permission.isCanEdit();
        } catch (Exception e) {
            log.error("检查图片标注权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public AnnotationPermissionInfo getAnnotationPermission(String imageId) {
        try {
            // 获取图片信息
            ImageRepository image = imageRepositoryRepo.findByImageId(imageId);
            if (image == null) {
                return new AnnotationPermissionInfo(false, false, "图片不存在");
            }
            
            // 检查图片类型是否可标注
            if (!Boolean.TRUE.equals(image.getIsAnnotatableType())) {
                return new AnnotationPermissionInfo(false, true, "图片类型不支持标注");
            }
            
            // 获取文件夹信息
            FolderInfo folder = folderInfoRepository.findByFolderId(image.getFolderId());
            if (folder == null) {
                return new AnnotationPermissionInfo(false, false, "文件夹不存在");
            }
            
            // 检查是否为标准样本文件夹
            boolean isStandardSample = "standard".equals(folder.getFolderType());
            
            // 获取版本的标注模板
            VersionAnnotationTemplate template = null;
            if (StringUtils.isNotEmpty(image.getVersionId()) && StringUtils.isNotEmpty(image.getImageType())) {
                Optional<VersionAnnotationTemplate> templateOpt = getTemplate(image.getVersionId(), image.getImageType());
                template = templateOpt.orElse(null);
            }
            
            // 构建权限信息
            AnnotationPermissionInfo permission = new AnnotationPermissionInfo();
            permission.setImageType(image.getImageType());
            permission.setStandardSample(isStandardSample);
            permission.setAnnotatableType(Boolean.TRUE.equals(image.getIsAnnotatableType()));
            permission.setTemplate(template);
            
            if (isStandardSample) {
                // 标准样本可编辑和查看
                permission.setCanEdit(true);
                permission.setCanView(true);
                permission.setReason("标准样本，可编辑标注");
            } else if (template != null) {
                // 普通样本有模板时只能查看
                permission.setCanEdit(false);
                permission.setCanView(true);
                permission.setReason("普通样本，只能查看标注模板");
            } else {
                // 没有模板时不能查看
                permission.setCanEdit(false);
                permission.setCanView(false);
                permission.setReason("没有可用的标注模板");
            }
            
            return permission;
            
        } catch (Exception e) {
            log.error("获取图片标注权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return new AnnotationPermissionInfo(false, false, "权限检查失败: " + e.getMessage());
        }
    }
    
    @Override
    public Optional<VersionAnnotationTemplate> getTemplateById(String templateId) {
        try {
            return templateRepository.findByTemplateId(templateId);
        } catch (Exception e) {
            log.error("根据模板ID获取模板失败: templateId={}, error={}", templateId, e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    @Override
    @Transactional
    public boolean deleteTemplate(String templateId) {
        try {
            Optional<VersionAnnotationTemplate> template = templateRepository.findByTemplateId(templateId);
            if (template.isPresent()) {
                templateRepository.delete(template.get());
                log.info("删除标注模板成功: templateId={}", templateId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除标注模板失败: templateId={}, error={}", templateId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean hasTemplate(String versionId, String imageType) {
        try {
            return templateRepository.existsByVersionIdAndImageType(versionId, imageType);
        } catch (Exception e) {
            log.error("检查版本模板是否存在失败: versionId={}, imageType={}, error={}", versionId, imageType, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public List<VersionAnnotationTemplate> getTemplatesByStandardFolder(String standardFolderId) {
        try {
            return templateRepository.findByStandardFolderId(standardFolderId);
        } catch (Exception e) {
            log.error("根据标准文件夹获取模板失败: standardFolderId={}, error={}", standardFolderId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public void deleteTemplatesByStandardFolder(String standardFolderId) {
        try {
            log.info("删除标准文件夹的所有模板: standardFolderId={}", standardFolderId);
            templateRepository.deleteByStandardFolderId(standardFolderId);
        } catch (Exception e) {
            log.error("删除标准文件夹模板失败: standardFolderId={}, error={}", standardFolderId, e.getMessage(), e);
            throw new RuntimeException("删除标准文件夹模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public List<VersionAnnotationTemplate> copyTemplatesToVersion(String sourceVersionId, String targetVersionId, String newStandardFolderId) {
        try {
            log.info("复制模板到新版本: sourceVersionId={}, targetVersionId={}, newStandardFolderId={}",
                    sourceVersionId, targetVersionId, newStandardFolderId);

            // 获取源版本的所有模板
            List<VersionAnnotationTemplate> sourceTemplates = templateRepository.findByVersionId(sourceVersionId);
            if (sourceTemplates.isEmpty()) {
                log.warn("源版本没有模板可复制: sourceVersionId={}", sourceVersionId);
                return new ArrayList<>();
            }

            // 验证新标准文件夹
            FolderInfo folder = folderInfoRepository.findByFolderId(newStandardFolderId);
            if (folder == null || !"standard".equals(folder.getFolderType())) {
                throw new RuntimeException("新标准文件夹无效: " + newStandardFolderId);
            }

            List<VersionAnnotationTemplate> newTemplates = new ArrayList<>();
            String currentUser = SecurityUtils.getUsername();
            Date now = new Date();

            for (VersionAnnotationTemplate sourceTemplate : sourceTemplates) {
                // 检查目标版本是否已有相同类型的模板
                if (templateRepository.existsByVersionIdAndImageType(targetVersionId, sourceTemplate.getImageType())) {
                    log.warn("目标版本已存在相同类型的模板，跳过复制: versionId={}, imageType={}",
                            targetVersionId, sourceTemplate.getImageType());
                    continue;
                }

                // 创建新模板
                VersionAnnotationTemplate newTemplate = new VersionAnnotationTemplate();
                newTemplate.setTemplateId(IdUtils.simpleUUID());
                newTemplate.setVersionId(targetVersionId);
                newTemplate.setImageType(sourceTemplate.getImageType());
                newTemplate.setCertType(sourceTemplate.getCertType());
                newTemplate.setCountryCode(sourceTemplate.getCountryCode());
                newTemplate.setStandardFolderId(newStandardFolderId);
                newTemplate.setCreateTime(now);
                newTemplate.setUpdateTime(now);
                newTemplate.setCreatedBy(currentUser);
                newTemplate.setUpdatedBy(currentUser);

                // 深拷贝标注项列表
                if (sourceTemplate.getAnnotations() != null) {
                    List<VersionAnnotationTemplate.AnnotationItem> newAnnotations = sourceTemplate.getAnnotations().stream()
                            .map(this::copyAnnotationItem)
                            .collect(Collectors.toList());
                    newTemplate.setAnnotations(newAnnotations);
                }

                newTemplates.add(templateRepository.save(newTemplate));
            }

            log.info("模板复制完成: 复制了{}个模板", newTemplates.size());
            return newTemplates;

        } catch (Exception e) {
            log.error("复制模板到新版本失败: {}", e.getMessage(), e);
            throw new RuntimeException("复制模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateTemplate(AnnotationTemplateDTO templateDTO) {
        try {
            if (templateDTO == null) {
                return false;
            }

            // 验证基本字段
            if (StringUtils.isEmpty(templateDTO.getVersionId()) ||
                StringUtils.isEmpty(templateDTO.getImageType()) ||
                StringUtils.isEmpty(templateDTO.getStandardFolderId())) {
                return false;
            }

            // 验证标注项列表
            if (templateDTO.getAnnotations() == null || templateDTO.getAnnotations().isEmpty()) {
                return false;
            }

            // 验证每个标注项
            for (AnnotationTemplateDTO.AnnotationItemDTO item : templateDTO.getAnnotations()) {
                if (StringUtils.isEmpty(item.getAnnotationType()) ||
                    StringUtils.isEmpty(item.getAnnotationName())) {
                    return false;
                }

                // 验证坐标信息（如果存在）
                if (item.getCoordinate() != null) {
                    AnnotationTemplateDTO.AnnotationCoordinateDTO coord = item.getCoordinate();
                    if (coord.getX() == null || coord.getY() == null ||
                        coord.getWidth() == null || coord.getHeight() == null ||
                        coord.getX() < 0 || coord.getX() > 100 ||
                        coord.getY() < 0 || coord.getY() > 100 ||
                        coord.getWidth() <= 0 || coord.getWidth() > 100 ||
                        coord.getHeight() <= 0 || coord.getHeight() > 100) {
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.error("验证模板数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public TemplateStatistics getTemplateStatistics(String versionId) {
        try {
            TemplateStatistics stats = new TemplateStatistics();

            // 获取版本的所有模板
            List<VersionAnnotationTemplate> templates = templateRepository.findByVersionId(versionId);
            stats.setTotalTemplates(templates.size());

            // 统计已标注的图片类型
            Set<String> annotatedTypes = templates.stream()
                    .map(VersionAnnotationTemplate::getImageType)
                    .collect(Collectors.toSet());
            stats.setAnnotatedTypes(annotatedTypes.size());
            stats.setAvailableTypes(new ArrayList<>(annotatedTypes));

            // 获取标准文件夹信息
            if (!templates.isEmpty()) {
                String standardFolderId = templates.get(0).getStandardFolderId();
                stats.setStandardFolderId(standardFolderId);
                stats.setHasStandardFolder(StringUtils.isNotEmpty(standardFolderId));
            } else {
                stats.setHasStandardFolder(false);
            }

            return stats;

        } catch (Exception e) {
            log.error("获取模板统计信息失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return new TemplateStatistics();
        }
    }

    /**
     * 深拷贝标注项
     */
    private VersionAnnotationTemplate.AnnotationItem copyAnnotationItem(VersionAnnotationTemplate.AnnotationItem source) {
        VersionAnnotationTemplate.AnnotationItem copy = new VersionAnnotationTemplate.AnnotationItem();
        copy.setAnnotationId(IdUtils.simpleUUID()); // 生成新的ID
        copy.setAnnotationType(source.getAnnotationType());
        copy.setAnnotationName(source.getAnnotationName());
        copy.setAnnotationValue(source.getAnnotationValue());
        copy.setRequired(source.getRequired());
        copy.setDisplayOrder(source.getDisplayOrder());

        // 拷贝坐标信息
        if (source.getCoordinate() != null) {
            VersionAnnotationTemplate.AnnotationCoordinate coordinate = new VersionAnnotationTemplate.AnnotationCoordinate();
            coordinate.setX(source.getCoordinate().getX());
            coordinate.setY(source.getCoordinate().getY());
            coordinate.setWidth(source.getCoordinate().getWidth());
            coordinate.setHeight(source.getCoordinate().getHeight());
            copy.setCoordinate(coordinate);
        }

        return copy;
    }
}
