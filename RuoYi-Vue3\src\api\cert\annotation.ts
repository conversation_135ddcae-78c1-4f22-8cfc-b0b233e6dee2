import request from '@/utils/request'

// ==================== TypeScript 接口定义 ====================

// 导入统一的类型定义
import type {
  AjaxResult,
  TableDataInfo,
  AnnotationCoordinate,
  AnnotationItem,
  AnnotationTemplateDTO,
  VersionAnnotationTemplate,
  AnnotationPermissionInfo,
  TemplateStatistics,
  ImageAnnotationRequestDTO,
  ImageAnnotationVO
} from '@/types'

// ==================== 版本标注模板API ====================

/**
 * 创建或更新标注模板
 * POST /api/annotation-templates
 */
export function saveAnnotationTemplate(data: AnnotationTemplateDTO): Promise<AjaxResult<VersionAnnotationTemplate>> {
  return request({
    url: '/api/annotation-templates',
    method: 'post',
    data
  })
}

/**
 * 获取版本的标注模板列表
 * GET /api/annotation-templates/version/{versionId}
 */
export function getVersionTemplates(versionId: string): Promise<TableDataInfo<VersionAnnotationTemplate[]>> {
  return request({
    url: `/api/annotation-templates/version/${versionId}`,
    method: 'get'
  })
}

/**
 * 获取指定版本和图片类型的模板
 * GET /api/annotation-templates/version/{versionId}/type/{imageType}
 */
export function getAnnotationTemplate(versionId: string, imageType: string): Promise<AjaxResult<VersionAnnotationTemplate>> {
  return request({
    url: `/api/annotation-templates/version/${versionId}/type/${imageType}`,
    method: 'get'
  })
}

/**
 * 删除标注模板
 * DELETE /api/annotation-templates/{templateId}
 */
export function deleteAnnotationTemplate(templateId: string): Promise<AjaxResult> {
  return request({
    url: `/api/annotation-templates/${templateId}`,
    method: 'delete'
  })
}

/**
 * 删除版本的所有模板
 * DELETE /api/annotation-templates/version/{versionId}
 */
export function deleteVersionTemplates(versionId: string): Promise<AjaxResult> {
  return request({
    url: `/api/annotation-templates/version/${versionId}`,
    method: 'delete'
  })
}

/**
 * 复制模板到新版本
 * POST /api/annotation-templates/copy
 */
export function copyTemplatesToVersion(
  sourceVersionId: string, 
  targetVersionId: string, 
  newStandardFolderId: string
): Promise<AjaxResult<VersionAnnotationTemplate[]>> {
  return request({
    url: '/api/annotation-templates/copy',
    method: 'post',
    params: {
      sourceVersionId,
      targetVersionId,
      newStandardFolderId
    }
  })
}

/**
 * 获取版本模板统计信息
 * GET /api/annotation-templates/version/{versionId}/statistics
 */
export function getTemplateStatistics(versionId: string): Promise<AjaxResult<TemplateStatistics>> {
  return request({
    url: `/api/annotation-templates/version/${versionId}/statistics`,
    method: 'get'
  })
}

/**
 * 验证模板数据
 * POST /api/annotation-templates/validate
 */
export function validateAnnotationTemplate(data: AnnotationTemplateDTO): Promise<AjaxResult> {
  return request({
    url: '/api/annotation-templates/validate',
    method: 'post',
    data
  })
}

// ==================== 图片标注API ====================

/**
 * 获取图片标注信息
 * GET /api/images/{imageId}/annotations
 */
export function getImageAnnotations(imageId: string): Promise<AjaxResult<ImageAnnotationVO>> {
  return request({
    url: `/api/images/${imageId}/annotations`,
    method: 'get'
  })
}

/**
 * 保存图片标注
 * POST /api/images/{imageId}/annotations
 */
export function saveImageAnnotations(imageId: string, data: ImageAnnotationRequestDTO): Promise<AjaxResult> {
  return request({
    url: `/api/images/${imageId}/annotations`,
    method: 'post',
    data
  })
}

/**
 * 检查图片标注权限
 * GET /api/images/{imageId}/annotation-permission
 */
export function checkAnnotationPermission(imageId: string): Promise<AjaxResult<AnnotationPermissionInfo>> {
  return request({
    url: `/api/images/${imageId}/annotation-permission`,
    method: 'get'
  })
}

/**
 * 批量更新文件夹图片类型
 * POST /api/images/folder/{folderId}/update-types
 */
export function updateFolderImageTypes(folderId: string): Promise<AjaxResult> {
  return request({
    url: `/api/images/folder/${folderId}/update-types`,
    method: 'post'
  })
}

/**
 * 获取文件夹可标注图片列表
 * GET /api/images/folder/{folderId}/annotatable
 */
export function getAnnotatableImages(folderId: string): Promise<TableDataInfo<any[]>> {
  return request({
    url: `/api/images/folder/${folderId}/annotatable`,
    method: 'get'
  })
}

/**
 * 根据类型获取文件夹图片列表
 * GET /api/images/folder/{folderId}/type/{imageType}
 */
export function getImagesByType(folderId: string, imageType: string): Promise<TableDataInfo<any[]>> {
  return request({
    url: `/api/images/folder/${folderId}/type/${imageType}`,
    method: 'get'
  })
}
