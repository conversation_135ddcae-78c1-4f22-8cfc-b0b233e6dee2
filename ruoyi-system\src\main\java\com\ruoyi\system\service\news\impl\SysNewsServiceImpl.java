package com.ruoyi.system.service.news.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.SysNewsMapper;
import com.ruoyi.system.domain.news.SysNews;
import com.ruoyi.system.service.news.ISysNewsService;
import java.util.Map;
import com.ruoyi.common.annotation.DataScope;

/**
 * 文章管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class SysNewsServiceImpl implements ISysNewsService 
{
    @Autowired
    private SysNewsMapper sysNewsMapper;

    /**
     * 查询文章管理
     * 
     * @param newsId 文章管理主键
     * @return 文章管理
     */
    @Override
    public SysNews selectSysNewsByNewsId(Long newsId)
    {
        return sysNewsMapper.selectSysNewsByNewsId(newsId);
    }

    /**
     * 查询文章列表
     * 
     * @param sysNews 文章管理
     * @return 文章管理
     */
    @Override
    public List<SysNews> selectSysNewsList(SysNews sysNews)
    {
        return sysNewsMapper.selectSysNewsList(sysNews);
    }

    /**
     * 查询文章列表
     * 
     * @param sysNews 文章管理
     * @return 文章管理
     */
    @Override
    @DataScope(deptAlias = "n", userAlias = "u")
    public List<SysNews> adminSysNewsList(SysNews sysNews)
    {
        return sysNewsMapper.adminSysNewsList(sysNews);
    }

    /**
     * 新增文章管理
     * 
     * @param sysNews 文章管理
     * @return 结果
     */
    @Override
    public int insertSysNews(SysNews sysNews)
    {
        sysNews.setUploadTime(DateUtils.getNowDate());
        return sysNewsMapper.insertSysNews(sysNews);
    }

    /**
     * 修改文章管理
     * 
     * @param sysNews 文章管理
     * @return 结果
     */
    @Override
    public int updateSysNews(SysNews sysNews)
    {
        sysNews.setUpdateTime(DateUtils.getNowDate());
        return sysNewsMapper.updateSysNews(sysNews);
    }

    /**
     * 批量删除文章管理
     * 
     * @param newsIds 需要删除的文章管理主键
     * @return 结果
     */
    @Override
    public int deleteSysNewsByNewsIds(Long[] newsIds)
    {
        return sysNewsMapper.deleteSysNewsByNewsIds(newsIds);
    }

    /**
     * 删除文章管理信息
     * 
     * @param newsId 文章管理主键
     * @return 结果
     */
    @Override
    public int deleteSysNewsByNewsId(Long newsId)
    {
        return sysNewsMapper.deleteSysNewsByNewsId(newsId);
    }


    
    @Override
    public List<Map<String, Object>> countNewsByTimeAndType(String type) {
        return sysNewsMapper.countNewsByTimeAndType(type);
    }
}
