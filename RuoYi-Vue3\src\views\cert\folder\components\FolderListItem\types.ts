/**
 * FolderListItem 组件类型定义
 */

export interface FolderListItemProps {
  /** 文件夹名称 */
  folderName: string
  /** 主图缩略图URL */
  thumbnailUrl?: string
  /** 是否显示复选框 */
  showCheckbox?: boolean
  /** 复选框是否被选中 */
  isChecked?: boolean
  /** 是否显示[比对]按钮 */
  showCompareButton?: boolean
  /** 是否显示[标准]标签 */
  showStandardBadge?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义样式类 */
  customClass?: string
}

export interface FolderListItemEmits {
  /** 复选框状态改变时触发 */
  'check-change': [checked: boolean]
  /** [比对]按钮被点击时触发 */
  'compare-clicked': []
  /** 图片加载失败时触发 */
  'image-error': [event: Event]
}

/**
 * 文件夹数据结构（基于您的MongoDB数据）
 */
export interface FolderData {
  /** MongoDB主键 */
  _id: {
    $oid: string
  }
  /** 业务文件夹ID */
  folderId: string
  /** 文件信息ID */
  fileInfoId: string
  /** 任务ID */
  taskId: string
  /** 版本ID */
  versionId?: string
  /** 文件夹类型 */
  folderType: 'standard' | 'regular'
  /** 文件夹名称 */
  folderName: string
  /** 预解析版本信息 */
  preParseVersionInfo?: {
    originalFolderName: string
    parsedVersionCode: string
    countryName: string
    countryCode: string
    certTypeName: string
    certTypeCode: string
    issueYear: string
    certNumberPrefix: string
    issuePlace: string
    parseStatus: 'SUCCESS' | 'FAILED' | 'PARSING'
    parseErrors: string[]
    parseTime: {
      $date: string
    }
  }
  /** 文件夹路径 */
  folderPath: string
  /** 已上传文件数量 */
  uploadedFileCount: number
  /** 总文件数量 */
  totalFileCount: number
  /** 上传进度 */
  uploadProgress: number
  /** 是否多版本 */
  isMultiVersion: boolean
  /** 国家信息 */
  countryInfo: {
    _id: {
      $numberLong: string
    }
    name: string
    nameEn: string
    code: string
    createTime: {
      $date: string
    }
    updateTime: {
      $date: string
    }
  }
  /** 证件类型信息 */
  certInfo: {
    _id: {
      $numberLong: string
    }
    zjlbdm: string
    zjlbmc: string
    sybj: string
    zjjc: string
    gjsy0: string
    bz: string
    createTime: {
      $date: string
    }
    updateTime: {
      $date: string
    }
  }
  /** 发行年份 */
  issueYear: string
  /** 文件数量 */
  fileCount: number
  /** 已处理文件数量 */
  processedFileCount: number
  /** 状态 */
  status: 'associated' | 'unassociated' | 'processing'
  /** 创建时间 */
  createTime: {
    $date: string
  }
  /** 更新时间 */
  updateTime: {
    $date: string
  }
  /** 审核状态 */
  reviewStatus: 'pending' | 'approved' | 'rejected'
  /** 版本关联方式 */
  versionAssociationMethod: 'manual_select' | 'auto_parse'
  /** 主图路径（您将要添加的字段） */
  mainPicPath?: string
}

/**
 * 简化的文件夹信息（用于组件显示）
 */
export interface SimpleFolderInfo {
  /** 文件夹ID */
  folderId: string
  /** 文件夹名称 */
  folderName: string
  /** 缩略图URL */
  thumbnailUrl?: string
  /** 文件夹类型 */
  folderType: 'standard' | 'regular'
  /** 状态 */
  status: 'associated' | 'unassociated' | 'processing'
  /** 国家名称 */
  countryName?: string
  /** 证件类型名称 */
  certTypeName?: string
  /** 发行年份 */
  issueYear?: string
  /** 文件数量 */
  fileCount?: number
  /** 是否已选中 */
  isSelected?: boolean
}

/**
 * 文件夹列表配置
 */
export interface FolderListConfig {
  /** 是否显示复选框 */
  showCheckbox?: boolean
  /** 是否显示比对按钮 */
  showCompareButton?: boolean
  /** 是否显示标准标签 */
  showStandardBadge?: boolean
  /** 是否允许多选 */
  allowMultiSelect?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 尺寸模式 */
  size?: 'compact' | 'default' | 'large'
  /** 自定义样式类 */
  customClass?: string
}

/**
 * 文件夹选择事件
 */
export interface FolderSelectionEvent {
  /** 选中的文件夹ID列表 */
  selectedFolderIds: string[]
  /** 选中的文件夹数据列表 */
  selectedFolders: FolderData[]
  /** 是否全选 */
  isAllSelected: boolean
  /** 总文件夹数量 */
  totalCount: number
}

/**
 * 文件夹操作事件
 */
export interface FolderActionEvent {
  /** 操作的文件夹 */
  folder: FolderData
  /** 操作类型 */
  action: 'view' | 'edit' | 'delete' | 'compare' | 'set-standard' | 'remove-standard'
  /** 操作时间 */
  timestamp: number
}

/**
 * 工具函数类型
 */
export type FolderFilterFunction = (folder: FolderData) => boolean
export type FolderSortFunction = (a: FolderData, b: FolderData) => number
export type FolderTransformFunction = (folder: FolderData) => SimpleFolderInfo 