# MongoDB 迁移脚本执行指南

## 📋 执行前准备

### 1. 确认MongoDB服务状态
```bash
# 检查MongoDB服务是否运行
docker ps | grep mongo
# 或者
systemctl status mongod
```

### 2. 备份数据库（重要！）
```bash
# 备份整个数据库
mongodump --uri="***********************************************************************" --out=backup_$(date +%Y%m%d_%H%M%S)
```

### 3. 验证连接
```bash
# 测试连接
mongo ****************************************************** --authSource admin --eval "db.runCommand('ping')"
```

## 🚀 执行迁移脚本

### 方式1：使用MongoDB Shell（推荐）

1. **连接到MongoDB**：
```bash
cd /path/to/your/project/RuoYi-Vue
mongo ****************************************************** --authSource admin
```

2. **在Shell中执行脚本**：
```javascript
// 在mongo shell中执行
load("sql/mongodb-migration-2025-01-15.js")
```

### 方式2：直接执行文件
```bash
cd /path/to/your/project/RuoYi-Vue
mongo ****************************************************** --authSource admin sql/mongodb-migration-2025-01-15.js
```

### 方式3：使用mongosh（MongoDB 5.0+）
```bash
cd /path/to/your/project/RuoYi-Vue
mongosh "***********************************************************************" --file sql/mongodb-migration-2025-01-15.js
```

## 📊 执行结果验证

### 1. 检查新集合是否创建
```javascript
// 在mongo shell中执行
db.getCollectionNames().filter(name => name.includes("version_annotation_template"))
```

### 2. 检查索引是否创建
```javascript
// 检查版本标注模板集合的索引
db.version_annotation_template.getIndexes()

// 检查文件夹集合的新索引
db.folder_info.getIndexes().filter(index => index.name.includes("standard"))

// 检查图片集合的新索引
db.image_repository.getIndexes().filter(index => index.name.includes("type"))
```

### 3. 检查字段更新情况
```javascript
// 检查文件夹的标准样本字段
db.folder_info.findOne({}, {isStandardSample: 1})

// 检查图片的可标注类型字段
db.image_repository.findOne({}, {isAnnotatableType: 1, imageType: 1})

// 统计可标注的图片数量
db.image_repository.countDocuments({isAnnotatableType: true})
```

## 🔍 故障排除

### 常见问题1：连接失败
```bash
# 检查MongoDB是否运行
docker ps | grep mongo

# 检查端口是否开放
netstat -an | grep 27017

# 重启MongoDB容器
docker restart <mongo_container_name>
```

### 常见问题2：权限不足
```bash
# 确保使用正确的认证数据库
mongo ****************************************************** --authSource admin

# 检查用户权限
db.runCommand({usersInfo: "admin"})
```

### 常见问题3：脚本语法错误
```bash
# 先测试语法
mongo --eval "print('语法测试')"

# 使用语法测试脚本
mongo ****************************************************** --authSource admin sql/test-migration-syntax.js
```

## 📝 执行日志示例

正常执行时应该看到类似输出：
```
=== 开始执行版本标注模板系统数据迁移 ===
1. 创建版本标注模板集合...
2. 创建版本标注模板索引...
版本标注模板索引创建完成
3. 为现有文件夹添加标准样本标志字段...
文件夹标准样本标志字段更新完成，影响文档数: 25
4. 为现有图片添加可标注类型字段...
图片可标注类型字段更新完成，影响文档数: 150
5. 自动识别图片类型并设置可标注标志...
图片类型自动识别完成，更新图片数: 45
...
=== 版本标注模板系统数据迁移完成 ===
```

## ⚠️ 注意事项

1. **执行前务必备份数据**
2. **在测试环境先执行验证**
3. **确保MongoDB副本集模式运行**（项目配置要求）
4. **脚本执行期间避免其他数据库操作**
5. **记录执行日志以便问题排查**

## 🔄 回滚方案

如果需要回滚：
```javascript
// 删除新创建的集合
db.version_annotation_template.drop()

// 移除新添加的字段
db.folder_info.updateMany({}, {$unset: {isStandardSample: ""}})
db.image_repository.updateMany({}, {$unset: {isAnnotatableType: ""}})

// 删除新创建的索引
db.folder_info.dropIndex("version_standard_sample_idx")
db.folder_info.dropIndex("is_standard_sample_idx")
db.image_repository.dropIndex("image_type_idx")
db.image_repository.dropIndex("is_annotatable_type_idx")
db.image_repository.dropIndex("version_image_type_idx")
```
