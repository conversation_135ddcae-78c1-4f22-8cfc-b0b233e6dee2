package com.ruoyi.system.service;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.dto.request.BatchTaskCreateDTO;
import com.ruoyi.system.domain.dto.response.BatchUploadTaskVO;
import com.ruoyi.system.domain.mongo.BatchUploadTask;

import java.util.List;
import java.util.Map;

/**
 * 批量上传任务服务接口 (合并版)
 *
 * 负责处理批量断点续传功能的完整业务逻辑，包括：
 * 1. 创建批量上传任务 (核心业务)
 * 2. 处理文件上传完成回调 (核心业务)
 * 3. 管理任务状态和进度 (流程编排)
 * 4. 任务查询和统计 (流程编排)
 * 5. 任务生命周期管理 (流程编排)
 *
 * 注意：此接口合并了原 IBatchUploadTaskService 的功能
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IBatchTaskService {

    /**
     * 创建批量上传任务 (统一接口)
     *
     * 一次性创建批量上传任务及其所有文件夹信息。
     * 支持单文件夹和多文件夹场景的统一处理。
     *
     * 包含以下操作：
     * 1. 创建BatchUploadTask
     * 2. 批量创建FolderInfo记录（1个或多个）
     * 3. 图片信息将在文件上传完成后实时创建
     *
     * @param dto 任务创建DTO（包含文件夹列表）
     * @return 创建的任务对象
     * @throws ServiceException 当参数无效时抛出业务异常
     */
    BatchUploadTask createBatchTask(BatchTaskCreateDTO dto) throws ServiceException;

    /**
     * 处理文件上传完成回调
     *
     * 当Tus服务器完成单个文件上传时调用此方法，执行以下操作：
     * 1. 根据taskId和folderPath查找对应的FolderInfo记录
     * 2. 创建ImageRepository记录，使用原始文件名记录文件信息和MinIO路径
     * 3. 更新文件夹的上传进度统计
     * 4. 检查任务是否完成，如果完成则更新任务状态
     * 5. 如果所有文件上传完成，自动触发版本解析
     *
     * @param taskId 任务ID，用于关联任务和文件
     * @param issuePlace 签发地，用于填充FolderInfo
     * @param certNumberPrefix 证号前缀，用于构建MinIO路径
     * @param originalFileName 用户上传的原始文件名
     * @param finalMinioPath 文件在MinIO中的最终存储路径
     * @param folderName 原始文件夹名称，用于存储在FolderInfo中
     * @param folderPath 完整文件夹路径，用于匹配对应的FolderInfo记录
     * @throws ServiceException 当taskId无效或数据库操作失败时抛出业务异常
     */
    void handleUploadCompletion(String taskId, String issuePlace, String certNumberPrefix,
                               String originalFileName, String finalMinioPath, String folderName, String folderPath) throws ServiceException;

    /**
     * 检查任务是否完成并触发后续处理
     *
     * 当任务的所有文件上传完成时，执行以下操作：
     * 1. 更新任务状态为 "COMPLETED"
     * 2. 触发异步版本解析
     * 3. 记录任务完成时间
     *
     * @param taskId 任务ID
     * @return 是否成功触发后续处理
     */
    boolean checkTaskCompletionAndTriggerParsing(String taskId);

    /**
     * 级联删除批量上传任务
     *
     * 删除指定的批量上传任务及其所有关联数据，包括：
     * 1. FolderInfo 记录
     * 2. ImageRepository 记录
     * 3. MinIO 中的实际文件
     * 4. BatchUploadTask 记录
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteBatchTaskWithCascade(String taskId);



    /**
     * 级联删除多版本批量上传任务
     *
     * 删除指定的多版本批量上传任务及其所有子版本和关联数据
     *
     * @param parentTaskId 父任务ID
     * @return 是否删除成功
     */
    boolean deleteMultiVersionBatchTaskWithCascade(String parentTaskId);

    // ==================== 以下方法来自原 IBatchUploadTaskService (流程编排功能) ====================

    /**
     * 创建批量上传任务 (简化版)
     *
     * @param task 任务信息
     * @return 创建的任务
     */
    BatchUploadTask createTask(BatchUploadTask task);

    /**
     * 根据任务ID获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情VO
     */
    BatchUploadTaskVO getTaskById(String taskId);

    /**
     * 根据MongoDB主键获取任务
     *
     * @param id MongoDB主键
     * @return 任务实体
     */
    BatchUploadTask getTaskByMongoId(String id);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateTaskStatus(String taskId, String status);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param processedFolders 已处理文件夹数
     * @param processedFiles 已处理文件数
     * @return 是否更新成功
     */
    boolean updateTaskProgress(String taskId, Integer processedFolders, Integer processedFiles);

    /**
     * 原子性更新任务进度和状态
     *
     * @param taskId 任务ID
     * @param processedFolders 已处理文件夹数
     * @param processedFiles 已处理文件数
     * @param status 任务状态
     * @return 是否更新成功
     */
    boolean updateTaskProgressAndStatus(String taskId, Integer processedFolders, Integer processedFiles, String status);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @return 是否完成成功
     */
    boolean completeTask(String taskId);

    /**
     * 标记任务失败
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 是否标记成功
     */
    boolean failTask(String taskId, String errorMessage);

    /**
     * 获取用户的任务列表
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    List<BatchUploadTaskVO> getTasksByUserId(Long userId);

    /**
     * 获取部门的任务列表
     *
     * @param deptId 部门ID
     * @return 任务列表
     */
    List<BatchUploadTaskVO> getTasksByDeptId(Long deptId);

    /**
     * 获取指定状态的任务列表
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<BatchUploadTaskVO> getTasksByStatus(String status);

    /**
     * 删除任务及其关联数据 (简化版)
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(String taskId);

    /**
     * 启动批量上传流程
     * 处理文件夹列表，创建文件夹记录，并调用图片处理服务
     *
     * @param taskId 任务ID
     * @param folderPaths 文件夹路径列表
     * @return 是否启动成功
     */
    boolean startBatchUploadProcess(String taskId, List<String> folderPaths);

    /**
     * 处理单个文件夹
     *
     * @param taskId 任务ID
     * @param folderPath 文件夹路径
     * @return 是否处理成功
     */
    boolean processSingleFolder(String taskId, String folderPath);

    /**
     * 获取任务统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息Map
     */
    Map<String, Object> getTaskStatistics(String taskId);

    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @return 是否重试成功
     */
    boolean retryTask(String taskId);

    /**
     * 暂停任务
     *
     * @param taskId 任务ID
     * @return 是否暂停成功
     */
    boolean pauseTask(String taskId);

    /**
     * 恢复任务
     *
     * @param taskId 任务ID
     * @return 是否恢复成功
     */
    boolean resumeTask(String taskId);
}
