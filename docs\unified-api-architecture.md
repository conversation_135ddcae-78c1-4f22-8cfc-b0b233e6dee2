# 统一API架构设计

## 设计原则

### 1. 前端适配后端，而非后端重复实现
- ✅ **保持后端接口的一致性和现代化**
- ✅ **调整前端调用来适配新的后端接口**
- ✅ **避免代码重复和维护负担**
- ❌ 不在新代码中重复实现旧接口功能

### 2. 统一的API路径设计
```
/api/versions          # 版本管理 (新统一API)
/api/folders           # 文件夹管理
/api/images            # 图像管理
/batch/tasks           # 批量任务管理
```

## 版本管理API

### 后端Controller: `CertVersionController`
**路径**: `/api/versions`

```java
POST   /api/versions                          # 创建新版本
GET    /api/versions                          # 查询版本列表 (支持分页和筛选)
GET    /api/versions/{versionId}              # 获取版本详情
PUT    /api/versions/{versionId}              # 更新版本信息
DELETE /api/versions/{versionId}              # 删除版本
GET    /api/versions/country/{countryId}      # 获取指定国家的版本信息
GET    /api/versions/{versionId}/folders      # 获取版本下的文件夹列表
PUT    /api/versions/{versionId}/folders/{folderId}/type  # 更新文件夹类型
```

### 前端API: `/src/api/samples/version.ts`
```typescript
// 统一使用新的API路径
export function getVersionList(params?: VersionQueryDTO)     // GET /api/versions
export function getVersionDetails(versionId: string)        // GET /api/versions/{versionId}
export function deleteVersion(versionId: string)            // DELETE /api/versions/{versionId}
export function createVersionFromFolder(data: any)          // POST /api/versions
```

## Service层架构

### 核心实现原则
1. **主要查询方法**: `listVersions(VersionQueryDTO)` - 统一的查询入口
2. **分页查询方法**: `getVersionsWithSamplesPaged()` - 统一的分页查询
3. **委托模式**: 旧方法委托给新方法，避免重复实现

```java
// ✅ 推荐：委托给统一方法
@Override
public List<CertVersion> listVersionsWithFilter(...) {
    VersionQueryDTO query = new VersionQueryDTO();
    // 设置查询参数
    return listVersions(query);  // 委托给统一方法
}

// ❌ 避免：重复实现相同逻辑
@Override
public List<CertVersion> listVersionsWithFilter(...) {
    Query query = new Query();
    // 重复的查询逻辑...
}
```

## 前端组件更新

### 版本管理页面: `VersionManagementView.vue`
```vue
<script setup>
// ✅ 使用统一的新API
import { getVersionList, getVersionDetails, deleteVersion } from '@/api/samples/version'

// ❌ 避免：混用新旧API
// import { listVersion, getVersion, delCertVersion } from '@/api/cert/version'
// import { getVersionList } from '@/api/samples/version'
</script>
```

## 数据流架构

```
前端组件 → 统一API (/api/samples/version.ts) → 后端Controller (/api/versions) → Service (统一方法) → MongoDB
```

### 查询流程
1. **前端**: `getVersionList(params)` 调用 `/api/versions`
2. **Controller**: `CertVersionController.listVersions()` 接收请求
3. **Service**: `listVersions(VersionQueryDTO)` 或 `getVersionsWithSamplesPaged()` 处理查询
4. **数据库**: MongoDB `cert_version` 集合查询
5. **返回**: 统一的 `TableDataInfo` 格式

## 兼容性策略

### 1. 渐进式迁移
- ✅ 保留旧API文件但标记为 `@deprecated`
- ✅ 新功能只使用新API
- ✅ 逐步迁移现有组件到新API

### 2. 清理计划
1. **Phase 1**: 实现新API并确保功能正常
2. **Phase 2**: 迁移所有前端组件到新API
3. **Phase 3**: 移除旧API文件和重复代码

## 优势

### 1. 代码质量
- **减少重复**: 避免多个方法实现相同逻辑
- **易于维护**: 统一的API路径和数据格式
- **类型安全**: TypeScript接口定义

### 2. 开发效率
- **一致性**: 统一的命名和结构
- **可预测**: 标准的RESTful设计
- **易扩展**: 清晰的架构边界

### 3. 系统稳定性
- **单一职责**: 每个方法有明确的职责
- **错误处理**: 统一的异常处理机制
- **日志记录**: 一致的日志格式

## 实施状态

### ✅ 已完成
- [x] 后端统一API Controller (`CertVersionController`)
- [x] Service层核心方法实现
- [x] 前端统一API文件 (`/api/samples/version.ts`)
- [x] 版本管理页面API调用更新

### 🔄 进行中
- [ ] 其他页面的API调用迁移
- [ ] 旧API文件标记为deprecated
- [ ] 完整的错误处理和日志记录

### 📋 待完成
- [ ] 移除重复的API文件
- [ ] 完善API文档
- [ ] 添加单元测试
