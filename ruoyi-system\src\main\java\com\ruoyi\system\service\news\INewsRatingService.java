package com.ruoyi.system.service.news;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.news.NewsRating;

/**
 * 文章评分Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface INewsRatingService 
{
    /**
     * 查询文章评分
     * 
     * @param ratingId 文章评分主键
     * @return 文章评分
     */
    public NewsRating selectNewsRatingByRatingId(Long ratingId);

    /**
     * 查询文章评分列表
     * 
     * @param newsRating 文章评分
     * @return 文章评分集合
     */
    public List<NewsRating> selectNewsRatingList(NewsRating newsRating);

    /**
     * 新增文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    public int insertNewsRating(NewsRating newsRating);

    /**
     * 修改文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    public int updateNewsRating(NewsRating newsRating);

    /**
     * 批量删除文章评分
     * 
     * @param ratingIds 需要删除的文章评分主键集合
     * @return 结果
     */
    public int deleteNewsRatingByRatingIds(Long[] ratingIds);

    /**
     * 删除文章评分信息
     * 
     * @param ratingId 文章评分主键
     * @return 结果
     */
    public int deleteNewsRatingByRatingId(Long ratingId);

    /**
     * 获取文章平均评分和评分人数
     * 
     * @param newsId 文章ID
     * @return 包含平均评分和评分人数的Map
     */
    public Map<String, Object> getAvgRatingByNewsId(Long newsId);
    
    /**
     * 添加用户评分
     * 
     * @param newsRating 评分信息
     * @return 结果
     */
    public int addUserRating(NewsRating newsRating);

    /**
     * 插入或更新文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    public int insertOrUpdateRating(NewsRating newsRating);
}
