/**
 * 兼容性API - 旧版 @/cert 目录的API迁移
 *
 * 注意：这些API是为了保持向后兼容性，建议逐步迁移到新版API
 *
 * 迁移指南：
 * - 旧版: import { listVersion } from '@/api/cert/version'
 * - 新版: import { getVersionList } from '@/api/cert'
 */

import request from '@/utils/request'

// ==================== 版本管理兼容性API ====================

/**
 * 查询证件版本列表（分页）- 兼容性API
 * @deprecated 请使用 getVersionList
 */
export function listVersion(query: any) {
  return request({
    url: '/cert/version/page',
    method: 'get',
    params: query,
    timeout: 60000
  })
}

/**
 * 查询证件版本列表（不分页）- 兼容性API
 * @deprecated 请使用 getVersionList
 */
export function listVersions(countryId: number, certType: string) {
  return request({
    url: '/cert/version/list',
    method: 'get',
    params: { countryId, certType }
  })
}

/**
 * 查询证件版本详细 - 兼容性API
 * @deprecated 请使用 getVersionDetails
 */
export function getVersion(id: string) {
  return request({
    url: '/cert/version/' + id,
    method: 'get'
  })
}

/**
 * 根据版本ID获取证件版本 - 兼容性API
 * @deprecated 请使用 getVersionDetails
 */
export function getVersionByVersionId(versionId: string) {
  return request({
    url: '/cert/version/byVersionId/' + versionId,
    method: 'get'
  })
}

/**
 * 新增证件版本 - 兼容性API
 * @deprecated 请使用 createVersionFromFolder
 */
export function addVersion(data: any) {
  return request({
    url: '/cert/version',
    method: 'post',
    data: data
  })
}

/**
 * 修改证件版本 - 兼容性API
 * @deprecated 请使用 updateVersion
 */
export function updateVersion(data: any) {
  return request({
    url: '/cert/version',
    method: 'put',
    data: data
  })
}

/**
 * 删除证件版本 - 兼容性API
 * @deprecated 请使用 deleteVersion
 */
export function delVersion(id: string) {
  return request({
    url: '/cert/version/' + id,
    method: 'delete'
  })
}

// ==================== 国家管理兼容性API ====================

/**
 * 查询国家列表，分页 - 兼容性API
 * @deprecated 请使用 getCountryList from '@/api/cert/country'
 */
export function listCountry(query: any) {
  return request({
    url: '/cert/country/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询国家列表，不分页 - 兼容性API
 * @deprecated 请使用 getAllCountries from '@/api/cert/country'
 */
export function listCountryAll(query: any) {
  return request({
    url: '/cert/country/listAll',
    method: 'get',
    params: query
  })
}

/**
 * 根据首字母查询国家列表 - 兼容性API
 * @deprecated 请使用 getCountriesByLetter from '@/api/cert/country'
 */
export function listCountryByLetter(letter: string) {
  return request({
    url: `/cert/country/listByLetter/${letter}`,
    method: 'get'
  })
}

/**
 * 获取所有首字母及对应的国家数量 - 兼容性API
 * @deprecated 请使用 getCountryLetters from '@/api/cert/country'
 */
export function getCountryLetters() {
  return request({
    url: '/cert/country/letters',
    method: 'get'
  })
}

/**
 * 查询国家详细信息 - 兼容性API
 * @deprecated 请使用 getCountryDetails from '@/api/cert/country'
 */
export function getCountry(countryCode: string) {
  return request({
    url: '/cert/country/' + countryCode,
    method: 'get'
  })
}
// ==================== 证件类型管理兼容性API ====================
