# 前端Vue组件功能指南

## 📋 目录结构

```
RuoYi-Vue3/src/views/cert/
├── annotations/           # 图片标注组件库
├── batch/                # 批量上传管理
├── country/              # 国家管理
├── sample/               # 样本管理
├── type/                 # 证件类型管理
└── version/              # 版本管理
```

## 🎯 核心功能模块

### 1. 证件类型管理 (`cert/type/`)

#### `index.vue` - 证件类型管理主页面
**路由**: `/cert/type`
**功能**: 
- 证件类型的增删改查
- 支持按名称和代码搜索
- 分页显示
- 导出功能

**API调用**:
- `getCertTypes()` - 获取证件类型列表
- `addType()` - 新增证件类型
- `updateType()` - 更新证件类型
- `delType()` - 删除证件类型

**字段映射**:
- `zjlbmc` - 证件类型名称
- `zjlbdm` - 证件类型代码
- `zyType` - 专业类型

---

### 2. 国家管理 (`cert/country/`)

#### `index.vue` - 国家信息管理
**路由**: `/cert/country`
**功能**:
- 国家信息的增删改查
- 支持中英文名称管理
- 国家代码管理

**API调用**:
- `getCountries()` - 获取国家列表
- `addCountry()` - 新增国家
- `updateCountry()` - 更新国家信息

---

### 3. 版本管理 (`cert/version/`)

#### `VersionManagementView.vue` - 版本管理主页面 ⭐
**路由**: `/cert/version`
**功能**:
- 证件版本信息管理
- 多条件筛选（国家、证件类型、年份）
- 标准样本文件夹设置
- 版本详情查看

**子组件**:
- `VersionDetailModal.vue` - 版本详情弹窗
- `FolderManagementModal.vue` - 文件夹管理弹窗
- `VersionFolderManagementModal.vue` - 版本文件夹管理

**API调用**:
- `getVersionList()` - 获取版本列表
- `getVersionDetails()` - 获取版本详情
- `setStandardFolder()` - 设置标准样本

#### `components/VersionDetailModal.vue`
**功能**:
- 显示版本详细信息
- 关联文件夹列表
- 标准样本状态管理
- 跳转到文件夹详情

#### `components/FolderManagementModal.vue`
**功能**:
- 文件夹列表管理
- 文件夹状态查看
- 批量操作支持

---

### 4. 样本管理 (`cert/sample/`)

#### `SampleManagementView.vue` - 样本管理主页面
**路由**: `/cert/sample`
**功能**:
- 文件夹级别的样本管理
- 版本关联操作
- 状态筛选和搜索

**子组件**:
- `VersionCreatorModal.vue` - 版本创建弹窗
- `VersionSelectorModal.vue` - 版本选择弹窗
- `ThirdPartyDetectionModal.vue` - 第三方检测弹窗
- `CertVersionForm.vue` - 证件版本表单

**API调用**:
- `getFolders()` - 获取文件夹列表
- `createVersion()` - 创建新版本
- `associateVersion()` - 关联现有版本

#### `FolderDetailView.vue` - 文件夹详情页面 ⭐⭐⭐
**路由**: `/cert/folder/:folderId`
**功能**:
- 图片标注工作区（核心功能）
- 图片列表展示和搜索
- 实时标注编辑和保存
- 标注进度跟踪

**子组件**:
- `AnnotationToolbar.vue` - 标注工具栏
- `AnnotationCanvas.vue` - 标注画布
- `AnnotationList.vue` - 标注列表

**Composables**:
- `useImageAnnotation()` - 图片标注逻辑
- `useFolderInfo()` - 文件夹信息管理

#### `CountryList.vue` - 国家列表页面
**路由**: `/cert/sample/countries`
**功能**:
- 按国家浏览证件版本
- 国家统计信息

#### `CountryCertVersions.vue` - 国家证件版本页面
**路由**: `/cert/sample/country/:countryId`
**功能**:
- 显示特定国家的所有证件版本
- 版本筛选和搜索

---

### 5. 批量上传管理 (`cert/batch/`)

#### `BatchTaskManagement.vue` - 批量任务管理
**路由**: `/cert/batch`
**功能**:
- 批量上传任务管理
- 多版本文件夹上传
- 任务状态跟踪

**子组件**:
- `UppyDashboardModal.vue` - 文件上传界面
- `MultiVersionMetadataModal.vue` - 多版本元数据编辑
- `FolderScanProgress.vue` - 文件夹扫描进度

**API调用**:
- `createBatchTask()` - 创建批量任务
- `uploadFiles()` - 文件上传
- `getTaskStatus()` - 获取任务状态

---

### 6. 图片标注组件库 (`cert/annotations/`) ⭐⭐⭐

#### `AnnotationCanvas.vue` - 核心标注画布
**功能**:
- 基于Annotorious 3.0的图片标注
- 支持矩形、多边形标注
- 实时标注数据同步
- 工具切换和状态管理

**Props**:
- `imageUrl` - 图片URL
- `tool` - 当前工具
- `modelValue` - 标注数据（v-model）

**Events**:
- `update:modelValue` - 标注数据更新
- `select-annotation` - 标注选择

#### `AnnotationToolbar.vue` - 标注工具栏
**功能**:
- 提供选择、矩形、多边形工具
- 工具状态高亮显示
- 工具提示

**Events**:
- `tool-selected` - 工具选择事件

#### `AnnotationList.vue` - 标注列表
**功能**:
- 标注项列表显示
- 标注文本编辑
- 标注删除和选择

**Props**:
- `modelValue` - 标注数组（v-model）

**Events**:
- `update:modelValue` - 标注数组更新
- `select-annotation` - 标注选择

---

## 🔧 Composables (组合式函数)

### `useImageAnnotation.js`
**功能**:
- 图片标注状态管理
- 标注数据的加载和保存
- 图片选择和切换逻辑

**返回值**:
- `imageList` - 图片列表
- `selectedImage` - 当前选中图片
- `currentAnnotations` - 当前标注数据
- `loadImageList()` - 加载图片列表
- `saveAnnotations()` - 保存标注数据

### `useFolderInfo.js`
**功能**:
- 文件夹信息管理
- 文件夹状态处理

**返回值**:
- `folderInfo` - 文件夹信息
- `loading` - 加载状态
- `loadFolderInfo()` - 加载文件夹信息

---

## 🧩 标注组件库 (`@/components/Annotation/`) ⭐⭐⭐

### 组件概览

标注组件库是系统的核心功能模块，提供了完整的图片标注解决方案。这些组件被设计为可复用的独立模块，支持在不同页面中灵活使用。

#### `AnnotationToolbar.vue` - 标注工具栏
**路径**: `/src/components/Annotation/AnnotationToolbar.vue`
**功能**:
- 标注工具选择和配置
- 权限状态显示和控制
- 标注保存和重置操作
- 标注类型选择器

**主要Props**:
```typescript
interface Props {
  canEdit: boolean        // 是否可编辑
  canView: boolean        // 是否可查看
  reason?: string         // 权限说明
  annotations: AnnotationItem[]  // 标注数据
}
```

**主要Events**:
- `save` - 保存标注数据
- `reset` - 重置标注
- `annotation-type-change` - 标注类型变更

#### `AnnotationList.vue` - 标注列表
**路径**: `/src/components/Annotation/AnnotationList.vue`
**功能**:
- 显示当前图片的所有标注
- 标注项的选择和删除
- 标注详情的编辑和查看
- 批量操作支持

**主要Props**:
```typescript
interface Props {
  annotations: AnnotationItem[]  // 标注数据列表
  canEdit: boolean              // 是否可编辑
  selectedAnnotations?: string[] // 选中的标注ID
}
```

**主要Events**:
- `annotation-select` - 标注选择变更
- `annotation-edit` - 编辑标注
- `annotation-delete` - 删除标注

#### `ImageTypeAnnotator.vue` - 图片类型标注器
**路径**: `/src/components/Annotation/ImageTypeAnnotator.vue`
**功能**:
- 图片类型的识别和标注
- 图片类型的修改和保存
- 可标注类型的判断和显示
- 样本类型状态显示

**主要Props**:
```typescript
interface Props {
  imageInfo: ImageInfo    // 图片信息
  canEdit: boolean        // 是否可编辑
}
```

**支持的图片类型**:
- `VISIBLE_DATA_PAGE` - 可见光数据页（可标注）
- `INFRARED_DATA_PAGE` - 红外数据页（可标注）
- `ULTRAVIOLET_DATA_PAGE` - 紫外数据页（可标注）
- `OTHER` - 其他类型（仅查看）

#### `ImageTypeAnnotatorCompact.vue` - 紧凑型图片类型标注器
**路径**: `/src/components/Annotation/ImageTypeAnnotatorCompact.vue`
**功能**:
- 紧凑布局的图片类型标注
- 适用于侧边栏或小空间显示
- 快速类型切换功能

#### `StandardSampleManager.vue` - 标准样本管理器
**路径**: `/src/components/Annotation/StandardSampleManager.vue`
**功能**:
- 标准样本状态的查看和管理
- 标准样本的设置和取消
- 标注模板的管理
- 权限控制和状态显示

**主要Props**:
```typescript
interface Props {
  folderId: string        // 文件夹ID
  versionId: string       // 版本ID
}
```

**主要功能**:
- 查看当前文件夹的标准样本状态
- 设置/取消标准样本
- 管理标注模板
- 显示可标注图片统计

### 组件导出 (`index.ts`)

```typescript
// 主要组件导出
export { default as AnnotationToolbar } from './AnnotationToolbar.vue'
export { default as AnnotationList } from './AnnotationList.vue'
export { default as StandardSampleManager } from './StandardSampleManager.vue'

// 类型导出
export type { AnnotationItem, AnnotationCoordinate } from '@/api/samples/annotation'
```

### 使用示例

#### 在页面中使用标注组件
```vue
<template>
  <div class="annotation-workspace">
    <!-- 标注工具栏 -->
    <AnnotationToolbar
      :can-edit="canEdit"
      :can-view="canView"
      :reason="permissionReason"
      :annotations="annotations"
      @save="handleSaveAnnotations"
      @reset="handleResetAnnotations"
    />

    <!-- 标注列表 -->
    <AnnotationList
      :annotations="annotations"
      :can-edit="canEdit"
      @annotation-select="handleAnnotationSelect"
      @annotation-edit="handleAnnotationEdit"
      @annotation-delete="handleAnnotationDelete"
    />

    <!-- 标准样本管理 -->
    <StandardSampleManager
      :folder-id="folderId"
      :version-id="versionId"
    />
  </div>
</template>

<script setup lang="ts">
import {
  AnnotationToolbar,
  AnnotationList,
  StandardSampleManager
} from '@/components/Annotation'

// 组件逻辑...
</script>
```

### 技术特性

#### 权限控制
- 基于文件夹类型的权限判断
- 标准样本vs普通样本的权限区分
- 图片类型的可标注性判断

#### 数据管理
- 统一的标注数据格式
- 实时的数据同步
- 批量操作支持

#### 用户体验
- 直观的权限状态显示
- 友好的错误提示
- 响应式布局设计

---

## 📡 API调用模式

### 证件管理API (`@/api/cert/`)
- `type.js` - 证件类型API
- `country.js` - 国家信息API
- `version.js` - 版本管理API（旧）

### 样本管理API (`@/api/samples/`)
- `version.ts` - 版本管理API（新）
- `folder.ts` - 文件夹管理API
- `image.ts` - 图片管理API
- `annotation.ts` - 标注数据API

---

## 🎨 样式和主题

### 全局样式
- 基于Element Plus主题
- 响应式设计
- 统一的色彩规范

### 组件样式
- SCSS模块化样式
- BEM命名规范
- 组件级样式隔离

---

## 🚀 开发建议

### 1. 组件开发
- 使用Vue 3 Composition API
- 优先使用`<script setup>`语法
- 合理使用Composables抽取逻辑

### 2. 状态管理
- 局部状态使用ref/reactive
- 跨组件状态使用Pinia
- 异步状态使用loading标识

### 3. API调用
- 统一错误处理
- 使用TypeScript类型定义
- 合理使用缓存策略

### 4. 性能优化
- 图片懒加载
- 组件按需加载
- 合理使用计算属性

---

## 🔄 组件依赖关系图

### 页面级组件依赖

```
VersionManagementView.vue
├── VersionDetailModal.vue
│   └── 调用: getVersionDetails(), getFoldersByVersionId()
├── FolderManagementModal.vue
└── VersionFolderManagementModal.vue

SampleManagementView.vue
├── VersionCreatorModal.vue
│   └── CertVersionForm.vue
├── VersionSelectorModal.vue
└── ThirdPartyDetectionModal.vue

FolderDetailView.vue (核心标注页面)
├── AnnotationCanvas.vue (基于Annotorious)
├── @/components/Annotation/
│   ├── AnnotationToolbar.vue
│   ├── AnnotationList.vue
│   ├── ImageTypeAnnotator.vue
│   └── StandardSampleManager.vue
├── useImageAnnotation() composable
└── useFolderInfo() composable

BatchTaskManagement.vue
├── UppyDashboardModal.vue (基于Uppy)
├── MultiVersionMetadataModal.vue
└── FolderScanProgress.vue
```

### 标注组件库内部依赖

```
@/components/Annotation/
├── AnnotationToolbar.vue
│   ├── 依赖: @/api/samples/annotation
│   └── 事件: save, reset, annotation-type-change
├── AnnotationList.vue
│   ├── 依赖: @/api/samples/annotation
│   └── 事件: annotation-select, annotation-edit, annotation-delete
├── ImageTypeAnnotator.vue
│   ├── 依赖: @/api/samples/image
│   └── 功能: 图片类型识别和标注
├── ImageTypeAnnotatorCompact.vue
│   └── 继承: ImageTypeAnnotator.vue (紧凑版)
├── StandardSampleManager.vue
│   ├── 依赖: @/api/samples/folder, @/api/samples/version
│   └── 功能: 标准样本管理
└── index.ts (统一导出)
```

---

## 📊 数据流向

### 1. 版本管理流程
```
用户操作 → VersionManagementView → API调用 → 后端处理 → MongoDB更新 → 前端状态更新
```

### 2. 标注工作流程
```
选择图片 → FolderDetailView → 加载标注数据 → AnnotationCanvas渲染 →
用户标注 → 实时保存 → MongoDB存储 → 状态同步
```

### 3. 批量上传流程
```
选择文件夹 → UppyDashboardModal → 文件上传 → 后端处理 →
版本解析 → MultiVersionMetadataModal → 确认保存 → 数据库存储
```

---

## 🎯 关键技术点

### 1. 图片标注技术栈
- **Annotorious 3.0**: 核心标注引擎
- **W3C Web Annotation**: 标注数据格式标准
- **Canvas API**: 图片渲染和交互
- **Vue 3 Reactivity**: 状态管理和数据绑定

### 2. 文件上传技术栈
- **Uppy**: 文件上传组件库
- **Tus Protocol**: 断点续传协议
- **MinIO**: 对象存储服务
- **FormData**: 多文件上传数据格式

### 3. 状态管理模式
- **Local State**: ref/reactive for component state
- **Composables**: 逻辑复用和状态共享
- **Props/Emits**: 父子组件通信
- **Event Bus**: 跨组件事件通信

---

## 🛠️ 开发工具和配置

### 1. 开发环境
- **Vite**: 构建工具和开发服务器
- **Vue DevTools**: 调试工具
- **Element Plus**: UI组件库
- **SCSS**: CSS预处理器

### 2. 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查（部分文件）
- **Vue SFC**: 单文件组件规范

### 3. 路由配置
```javascript
// 主要路由结构
/cert/type          → 证件类型管理
/cert/country       → 国家管理
/cert/version       → 版本管理
/cert/sample        → 样本管理
/cert/batch         → 批量任务
/cert/folder/:id    → 文件夹详情（标注页面）
```

---

## 📝 后续开发重点

### 1. 功能增强
- **标注类型扩展**: 支持点标注、线标注、自由绘制
- **标注模板**: 预定义标注模板和快速标注
- **协作标注**: 多用户同时标注和冲突解决
- **标注审核**: 标注质量检查和审核流程

### 2. 性能优化
- **虚拟滚动**: 大量图片列表性能优化
- **图片预加载**: 智能预加载策略
- **缓存策略**: API响应缓存和本地存储
- **懒加载**: 组件和图片按需加载

### 3. 用户体验
- **快捷键支持**: 标注工具快捷键
- **拖拽操作**: 文件夹拖拽排序
- **批量操作**: 批量标注和批量编辑
- **进度指示**: 详细的操作进度反馈

### 4. 移动端适配
- **响应式布局**: 适配不同屏幕尺寸
- **触摸操作**: 移动端标注手势支持
- **离线功能**: 离线标注和同步
- **PWA支持**: 渐进式Web应用

---

## 🔍 调试和测试

### 1. 常见问题排查
- **图片加载失败**: 检查MinIO配置和代理设置
- **标注数据丢失**: 检查保存API和数据格式
- **组件渲染异常**: 检查props传递和响应式数据
- **路由跳转问题**: 检查路由配置和权限

### 2. 测试策略
- **单元测试**: 组件逻辑测试
- **集成测试**: API调用测试
- **E2E测试**: 完整工作流测试
- **性能测试**: 大数据量场景测试

### 3. 监控和日志
- **错误监控**: 前端错误收集和上报
- **性能监控**: 页面加载和交互性能
- **用户行为**: 标注操作统计和分析
- **API监控**: 接口调用成功率和响应时间
