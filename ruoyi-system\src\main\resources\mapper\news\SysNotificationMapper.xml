<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.SysNotificationMapper">
    
    <resultMap type="SysNotification" id="SysNotificationResult">
        <result property="notificationId"    column="notification_id"    />
        <result property="userId"    column="user_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="sourceId"    column="source_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSysNotificationVo">
        select notification_id, user_id, title, content, type, status, source_id, create_time
        from sys_notification
    </sql>

    <select id="selectSysNotificationList" parameterType="SysNotification" resultMap="SysNotificationResult">
        <include refid="selectSysNotificationVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sourceId != null "> and source_id = #{sourceId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysNotificationByNotificationId" parameterType="Long" resultMap="SysNotificationResult">
        <include refid="selectSysNotificationVo"/>
        where notification_id = #{notificationId}
    </select>
    
    <select id="selectUnreadCount" parameterType="Long" resultType="Integer">
        select count(1) from sys_notification where user_id = #{userId} and status = '0'
    </select>
        
    <insert id="insertSysNotification" parameterType="SysNotification" useGeneratedKeys="true" keyProperty="notificationId">
        insert into sys_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSysNotification" parameterType="SysNotification">
        update sys_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where notification_id = #{notificationId}
    </update>

    <delete id="deleteSysNotificationByNotificationId" parameterType="Long">
        delete from sys_notification where notification_id = #{notificationId}
    </delete>

    <delete id="deleteSysNotificationByNotificationIds" parameterType="String">
        delete from sys_notification where notification_id in 
        <foreach item="notificationId" collection="array" open="(" separator="," close=")">
            #{notificationId}
        </foreach>
    </delete>
    
    <update id="markAsRead" parameterType="Long">
        update sys_notification set status = '1' where notification_id = #{notificationId}
    </update>
    
    <update id="markAllAsRead" parameterType="Long">
        update sys_notification set status = '1' where user_id = #{userId} and status = '0'
    </update>
</mapper>
