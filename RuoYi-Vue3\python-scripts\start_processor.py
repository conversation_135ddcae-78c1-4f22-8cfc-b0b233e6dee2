#!/usr/bin/env python3
"""
批量图像处理器启动脚本
可以作为服务运行，定期检查并处理待处理的任务
"""

import time
import signal
import sys
from batch_image_processor import BatchImageProcessor
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProcessorService:
    def __init__(self):
        self.processor = BatchImageProcessor()
        self.running = True
        
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("接收到停止信号，正在关闭服务...")
        self.running = False
        
    def run(self):
        """运行服务"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        logger.info("批量图像处理服务启动")
        
        while self.running:
            try:
                # 获取待处理任务
                pending_tasks = self.processor.get_pending_tasks()
                
                if pending_tasks:
                    logger.info(f"发现 {len(pending_tasks)} 个待处理任务")
                    
                    for task in pending_tasks:
                        if not self.running:
                            break
                            
                        task_id = task['task_id']
                        logger.info(f"开始处理任务: {task_id}")
                        
                        try:
                            success = self.processor.process_batch_task(task_id)
                            if success:
                                logger.info(f"任务 {task_id} 处理完成")
                            else:
                                logger.error(f"任务 {task_id} 处理失败")
                        except Exception as e:
                            logger.error(f"处理任务 {task_id} 时发生异常: {str(e)}")
                        
                        # 任务间隔
                        time.sleep(2)
                else:
                    logger.debug("没有待处理任务，等待中...")
                
                # 检查间隔
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"服务运行异常: {str(e)}")
                time.sleep(30)  # 异常后等待30秒再继续
        
        logger.info("批量图像处理服务已停止")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 处理特定任务
        task_id = sys.argv[1]
        processor = BatchImageProcessor()
        processor.process_batch_task(task_id)
    else:
        # 运行服务
        service = ProcessorService()
        service.run()

if __name__ == '__main__':
    main() 