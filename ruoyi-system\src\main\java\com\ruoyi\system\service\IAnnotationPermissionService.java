package com.ruoyi.system.service;

import com.ruoyi.system.service.IVersionAnnotationTemplateService.AnnotationPermissionInfo;

/**
 * 标注权限控制服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IAnnotationPermissionService {
    
    /**
     * 检查图片是否可标注
     */
    boolean canAnnotateImage(String imageId);
    
    /**
     * 检查图片是否可查看标注
     */
    boolean canViewAnnotations(String imageId);
    
    /**
     * 获取图片标注权限详细信息
     */
    AnnotationPermissionInfo getAnnotationPermission(String imageId);
    
    /**
     * 检查文件夹是否可设置为标准样本
     */
    boolean canSetAsStandardSample(String folderId);
    
    /**
     * 检查文件夹是否可取消标准样本
     */
    boolean canRemoveStandardSample(String folderId);
    
    /**
     * 验证标注操作权限
     */
    boolean validateAnnotationOperation(String imageId, String operation);
    
    /**
     * 获取权限检查结果
     */
    PermissionCheckResult checkPermissions(String imageId, String userId);
    
    /**
     * 权限检查结果
     */
    class PermissionCheckResult {
        private boolean canEdit;
        private boolean canView;
        private boolean canSetStandard;
        private boolean canRemoveStandard;
        private String reason;
        private String imageType;
        private boolean isStandardSample;
        
        // 构造函数
        public PermissionCheckResult() {}
        
        public PermissionCheckResult(boolean canEdit, boolean canView, String reason) {
            this.canEdit = canEdit;
            this.canView = canView;
            this.reason = reason;
        }
        
        // getters and setters
        public boolean isCanEdit() { return canEdit; }
        public void setCanEdit(boolean canEdit) { this.canEdit = canEdit; }
        
        public boolean isCanView() { return canView; }
        public void setCanView(boolean canView) { this.canView = canView; }
        
        public boolean isCanSetStandard() { return canSetStandard; }
        public void setCanSetStandard(boolean canSetStandard) { this.canSetStandard = canSetStandard; }
        
        public boolean isCanRemoveStandard() { return canRemoveStandard; }
        public void setCanRemoveStandard(boolean canRemoveStandard) { this.canRemoveStandard = canRemoveStandard; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public String getImageType() { return imageType; }
        public void setImageType(String imageType) { this.imageType = imageType; }
        
        public boolean isStandardSample() { return isStandardSample; }
        public void setStandardSample(boolean standardSample) { isStandardSample = standardSample; }
    }
}
