# 🔧 批量任务管理版本筛选优化 - 修复报告

## 📋 问题概述

在批量任务管理页面中，点击左侧文件夹列表时，右侧版本筛选使用的是文件夹名称中的中文信息（如"美国"、"公务护照"），但应该使用预解析的版本信息中的代码（如"USA"、"13"）进行筛选。同时需要在比对区域添加审核功能。

## 🔍 问题分析

### 1. **版本筛选关键词问题**

#### **当前实现**：
```javascript
// 从文件夹名称中提取关键词
const parts = selectedFolder.value.folderName.split('_')
if (parts.length > 0) {
  keywords.push(parts[0]) // 提取国家名 - "美国"
}
if (parts.length > 1) {
  keywords.push(parts[1]) // 提取证件类型 - "公务护照"
}
```

#### **问题**：
- 使用中文名称进行搜索，如"美国"、"公务护照"
- 后端版本数据存储的是代码，如"USA"、"13"
- 导致搜索匹配不到正确的版本

### 2. **数据结构分析**

根据提供的 FolderInfo 数据结构：
```json
{
  "preParseVersionInfo": {
    "parsedVersionCode": "GBR_13_2004_XDD85",
    "countryName": "英国",
    "countryCode": "GBR",        // ← 应该使用这个
    "certTypeName": "公务普通护照",
    "certTypeCode": "13",        // ← 应该使用这个
    "issueYear": "2004",
    "certNumberPrefix": "XDD85",
    "issuePlace": "哈瓦那"
  },
  "countryInfo": {
    "code": "GBR"               // ← 对应后端存储
  },
  "certInfo": {
    "zjlbdm": "13"              // ← 对应后端存储
  }
}
```

## 🛠️ 修复方案

### 1. **优化前端关键词提取逻辑**

#### **修改 TaskWorkspaceView.vue 中的 contextKeywords 计算属性**：

```javascript
// 修改前：使用文件夹名称
const parts = selectedFolder.value.folderName.split('_')
keywords.push(parts[0]) // "美国"
keywords.push(parts[1]) // "公务护照"

// 修改后：使用预解析的代码
if (selectedFolder.value?.preParseVersionInfo) {
  const parseInfo = selectedFolder.value.preParseVersionInfo
  if (parseInfo.countryCode) {
    keywords.push(parseInfo.countryCode) // "USA", "GBR"
  }
  if (parseInfo.certTypeCode) {
    keywords.push(parseInfo.certTypeCode) // "13"
  }
}
```

### 2. **增强后端搜索逻辑**

#### **修改 CertVersionServiceImpl.java 中的关键词搜索**：

```java
// 修改前：只搜索版本代码和描述
Criteria keywordCriteria = new Criteria().orOperator(
    Criteria.where("versionCode").regex(keyword, "i"),
    Criteria.where("description").regex(keyword, "i")
);

// 修改后：支持多字段搜索
String[] keywords = keyword.trim().split("\\s+");
for (String kw : keywords) {
    Criteria singleKeywordCriteria = new Criteria().orOperator(
        Criteria.where("versionCode").regex(kw, "i"),
        Criteria.where("description").regex(kw, "i"),
        Criteria.where("countryInfo.code").regex(kw, "i"),  // 国家代码
        Criteria.where("countryInfo.name").regex(kw, "i"),  // 国家名称
        Criteria.where("certInfo.zjlbdm").regex(kw, "i"),   // 证件类型代码
        Criteria.where("certInfo.zjlbmc").regex(kw, "i")    // 证件类型名称
    );
}
```

### 3. **添加审核功能**

#### **在 CompareArea.vue 中添加审核按钮**：

```vue
<div class="footer-actions">
  <el-button
    v-if="selectedFolder && currentVersion"
    type="primary"
    @click="handleAssociate"
  >
    关联到当前版本
  </el-button>
  <el-button
    v-if="selectedFolder && selectedFolder.reviewStatus === 'pending'"
    type="success"
    @click="handleApprove"
  >
    审核通过
  </el-button>
  <el-button
    v-if="selectedFolder && selectedFolder.reviewStatus === 'pending'"
    type="danger"
    @click="handleReject"
  >
    审核驳回
  </el-button>
</div>
```

#### **添加审核处理逻辑**：

```javascript
// 审核通过
const handleApproveFolder = async (folder: FolderInfoVO) => {
  const reviewData: VersionReviewDTO = {
    reviewResult: 'approved',
    reviewComment: '审核通过'
  }
  const response = await reviewFolderVersion(folder.folderId, reviewData)
  // 处理结果...
}

// 审核驳回
const handleRejectFolder = async (folder: FolderInfoVO) => {
  const { value: comment } = await ElMessageBox.prompt('请输入驳回原因：')
  const reviewData: VersionReviewDTO = {
    reviewResult: 'rejected',
    reviewComment: comment
  }
  const response = await reviewFolderVersion(folder.folderId, reviewData)
  // 处理结果...
}
```

## ✅ 修复结果

### 1. **版本筛选准确性提升**
- ✅ **使用正确的代码进行搜索**：USA、GBR、13 等
- ✅ **支持多关键词匹配**：同时匹配国家代码和证件类型代码
- ✅ **兼容性保持**：保留文件夹名称解析作为回退方案
- ✅ **搜索范围扩大**：支持代码和名称双重匹配

### 2. **审核功能完善**
- ✅ **审核按钮显示**：只在待审核状态下显示
- ✅ **审核通过功能**：一键审核通过
- ✅ **审核驳回功能**：支持输入驳回原因
- ✅ **状态同步**：审核后自动刷新数据

### 3. **用户体验优化**
- ✅ **搜索精度提升**：准确匹配相关版本
- ✅ **操作便捷性**：在比对界面直接完成审核
- ✅ **反馈及时性**：操作后立即显示结果

## 🎯 技术实现亮点

### 1. **智能关键词提取**
```javascript
// 优先使用预解析信息，回退到文件夹名称
if (folder.preParseVersionInfo) {
  keywords.push(parseInfo.countryCode)  // 精确代码
  keywords.push(parseInfo.certTypeCode) // 精确代码
} else {
  // 回退方案：解析文件夹名称
  const parts = folder.folderName.split('_')
  keywords.push(parts[0], parts[1])
}
```

### 2. **多维度搜索支持**
```java
// 支持代码和名称的双重匹配
Criteria.where("countryInfo.code").regex(kw, "i"),    // USA
Criteria.where("countryInfo.name").regex(kw, "i"),    // 美国
Criteria.where("certInfo.zjlbdm").regex(kw, "i"),     // 13
Criteria.where("certInfo.zjlbmc").regex(kw, "i")      // 公务普通护照
```

### 3. **审核流程集成**
```javascript
// 在比对界面直接完成审核，无需跳转
const handleApprove = () => emit('approve-folder', selectedFolder)
const handleReject = () => emit('reject-folder', selectedFolder)
```

## 📊 数据流程优化

### **修改前的流程**：
```
文件夹选择 → 解析文件夹名称 → 提取中文关键词 → 搜索版本 → 匹配失败
```

### **修改后的流程**：
```
文件夹选择 → 读取预解析信息 → 提取代码关键词 → 多维度搜索 → 精确匹配
```

## 🎉 总结

本次修复成功解决了批量任务管理中的关键问题：

1. **版本筛选精度问题**：从使用中文名称改为使用标准代码
2. **搜索匹配问题**：增强后端搜索逻辑，支持多字段匹配
3. **审核功能缺失**：在比对界面添加完整的审核功能

修复后的系统能够：
- 准确筛选相关版本（如 USA + 13 匹配美国公务护照版本）
- 在比对界面直接完成审核操作
- 提供更好的用户体验和操作效率

所有修改都保持了向后兼容性，确保系统稳定运行。
