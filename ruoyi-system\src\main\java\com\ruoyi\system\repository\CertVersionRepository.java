package com.ruoyi.system.repository;

import com.ruoyi.system.domain.mongo.CertVersion;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 证件版本数据访问层
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface CertVersionRepository extends MongoRepository<CertVersion, String> {

    /**
     * 根据版本ID查询证件版本
     *
     * @param versionId 版本ID
     * @return 证件版本
     */
    CertVersion findByVersionId(String versionId);

    /**
     * 根据版本代码查询证件版本
     *
     * @param versionCode 版本代码
     * @return 证件版本
     */
    CertVersion findByVersionCode(String versionCode);

    /**
     * 根据国家信息查询证件版本列表
     *
     * @param countryId 国家ID
     * @return 证件版本列表
     */
    @Query("{'countryInfo.id': ?0}")
    List<CertVersion> findByCountryId(Long countryId);

    /**
     * 根据证件类型查询证件版本列表
     *
     * @param certTypeId 证件类型ID
     * @return 证件版本列表
     */
    @Query("{'certInfo.id': ?0}")
    List<CertVersion> findByCertTypeId(Long certTypeId);

    /**
     * 根据发行年份查询证件版本列表
     *
     * @param issueYear 发行年份
     * @return 证件版本列表
     */
    List<CertVersion> findByIssueYear(String issueYear);

    /**
     * 根据状态查询证件版本列表
     *
     * @param status 状态
     * @return 证件版本列表
     */
    List<CertVersion> findByStatus(String status);

    /**
     * 根据部门查询证件版本列表
     *
     * @param deptId 部门ID
     * @return 证件版本列表
     */
    @Query("{'deptInfo.deptId': ?0}")
    List<CertVersion> findByDeptId(Long deptId);

    /**
     * 根据创建者查询证件版本列表
     *
     * @param userId 用户ID
     * @return 证件版本列表
     */
    @Query("{'creatorInfo.userId': ?0}")
    List<CertVersion> findByCreatorId(Long userId);

    /**
     * 根据国家和证件类型查询证件版本列表
     *
     * @param countryId 国家ID
     * @param certTypeId 证件类型ID
     * @return 证件版本列表
     */
    @Query("{'countryInfo.id': ?0, 'certInfo.id': ?1}")
    List<CertVersion> findByCountryIdAndCertTypeId(Long countryId, Long certTypeId);

    /**
     * 根据国家、证件类型和发行年份查询证件版本列表
     *
     * @param countryId 国家ID
     * @param certTypeId 证件类型ID
     * @param issueYear 发行年份
     * @return 证件版本列表
     */
    @Query("{'countryInfo.id': ?0, 'certInfo.id': ?1, 'issueYear': ?2}")
    List<CertVersion> findByCountryIdAndCertTypeIdAndIssueYear(Long countryId, Long certTypeId, String issueYear);
}