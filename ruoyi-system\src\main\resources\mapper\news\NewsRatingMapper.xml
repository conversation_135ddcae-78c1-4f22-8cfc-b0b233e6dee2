<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.NewsRatingMapper">
    
    <resultMap type="NewsRating" id="NewsRatingResult">
        <result property="ratingId"    column="rating_id"    />
        <result property="newsId"    column="news_id"    />
        <result property="userId"    column="user_id"    />
        <result property="score"    column="score"    />
        <result property="createdAt"    column="created_at"    />
        <result property="newsTitle" column="news_title" />
        <result property="userName" column="user_name" />
    </resultMap>

    <sql id="selectNewsRatingVo">
        select 
            nr.rating_id, 
            nr.news_id, 
            nr.user_id, 
            nr.score, 
            nr.created_at,
            n.news_title,
            u.user_name
        from 
            news_rating nr
        left join 
            sys_news n on n.news_id = nr.news_id
        left join 
            sys_user u on u.user_id = nr.user_id
    </sql>

    <select id="selectNewsRatingList" parameterType="NewsRating" resultMap="NewsRatingResult">
        <include refid="selectNewsRatingVo"/>
        <where>  
            <if test="newsId != null "> and news_id = #{newsId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectNewsRatingByRatingId" parameterType="Long" resultMap="NewsRatingResult">
        <include refid="selectNewsRatingVo"/>
        where rating_id = #{ratingId}
    </select>

    <insert id="insertNewsRating" parameterType="NewsRating">
        insert into news_rating
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="newsId != null">news_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="score != null">score,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="newsId != null">#{newsId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="score != null">#{score},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateNewsRating" parameterType="NewsRating">
        update news_rating
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsId != null">news_id = #{newsId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where rating_id = #{ratingId}
    </update>

    <delete id="deleteNewsRatingByRatingId" parameterType="Long">
        delete from news_rating where rating_id = #{ratingId}
    </delete>

    <delete id="deleteNewsRatingByRatingIds" parameterType="String">
        delete from news_rating where rating_id in 
        <foreach item="ratingId" collection="array" open="(" separator="," close=")">
            #{ratingId}
        </foreach>
    </delete>

    <!-- 查询文章平均评分 -->
    <select id="selectAvgRatingByNewsId" parameterType="Long" resultType="Double">
        select AVG(score) from news_rating where news_id = #{newsId}
    </select>
    
    <!-- 查询文章评分人数 -->
    <select id="selectRatingCountByNewsId" parameterType="Long" resultType="Integer">
        select COUNT(1) from news_rating where news_id = #{newsId}
    </select>
    
    <!-- 查询用户对文章的评分 -->
    <select id="selectRatingByNewsIdAndUserId" resultMap="NewsRatingResult">
        select * from news_rating
        where news_id = #{newsId} and user_id = #{userId}
    </select>
</mapper>