package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.dto.request.VersionQueryDTO;
import com.ruoyi.system.domain.mongo.CertVersion;
import com.ruoyi.system.domain.dto.request.CertVersionCreateDTO;
import com.ruoyi.system.domain.dto.request.VersionCreateFromFolderDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 证件版本表Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ICertVersionService {
    
    /**
     * 查询证件版本列表
     * 
     * @param countryId 国家ID
     * @param certType 证件类型
     * @return 证件版本集合
     */
    public List<CertVersion> listVersions(Integer countryId, String certType);
    
    /**
     * greenfox - 新增：带过滤条件的版本查询 - 2025-01-15
     * 
     * @param countryId 国家ID
     * @param certType 证件类型
     * @param versionStatus 版本状态
     * @param needManualCheck 是否需要人工确认
     * @return 证件版本集合
     */
    public List<CertVersion> listVersionsWithFilter(Integer countryId, String certType, String versionStatus, Boolean needManualCheck);

    /**
     * greenfox - 新增：带过滤条件的分页版本查询 - 2025-01-15
     * 
     * @param countryId 国家ID
     * @param certType 证件类型
     * @param versionStatus 版本状态
     * @param needManualCheck 是否需要人工确认
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果（包含rows和total）
     */
    public Map<String, Object> listVersionsWithFilterPaged(Integer countryId, String certType, String versionStatus, Boolean needManualCheck, Integer pageNum, Integer pageSize);
    
    /**
     * 获取证件版本详情
     * 
     * @param id 证件版本ID
     * @return 证件版本
     */
    CertVersion getVersion(String id);
    
    /**
     * 根据版本ID获取证件版本
     *
     * @param versionId 版本ID
     * @return 证件版本
     */
    CertVersion getVersionByVersionId(String versionId);

    /**
     * 根据版本号查找版本
     *
     * @param versionCode 版本号
     * @return 证件版本，如果不存在则返回null
     */
    CertVersion findByVersionCode(String versionCode);
    
    /**
     * 新增证件版本
     * 
     * @param version 证件版本
     * @return 新增后的证件版本
     */
    CertVersion addVersion(CertVersion version);
    
    /**
     * 修改证件版本
     * 
     * @param version 证件版本
     * @return 修改后的证件版本
     */
    CertVersion updateVersion(CertVersion version);
    
    /**
     * 删除证件版本
     * 
     * @param id 证件版本ID
     */
    void deleteVersion(String id);
    
    /**
     * 更新证件版本扩展属性
     * 
     * @param id 证件版本ID
     * @param extraAttributes 扩展属性
     * @return 更新后的证件版本
     */
    CertVersion updateExtraAttributes(String id, Map<String, Object> extraAttributes);

    /**
     * 获取指定国家的所有版本信息
     * 
     * @param countryId 国家ID
     * @return 版本信息列表
     */
    List<Map<String, Object>> getVersionsWithSamples(Integer countryId);

    /**
     * 获取所有已有样本的版本信息
     * 
     * @return 版本信息列表
     */
    List<Map<String, Object>> getAllVersionsWithSamples();

    /**
     * 获取指定国家已有样本的证件类型
     * 
     * @param countryId 国家ID
     * @return 证件类型列表，每个元素包含 zjlbdm 和 zjlbmc
     */
    List<Map<String, Object>> getCertTypesWithSamples(Integer countryId);

    /**
     * 获取所有已有样本的证件类型
     * 
     * @return 证件类型列表，每个元素包含 zjlbdm 和 zjlbmc
     */
    List<Map<String, Object>> getAllCertTypesWithSamples();

    /**
     * 批量添加证件版本
     * 
     * @param versions 证件版本列表
     * @return 添加结果
     */
    List<CertVersion> batchAddVersions(List<CertVersion> versions);

    /**
     * 从文件夹名解析并创建证件版本
     * 
     * @param folderNames 文件夹名列表
     * @return 处理结果
     */
    Map<String, Object> createVersionsFromFolders(List<String> folderNames);

    /**
     * 创建证件版本并上传图片到 MinIO
     * 
     * @param folderName 文件夹名称
     * @param imageFile 图片文件（可选）
     * @param localImagePath 本地图片路径（可选）
     * @return 创建的证件版本
     */
    CertVersion createVersionWithImageUpload(String folderName, MultipartFile imageFile, String localImagePath) throws Exception;

    /**
     * 根据文件夹名称查找或创建version记录
     * 
     * @param folderName 文件夹名称（例如：2021年胡志明市签发的加拿大签证）
     * @return version记录，如果不存在则自动创建
     */
    CertVersion findOrCreateVersionByFolderName(String folderName);
    
    /**
     * 解析文件夹名称，提取版本信息
     * 
     * @param folderName 文件夹名称
     * @return 解析后的版本信息Map
     */
    Map<String, String> parseFolderNameForVersion(String folderName);
    
    /**
     * 根据国家中文名称获取国家代码
     * 
     * @param countryName 国家中文名称
     * @return 三位国家代码
     */
    String getCountryCodeByName(String countryName);
    
    /**
     * 根据证件类型中文名称获取英文类型
     * 
     * @param categoryName 证件类型中文名称（如：签证、护照）
     * @return 英文证件类型（如：Visa、Passport）
     */
    String getCertTypeByCategory(String categoryName);
    
    /**
     * greenfox - 新增：统一Version创建方法 - 2025-01-15
     * 支持标准样本和批量上传两种模式，确保数据结构一致
     * 
     * @param request 统一创建请求参数
     * @return 创建的Version记录
     */
    CertVersion createUnifiedVersion(CertVersionCreateDTO request);
    
    /**
     * greenfox - 新增：数据结构统一迁移方法 - 2025-01-15
     * 将现有Version记录统一为标准格式
     * 
     * @return 迁移结果统计
     */
    Map<String, Object> unifyVersionDataStructure();

    /**
     * greenfox - 新增：获取指定国家的版本信息（支持强制刷新） - 2025-01-15
     * 
     * @param countryId 国家ID
     * @param forceRefresh 是否强制刷新数据
     * @return 版本信息列表
     */
    List<Map<String, Object>> getVersionsWithSamplesRefreshed(Integer countryId, boolean forceRefresh);

    /**
     * greenfox - 新增：分页获取指定国家的版本信息 - 2025-01-15
     * 
     * @param countryId 国家ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param certType 证件类型筛选（可选）
     * @param versionStatus 版本状态筛选（可选）
     * @param keyword 关键词搜索（可选）
     * @return 分页结果
     */
    Map<String, Object> getVersionsWithSamplesPaged(Integer countryId, int pageNum, int pageSize, 
                                                   String certType, String versionStatus, String keyword);
    
    /**
     * greenfox - 新增：查找相似版本 - 2025-01-15
     * 根据证件类型、发行年份、国家代码查找已存在的相似版本
     * 
     * @param certType 证件类型
     * @param issueYear 发行年份
     * @param countryCode 国家代码
     * @return 相似版本列表
     */
    List<CertVersion> findSimilarVersions(String certType, String issueYear, String countryCode);
    
    /**
     * greenfox - 新增：智能版本匹配 - 2025-01-15
     * 更智能的版本匹配，支持模糊匹配和兼容性检查
     * 
     * @param folderName 文件夹名称
     * @param certType 证件类型
     * @param issueYear 发行年份
     * @param countryCode 国家代码
     * @return 最佳匹配的版本，如果没有匹配则返回null
     */
    CertVersion findBestMatchingVersion(String folderName, String certType, String issueYear, String countryCode);
    
    /**
     * 从文件夹创建新版本
     * 当用户认为一个"待处理"文件夹本身就代表一个新版本时，调用此方法来创建新版本并自动完成关联
     *
     * @param dto 创建请求DTO
     * @return 创建的版本
     */
    CertVersion createVersionFromFolder(VersionCreateFromFolderDTO dto);

    /**
     * 管理文件夹类型 (设为标准/普通)
     * 在一个版本被创建和关联完成后，提供后续管理功能，允许用户在版本内指定或更改哪个文件夹是"标准样本"
     *
     * @param versionId 版本ID
     * @param folderId 文件夹ID
     * @param newType 新类型 ("standard" 或 "regular")
     */
    void updateFolderType(String versionId, String folderId, String newType);

    /**
     * 获取版本下的文件夹列表
     *
     * @param versionId 版本ID
     * @return 文件夹列表
     */
    List<Map<String, Object>> getVersionFolders(String versionId);

    /**
     * 设置标准样本文件夹
     *
     * @param versionId 版本ID
     * @param folderId 文件夹ID
     */
    void setStandardFolder(String versionId, String folderId);

    /**
     * 取消标准样本文件夹
     *
     * @param versionId 版本ID
     */
    void removeStandardFolder(String versionId);

    /**
     * 查询版本列表
     *
     * @param query 查询条件
     * @return 版本列表
     */
    List<CertVersion> listVersions(VersionQueryDTO query);

    /**
     * 获取版本详情
     *
     * @param versionId 版本ID
     * @return 版本详情
     */
    CertVersion getVersionDetails(String versionId);

    /**
     * 修复标准文件夹数据
     * 确保文件夹类型和图片样本类型的一致性
     *
     * @param versionId 版本ID
     */
    void fixStandardFolderData(String versionId);

    /**
     * 根据任务ID获取版本列表
     * 通过任务ID查找相关的文件夹，然后获取这些文件夹关联的版本
     *
     * @param taskId 任务ID
     * @return 版本列表
     */
    List<CertVersion> getVersionsByTaskId(String taskId);
}
