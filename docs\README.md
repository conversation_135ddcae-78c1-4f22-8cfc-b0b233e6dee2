# 证件样本管理系统文档中心

## 📋 文档概览

本目录包含证件样本管理系统的完整技术文档，记录了系统架构、API设计、前后端实现状态。

## 🎯 2025年7月9日 - 系统当前状态

### 📚 核心架构文档

| 文档名称 | 描述 | 状态 |
|---------|------|------|
| [🏗️ 系统架构说明](./cert-sample-management-system-architecture.md) | 完整的系统架构、技术栈和核心设计说明 | ✅ 最新 |
| [📚 后端文件指南](./backend-cert-management-files-guide.md) | 后端所有文件的功能说明和依赖关系 | ✅ 最新 |
| [🎨 前端组件指南](./frontend-vue-components-guide.md) | 前端Vue组件结构和使用说明 | ✅ 最新 |
| [📖 项目概览](./project-overview.md) | 项目整体介绍和技术特性 | ✅ 最新 |

### 🔧 实施指南文档

| 文档名称 | 描述 | 状态 |
|---------|------|------|
| [🚀 部署检查清单](./deployment-checklist.md) | 系统部署和配置检查清单 | ✅ 最新 |
| [📖 API统一指南](./api-unification-guide.md) | API路径统一和迁移指南 | ✅ 参考 |
| [🔄 统一API架构](./unified-api-architecture.md) | 统一API架构设计说明 | ✅ 参考 |

## 🚀 系统功能特性

### ✅ 已实现核心功能

- **批量任务管理** - 支持多文件夹批量上传任务，统一API接口
- **文件夹解析** - 自动解析 `国家_证件类型_年份_编号_地区` 格式
- **样本版本管理** - 证件版本的创建、关联和管理
- **图片标注系统** - 基于Annotorious的图片标注功能（核心功能）
- **文件上传** - 基于Tus协议的断点续传文件上传
- **权限控制** - 基于若依框架的权限管理
- **数据存储** - MongoDB业务数据 + MySQL系统数据

### 🔧 技术特性

- **前后端分离** - Vue 3 + Spring Boot 3架构
- **类型安全** - 完整的TypeScript类型支持
- **对象存储** - MinIO文件存储服务
- **标注引擎** - Annotorious 3.5.1图片标注库
- **数据库** - MongoDB + MySQL双数据库架构
- **组件化开发** - Vue 3 Composition API + Element Plus

## 📊 技术栈

### 前端技术
- **Vue 3** - 现代化前端框架，使用Composition API
- **TypeScript** - 完整的类型安全支持
- **Element Plus** - 企业级UI组件库
- **Uppy + Tus** - 文件上传和断点续传
- **Annotorious** - 图片标注库

### 后端技术
- **Spring Boot 3** - 现代化Java后端框架
- **Java 17** - 最新LTS版本
- **Spring Data MongoDB** - MongoDB数据访问
- **Spring Security** - 安全认证和授权
- **MinIO** - 对象存储服务

### 数据存储
- **MongoDB** - 业务数据存储（任务、版本、标注等）
- **MySQL** - 系统数据存储（用户、权限、字典等）
- **MinIO** - 文件对象存储

## 🔍 快速导航

### 📚 开发者指南
- [🏗️ 系统架构说明](./cert-sample-management-system-architecture.md) - 了解系统整体架构和核心设计
- [📚 后端文件指南](./backend-cert-management-files-guide.md) - 后端代码结构和文件说明
- [🎨 前端组件指南](./frontend-vue-components-guide.md) - 前端Vue组件使用指南
- [📖 项目概览](./project-overview.md) - 项目整体介绍和技术特性

### 🔧 实施指南
- [🚀 部署检查清单](./deployment-checklist.md) - 系统部署和配置指南
- [📖 API统一指南](./api-unification-guide.md) - API设计规范和迁移指南
- [🔄 统一API架构](./unified-api-architecture.md) - API架构设计说明

### 📁 文档管理
- [📁 归档文档](./archive/) - 已完成的重构记录和历史文档
- [📝 文档清理记录](./documentation-cleanup-2025-07-09.md) - 2025-07-09文档清理总结

## 📈 当前系统状态

### ✅ 已完成功能
- **批量文件上传** - 基于Tus协议的断点续传上传
- **文件夹管理** - 文件夹信息解析和版本关联
- **图片标注** - 基于Annotorious的图片标注功能（核心功能）
- **版本管理** - 证件版本的基础管理功能
- **权限控制** - 基于若依框架的用户权限管理

### 🔄 开发中功能
- **标注显示优化** - 解决标注矩形显示问题
- **版本管理完善** - 标准样本设置和管理
- **权限控制细化** - 基于文件夹类型的权限控制

## 🛠️ 开发环境

### 前端开发
```bash
cd RuoYi-Vue3
npm install
npm run dev
```

### 后端开发
- 使用IntelliJ IDEA打开项目
- 启动 `RuoYiApplication.java`
- 确保MongoDB和MySQL服务运行

### 数据库配置
- **MongoDB**: 业务数据存储（任务、版本、图片、标注）
- **MySQL**: 系统数据存储（用户、权限、字典）
- **MinIO**: 文件对象存储

## 📞 技术支持

如有技术问题，请参考相关文档或联系开发团队。

---

**文档维护**: 开发团队
**最后更新**: 2025-07-09
**系统版本**: Vue 3 + Spring Boot 3 + MongoDB
