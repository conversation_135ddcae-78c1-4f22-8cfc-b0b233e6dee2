package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

/**
 * 地名翻译服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IPlaceNameTranslationService {

    /**
     * 将中文地名翻译为英文
     * 
     * @param chinesePlaceName 中文地名
     * @return 英文地名，如果翻译失败返回原名称
     */
    String translateToEnglish(String chinesePlaceName);

    /**
     * 批量翻译地名
     * 
     * @param chinesePlaceNames 中文地名列表
     * @return 翻译结果映射
     */
    Map<String, String> batchTranslate(List<String> chinesePlaceNames);

    /**
     * 添加地名映射
     * 
     * @param chineseName 中文名称
     * @param englishName 英文名称
     * @param placeType 地名类型
     * @param countryCode 国家代码
     * @return 是否添加成功
     */
    boolean addPlaceNameMapping(String chineseName, String englishName, String placeType, String countryCode);

    /**
     * 删除地名映射
     * 
     * @param chineseName 中文名称
     * @return 是否删除成功
     */
    boolean removePlaceNameMapping(String chineseName);

    /**
     * 获取所有地名映射
     * 
     * @return 地名映射列表
     */
    Map<String, String> getAllPlaceNameMappings();

    /**
     * 重新加载配置
     */
    void reloadConfiguration();

    /**
     * 清除缓存
     */
    void clearCache();

    /**
     * 获取翻译统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getTranslationStatistics();
}
