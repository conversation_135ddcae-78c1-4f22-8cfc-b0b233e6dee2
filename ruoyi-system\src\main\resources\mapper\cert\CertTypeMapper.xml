<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CertTypeMapper">

    <resultMap type="CertType" id="CertTypeResult">
        <result property="id"       column="id"       />
        <result property="zjlbdm"   column="zjlbdm"   />
        <result property="zjlbmc"   column="zjlbmc"   />
        <result property="sybj"     column="sybj"     />
        <result property="zjjc"     column="zjjc"     />
        <result property="gjsy"     column="gjsy"     />
        <result property="gjsy0"    column="gjsy0"    />
        <result property="bz"       column="bz"       />
        <result property="zyType"   column="zy_type"  />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectCertTypeVo">
        select id, zjlbdm, zjlbmc, sybj, zjjc, gjsy, gjsy0, bz, zy_type, create_time, update_time from cert_type
    </sql>

    <select id="selectCertTypeList" parameterType="CertType" resultMap="CertTypeResult">
        <include refid="selectCertTypeVo"/>
        <where>
            <if test="zjlbdm != null  and zjlbdm != ''"> and zjlbdm = #{zjlbdm}</if>
            <if test="zjlbmc != null  and zjlbmc != ''"> and zjlbmc like concat('%', #{zjlbmc}, '%')</if>
            <if test="sybj != null  and sybj != ''"> and sybj = #{sybj}</if>
            <if test="zjjc != null  and zjjc != ''"> and zjjc like concat('%', #{zjjc}, '%')</if>
            <if test="gjsy != null  and gjsy != ''"> and gjsy = #{gjsy}</if>
            <if test="gjsy0 != null  and gjsy0 != ''"> and gjsy0 = #{gjsy0}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
        </where>
    </select>

    <select id="selectCertTypeById" parameterType="Long" resultMap="CertTypeResult">
        <include refid="selectCertTypeVo"/>
        where id = #{id}
    </select>

    <select id="selectCertTypeByCode" parameterType="String" resultMap="CertTypeResult">
        <include refid="selectCertTypeVo"/>
        where zjlbdm = #{zjlbdm}
    </select>

    <insert id="insertCertType" parameterType="CertType" useGeneratedKeys="true" keyProperty="id">
        insert into cert_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zjlbdm != null">zjlbdm,</if>
            <if test="zjlbmc != null">zjlbmc,</if>
            <if test="sybj != null">sybj,</if>
            <if test="zjjc != null">zjjc,</if>
            <if test="gjsy != null">gjsy,</if>
            <if test="gjsy0 != null">gjsy0,</if>
            <if test="bz != null">bz,</if>
            <if test="zyType != null">zy_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zjlbdm != null">#{zjlbdm},</if>
            <if test="zjlbmc != null">#{zjlbmc},</if>
            <if test="sybj != null">#{sybj},</if>
            <if test="zjjc != null">#{zjjc},</if>
            <if test="gjsy != null">#{gjsy},</if>
            <if test="gjsy0 != null">#{gjsy0},</if>
            <if test="bz != null">#{bz},</if>
            <if test="zyType != null">#{zyType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCertType" parameterType="CertType">
        update cert_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="zjlbdm != null">zjlbdm = #{zjlbdm},</if>
            <if test="zjlbmc != null">zjlbmc = #{zjlbmc},</if>
            <if test="sybj != null">sybj = #{sybj},</if>
            <if test="zjjc != null">zjjc = #{zjjc},</if>
            <if test="gjsy != null">gjsy = #{gjsy},</if>
            <if test="gjsy0 != null">gjsy0 = #{gjsy0},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="zyType != null">zy_type = #{zyType},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCertTypeById" parameterType="Long">
        delete from cert_type where id = #{id}
    </delete>

    <delete id="deleteCertTypeByIds" parameterType="String">
        delete from cert_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>