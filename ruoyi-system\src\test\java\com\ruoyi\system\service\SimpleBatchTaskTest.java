package com.ruoyi.system.service;

import com.ruoyi.system.domain.dto.request.SimpleBatchTaskCreateDTO;
import com.ruoyi.system.domain.mongo.BatchUploadTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化批量任务测试类
 * 
 * 测试简化后的批量上传任务创建和管理功能
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@SpringBootTest
@ActiveProfiles("test")
public class SimpleBatchTaskTest {

    @Autowired
    private IBatchTaskService batchTaskService;

    /**
     * 测试简化任务创建
     */
    @Test
    public void testCreateSimpleBatchTask() {
        // 准备测试数据
        SimpleBatchTaskCreateDTO dto = new SimpleBatchTaskCreateDTO();
        dto.setTaskName("测试简化任务_" + System.currentTimeMillis());
        dto.setDescription("简化批量上传测试");
        dto.setDeptId(100L);
        dto.setCreatedBy("testUser");
        dto.setTotalFiles(10);
        dto.setTotalFolders(2);

        // 验证DTO有效性
        assertTrue(dto.isValid());

        // 执行测试
        try {
            BatchUploadTask task = batchTaskService.createBatchTask(dto);

            // 验证结果
            assertNotNull(task);
            assertNotNull(task.getTaskId());
            assertEquals(dto.getTaskName(), task.getTaskName());
            assertEquals("UPLOADING", task.getStatus());
            assertEquals(10, task.getTotalFiles());
            assertEquals(2, task.getTotalFolders());
            assertEquals(0, task.getProcessedFiles());
            assertEquals(0, task.getProcessedFolders());

            System.out.println("简化任务创建测试通过！");
            System.out.println("任务ID: " + task.getTaskId());
            System.out.println("任务名称: " + task.getTaskName());

        } catch (Exception e) {
            fail("简化任务创建失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件夹信息创建
     */
    @Test
    public void testCreateFolderInfo() {
        try {
            // 先创建一个任务
            SimpleBatchTaskCreateDTO taskDto = new SimpleBatchTaskCreateDTO();
            taskDto.setTaskName("测试文件夹信息_" + System.currentTimeMillis());
            taskDto.setDescription("文件夹信息测试");
            taskDto.setDeptId(100L);
            taskDto.setCreatedBy("testUser");
            taskDto.setTotalFiles(5);
            taskDto.setTotalFolders(1);

            BatchUploadTask task = batchTaskService.createBatchTask(taskDto);
            assertNotNull(task);

            // 创建文件夹信息
            String folderId = batchTaskService.createFolderInfo(
                task.getTaskId(),
                "测试文件夹",
                "/test/folder/path",
                1L, // 国家ID
                1L, // 证件类型ID
                "2023",
                "北京",
                5
            );

            // 验证结果
            assertNotNull(folderId);
            assertFalse(folderId.trim().isEmpty());

            System.out.println("文件夹信息创建测试通过！");
            System.out.println("文件夹ID: " + folderId);

        } catch (Exception e) {
            fail("文件夹信息创建失败: " + e.getMessage());
        }
    }

    /**
     * 测试DTO数据验证
     */
    @Test
    public void testDTOValidation() {
        SimpleBatchTaskCreateDTO dto = new SimpleBatchTaskCreateDTO();
        
        // 测试空数据
        assertFalse(dto.isValid());
        
        // 测试部分数据
        dto.setCreatedBy("testUser");
        assertFalse(dto.isValid()); // 缺少deptId
        
        // 测试有效的DTO
        dto.setDeptId(100L);
        assertTrue(dto.isValid());
        
        // 测试边界条件
        dto.setCreatedBy("   "); // 空白字符串
        assertFalse(dto.isValid());
        
        dto.setCreatedBy("validUser");
        assertTrue(dto.isValid());

        System.out.println("DTO验证测试通过！");
    }
}
