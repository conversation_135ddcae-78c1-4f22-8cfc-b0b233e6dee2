package com.ruoyi.system.domain.dto.request;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * 文件夹信息更新DTO
 * 用于更新样本文件夹的各种属性
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class FolderUpdateDTO {
    
    /** 文件夹名称 */
    @NotBlank(message = "文件夹名称不能为空")
    private String folderName;
    
    /** 文件夹类型 (standard/regular) */
    @NotBlank(message = "文件夹类型不能为空")
    private String folderType;
    
    /** 状态 (unassociated/associated/processing) */
    @NotBlank(message = "状态不能为空")
    private String status;
    
    /** 审核状态 (pending/approved/rejected) */
    private String reviewStatus;
    
    /** 版本关联方式 (auto_detect/manual_select/third_party) */
    private String versionAssociationMethod;
    
    /** 国家ID */
    @NotNull(message = "国家ID不能为空")
    private Long countryId;
    
    /** 证件类型ID */
    @NotNull(message = "证件类型ID不能为空")
    private Long certTypeId;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 版本ID */
    private String versionId;
    
    /** 文件总数 */
    @Min(value = 0, message = "文件总数不能小于0")
    private Integer fileCount;
    
    /** 已处理文件数 */
    @Min(value = 0, message = "已处理文件数不能小于0")
    private Integer processedFileCount;
    
    /** 审核意见 */
    private String reviewComments;
    
    /** 国家信息 (前端传递，用于构建完整的国家对象) */
    private CountryInfo countryInfo;
    
    /** 证件类型信息 (前端传递，用于构建完整的证件类型对象) */
    private CertInfo certInfo;
    
    /**
     * 国家信息内嵌类
     */
    @Data
    public static class CountryInfo {
        private Long id;
        private String name;
        private String nameEn;
        private String code;
    }
    
    /**
     * 证件类型信息内嵌类
     */
    @Data
    public static class CertInfo {
        private Long id;
        private String zjlbdm;
        private String zjlbmc;
    }
}
