import pymongo
import json
from datetime import datetime

def export_cert_types():
    """简单的证件类型导出脚本"""

    # MongoDB连接配置 - 请根据你的实际情况修改
    MONGO_URI = "****************************************"
    DATABASE_NAME = "docu_research"
    COLLECTION_NAME = "document_version"

    try:
        # 连接MongoDB
        client = pymongo.MongoClient(MONGO_URI)
        db = client[DATABASE_NAME]
        collection = db[COLLECTION_NAME]

        print(f"连接到MongoDB: {MONGO_URI}/{DATABASE_NAME}")

        # 查询不同的证件类型及其数量
        pipeline = [
            {
                "$group": {
                    "_id": "$certInfo.type",
                    "count": {"$sum": 1}
                }
            },
            {
                "$sort": {"count": -1}
            }
        ]

        results = list(collection.aggregate(pipeline))

        # 处理结果
        cert_types = {}
        distinct_types = []

        for result in results:
            cert_type = result["_id"]
            count = result["count"]
            if cert_type:  # 过滤空值
                cert_types[cert_type] = count
                distinct_types.append(cert_type)

        # 打印结果
        print(f"\n找到 {len(distinct_types)} 种不同的证件类型:")
        for i, (cert_type, count) in enumerate(cert_types.items(), 1):
            print(f"{i:2d}. {cert_type}: {count} 条记录")

        # 导出到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 导出统计信息到JSON
        with open(f"cert_types_stats_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(cert_types, f, ensure_ascii=False, indent=2)

        # 导出证件类型列表到文本文件
        with open(f"cert_types_list_{timestamp}.txt", 'w', encoding='utf-8') as f:
            f.write("证件类型列表:\n")
            f.write("=" * 30 + "\n")
            for i, cert_type in enumerate(distinct_types, 1):
                f.write(f"{i}. {cert_type}\n")
            f.write(f"\n总计: {len(distinct_types)} 种证件类型")

        print(f"\n结果已导出到:")
        print(f"- cert_types_stats_{timestamp}.json (统计信息)")
        print(f"- cert_types_list_{timestamp}.txt (类型列表)")

        # 关闭连接
        client.close()

    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    export_cert_types()
