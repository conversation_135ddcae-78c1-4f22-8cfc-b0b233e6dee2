import request from '@/utils/request'
import type { Country } from '@/types/common'

// ==================== TypeScript 接口定义 ====================

/** 国家查询请求DTO */
export interface CountryQueryDTO {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 国家代码 */
  code?: string
  /** 中文名称 */
  name?: string
  /** 英文名称 */
  nameEn?: string
  /** 状态 */
  status?: string
}

/** 国家创建/更新请求DTO */
export interface CountryCreateUpdateDTO {
  /** 国家代码 */
  code: string
  /** 中文名称 */
  name: string
  /** 英文名称 */
  nameEn: string
  /** 国旗图标路径 */
  flagIcon?: string
  /** 状态 */
  status?: string
  /** 排序 */
  orderNum?: number
}

/** 首字母统计响应VO */
export interface LetterCountVO {
  /** 首字母 */
  letter: string
  /** 国家数量 */
  count: number
}

// ==================== API 函数 ====================

/**
 * 查询国家列表（分页）
 * GET /cert/country/list
 */
export function getCountryList(params?: CountryQueryDTO) {
  return request({
    url: '/cert/country/list',
    method: 'get',
    params
  })
}

/**
 * 查询国家列表（不分页）
 * GET /cert/country/listAll
 */
export function getAllCountries(params?: CountryQueryDTO) {
  return request({
    url: '/cert/country/listAll',
    method: 'get',
    params
  })
}

/**
 * 搜索国家（支持中英文名称模糊查询）
 * GET /cert/country/search
 */
export function searchCountries(keyword: string) {
  return request({
    url: '/cert/country/search',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 根据首字母查询国家列表
 * GET /cert/country/listByLetter/{letter}
 */
export function listCountryByLetter(letter: string) {
  return request({
    url: `/cert/country/listByLetter/${letter}`,
    method: 'get'
  })
}

/**
 * 获取所有首字母及对应的国家数量
 * GET /cert/country/letters
 */
export function getCountryLetters() {
  return request({
    url: '/cert/country/letters',
    method: 'get'
  })
}

/**
 * 获取国家详细信息
 * GET /cert/country/{id}
 */
export function getCountryDetails(id: number) {
  return request({
    url: `/cert/country/${id}`,
    method: 'get'
  })
}

/**
 * 根据国家代码查询国家信息
 * GET /cert/country/code/{code}
 */
export function getCountryByCode(code: string) {
  return request({
    url: `/cert/country/code/${code}`,
    method: 'get'
  })
}

/**
 * 验证国家代码是否可用
 * GET /cert/country/checkCode/{code}
 */
export function checkCountryCode(code: string) {
  return request({
    url: `/cert/country/checkCode/${code}`,
    method: 'get'
  })
}

/**
 * 新增国家
 * POST /cert/country
 */
export function createCountry(data: CountryCreateUpdateDTO) {
  return request({
    url: '/cert/country',
    method: 'post',
    data
  })
}

/**
 * 修改国家
 * PUT /cert/country
 */
export function updateCountry(data: CountryCreateUpdateDTO & { id: number }) {
  return request({
    url: '/cert/country',
    method: 'put',
    data
  })
}

/**
 * 删除国家
 * DELETE /cert/country/{ids}
 */
export function deleteCountries(ids: number[]) {
  return request({
    url: `/cert/country/${ids.join(',')}`,
    method: 'delete'
  })
}

/**
 * 导出国家列表
 * POST /cert/country/export
 */
export function exportCountries(params?: CountryQueryDTO) {
  return request({
    url: '/cert/country/export',
    method: 'post',
    params,
    responseType: 'blob'
  })
}

/**
 * 检查国家图标路径（调试用）
 * GET /cert/country/checkFlags
 */
export function checkCountryFlags() {
  return request({
    url: '/cert/country/checkFlags',
    method: 'get'
  })
}
