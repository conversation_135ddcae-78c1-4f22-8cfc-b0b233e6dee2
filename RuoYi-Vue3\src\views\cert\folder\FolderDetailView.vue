<template>
  <div class="folder-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>样本管理</el-breadcrumb-item>
        <el-breadcrumb-item>文件夹详情</el-breadcrumb-item>
        <el-breadcrumb-item v-if="folderInfo">{{ folderInfo.folderName }}</el-breadcrumb-item>
      </el-breadcrumb>

      <div class="header-actions">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <el-button @click="refreshData" :icon="Refresh" :loading="loading">刷新</el-button>
      </div>
    </div>

    <!-- 文件夹信息卡片 -->
    <el-card class="folder-info-card" v-if="folderInfo">
      <template #header>
        <span>文件夹信息</span>
      </template>
      <div class="folder-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="文件夹名称">{{ folderInfo.folderName }}</el-descriptions-item>
          <el-descriptions-item label="图片数量">{{ imageList.length }}</el-descriptions-item>
          <el-descriptions-item label="已标注">{{ annotatedCount }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(folderInfo.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(folderInfo.status)">{{ folderInfo.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标注进度">
            <el-progress :percentage="annotationProgress" :stroke-width="8" />
          </el-descriptions-item>
          <el-descriptions-item label="版本解析状态">
            <div v-if="folderInfo.preParseVersionInfo">
              <el-tag :type="getPreParseStatusType(folderInfo.preParseVersionInfo.parseStatus)" size="small">
                {{ getPreParseStatusText(folderInfo.preParseVersionInfo.parseStatus) }}
              </el-tag>
              <div v-if="folderInfo.preParseVersionInfo.parsedVersionCode" class="version-code">
                {{ folderInfo.preParseVersionInfo.parsedVersionCode }}
              </div>
            </div>
            <span v-else class="no-parse">未解析</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 样本操作区域 -->
    <el-card class="action-card" v-if="folderInfo">
      <template #header>
        <span>样本操作</span>
      </template>

      <div class="action-buttons">
        <!-- 编辑信息 -->
        <el-button
          type="primary"
          @click="handleEditInfo"
          :icon="Edit"
        >
          编辑样本信息
        </el-button>

        <!-- 智能检查 -->
        <el-button
          type="warning"
          @click="handleIntelligentCheck"
          :icon="Search"
        >
          智能检查
        </el-button>

        <!-- 关联样本 -->
        <el-button
          v-if="folderInfo.status === 'unassociated'"
          type="success"
          @click="handleAssociateSample"
          :icon="Link"
        >
          关联到现有样本
        </el-button>

        <!-- 创建新样本 -->
        <el-button
          v-if="folderInfo.status === 'unassociated'"
          type="info"
          @click="handleCreateNewSample"
          :icon="Plus"
        >
          创建新样本
        </el-button>
      </div>

      <!-- 版本解析结果区域 -->
      <div v-if="folderInfo.status === 'unassociated' && parsedVersionInfo" class="version-parse-section">
        <el-divider content-position="left">
          <span class="divider-title">版本解析结果</span>
        </el-divider>

        <div class="parse-result">
          <el-form label-width="120px" size="small">
            <el-form-item label="解析状态">
              <el-tag :type="getParseStatusType(parsedVersionInfo.parseStatus)">
                {{ getParseStatusText(parsedVersionInfo.parseStatus) }}
              </el-tag>
            </el-form-item>

            <el-form-item label="版本代码">
              <el-input
                v-model="parsedVersionInfo.parsedVersionCode"
                placeholder="解析出的版本代码"
                :disabled="!canEditVersionCode"
                clearable
                style="width: 100%;"
              />
              <div class="form-hint">
                系统自动解析的版本代码，您可以修改后确认
              </div>
            </el-form-item>

            <el-form-item label="版本描述">
              <el-input
                v-model="versionDescription"
                type="textarea"
                placeholder="请输入版本描述"
                :rows="3"
                style="width: 100%;"
              />
            </el-form-item>

            <el-form-item v-if="parsedVersionInfo.parseErrors && parsedVersionInfo.parseErrors.length > 0" label="解析错误">
              <div class="parse-errors">
                <el-tag
                  v-for="error in parsedVersionInfo.parseErrors"
                  :key="error"
                  type="danger"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px;"
                >
                  {{ error }}
                </el-tag>
              </div>
            </el-form-item>
          </el-form>

          <div class="parse-actions">
            <el-button
              type="primary"
              @click="handleConfirmVersion"
              :loading="confirmingVersion"
              :disabled="!parsedVersionInfo.parsedVersionCode"
            >
              确认创建版本
            </el-button>
            <el-button
              @click="handleReparseVersion"
              :loading="reparsing"
            >
              重新解析
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：图片列表 -->
      <div class="left-panel">
        <el-card class="image-list-card">
          <template #header>
            <div class="card-header">
              <span>图片列表 ({{ imageList.length }})</span>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索图片..."
                size="small"
                style="width: 200px;"
                clearable
              />
            </div>
          </template>

          <div class="image-list" v-loading="imageLoading">
            <div
              v-for="(image, index) in filteredImageList"
              :key="image.imageId"
              class="image-item"
              :class="{
                active: selectedImage && selectedImage.imageId === image.imageId
              }"
              @click="selectImage(image, index)"
            >
              <div class="image-thumbnail">
                <img
                  :src="getImageThumbnail(image)"
                  :alt="image.imageName"
                  @error="handleImageError"
                />
                <div class="image-overlay">
                  <div class="image-status">
                    <el-tag
                      v-if="image.isAnnotated"
                      type="success"
                      size="small"
                    >
                      已标注
                    </el-tag>
                    <el-tag
                      v-else
                      type="warning"
                      size="small"
                    >
                      未标注
                    </el-tag>
                  </div>
                </div>
              </div>
              <div class="image-info">
                <div class="image-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="handleEditImage(image)"
                    :icon="Edit"
                  >
                    编辑
                  </el-button>
                </div>
                <div class="image-name" :title="image.imageName">
                  {{ image.imageName }}
                </div>
                <div class="image-meta">
                  {{ formatFileSize(image.fileSize) }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：标注工作区 -->
      <div class="right-panel">
        <div class="annotation-workspace" v-if="selectedImage">
          <!-- 标注工具栏 -->
          <div class="annotation-toolbar">
            <AnnotationToolbar
              @tool-selected="handleToolChange"
            />

            <!-- 额外的操作按钮 -->
            <div class="toolbar-actions">
              <el-button
                type="primary"
                @click="handleSaveAnnotation"
                :loading="saving"
                size="small"
              >
                保存标注
              </el-button>
              <el-button
                type="danger"
                @click="handleClearAnnotation"
                :disabled="currentAnnotations.length === 0"
                size="small"
              >
                清空标注
              </el-button>
              <el-button
                @click="handleUndo"
                :disabled="!canUndo"
                size="small"
              >
                撤销
              </el-button>
            </div>
          </div>

          <!-- 标注画布和列表 -->
          <div class="annotation-content">
            <!-- 中间：标注画布 -->
            <div class="annotation-canvas-container">
              <AnnotationCanvas
                ref="annotationCanvasRef"
                :imageUrl="selectedImage?.imageUrl"
                :tool="currentTool"
                v-model="currentAnnotations"
                @select-annotation="handleAnnotationSelect"
              />
            </div>

            <!-- 右侧：标注列表 -->
            <div class="annotation-list-container">
              <!-- 图片类型标注 -->
              <ImageTypeAnnotatorCompact
                :image-info="selectedImage"
                :can-edit="canEditImageType"
                :readonly-reason="imageTypeReadonlyReason"
                :saving="savingImageType"
                @save="handleSaveImageType"
                @type-change="handleImageTypeChange"
              />

              <!-- 标注列表 -->
              <AnnotationList
                v-model="currentAnnotations"
                :selected-annotation-id="selectedAnnotation?.id"
                @select-annotation="handleAnnotationSelect"
              />
            </div>
          </div>
        </div>

        <!-- 未选择图片时的提示 -->
        <div class="no-selection" v-else>
          <el-empty description="请从左侧选择一张图片开始标注" />
        </div>
      </div>
    </div>

    <!-- 图片信息编辑模态框 -->
    <ImageInfoEditModal
      v-model:visible="imageEditModalVisible"
      :image-info="editingImage"
      :can-edit="canEditImageInfo"
      @saved="handleImageInfoSaved"
      @close="handleImageEditModalClose"
    />

    <!-- 样本编辑弹窗 -->
    <SampleEditModal
      v-model="editModalVisible"
      :folder-info="folderInfo"
      @success="handleEditSuccess"
    />

    <!-- 智能检查弹窗 -->
    <ThirdPartyDetectionModal
      v-model="detectionModalVisible"
      :target-folder="folderInfo"
      @detection-complete="handleDetectionComplete"
    />

    <!-- 样本关联弹窗 -->
    <VersionSelectorModal
      v-model="selectorModalVisible"
      :target-folder="folderInfo"
      @version-selected="handleVersionSelected"
    />

    <!-- 推荐版本列表 -->
    <el-table v-loading="recommendLoading" :data="recommendedVersions" height="250px" stripe>
      <el-table-column prop="versionCode" label="版本代码" />
      <el-table-column prop="matchReason" label="推荐理由" />
      <el-table-column prop="matchScore" label="匹配度">
        <template #default="{ row }">
          <el-progress :percentage="row.matchScore * 100" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleConfirmAssociation(row)">
            {{ row.isNewVersion ? '创建并关联' : '选择此版本' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Refresh, Edit, Search, Link, Plus } from '@element-plus/icons-vue'

// 导入组件
import AnnotationToolbar from '../annotations/AnnotationToolbar.vue'
import AnnotationCanvas from '../annotations/AnnotationCanvas.vue'
import AnnotationList from '../annotations/AnnotationList.vue'
import ImageTypeAnnotatorCompact from '@/components/Annotation/ImageTypeAnnotatorCompact.vue'
import ImageInfoEditModal from '../sample/components/ImageInfoEditModal.vue'

// 导入弹窗组件
import SampleEditModal from './components/SampleEditModal.vue'
import ThirdPartyDetectionModal from '../sample/modals/ThirdPartyDetectionModal.vue'
import VersionSelectorModal from '../sample/modals/VersionSelectorModal.vue'

// 导入API
import { updateImageInfo } from '@/api/cert/image.js'
import { reparseFolder } from '@/api/cert/folder.js'
import { createVersionFromFolder } from '@/api/cert/version.js'

// 导入工具函数
import {
  isStandardSampleFolder,
  isStandardSampleByVersion,
  canAnnotateImage,
  getFolderTypeDisplayName,
  getFolderTypeTagType
} from '@/utils/folderUtils.js'

// 导入composables
import { useImageAnnotation } from '@/composables/useAnnotation.ts'
import { useFolderInfo } from '@/composables/useFolder.ts'

const route = useRoute()
const router = useRouter()

// 使用composables
const {
  imageList,
  selectedImage,
  currentAnnotations,
  imageLoading,
  saving,
  hasAnnotationChanges,
  selectedAnnotation,
  annotatedCount,
  annotationProgress,
  loadImageList,
  loadImageAnnotations,
  saveAnnotations,
  selectImage,
  selectAnnotation,
  clearAnnotations
} = useImageAnnotation()

const {
  folderInfo,
  loading,
  loadFolderInfo,
  getStatusType
} = useFolderInfo()

// 其他响应式数据
const searchKeyword = ref('')
const annotationCanvasRef = ref(null)

// 图片编辑相关
const imageEditModalVisible = ref(false)
const editingImage = ref(null)

// 图片类型标注相关
const savingImageType = ref(false)

// 标注工具状态
const currentTool = ref('select')
const annotationHistory = ref([])
const canUndo = ref(false)

// 弹窗状态
const editModalVisible = ref(false)
const detectionModalVisible = ref(false)
const selectorModalVisible = ref(false)

// 版本解析相关
const parsedVersionInfo = ref(null)
const versionDescription = ref('')
const canEditVersionCode = ref(true)
const confirmingVersion = ref(false)
const reparsing = ref(false)

// 推荐版本相关
const recommendLoading = ref(false)
const recommendedVersions = ref([])

// 计算属性
const filteredImageList = computed(() => {
  if (!searchKeyword.value) return imageList.value
  return imageList.value.filter(image =>
    image.imageName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 生命周期
onMounted(() => {
  loadFolderData()
})

// 监听路由参数变化
watch(() => route.params.folderId, (newFolderId) => {
  if (newFolderId) {
    loadFolderData()
  }
})

// 监听选中图片变化
watch(selectedImage, async (newImage, oldImage) => {
  if (newImage && newImage.imageId !== oldImage?.imageId) {
    // 清空旧标注
    currentAnnotations.value = []
    selectedAnnotation.value = null
    hasAnnotationChanges.value = false

    // 加载新图片的标注
    await loadImageAnnotations(newImage.imageId)
  }
}, { immediate: true })

// 监听标注数据变化
watch(currentAnnotations, (newAnnotations, oldAnnotations) => {
  if (oldAnnotations && newAnnotations !== oldAnnotations) {
    hasAnnotationChanges.value = true
  }
}, { deep: true })

// 方法
const loadFolderData = async () => {
  const folderId = route.params.folderId
  if (!folderId) {
    ElMessage.error('文件夹ID不能为空')
    return
  }

  try {
    // 并行加载文件夹信息和图片列表
    await Promise.all([
      loadFolderInfo(folderId),
      loadImageList(folderId)
    ])

    // 如果有图片，默认选择第一张
    if (imageList.value.length > 0) {
      selectImage(imageList.value[0])
    }
  } catch (error) {
    console.error('加载文件夹数据失败:', error)
    ElMessage.error('加载文件夹数据失败')
  }
}

const startAnnotation = (image) => {
  // 选择图片并开始标注
  selectImage(image)
  ElMessage.info(`开始标注图片: ${image.imageName}`)
}

const handleSaveAnnotation = async () => {
  try {
    // 获取图片尺寸
    const imageElement = annotationCanvasRef.value?.imageElement
    const imageWidth = imageElement?.naturalWidth
    const imageHeight = imageElement?.naturalHeight

    console.log('📏 获取到的图片尺寸:', { width: imageWidth, height: imageHeight })

    const success = await saveAnnotations(imageWidth, imageHeight)
    if (success) {
      // 清空历史记录，因为已经保存
      annotationHistory.value = []
      canUndo.value = false
      console.log('标注保存成功')
    }
  } catch (error) {
    console.error('保存标注失败:', error)
    ElMessage.error('保存标注失败')
  }
}

const handleClearAnnotation = async () => {
  try {
    await ElMessageBox.confirm('确定要清除当前图片的所有标注吗？', '确认清除', {
      type: 'warning'
    })

    clearAnnotations()
    await handleSaveAnnotation()
  } catch (error) {
    // 用户取消操作
  }
}

const handleAnnotationSelect = (annotationId) => {
  selectAnnotation(annotationId)
}

// 工具栏事件处理
const handleToolChange = (tool) => {
  currentTool.value = tool
}

const handleUndo = () => {
  if (annotationHistory.value.length > 0) {
    currentAnnotations.value = annotationHistory.value.pop()
    hasAnnotationChanges.value = true
    canUndo.value = annotationHistory.value.length > 0
  }
}

const refreshData = () => {
  loadFolderData()
}

const goBack = () => {
  router.go(-1)
}

// 图片编辑相关方法
const handleEditImage = (image) => {
  editingImage.value = image
  imageEditModalVisible.value = true
}

const handleImageInfoSaved = (updatedImageInfo) => {
  // 更新图片列表中的对应项
  const index = imageList.value.findIndex(img => img.imageId === updatedImageInfo.imageId)
  if (index !== -1) {
    imageList.value[index] = { ...imageList.value[index], ...updatedImageInfo }
  }

  // 如果是当前选中的图片，也更新选中项
  if (selectedImage.value && selectedImage.value.imageId === updatedImageInfo.imageId) {
    selectedImage.value = { ...selectedImage.value, ...updatedImageInfo }
  }

  ElMessage.success('图片信息更新成功')
}

const handleImageEditModalClose = () => {
  editingImage.value = null
  imageEditModalVisible.value = false
}

// 权限控制
const canEditImageInfo = computed(() => {
  // 根据实际权限逻辑判断是否可以编辑图片信息
  return true // 暂时返回true，实际应该根据用户权限判断
})

const canEditImageType = computed(() => {
  // 根据实际权限逻辑判断是否可以编辑图片类型
  return true // 暂时返回true，实际应该根据用户权限判断
})

const imageTypeReadonlyReason = computed(() => {
  return canEditImageType.value ? '' : '当前用户无编辑权限'
})

// 图片类型标注相关方法
const handleSaveImageType = async (imageType) => {
  try {
    savingImageType.value = true
    await updateImageInfo(selectedImage.value.imageId, { imageType })

    // 更新本地数据
    if (selectedImage.value) {
      selectedImage.value.imageType = imageType
    }

    ElMessage.success('图片类型保存成功')
  } catch (error) {
    console.error('保存图片类型失败:', error)
    ElMessage.error('保存图片类型失败')
  } finally {
    savingImageType.value = false
  }
}

const handleImageTypeChange = (imageType) => {
  console.log('图片类型变更为:', imageType)
}

const getImageThumbnail = (image) => {
  // 根据minioPath生成图片URL，如果没有图片就返回空字符串
  return getImageUrl(image) || ''
}

// 根据minioPath生成图片URL
const getImageUrl = (image) => {
  if (!image || !image.minioPath) {
    console.warn('图片对象或minioPath为空:', image)
    return ''
  }

  console.log('生成图片URL，minioPath:', image.minioPath)

  // 临时方案：先尝试直接访问MinIO，看看文件是否存在
  const directMinioUrl = `http://localhost:9000/xjlfiles/${image.minioPath}`
  console.log('直接MinIO URL:', directMinioUrl)

  // 如果直接访问失败，再尝试代理
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`
  console.log('代理URL:', proxyUrl)

  // 先返回代理URL，如果失败会在图片加载错误时处理
  return proxyUrl
}

const handleImageError = (event) => {
  const img = event.target
  const currentSrc = img.src

  console.error('图片加载失败:', currentSrc)

  // 如果当前是代理URL失败，尝试直接访问MinIO
  if (currentSrc.includes('/dev-api/common/image/proxy')) {
    // 提取原始MinIO URL
    const urlMatch = currentSrc.match(/url=([^&]+)/)
    if (urlMatch) {
      const originalUrl = decodeURIComponent(urlMatch[1])
      console.log('尝试直接访问MinIO:', originalUrl)
      img.src = originalUrl
      return
    }
  }

  // 最后的回退方案：不显示图片
  console.log('图片加载失败，不显示默认图片')
  img.style.display = 'none'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString()
}

// ==================== 样本操作方法 ====================

// 编辑信息
const handleEditInfo = () => {
  editModalVisible.value = true
}

// 智能检查
const handleIntelligentCheck = () => {
  detectionModalVisible.value = true
}

// 关联样本
const handleAssociateSample = () => {
  selectorModalVisible.value = true
}

// 创建新样本
const handleCreateNewSample = async () => {
  try {
    reparsing.value = true
    ElMessage.info('正在解析文件夹名称...')

    // 调用 reparseFolder API
    const result = await reparseFolder(folderInfo.value.folderId)

    if (result.code === 200) {
      parsedVersionInfo.value = {
        parseStatus: result.data.parseStatus,
        parsedVersionCode: result.data.parsedVersionCode,
        parseErrors: result.data.parseErrors || []
      }

      // 根据解析状态设置是否可编辑
      canEditVersionCode.value = result.data.parseStatus !== 'SUCCESS'

      if (result.data.parseStatus === 'SUCCESS') {
        ElMessage.success('版本解析成功')
      } else if (result.data.parseStatus === 'PARTIAL') {
        ElMessage.warning('版本解析部分成功，请检查并修改版本代码')
      } else {
        ElMessage.error('版本解析失败，请手动输入版本代码')
      }
    } else {
      ElMessage.error(result.msg || '解析失败')
    }
  } catch (error) {
    console.error('解析版本失败:', error)
    ElMessage.error('解析版本失败: ' + (error.message || '未知错误'))
  } finally {
    reparsing.value = false
  }
}

// 处理各种操作的回调
const handleEditSuccess = () => {
  ElMessage.success('样本信息更新成功')
  refreshData()
}

const handleDetectionComplete = (result) => {
  ElMessage.success('智能检查完成')
  // 可以根据检查结果给出建议
  console.log('检查结果:', result)
}

const handleVersionSelected = (versionId) => {
  ElMessage.success('样本关联成功')
  refreshData()
}

// 推荐版本相关方法
const fetchRecommendedVersions = async () => {
  if (!folderInfo.value?.folderId) return;
  recommendLoading.value = true;
  try {
    // 从文件夹预解析信息中获取推荐版本
    const preParseInfo = folderInfo.value.preParseVersionInfo;

    if (preParseInfo && preParseInfo.parsedVersionCode) {
      // 构建推荐版本对象
      const recommendedVersion = {
        versionId: null, // 新版本，还没有ID
        versionCode: preParseInfo.parsedVersionCode,
        matchReason: '基于文件夹名称解析生成',
        matchScore: 0.95, // 高匹配度
        isNewVersion: true // 标记为新版本
      };

      recommendedVersions.value = [recommendedVersion];
    } else {
      // 如果没有解析到版本代码，显示空列表
      recommendedVersions.value = [];
    }
  } catch (error) {
    console.error('获取推荐版本失败:', error);
    ElMessage.error('获取推荐版本失败');
  } finally {
    recommendLoading.value = false;
  }
};

const handleConfirmAssociation = async (version) => {
  try {
    if (version.isNewVersion) {
      // 如果是新版本，先创建版本再关联
      const createData = {
        folderId: folderInfo.value.folderId,
        versionCode: version.versionCode,
        description: `基于文件夹"${folderInfo.value.folderName}"创建的版本`
      };

      const createResponse = await createVersionFromFolder(createData);

      if (createResponse.code === 200) {
        ElMessage.success(`新版本"${version.versionCode}"创建并关联成功`);
        emit('association-changed');
        emit('update:modelValue', false);
      } else {
        ElMessage.error(createResponse.msg || '创建版本失败');
      }
    } else {
      // 如果是现有版本，直接关联
      const response = await associateFolderToVersion(folderInfo.value.folderId, version.versionId);

      if (response.code === 200) {
        ElMessage.success(`文件夹已成功关联到版本: ${version.versionCode}`);
        emit('association-changed');
        emit('update:modelValue', false);
      } else {
        ElMessage.error(response.msg || '关联失败');
      }
    }
  } catch (error) {
    console.error('版本关联失败:', error);
    ElMessage.error('版本关联操作失败');
  }
};

const handleCreateVersion = async () => {
  if (!newVersionForm.value.versionCode.trim()) {
    ElMessage.warning('请输入版本代码');
    return;
  }

  try {
    // 创建新版本并关联
    const createData = {
      folderId: folderInfo.value.folderId,
      versionCode: newVersionForm.value.versionCode,
      description: `基于文件夹"${folderInfo.value.folderName}"创建的版本`
    };

    const response = await createVersionFromFolder(createData);

    if (response.code === 200) {
      ElMessage.success(`新版本"${newVersionForm.value.versionCode}"创建并关联成功`);
      emit('association-changed');
      emit('update:modelValue', false);
    } else {
      ElMessage.error(response.msg || '创建版本失败');
    }
  } catch (error) {
    console.error('创建版本失败:', error);
    ElMessage.error('创建版本失败: ' + (error.message || error));
  }
};

// 版本解析相关方法
const handleReparseVersion = async () => {
  await handleCreateNewSample()
}

const handleConfirmVersion = async () => {
  if (!parsedVersionInfo.value?.parsedVersionCode) {
    ElMessage.warning('请先输入版本代码')
    return
  }

  try {
    confirmingVersion.value = true

    // 构造创建版本的DTO
    const dto = {
      folderId: folderInfo.value.folderId,
      versionCode: parsedVersionInfo.value.parsedVersionCode.trim(),
      description: versionDescription.value.trim()
    }

    console.log('创建版本DTO:', dto)

    // 调用创建API
    const result = await createVersionFromFolder(dto)

    if (result.code === 200) {
      ElMessage.success(`版本 "${parsedVersionInfo.value.parsedVersionCode}" 创建成功`)

      // 清空解析结果
      parsedVersionInfo.value = null
      versionDescription.value = ''

      // 刷新数据
      refreshData()
    } else {
      ElMessage.error(result.msg || '创建失败')
    }
  } catch (error) {
    console.error('创建版本失败:', error)
    ElMessage.error('创建版本失败: ' + (error.message || '未知错误'))
  } finally {
    confirmingVersion.value = false
  }
}

// 获取解析状态类型
const getParseStatusType = (status) => {
  switch (status) {
    case 'SUCCESS':
      return 'success'
    case 'PARTIAL':
      return 'warning'
    case 'FAILED':
    default:
      return 'danger'
  }
}

// 获取解析状态文本
const getParseStatusText = (status) => {
  switch (status) {
    case 'SUCCESS':
      return '解析成功'
    case 'PARTIAL':
      return '部分成功'
    case 'FAILED':
      return '解析失败'
    default:
      return '未知状态'
  }
}

// 获取预解析状态类型
const getPreParseStatusType = (status) => {
  switch (status) {
    case 'PARSED':
      return 'success'
    case 'PARSING':
      return 'warning'
    case 'UPLOADED':
      return 'info'
    case 'PARSE_FAILED':
    default:
      return 'danger'
  }
}

// 获取预解析状态文本
const getPreParseStatusText = (status) => {
  switch (status) {
    case 'PARSED':
      return '已解析'
    case 'PARSING':
      return '解析中'
    case 'UPLOADED':
      return '已上传'
    case 'PARSE_FAILED':
      return '解析失败'
    default:
      return '未知状态'
  }
}
</script>

<style scoped>
.folder-detail-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.folder-info-card {
  margin-bottom: 20px;
}

.action-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.version-parse-section {
  margin-top: 20px;
}

.divider-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.parse-result {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.form-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.parse-errors {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.parse-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

/* 版本解析状态样式 */
.version-code {
  margin-top: 4px;
  font-size: 12px;
  color: #606266;
  font-family: 'Courier New', monospace;
}

.no-parse {
  color: #909399;
  font-style: italic;
}

.main-content {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.image-list-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.image-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.image-item:hover {
  background-color: #f5f7fa;
}

.image-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

/* 标准样本样式 */
.image-item.standard-sample {
  border-left: 2px solid #67c23a;
}

.image-item.standard-sample:hover {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
}

.image-item.standard-sample.active {
  background-color: #e6f7ff;
  border-left: 3px solid #67c23a;
}

/* 普通样本样式 */
.image-item.ordinary-sample {
  border-left: 2px solid #909399;
}

.image-item.ordinary-sample:hover {
  background-color: #f5f7fa;
  border-left-color: #909399;
}

.image-item.ordinary-sample.active {
  background-color: #e6f7ff;
  border-left: 3px solid #909399;
}

.image-thumbnail {
  position: relative;
  width: 60px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px;
}

.image-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-meta {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

/* 样本类型标签样式 */
.sample-type {
  margin-bottom: 6px;
}

.sample-type .el-tag {
  font-size: 11px;
}

/* 标注操作按钮样式 */
.annotation-actions {
  margin-top: 4px;
}

.annotation-actions .el-button {
  padding: 4px 8px;
  font-size: 11px;
  height: auto;
  min-height: 24px;
}

.annotation-workspace {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.annotation-toolbar {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.annotation-content {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.annotation-canvas-container {
  flex: 2;
  min-width: 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.annotation-list-container {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fafafa;
  border-radius: 8px;
}

/* 推荐版本样式 */
.recommended-versions-table {
  margin-top: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.recommended-versions-table .el-table th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

.recommended-versions-table .el-table td {
  padding: 8px 12px;
}

.recommended-versions-table .el-table .el-progress {
  margin-top: 4px;
}

.recommended-versions-table .el-table .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    height: 200px;
  }

  .image-list {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    max-height: 150px;
  }

  .image-item {
    flex-direction: column;
    min-width: 120px;
    border-bottom: none;
    border-right: 1px solid #f0f0f0;
  }

  .image-thumbnail {
    width: 100px;
    height: 80px;
    margin-right: 0;
    margin-bottom: 5px;
  }

  .annotation-content {
    flex-direction: column;
  }

  .annotation-list-container {
    width: 100%;
    height: 200px;
  }
}
</style>
