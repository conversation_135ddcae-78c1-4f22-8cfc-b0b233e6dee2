package com.ruoyi.system.controller.news;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.news.SysNotification;
import com.ruoyi.system.service.news.ISysNotificationService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 消息通知Controller
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
@RestController
@RequestMapping("/system/notification")
public class SysNotificationController extends BaseController
{
    @Autowired
    private ISysNotificationService sysNotificationService;

    /**
     * 查询消息通知列表
     */
    @PreAuthorize("@ss.hasPermi('system:notification:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysNotification sysNotification)
    {
        startPage();
        // 只查询当前登录用户的消息
        sysNotification.setUserId(getUserId());
        List<SysNotification> list = sysNotificationService.selectSysNotificationList(sysNotification);
        return getDataTable(list);
    }

    /**
     * 获取消息通知详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notification:query')")
    @GetMapping(value = "/{notificationId}")
    public AjaxResult getInfo(@PathVariable("notificationId") Long notificationId)
    {
        return success(sysNotificationService.selectSysNotificationByNotificationId(notificationId));
    }
    
    /**
     * 获取未读消息数量
     */
    @PreAuthorize("@ss.hasPermi('system:notification:list')")
    @GetMapping("/unread")
    public AjaxResult getUnreadCount()
    {
        int count = sysNotificationService.selectUnreadCount(getUserId());
        return success(count);
    }

    /**
     * 将消息标记为已读
     */
    @PreAuthorize("@ss.hasPermi('system:notification:edit')")
    @PutMapping("/read/{notificationId}")
    public AjaxResult markAsRead(@PathVariable("notificationId") Long notificationId)
    {
        return toAjax(sysNotificationService.markAsRead(notificationId));
    }
    
    /**
     * 将所有消息标记为已读
     */
    @PreAuthorize("@ss.hasPermi('system:notification:edit')")
    @PutMapping("/readAll")
    public AjaxResult markAllAsRead()
    {
        return toAjax(sysNotificationService.markAllAsRead(getUserId()));
    }

    /**
     * 删除消息通知
     */
    @PreAuthorize("@ss.hasPermi('system:notification:remove')")
    @Log(title = "消息通知", businessType = BusinessType.DELETE)
	@DeleteMapping("/{notificationIds}")
    public AjaxResult remove(@PathVariable Long[] notificationIds)
    {
        return toAjax(sysNotificationService.deleteSysNotificationByNotificationIds(notificationIds));
    }
}
