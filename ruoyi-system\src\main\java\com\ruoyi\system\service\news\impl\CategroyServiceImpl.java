package com.ruoyi.system.service.news.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.CategroyMapper;
import com.ruoyi.system.domain.news.Categroy;
import com.ruoyi.system.service.news.ICategroyService;

/**
 * 栏目信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class CategroyServiceImpl implements ICategroyService 
{
    @Autowired
    private CategroyMapper categroyMapper;

    /**
     * 查询栏目信息
     * 
     * @param categroyID 栏目信息主键
     * @return 栏目信息
     */
    @Override
    public Categroy selectCategroyByCategroyID(Long categroyID)
    {
        return categroyMapper.selectCategroyByCategroyID(categroyID);
    }

    /**
     * 查询栏目信息列表
     * 
     * @param categroy 栏目信息
     * @return 栏目信息
     */
    @Override
    public List<Categroy> selectCategroyList(Categroy categroy)
    {
        return categroyMapper.selectCategroyList(categroy);
    }

    /**
     * 新增栏目信息
     * 
     * @param categroy 栏目信息
     * @return 结果
     */
    @Override
    public int insertCategroy(Categroy categroy)
    {
        return categroyMapper.insertCategroy(categroy);
    }

    /**
     * 修改栏目信息
     * 
     * @param categroy 栏目信息
     * @return 结果
     */
    @Override
    public int updateCategroy(Categroy categroy)
    {
        return categroyMapper.updateCategroy(categroy);
    }

    /**
     * 批量删除栏目信息
     * 
     * @param categroyIDs 需要删除的栏目信息主键
     * @return 结果
     */
    @Override
    public int deleteCategroyByCategroyIDs(Long[] categroyIDs)
    {
        return categroyMapper.deleteCategroyByCategroyIDs(categroyIDs);
    }

    /**
     * 删除栏目信息信息
     * 
     * @param categroyID 栏目信息主键
     * @return 结果
     */
    @Override
    public int deleteCategroyByCategroyID(Long categroyID)
    {
        return categroyMapper.deleteCategroyByCategroyID(categroyID);
    }
}
