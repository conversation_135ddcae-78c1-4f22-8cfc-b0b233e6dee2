@echo off
REM 修复 cert_version 集合的 folderName 索引问题
REM 使用方法: fix-index.bat [数据库名]

set DB_NAME=%1
if "%DB_NAME%"=="" set DB_NAME=docu_research

echo === 修复 cert_version 集合的 folderName 索引问题 ===
echo 数据库: %DB_NAME%

REM 执行MongoDB命令
mongo %DB_NAME% --eval "print('检查当前索引...'); var indexes = db.cert_version.getIndexes(); var hasFolderNameIndex = false; var indexName = ''; indexes.forEach(function(index) { if (index.key && index.key.folderName === 1) { hasFolderNameIndex = true; indexName = index.name; print('发现错误的 folderName 索引: ' + index.name); } }); if (hasFolderNameIndex) { print('删除错误的索引: ' + indexName); db.cert_version.dropIndex(indexName); print('✓ 索引删除成功'); } else { print('✓ 未发现 folderName 索引，无需处理'); } print('当前索引列表:'); db.cert_version.getIndexes().forEach(function(index) { print('  - ' + index.name + ': ' + JSON.stringify(index.key)); });"

echo === 修复完成 ===
pause
