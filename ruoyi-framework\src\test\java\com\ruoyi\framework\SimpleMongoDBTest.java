package com.ruoyi.framework;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;

public class SimpleMongoDBTest {
    public static void main(String[] args) {
        String connectionString = "**************************************************************************************";
        
        try (MongoClient mongoClient = MongoClients.create(connectionString)) {
            System.out.println("MongoDB 连接成功！");
            System.out.println("可用数据库列表:");
            mongoClient.listDatabaseNames().forEach(System.out::println);
        } catch (Exception e) {
            System.err.println("MongoDB 连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 