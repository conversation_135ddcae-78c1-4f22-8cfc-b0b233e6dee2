package com.ruoyi.system.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.system.dto.MultiVersionBatchTaskCreateDTO;
import com.ruoyi.system.dto.VersionFolderDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 多级文件夹批量上传集成测试
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class MultiLevelFolderUploadIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testCreateMultiVersionBatchTask_Success() throws Exception {
        // 准备测试数据
        MultiVersionBatchTaskCreateDTO dto = createTestMultiVersionDTO();

        // 执行请求
        mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.taskId").exists());
    }

    @Test
    void testCreateMultiVersionBatchTask_WithSpecialCharacters() throws Exception {
        // 包含特殊字符的测试数据
        MultiVersionBatchTaskCreateDTO dto = new MultiVersionBatchTaskCreateDTO();
        dto.setTaskName("2024-01-15_技术部_特殊字符测试任务");
        dto.setDepartmentId(1L);

        List<VersionFolderDTO> specialVersions = Arrays.asList(
            createVersionFolder("España_Pasaporte_2023_D3456_Madrid", "España", "Pasaporte"),
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照"),
            createVersionFolder("test folder with spaces", "Test Country", "Test Type")
        );
        dto.setVersionFolders(specialVersions);

        mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testFileUploadWithOriginalFilename() throws Exception {
        // 1. 先创建批量任务
        MultiVersionBatchTaskCreateDTO dto = createTestMultiVersionDTO();
        
        String response = mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // 提取 taskId（这里需要根据实际响应格式调整）
        String taskId = extractTaskIdFromResponse(response);

        // 2. 测试文件上传
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "身份证正面.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );

        mockMvc.perform(multipart("/tus/upload")
                .file(file)
                .param("taskId", taskId)
                .param("folderName", "中国_护照_2023_A1234_北京")
                .param("originalFilename", "身份证正面.jpg"))
                .andExpect(status().isOk());

        // 3. 验证文件信息保存
        mockMvc.perform(get("/batch-tasks/" + taskId + "/files"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].filename").value("身份证正面.jpg"))
                .andExpect(jsonPath("$.data[0].originalFilename").value("身份证正面.jpg"));
    }

    @Test
    void testBatchTaskDeletion_CascadeDelete() throws Exception {
        // 1. 创建批量任务
        MultiVersionBatchTaskCreateDTO dto = createTestMultiVersionDTO();
        
        String response = mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        String taskId = extractTaskIdFromResponse(response);

        // 2. 上传一些文件
        uploadTestFiles(taskId);

        // 3. 删除任务
        mockMvc.perform(delete("/batch-tasks/" + taskId))
                .andExpect(status().isOk());

        // 4. 验证级联删除
        mockMvc.perform(get("/batch-tasks/" + taskId))
                .andExpect(status().isNotFound());

        mockMvc.perform(get("/batch-tasks/" + taskId + "/folders"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isEmpty());

        mockMvc.perform(get("/batch-tasks/" + taskId + "/files"))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    void testLargeFileSetPerformance() throws Exception {
        // 创建大量版本的测试数据
        MultiVersionBatchTaskCreateDTO dto = new MultiVersionBatchTaskCreateDTO();
        dto.setTaskName("2024-01-15_技术部_大量文件性能测试");
        dto.setDepartmentId(1L);

        List<VersionFolderDTO> largeVersionSet = createLargeVersionSet(50);
        dto.setVersionFolders(largeVersionSet);

        long startTime = System.currentTimeMillis();

        mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能要求：50个版本应该在3秒内完成
        assert duration < 3000 : "创建50个版本任务耗时过长: " + duration + "ms";
    }

    @Test
    void testErrorHandling_InvalidData() throws Exception {
        // 测试无效数据
        MultiVersionBatchTaskCreateDTO invalidDto = new MultiVersionBatchTaskCreateDTO();
        // 缺少必要字段

        mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    void testErrorHandling_DuplicateFolders() throws Exception {
        // 测试重复文件夹
        MultiVersionBatchTaskCreateDTO dto = new MultiVersionBatchTaskCreateDTO();
        dto.setTaskName("重复文件夹测试");
        dto.setDepartmentId(1L);

        List<VersionFolderDTO> duplicateVersions = Arrays.asList(
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照"),
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照") // 重复
        );
        dto.setVersionFolders(duplicateVersions);

        mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value(containsString("重复")));
    }

    @Test
    void testFolderPathMapping() throws Exception {
        // 测试文件夹路径映射
        MultiVersionBatchTaskCreateDTO dto = createTestMultiVersionDTO();
        
        String response = mockMvc.perform(post("/batch-tasks/multi-version")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        String taskId = extractTaskIdFromResponse(response);

        // 验证文件夹信息
        mockMvc.perform(get("/batch-tasks/" + taskId + "/folders"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].folderPath").value("中国_护照_2023_A1234_北京"))
                .andExpect(jsonPath("$.data[0].folderName").value("中国_护照_2023_A1234_北京"))
                .andExpect(jsonPath("$.data[1].folderPath").value("美国_护照_2022_B5678_纽约"))
                .andExpect(jsonPath("$.data[1].folderName").value("美国_护照_2022_B5678_纽约"));
    }

    // 辅助方法
    private MultiVersionBatchTaskCreateDTO createTestMultiVersionDTO() {
        MultiVersionBatchTaskCreateDTO dto = new MultiVersionBatchTaskCreateDTO();
        dto.setTaskName("2024-01-15_技术部_多版本批量上传任务");
        dto.setDepartmentId(1L);

        List<VersionFolderDTO> versions = Arrays.asList(
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照"),
            createVersionFolder("美国_护照_2022_B5678_纽约", "美国", "护照"),
            createVersionFolder("英国_驾照_2024_C9012_伦敦", "英国", "驾照")
        );
        dto.setVersionFolders(versions);

        return dto;
    }

    private VersionFolderDTO createVersionFolder(String folderName, String country, String certType) {
        VersionFolderDTO version = new VersionFolderDTO();
        version.setFolderName(folderName);
        version.setFolderPath(folderName);
        version.setCountryName(country);
        version.setCertTypeName(certType);
        version.setIssueYear("2023");
        version.setCertNumberPrefix("A1234");
        version.setIssuePlace("测试地点");
        version.setFileCount(3);
        return version;
    }

    private List<VersionFolderDTO> createLargeVersionSet(int count) {
        return java.util.stream.IntStream.range(0, count)
            .mapToObj(i -> createVersionFolder(
                "测试文件夹_" + i,
                "测试国家_" + i,
                "测试类型_" + i
            ))
            .collect(java.util.stream.Collectors.toList());
    }

    private String extractTaskIdFromResponse(String response) {
        // 这里需要根据实际的响应格式来解析 taskId
        // 简化示例
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(response);
            return node.get("data").get("taskId").asText();
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract taskId from response", e);
        }
    }

    private void uploadTestFiles(String taskId) throws Exception {
        String[] testFiles = {
            "身份证正面.jpg",
            "身份证背面.jpg",
            "护照首页.jpg"
        };

        for (String filename : testFiles) {
            MockMultipartFile file = new MockMultipartFile(
                "file",
                filename,
                "image/jpeg",
                ("test content for " + filename).getBytes()
            );

            mockMvc.perform(multipart("/tus/upload")
                    .file(file)
                    .param("taskId", taskId)
                    .param("folderName", "中国_护照_2023_A1234_北京")
                    .param("originalFilename", filename))
                    .andExpect(status().isOk());
        }
    }
}
