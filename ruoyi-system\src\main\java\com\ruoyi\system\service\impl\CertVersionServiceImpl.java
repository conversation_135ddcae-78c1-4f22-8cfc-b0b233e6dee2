package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.mongo.CertVersion;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.dto.request.VersionCreateFromFolderDTO;
import com.ruoyi.system.domain.dto.request.VersionQueryDTO;
import com.ruoyi.system.domain.dto.request.CertVersionCreateDTO;
import com.ruoyi.system.repository.CertVersionRepository;
import com.ruoyi.system.service.ICertVersionService;
import com.ruoyi.system.service.IFolderInfoService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysDeptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import com.mongodb.client.result.UpdateResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Set;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 证件版本表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class CertVersionServiceImpl implements ICertVersionService {
    
    private static final Logger logger = LoggerFactory.getLogger(CertVersionServiceImpl.class);
    
    private final CertVersionRepository certVersionRepository;
    private final IFolderInfoService folderInfoService;
    private final ISysUserService userService;
    private final ISysDeptService deptService;

    @Autowired
    private MongoTemplate mongoTemplate;

    public CertVersionServiceImpl(CertVersionRepository certVersionRepository,
                                 IFolderInfoService folderInfoService,
                                 ISysUserService userService,
                                 ISysDeptService deptService) {
        this.certVersionRepository = certVersionRepository;
        this.folderInfoService = folderInfoService;
        this.userService = userService;
        this.deptService = deptService;
    }

    // ========== 用户要求的四个核心方法 ==========

    /**
     * 从文件夹创建新版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CertVersion createVersionFromFolder(VersionCreateFromFolderDTO dto) {
        logger.info("开始从文件夹创建新版本: folderId={}, versionCode={}", dto.getFolderId(), dto.getVersionCode());
        
        try {
            // 1. 校验：查找 FolderInfo，确保其存在且未被关联
            // 注意：前端传递的是folderId（业务ID），需要使用正确的查找方法
            FolderInfo folderInfo = folderInfoService.getFolderByFolderId(dto.getFolderId());
            if (folderInfo == null) {
                throw new RuntimeException("文件夹不存在: " + dto.getFolderId());
            }
            
            if (folderInfo.getStatus() == "associated") {
                throw new RuntimeException("文件夹已经关联到版本: " + folderInfo.getAssociationInfo().getVersionCode());
            }
            
            // 2. 创建版本：新建 CertVersion 对象，从 FolderInfo 继承元数据
            CertVersion newVersion = new CertVersion();
            
            // 生成版本ID
            String versionId = "V" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
            newVersion.setVersionId(versionId);

            // 从 DTO 获取 versionCode
            newVersion.setVersionCode(dto.getVersionCode());
            newVersion.setDescription(dto.getDescription());
            
            // 从 FolderInfo 继承元数据
            newVersion.setCountryInfo(folderInfo.getCountryInfo());
            newVersion.setCertInfo(folderInfo.getCertInfo());
            newVersion.setIssueYear(folderInfo.getIssueYear());
            newVersion.setMainPicPath(folderInfo.getMainPicPath()); // 继承主图路径

            // 设置 standardFolderId 为 folderId
            newVersion.setStandardFolderId(dto.getFolderId());
            
            // 自动获取当前登录用户信息
            Long currentUserId = SecurityUtils.getUserId();
            Long currentDeptId = SecurityUtils.getDeptId();

            // 设置创建者信息
            newVersion.setCreatorInfo(getCurrentUserInfo(currentUserId));
            newVersion.setDeptInfo(getCurrentDeptInfo(currentDeptId));
            
            // 设置时间戳
            newVersion.setCreateTime(new Date());
            newVersion.setUpdateTime(new Date());
            newVersion.setStatus("1"); // 启用状态
            
            // 3. 保存版本：调用 certVersionRepository.save() 保存新版本
            CertVersion savedVersion = certVersionRepository.save(newVersion);
            logger.info("成功保存新版本: {}", savedVersion.getVersionId());

            // 4. 关联文件夹：调用 folderInfoService.associateFolderToVersion()
            boolean associateSuccess = folderInfoService.associateFolderToVersion(dto.getFolderId(), savedVersion.getVersionId(),"CREATED_NEW");
            if (!associateSuccess) {
                throw new RuntimeException("关联文件夹到版本失败");
            }
            
            // 5. 更新文件夹类型：将其 folderType 更新为 "standard" 并保存
            FolderInfo updatedFolder = folderInfoService.getFolderByMongoId(dto.getFolderId());
            if (updatedFolder != null) {
                updatedFolder.setFolderType("standard");
                updatedFolder.setUpdateTime(new Date());
                folderInfoService.updateFolder(updatedFolder);
                logger.info("成功更新文件夹类型为标准样本");
            }
            
            // 6. 返回新创建的 CertVersion 对象
            logger.info("成功从文件夹创建新版本: {}", savedVersion.getVersionId());
            return savedVersion;
            
        } catch (Exception e) {
            logger.error("从文件夹创建新版本失败: {}", e.getMessage(), e);
            throw new RuntimeException("从文件夹创建新版本失败: " + e.getMessage(), e);
        }
    }

    /**
     * 管理文件夹类型（设为标准/普通）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFolderType(String versionId, String folderId, String newType) {
        logger.info("开始更新文件夹类型: versionId={}, folderId={}, newType={}", versionId, folderId, newType);
        
        try {
            // 1. 校验：查找 CertVersion 和 FolderInfo，并验证文件夹确实属于该版本
            CertVersion version = certVersionRepository.findByVersionId(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }
            
            FolderInfo folder = folderInfoService.getFolderByFolderId(folderId);
            if (folder == null) {
                throw new RuntimeException("文件夹不存在: " + folderId);
            }
            
            if (!versionId.equals(folder.getAssociationInfo().getVersionId())) {
                throw new RuntimeException("文件夹不属于该版本");
            }
            
            // 2. 分支逻辑处理
            if ("standard".equals(newType)) {
                // 如果 newType 是 "standard"，处理旧标准文件夹的降级
                handleStandardFolderPromotion(versionId, folderId, version, folder);
            } else if ("regular".equals(newType)) {
                // 如果 newType 是 "regular"，处理当前标准文件夹的降级
                handleStandardFolderDemotion(versionId, folderId, version, folder);
            } else {
                throw new RuntimeException("不支持的文件夹类型: " + newType);
            }
            
            logger.info("成功更新文件夹类型: versionId={}, folderId={}, newType={}", versionId, folderId, newType);
            
        } catch (Exception e) {
            logger.error("更新文件夹类型失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新文件夹类型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询版本列表（按国家和证件类型）
     */
    @Override
    public List<CertVersion> listVersions(Integer countryId, String certType) {
        try {
            logger.info("查询版本列表: countryId={}, certType={}", countryId, certType);

            Query query = new Query();

            if (countryId != null) {
                query.addCriteria(Criteria.where("countryInfo.id").is(countryId.longValue()));
            }

            if (StringUtils.hasText(certType)) {
                query.addCriteria(Criteria.where("certInfo.zjlbdm").is(certType));
            }

            query.with(Sort.by(Sort.Direction.DESC, "createTime"));

            List<CertVersion> versions = mongoTemplate.find(query, CertVersion.class, "cert_version");
            logger.info("查询到版本数量: {}", versions.size());

            return versions;

        } catch (Exception e) {
            logger.error("查询版本列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询版本列表（按查询条件）
     */
    @Override
    public List<CertVersion> listVersions(VersionQueryDTO query) {
        try {
            logger.info("查询版本列表: {}", query);

            Query mongoQuery = new Query();

            // 按国家ID筛选
            if (query.getCountryId() != null) {
                mongoQuery.addCriteria(Criteria.where("countryInfo.id").is(query.getCountryId().longValue()));
            }

            // 按证件类型筛选
            if (StringUtils.hasText(query.getCertType())) {
                mongoQuery.addCriteria(Criteria.where("certInfo.zjlbdm").is(query.getCertType()));
            }

            // 按发行年份筛选
            if (StringUtils.hasText(query.getIssueYear())) {
                mongoQuery.addCriteria(Criteria.where("issueYear").is(query.getIssueYear()));
            }

            // 按状态筛选
            if (StringUtils.hasText(query.getStatus())) {
                mongoQuery.addCriteria(Criteria.where("status").is(query.getStatus()));
            }

            // 按是否需要人工确认筛选
            if (query.getNeedManualCheck() != null) {
                mongoQuery.addCriteria(Criteria.where("needManualCheck").is(query.getNeedManualCheck()));
            }

            // 关键词搜索（在版本代码和描述中搜索）
            if (StringUtils.hasText(query.getKeyword())) {
                Criteria keywordCriteria = new Criteria().orOperator(
                    Criteria.where("versionCode").regex(query.getKeyword(), "i"),
                    Criteria.where("description").regex(query.getKeyword(), "i")
                );
                mongoQuery.addCriteria(keywordCriteria);
            }

            // 按创建时间倒序排列
            mongoQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));

            List<CertVersion> versions = mongoTemplate.find(mongoQuery, CertVersion.class, "cert_version");
            logger.info("查询到版本数量: {}", versions.size());

            return versions;

        } catch (Exception e) {
            logger.error("查询版本列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取版本详情
     */
    @Override
    public CertVersion getVersionDetails(String versionId) {
        // TODO: 实现版本详情查询逻辑
        logger.info("获取版本详情: {}", versionId);
        return certVersionRepository.findByVersionId(versionId);
    }

    // ========== 辅助方法 ==========

    /**
     * 处理标准文件夹提升（将文件夹设为标准）
     */
    private void handleStandardFolderPromotion(String versionId, String folderId, CertVersion version, FolderInfo folder) {
        // 1. 如果版本已有标准文件夹，先将其降级为普通文件夹
        if (version.getStandardFolderId() != null && !version.getStandardFolderId().equals(folderId)) {
            FolderInfo oldStandardFolder = folderInfoService.getFolderByMongoId(version.getStandardFolderId());
            if (oldStandardFolder != null) {
                oldStandardFolder.setFolderType("regular");
                oldStandardFolder.setUpdateTime(new Date());
                folderInfoService.updateFolder(oldStandardFolder);
                logger.info("已将旧标准文件夹降级为普通文件夹: {}", version.getStandardFolderId());
            }
        }
        
        // 2. 更新版本的标准文件夹ID
        version.setStandardFolderId(folderId);
        version.setUpdateTime(new Date());
        certVersionRepository.save(version);
        
        // 3. 更新新标准文件夹
        folder.setFolderType("standard");
        folder.setUpdateTime(new Date());
        folderInfoService.updateFolder(folder);
        
        logger.info("成功设置标准文件夹: versionId={}, folderId={}", versionId, folderId);
    }

    /**
     * 处理标准文件夹降级（将标准文件夹设为普通）
     */
    private void handleStandardFolderDemotion(String versionId, String folderId, CertVersion version, FolderInfo folder) {
        // 1. 检查是否是当前的标准文件夹
        if (folderId.equals(version.getStandardFolderId())) {
            // 2. 清除版本的标准文件夹ID
            version.setStandardFolderId(null);
            version.setUpdateTime(new Date());
            certVersionRepository.save(version);
            
            logger.info("已清除版本的标准文件夹: versionId={}", versionId);
        }
        
        // 3. 更新文件夹类型为普通
        folder.setFolderType("regular");
        folder.setUpdateTime(new Date());
        folderInfoService.updateFolder(folder);
        
        logger.info("成功降级标准文件夹为普通文件夹: versionId={}, folderId={}", versionId, folderId);
    }

    /**
     * 获取当前用户信息
     */
    private SysUser getCurrentUserInfo(Long userId) {
        try {
            if (userId == null) {
                logger.warn("用户ID为空，返回默认用户信息");
                SysUser defaultUser = new SysUser();
                defaultUser.setUserId(0L);
                defaultUser.setUserName("system");
                defaultUser.setNickName("系统用户");
                return defaultUser;
            }

            SysUser userInfo = userService.selectUserById(userId);
            if (userInfo == null) {
                logger.warn("未找到用户信息，userId: {}", userId);
                SysUser defaultUser = new SysUser();
                defaultUser.setUserId(userId);
                defaultUser.setUserName("unknown");
                defaultUser.setNickName("未知用户");
                return defaultUser;
            }

            logger.debug("获取用户信息成功: userId={}, userName={}", userId, userInfo.getUserName());
            return userInfo;
        } catch (Exception e) {
            logger.error("获取用户信息失败，userId: {}, 错误: {}", userId, e.getMessage(), e);
            SysUser defaultUser = new SysUser();
            defaultUser.setUserId(userId);
            defaultUser.setUserName("error");
            defaultUser.setNickName("获取失败");
            return defaultUser;
        }
    }

    /**
     * 获取当前部门信息
     */
    private SysDept getCurrentDeptInfo(Long deptId) {
        try {
            if (deptId == null) {
                logger.warn("部门ID为空，返回默认部门信息");
                SysDept defaultDept = new SysDept();
                defaultDept.setDeptId(0L);
                defaultDept.setDeptName("系统部门");
                return defaultDept;
            }

            SysDept deptInfo = deptService.selectDeptById(deptId);
            if (deptInfo == null) {
                logger.warn("未找到部门信息，deptId: {}", deptId);
                SysDept defaultDept = new SysDept();
                defaultDept.setDeptId(deptId);
                defaultDept.setDeptName("未知部门");
                return defaultDept;
            }

            logger.debug("获取部门信息成功: deptId={}, deptName={}", deptId, deptInfo.getDeptName());
            return deptInfo;
        } catch (Exception e) {
            logger.error("获取部门信息失败，deptId: {}, 错误: {}", deptId, e.getMessage(), e);
            SysDept defaultDept = new SysDept();
            defaultDept.setDeptId(deptId);
            defaultDept.setDeptName("获取失败");
            return defaultDept;
        }
    }

    // ========== 核心查询方法实现 ==========

    @Override
    public List<CertVersion> listVersionsWithFilter(Integer countryId, String certType, String versionStatus, Boolean needManualCheck) {
        // 委托给统一的查询方法
        VersionQueryDTO query = new VersionQueryDTO();
        query.setCountryId(countryId != null ? countryId.longValue() : null);
        query.setCertType(certType);
        query.setStatus(versionStatus);
        query.setNeedManualCheck(needManualCheck);

        return listVersions(query);
    }

    @Override
    public Map<String, Object> listVersionsWithFilterPaged(Integer countryId, String certType, String versionStatus, Boolean needManualCheck, Integer pageNum, Integer pageSize) {
        // 委托给统一的分页查询方法
        return getVersionsWithSamplesPaged(countryId, pageNum, pageSize, certType, versionStatus, null);
    }

    @Override
    public CertVersion getVersion(String id) {
        try {
            logger.info("根据ID获取版本: {}", id);
            return certVersionRepository.findById(id).orElse(null);
        } catch (Exception e) {
            logger.error("根据ID获取版本失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public CertVersion getVersionByVersionId(String versionId) {
        try {
            logger.info("根据版本ID获取版本: {}", versionId);
            return certVersionRepository.findByVersionId(versionId);
        } catch (Exception e) {
            logger.error("根据版本ID获取版本失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public CertVersion findByVersionCode(String versionCode) {
        try {
            logger.info("根据版本号查找版本: {}", versionCode);
            Query query = new Query(Criteria.where("versionCode").is(versionCode));
            CertVersion version = mongoTemplate.findOne(query, CertVersion.class, "cert_version");
            logger.info("查找版本结果: {}", version != null ? "找到版本" : "未找到版本");
            return version;
        } catch (Exception e) {
            logger.error("根据版本号查找版本失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public CertVersion addVersion(CertVersion version) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public CertVersion updateVersion(CertVersion version) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public void deleteVersion(String id) {
        try {
            logger.info("删除版本: {}", id);
            certVersionRepository.deleteById(id);
            logger.info("删除版本成功: {}", id);
        } catch (Exception e) {
            logger.error("删除版本失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除版本失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CertVersion updateExtraAttributes(String id, Map<String, Object> extraAttributes) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public List<Map<String, Object>> getVersionsWithSamples(Integer countryId) {
        // TODO: 实现原有方法
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getAllVersionsWithSamples() {
        // TODO: 实现原有方法
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getCertTypesWithSamples(Integer countryId) {
        // TODO: 实现原有方法
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getAllCertTypesWithSamples() {
        // TODO: 实现原有方法
        return new ArrayList<>();
    }

    @Override
    public List<CertVersion> batchAddVersions(List<CertVersion> versions) {
        // TODO: 实现原有方法
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> createVersionsFromFolders(List<String> folderNames) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public CertVersion createVersionWithImageUpload(String folderName, MultipartFile imageFile, String localImagePath) throws Exception {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public CertVersion findOrCreateVersionByFolderName(String folderName) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public Map<String, String> parseFolderNameForVersion(String folderName) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public String getCountryCodeByName(String countryName) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public String getCertTypeByCategory(String categoryName) {
        // TODO: 实现原有方法
        return null;
    }

    @Override
    public CertVersion findBestMatchingVersion(String folderName, String certType, String issueYear, String countryCode) {
        // TODO: 实现智能版本匹配逻辑
        logger.info("查找最佳匹配版本: folderName={}, certType={}, issueYear={}, countryCode={}",
                   folderName, certType, issueYear, countryCode);
        return null;
    }

    @Override
    public CertVersion createUnifiedVersion(CertVersionCreateDTO request) {
        // TODO: 实现统一版本创建逻辑
        logger.info("创建统一版本: {}", request);
        return null;
    }

    @Override
    public Map<String, Object> unifyVersionDataStructure() {
        // TODO: 实现数据结构统一迁移逻辑
        logger.info("统一版本数据结构");
        return null;
    }

    @Override
    public List<Map<String, Object>> getVersionsWithSamplesRefreshed(Integer countryId, boolean forceRefresh) {
        // TODO: 实现带刷新的版本查询逻辑
        logger.info("获取版本信息（支持刷新）: countryId={}, forceRefresh={}", countryId, forceRefresh);
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getVersionsWithSamplesPaged(Integer countryId, int pageNum, int pageSize,
                                                          String certType, String versionStatus, String keyword) {
        try {
            logger.info("分页获取版本信息: countryId={}, pageNum={}, pageSize={}, certType={}, versionStatus={}, keyword={}",
                       countryId, pageNum, pageSize, certType, versionStatus, keyword);

            // 构建查询条件
            Query query = new Query();

            // 按国家ID筛选
            if (countryId != null) {
                query.addCriteria(Criteria.where("countryInfo.id").is(countryId.longValue()));
            }

            // 按证件类型筛选
            if (StringUtils.hasText(certType)) {
                query.addCriteria(Criteria.where("certInfo.zjlbdm").is(certType));
            }

            // 按版本状态筛选
            if (StringUtils.hasText(versionStatus)) {
                query.addCriteria(Criteria.where("status").is(versionStatus));
            }

            // 关键词搜索
            if (StringUtils.hasText(keyword)) {
                Criteria keywordCriteria = new Criteria().orOperator(
                    Criteria.where("versionCode").regex(keyword, "i"),
                    Criteria.where("description").regex(keyword, "i")
                );
                query.addCriteria(keywordCriteria);
            }

            // 按创建时间倒序排列
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));

            // 计算总数
            long total = mongoTemplate.count(query, CertVersion.class, "cert_version");

            // 分页查询
            if (pageNum > 0 && pageSize > 0) {
                query.skip((long) (pageNum - 1) * pageSize).limit(pageSize);
            }

            List<CertVersion> versions = mongoTemplate.find(query, CertVersion.class, "cert_version");
            logger.info("分页查询到版本数量: {}, 总数: {}", versions.size(), total);

            // 为每个版本统计关联的文件夹数量
            List<Map<String, Object>> versionsWithFolderCount = new ArrayList<>();
            for (CertVersion version : versions) {
                Map<String, Object> versionMap = new HashMap<>();

                // 复制版本的所有字段
                versionMap.put("id", version.getId());
                versionMap.put("versionId", version.getVersionId());
                versionMap.put("versionCode", version.getVersionCode());
                versionMap.put("description", version.getDescription());
                versionMap.put("countryInfo", version.getCountryInfo());
                versionMap.put("certInfo", version.getCertInfo());
                versionMap.put("issueYear", version.getIssueYear());
                versionMap.put("status", version.getStatus());
                versionMap.put("standardFolderId", version.getStandardFolderId());
                versionMap.put("mainPicPath", version.getMainPicPath());
                versionMap.put("trainingImage", version.getTrainingImage());
                versionMap.put("deptInfo", version.getDeptInfo());
                versionMap.put("creatorInfo", version.getCreatorInfo());
                versionMap.put("createTime", version.getCreateTime());
                versionMap.put("updateTime", version.getUpdateTime());

                // 统计关联的文件夹数量
                Query folderQuery = new Query(Criteria.where("associationInfo.versionId").is(version.getVersionId()));
                long folderCount = mongoTemplate.count(folderQuery, FolderInfo.class, "folder_info");
                versionMap.put("folderCount", folderCount);

                versionsWithFolderCount.add(versionMap);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("rows", versionsWithFolderCount);
            result.put("total", total);

            return result;

        } catch (Exception e) {
            logger.error("分页获取版本信息失败: {}", e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("rows", new ArrayList<>());
            result.put("total", 0L);
            return result;
        }
    }

    @Override
    public List<Map<String, Object>> getVersionFolders(String versionId) {
        try {
            logger.info("获取版本[{}]下的文件夹列表", versionId);

            // 1. 验证版本是否存在
            CertVersion version = certVersionRepository.findByVersionId(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }

            // 2. 查询关联的文件夹 - 修复查询条件
            Query query = new Query(Criteria.where("associationInfo.versionId").is(versionId));
            List<FolderInfo> folders = mongoTemplate.find(query, FolderInfo.class, "folder_info");

            // 3. 构建返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            for (FolderInfo folder : folders) {
                Map<String, Object> folderData = new HashMap<>();
                folderData.put("folderId", folder.getFolderId());
                folderData.put("folderName", folder.getFolderName());
                folderData.put("filename", folder.getFilename());
                folderData.put("status", folder.getStatus());
                folderData.put("reviewStatus", folder.getReviewStatus());
                folderData.put("createTime", folder.getCreateTime());
                folderData.put("updateTime", folder.getUpdateTime());
                
                // 添加 folderType 字段
                folderData.put("folderType", folder.getFolderType());
                
                // 添加 mainPicPath 字段（前端需要）
                folderData.put("mainPicPath", folder.getMainPicPath());

                // 统计图片数量
                Query imageQuery = new Query(Criteria.where("folderId").is(folder.getFolderId()));
                long imageCount = mongoTemplate.count(imageQuery, "image_repository");
                folderData.put("imageCount", imageCount);

                result.add(folderData);
            }

            logger.info("版本[{}]下共有{}个文件夹", versionId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取版本文件夹列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取版本文件夹列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void setStandardFolder(String versionId, String folderId) {
        try {
            logger.info("设置版本[{}]的标准样本文件夹: {}", versionId, folderId);

            // 1. 验证版本是否存在
            CertVersion version = certVersionRepository.findByVersionId(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }

            // 2. 验证文件夹是否存在且属于该版本 - 修复查询条件
            Query folderQuery = new Query(Criteria.where("folderId").is(folderId).and("associationInfo.versionId").is(versionId));
            FolderInfo folder = mongoTemplate.findOne(folderQuery, FolderInfo.class, "folder_info");
            if (folder == null) {
                throw new RuntimeException("文件夹不存在或不属于该版本: " + folderId);
            }

            // 3. 验证文件夹状态
            if (!"associated".equals(folder.getStatus())) {
                throw new RuntimeException("只有已关联的文件夹才能设为标准样本");
            }

            if (!"approved".equals(folder.getReviewStatus())) {
                throw new RuntimeException("只有审核通过的文件夹才能设为标准样本");
            }

            // 4. 记录原标准样本文件夹（用于日志）
            String oldStandardFolderId = version.getStandardFolderId();
            if (oldStandardFolderId != null && !oldStandardFolderId.equals(folderId)) {
                logger.info("版本[{}]原标准样本文件夹[{}]将被替换为[{}]", versionId, oldStandardFolderId, folderId);
            }

            // 5. 如果有原标准样本文件夹，先将其恢复为普通文件夹
            if (oldStandardFolderId != null && !oldStandardFolderId.equals(folderId)) {
                updateFolderType(oldStandardFolderId, "regular");
                updateImagesSampleType(oldStandardFolderId, "ordinary");
                logger.info("已将原标准样本文件夹[{}]恢复为普通文件夹，图片不可标注", oldStandardFolderId);
            }

            // 6. 更新版本的标准文件夹ID
            version.setStandardFolderId(folderId);
            version.setUpdateTime(new Date());
            certVersionRepository.save(version);

            // 7. 更新文件夹类型为标准样本
            updateFolderType(folderId, "standard");
            logger.info("已将文件夹[{}]设为标准样本类型", folderId);

            // 8. 将新标准样本文件夹的图片设为标准样本，可标注（用于AI训练）
            updateImagesSampleType(folderId, "standard");
            logger.info("已将标准样本文件夹[{}]的图片设为标准样本，可标注", folderId);

            logger.info("成功设置版本[{}]的标准样本文件夹: {}", versionId, folderId);

        } catch (Exception e) {
            logger.error("设置标准样本文件夹失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置标准样本文件夹失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新文件夹类型
     */
    private void updateFolderType(String folderId, String folderType) {
        try {
            logger.info("更新文件夹[{}]类型为: {}", folderId, folderType);

            Query query = new Query(Criteria.where("folderId").is(folderId));
            Update update = new Update()
                    .set("folderType", folderType)
                    .set("updateTime", new Date());

            UpdateResult result = mongoTemplate.updateFirst(query, update, "folder_info");
            logger.info("成功更新文件夹[{}]类型为: {}, 影响记录数: {}", folderId, folderType, result.getModifiedCount());

        } catch (Exception e) {
            logger.error("更新文件夹类型失败: folderId={}, folderType={}, 错误: {}", folderId, folderType, e.getMessage(), e);
            throw new RuntimeException("更新文件夹类型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新指定文件夹下所有图片的样本类型（已废弃）
     *
     * 注意：此方法已废弃，因为：
     * 1. sampleType字段已从ImageRepository移除，现在存储在FolderInfo中
     * 2. isAnnotatable字段已删除，现在使用isAnnotatableType基于图片类型判断
     * 3. 标注权限现在基于folderType和isAnnotatableType自动判断
     */
    @Deprecated
    private void updateImagesSampleType(String folderId, String sampleType) {
        try {
            logger.info("updateImagesSampleType方法已废弃，folderId: {}, sampleType: {}", folderId, sampleType);
            logger.info("样本类型现在存储在FolderInfo.folderType中，标注权限基于图片类型自动判断");

            // 此方法已废弃，不再执行任何更新操作
            // 如果需要更新样本类型，应该更新FolderInfo.folderType字段

        } catch (Exception e) {
            logger.error("更新图片样本类型失败: folderId={}, sampleType={}, 错误: {}",
                        folderId, sampleType, e.getMessage(), e);
            throw new RuntimeException("更新图片样本类型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新文件夹下所有图片的标注权限（已废弃）
     *
     * 注意：此方法已废弃，因为：
     * 1. isAnnotatable字段已删除，现在使用isAnnotatableType基于图片类型判断
     * 2. sampleType字段已从ImageRepository移除，现在存储在FolderInfo中
     * 3. 标注权限现在基于folderType和isAnnotatableType自动判断
     */
    @Deprecated
    private void updateImagesAnnotationPermission(String folderId, boolean isAnnotatable) {
        try {
            logger.info("updateImagesAnnotationPermission方法已废弃，folderId: {}, isAnnotatable: {}", folderId, isAnnotatable);
            logger.info("标注权限现在基于FolderInfo.folderType和ImageRepository.isAnnotatableType自动判断");

            // 此方法已废弃，不再执行任何更新操作
            // 标注权限现在通过以下逻辑判断：
            // 1. 文件夹必须是标准样本（folderType='standard'）
            // 2. 图片必须是可标注类型（isAnnotatableType=true）

        } catch (Exception e) {
            logger.error("更新图片标注权限失败: folderId={}, isAnnotatable={}, 错误: {}",
                        folderId, isAnnotatable, e.getMessage(), e);
            throw new RuntimeException("更新图片标注权限失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void removeStandardFolder(String versionId) {
        try {
            logger.info("取消版本[{}]的标准样本文件夹设置", versionId);

            // 1. 验证版本是否存在
            CertVersion version = certVersionRepository.findByVersionId(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }

            // 2. 如果有标准文件夹，先将其设为普通文件夹并恢复图片标注权限
            String currentStandardFolderId = version.getStandardFolderId();
            if (currentStandardFolderId != null) {
                updateFolderType(currentStandardFolderId, "regular");
                logger.info("已将原标准样本文件夹[{}]设为普通文件夹", currentStandardFolderId);

                updateImagesAnnotationPermission(currentStandardFolderId, true);
                logger.info("已将原标准样本文件夹[{}]的图片恢复为可标注", currentStandardFolderId);
            }

            // 3. 清除标准文件夹ID
            version.setStandardFolderId(null);
            version.setUpdateTime(new Date());
            certVersionRepository.save(version);

            logger.info("成功取消版本[{}]的标准样本文件夹设置", versionId);

        } catch (Exception e) {
            logger.error("取消标准样本文件夹失败: {}", e.getMessage(), e);
            throw new RuntimeException("取消标准样本文件夹失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CertVersion> findSimilarVersions(String certType, String issueYear, String countryCode) {
        // TODO: 实现相似版本查找逻辑
        logger.info("查找相似版本: certType={}, issueYear={}, countryCode={}", certType, issueYear, countryCode);
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void fixStandardFolderData(String versionId) {
        try {
            logger.info("开始修复版本[{}]的标准样本数据", versionId);

            // 1. 验证版本是否存在
            CertVersion version = certVersionRepository.findByVersionId(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }

            // 2. 如果版本有标准文件夹，修复其数据
            String standardFolderId = version.getStandardFolderId();
            if (standardFolderId != null) {
                logger.info("修复标准文件夹[{}]的数据", standardFolderId);

                // 2.1 更新文件夹类型为标准
                updateFolderType(standardFolderId, "standard");
                logger.info("已将文件夹[{}]类型设为标准", standardFolderId);

                // 2.2 更新文件夹下所有图片为不可标注（但保持普通样本类型）
                updateImagesAnnotationPermission(standardFolderId, false);
                logger.info("已将标准样本文件夹[{}]下的图片设为不可标注", standardFolderId);
            }

            // 3. 将版本下其他文件夹设为普通类型
            Query folderQuery = new Query(Criteria.where("versionId").is(versionId));
            if (standardFolderId != null) {
                folderQuery.addCriteria(Criteria.where("folderId").ne(standardFolderId));
            }

            Update folderUpdate = new Update()
                    .set("folderType", "regular")
                    .set("updateTime", new Date());

            UpdateResult folderResult = mongoTemplate.updateMulti(folderQuery, folderUpdate, "folder_info");
            logger.info("已将版本[{}]下{}个非标准文件夹设为普通类型", versionId, folderResult.getModifiedCount());

            // 4. 将其他文件夹下的图片设为普通样本
            Query imageQuery = new Query(Criteria.where("versionId").is(versionId));
            if (standardFolderId != null) {
                imageQuery.addCriteria(Criteria.where("folderId").ne(standardFolderId));
            }

            Update imageUpdate = new Update()
                    .set("sampleType", "ordinary")
                    .set("isAnnotatable", false)
                    .set("updateTime", new Date());

            UpdateResult imageResult = mongoTemplate.updateMulti(imageQuery, imageUpdate, "image_repository");
            logger.info("已将版本[{}]下{}张非标准图片设为普通样本", versionId, imageResult.getModifiedCount());

            logger.info("成功修复版本[{}]的标准样本数据", versionId);

        } catch (Exception e) {
            logger.error("修复标准样本数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("修复标准样本数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CertVersion> getVersionsByTaskId(String taskId) {
        try {
            logger.info("根据任务ID获取版本列表: taskId={}", taskId);

            // 1. 根据任务ID查找相关的文件夹
            Query folderQuery = new Query(Criteria.where("taskId").is(taskId));
            List<FolderInfo> folders = mongoTemplate.find(folderQuery, FolderInfo.class, "folder_info");
            
            if (folders.isEmpty()) {
                logger.info("任务[{}]下没有找到任何文件夹", taskId);
                return new ArrayList<>();
            }

            // 2. 提取所有不重复的版本ID
            Set<String> versionIds = folders.stream()
                    .map(folder -> folder.getAssociationInfo() != null ? folder.getAssociationInfo().getVersionId() : null)
                    .filter(Objects::nonNull)
                    .filter(versionId -> !versionId.trim().isEmpty())
                    .collect(Collectors.toSet());

            if (versionIds.isEmpty()) {
                logger.info("任务[{}]下的文件夹都没有关联到版本", taskId);
                return new ArrayList<>();
            }

            // 3. 根据版本ID获取版本详情
            Query versionQuery = new Query(Criteria.where("versionId").in(versionIds));
            List<CertVersion> versions = mongoTemplate.find(versionQuery, CertVersion.class, "cert_version");

            // 4. 为每个版本添加文件夹数量信息
            for (CertVersion version : versions) {
                long folderCount = folders.stream()
                        .filter(folder -> folder.getAssociationInfo() != null && 
                                version.getVersionId().equals(folder.getAssociationInfo().getVersionId()))
                        .count();
                
                // 将文件夹数量存储在扩展属性中（如果CertVersion有扩展属性字段）
                // 或者可以通过其他方式返回这个信息
                logger.debug("版本[{}]包含{}个文件夹", version.getVersionId(), folderCount);
            }

            logger.info("成功获取任务[{}]的版本列表，共{}个版本", taskId, versions.size());
            return versions;

        } catch (Exception e) {
            logger.error("根据任务ID获取版本列表失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("根据任务ID获取版本列表失败: " + e.getMessage(), e);
        }
    }
}
