import base64
import json
import os
import re
from datetime import datetime
from time import sleep
import time
import requests
import xml.etree.ElementTree as ET
from minio import Minio
from pymongo import MongoClient
import logging
from urllib.parse import urlparse
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchImageProcessor:
    def __init__(self):
        # MinIO配置
        self.minio_client = Minio(
            'localhost:9000',
            access_key='minioadmin',
            secret_key='minioadmin',
            secure=False
        )
        
        # MongoDB配置
        self.mongo_client = MongoClient('mongodb://localhost:27017/')
        self.db = self.mongo_client['cert_database']
        self.batch_tasks = self.db['batch_upload_task']
        self.batch_images = self.db['batch_upload_image']
        self.versions = self.db['cert_version']
        self.countries = self.db['cert_country']
        
        # 图像识别API配置
        self.recognition_url = 'http://127.0.0.1:18080/bj/optical/inspection'
        
        # 中文光源类型映射
        self.light_type_mapping = {
            '常光': 'visible',
            '可见光': 'visible',
            '红外': 'infrared',
            '红外光': 'infrared',
            '红外左侧光': 'infrared_left',
            '红外右侧光': 'infrared_right',
            '左侧光': 'infrared_left',
            '右侧光': 'infrared_right',
            '紫外': 'ultraviolet',
            '紫外光': 'ultraviolet',
            '激光全息图': 'laser_hologram',
            '激光全息图1': 'laser_hologram',
            '激光全息图2': 'laser_hologram',
            '激光全息图3': 'laser_hologram'
        }

    def process_batch_task(self, task_id):
        """处理批量任务"""
        try:
            logger.info(f"开始处理批量任务: {task_id}")
            
            # 获取任务信息
            task = self.batch_tasks.find_one({"task_id": task_id})
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            # 更新任务状态为处理中
            self.update_task_status(task_id, "PROCESSING")
            
            # 获取任务下的所有图片
            images = list(self.batch_images.find({"task_id": task_id}))
            total_images = len(images)
            
            logger.info(f"任务 {task_id} 共有 {total_images} 张图片需要处理")
            
            # 统计信息
            stats = {
                'recognized_versions': 0,
                'new_versions': 0,
                'failed_recognition': 0,
                'processed_count': 0
            }
            
            # 处理每张图片
            for i, image in enumerate(images):
                try:
                    logger.info(f"处理第 {i+1}/{total_images} 张图片: {image['file_name']}")
                    
                    # 只处理可见光图片进行版本识别
                    if image['light_type'] == 'visible':
                        success = self.process_visible_image(image, task['folder_name'])
                        if success:
                            stats['recognized_versions'] += 1
                        else:
                            stats['failed_recognition'] += 1
                    
                    stats['processed_count'] += 1
                    
                    # 更新任务进度
                    self.update_task_progress(task_id, stats['processed_count'])
                    
                    # 记录处理日志
                    self.log_task_progress(task_id, f"已处理 {stats['processed_count']}/{total_images} 张图片")
                    
                except Exception as e:
                    logger.error(f"处理图片失败: {image['file_name']}, 错误: {str(e)}")
                    stats['failed_recognition'] += 1
                    self.log_task_error(task_id, f"处理图片失败: {image['file_name']}, 错误: {str(e)}")
            
            # 更新任务状态为完成
            self.update_task_status(task_id, "COMPLETED", stats)
            logger.info(f"批量任务 {task_id} 处理完成")
            
            return True
            
        except Exception as e:
            logger.error(f"处理批量任务失败: {task_id}, 错误: {str(e)}")
            self.update_task_status(task_id, "FAILED", error_message=str(e))
            return False

    def process_visible_image(self, image, folder_name):
        """处理可见光图片进行版本识别"""
        try:
            # 从MinIO下载图片
            image_data = self.download_image_from_minio(image['image_url'])
            if not image_data:
                logger.error(f"下载图片失败: {image['image_url']}")
                return False
            
            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 调用图像识别API
            success, recognition_result, message = self.recognize_image(image_base64, image['_id'])
            
            if success and recognition_result:
                # 解析识别结果，确定版本信息
                version_info = self.parse_recognition_result(recognition_result, folder_name)
                
                # 查找或创建版本
                version_id = self.find_or_create_version(version_info, folder_name)
                
                if version_id:
                    # 更新图片的版本信息
                    self.batch_images.update_one(
                        {"_id": image['_id']},
                        {
                            "$set": {
                                "version_id": version_id,
                                "recognition_result": recognition_result,
                                "update_time": datetime.now()
                            }
                        }
                    )
                    
                    # 更新同一任务下其他图片的版本信息
                    self.update_related_images_version(image['task_id'], version_id)
                    
                    logger.info(f"图片 {image['file_name']} 识别成功，版本ID: {version_id}")
                    return True
                else:
                    logger.error(f"创建版本失败: {image['file_name']}")
                    return False
            else:
                logger.error(f"图片识别失败: {image['file_name']}, 消息: {message}")
                return False
                
        except Exception as e:
            logger.error(f"处理可见光图片失败: {image['file_name']}, 错误: {str(e)}")
            return False

    def recognize_image(self, image_base64, image_id):
        """调用图像识别API"""
        try:
            headers = {
                'Content-Type': 'application/json;CHARSET=UTF-8',
            }
            
            data = {
                "cmd": 'get_db_data',
                "pro_id": str(image_id),
                "data": {
                    "vi": image_base64,
                }
            }
            
            start_time = time.time()
            response = requests.post(self.recognition_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                res_result = response.json()
                logger.info(f"识别API响应: {res_result}")
                
                if "data" in res_result:
                    data_result = res_result["data"]
                    if "xml_str" in data_result:
                        res_json = self.parse_xml_result(data_result["xml_str"])
                        end_time = time.time()
                        logger.info(f"图像识别耗时: {end_time - start_time:.2f}秒")
                        return True, res_json, "成功"
                    else:
                        return False, {}, f"{image_id}解析失败"
                else:
                    return False, {}, "API返回数据格式错误"
            else:
                logger.error(f"识别API调用失败: {response.text}")
                return False, {}, f"API调用失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"调用识别API异常: {str(e)}")
            return False, {}, f"API调用异常: {str(e)}"

    def parse_xml_result(self, base64_xml_str):
        """解析XML识别结果"""
        try:
            namespaces = {
                'ct': 'http://trdoccheck.bsi.bund.de/ct/4',
                'dc': 'http://trdoccheck.bsi.bund.de/dc/4',
                'dco': 'http://trdoccheck.bsi.bund.de/dco/4',
                'nc': 'http://trdoccheck.gdis.bjnja.com/nc/1'
            }
            
            xml_bytes = base64.b64decode(base64_xml_str)
            xml_string = xml_bytes.decode('utf-8')
            logger.debug(f"解析XML: {xml_string}")
            
            root = ET.fromstring(xml_string)
            items_element = root.find('.//nc:Items', namespaces)
            items_element_version = root.find('.//nc:Version', namespaces)
            
            items_dict = self.xml_to_dict(items_element) if items_element is not None else {}
            items_dict_version = self.xml_to_dict(items_element_version) if items_element_version is not None else {}
            
            # 构建结果字典
            json_result = {}
            
            # 基本信息
            field_mapping = {
                "PassportType": "cert_type",
                "CountryCode": "country_code", 
                "DateOfIssue": "issue_date",
                "DateOfExpiry": "expiry_date",
                "Authority": "authority",
                "PassportNo": "passport_no",
                "Names": "names",
                "Nationality": "nationality",
                "DateOfBirth": "birth_date",
                "PlaceOfBirth": "birth_place",
                "Sex": "sex"
            }
            
            for xml_key, json_key in field_mapping.items():
                if xml_key in items_dict:
                    json_result[json_key] = items_dict[xml_key]
            
            # 版本信息
            if "text" in items_dict_version:
                json_result["version"] = items_dict_version["text"]
            
            return json_result
            
        except Exception as e:
            logger.error(f"解析XML结果失败: {str(e)}")
            return {}

    def xml_to_dict(self, element):
        """XML转字典的辅助方法"""
        result = {}
        if element is None:
            return result
            
        if element.attrib:
            result.update(element.attrib)
        if element.text and element.text.strip():
            result['text'] = element.text.strip()
            
        for child in element:
            child_dict = self.xml_to_dict(child)
            if child.tag == '{http://trdoccheck.gdis.bjnja.com/nc/1}Item':
                name = child.attrib.get('name', '')
                formated = child.attrib.get('formated', '')
                if name and formated:
                    result[name] = formated
            else:
                tag_name = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if tag_name in result:
                    if not isinstance(result[tag_name], list):
                        result[tag_name] = [result[tag_name]]
                    result[tag_name].append(child_dict)
                else:
                    result[tag_name] = child_dict
        return result

    def parse_recognition_result(self, recognition_result, folder_name):
        """解析识别结果，提取版本信息"""
        version_info = {
            'country_code': '',
            'cert_type': '',
            'issue_year': '',
            'version_name': folder_name
        }
        
        try:
            # 从识别结果中提取国家代码
            if 'country_code' in recognition_result:
                version_info['country_code'] = recognition_result['country_code']
            
            # 从识别结果中提取证件类型
            if 'cert_type' in recognition_result:
                version_info['cert_type'] = recognition_result['cert_type']
            
            # 从签发日期中提取年份
            if 'issue_date' in recognition_result:
                issue_date = recognition_result['issue_date']
                # 尝试提取年份 (格式可能是 YYYY-MM-DD 或 YYYYMMDD)
                year_match = re.search(r'(\d{4})', issue_date)
                if year_match:
                    version_info['issue_year'] = year_match.group(1)
            
            # 如果没有从识别结果中获取到信息，尝试从文件夹名称中解析
            if not version_info['issue_year']:
                year_match = re.search(r'(\d{4})', folder_name)
                if year_match:
                    version_info['issue_year'] = year_match.group(1)
            
            # 从文件夹名称中提取国家信息
            if not version_info['country_code']:
                country_keywords = {
                    '俄罗斯': 'RUS',
                    '美国': 'USA', 
                    '英国': 'GBR',
                    '德国': 'DEU',
                    '法国': 'FRA',
                    '日本': 'JPN',
                    '韩国': 'KOR',
                    '意大利': 'ITA',
                    '西班牙': 'ESP',
                    '加拿大': 'CAN'
                }
                
                for country_name, country_code in country_keywords.items():
                    if country_name in folder_name:
                        version_info['country_code'] = country_code
                        break
            
            # 从文件夹名称中提取证件类型
            if not version_info['cert_type']:
                if '签证' in folder_name or 'visa' in folder_name.lower():
                    version_info['cert_type'] = 'VISA'
                elif '护照' in folder_name or 'passport' in folder_name.lower():
                    version_info['cert_type'] = 'PASSPORT'
                elif '身份证' in folder_name or 'id' in folder_name.lower():
                    version_info['cert_type'] = 'ID'
            
            logger.info(f"解析版本信息: {version_info}")
            return version_info
            
        except Exception as e:
            logger.error(f"解析识别结果失败: {str(e)}")
            return version_info

    def find_or_create_version(self, version_info, folder_name):
        """查找或创建版本"""
        try:
            # 构建查询条件
            query = {}
            if version_info['country_code']:
                query['country_code'] = version_info['country_code']
            if version_info['cert_type']:
                query['cert_type'] = version_info['cert_type']
            if version_info['issue_year']:
                query['issue_year'] = version_info['issue_year']
            
            # 查找现有版本
            existing_version = self.versions.find_one(query)
            
            if existing_version:
                logger.info(f"找到现有版本: {existing_version['version_id']}")
                return existing_version['version_id']
            else:
                # 创建新版本
                version_id = self.generate_version_id()
                
                new_version = {
                    'version_id': version_id,
                    'version_name': folder_name,
                    'country_code': version_info['country_code'],
                    'cert_type': version_info['cert_type'],
                    'issue_year': version_info['issue_year'],
                    'status': '1',
                    'is_custom': True,  # 标记为自定义版本
                    'create_time': datetime.now(),
                    'update_time': datetime.now(),
                    'created_by': 'batch_processor',
                    'remark': f'批量处理自动创建 - {folder_name}'
                }
                
                # 如果有国家代码，获取国家信息
                if version_info['country_code']:
                    country_info = self.countries.find_one({'code': version_info['country_code']})
                    if country_info:
                        new_version['country_info'] = country_info
                
                self.versions.insert_one(new_version)
                logger.info(f"创建新版本: {version_id}")
                
                return version_id
                
        except Exception as e:
            logger.error(f"查找或创建版本失败: {str(e)}")
            return None

    def update_related_images_version(self, task_id, version_id):
        """更新同一任务下其他图片的版本信息"""
        try:
            # 更新同一任务下所有图片的版本ID
            result = self.batch_images.update_many(
                {"task_id": task_id, "version_id": {"$exists": False}},
                {
                    "$set": {
                        "version_id": version_id,
                        "update_time": datetime.now()
                    }
                }
            )
            
            logger.info(f"更新了 {result.modified_count} 张图片的版本信息")
            
        except Exception as e:
            logger.error(f"更新相关图片版本失败: {str(e)}")

    def download_image_from_minio(self, image_url):
        """从MinIO下载图片"""
        try:
            # 解析MinIO URL
            parsed_url = urlparse(image_url)
            path_parts = parsed_url.path.strip('/').split('/')
            
            if len(path_parts) < 2:
                logger.error(f"无效的MinIO URL: {image_url}")
                return None
            
            bucket_name = path_parts[0]
            object_name = '/'.join(path_parts[1:])
            
            # 从MinIO下载对象
            response = self.minio_client.get_object(bucket_name, object_name)
            image_data = response.read()
            response.close()
            response.release_conn()
            
            return image_data
            
        except Exception as e:
            logger.error(f"从MinIO下载图片失败: {image_url}, 错误: {str(e)}")
            return None

    def detect_light_type_from_filename(self, filename):
        """从文件名检测光源类型"""
        filename_lower = filename.lower()
        
        for chinese_name, english_type in self.light_type_mapping.items():
            if chinese_name in filename:
                return english_type
        
        # 如果没有匹配到中文，尝试英文关键词
        english_mapping = {
            'visible': 'visible',
            'infrared': 'infrared',
            'uv': 'ultraviolet',
            'ultraviolet': 'ultraviolet',
            'laser': 'laser_hologram',
            'hologram': 'laser_hologram'
        }
        
        for keyword, light_type in english_mapping.items():
            if keyword in filename_lower:
                return light_type
        
        # 默认返回可见光
        return 'visible'

    def generate_version_id(self):
        """生成版本ID"""
        return f"V{uuid.uuid4().hex[:8]}"

    def update_task_status(self, task_id, status, stats=None, error_message=None):
        """更新任务状态"""
        try:
            update_data = {
                "status": status,
                "update_time": datetime.now()
            }
            
            if stats:
                update_data["result_stats"] = stats
            
            if error_message:
                update_data["error_message"] = error_message
            
            self.batch_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )
            
            logger.info(f"更新任务状态: {task_id} -> {status}")
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")

    def update_task_progress(self, task_id, processed_count):
        """更新任务进度"""
        try:
            self.batch_tasks.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "processed_images": processed_count,
                        "update_time": datetime.now()
                    }
                }
            )
        except Exception as e:
            logger.error(f"更新任务进度失败: {str(e)}")

    def log_task_progress(self, task_id, message):
        """记录任务进度日志"""
        try:
            log_entry = {
                "task_id": task_id,
                "timestamp": datetime.now(),
                "level": "INFO",
                "message": message
            }
            
            # 可以存储到专门的日志集合中
            self.db['batch_task_logs'].insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"记录任务日志失败: {str(e)}")

    def log_task_error(self, task_id, message):
        """记录任务错误日志"""
        try:
            log_entry = {
                "task_id": task_id,
                "timestamp": datetime.now(),
                "level": "ERROR",
                "message": message
            }
            
            self.db['batch_task_logs'].insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"记录任务错误日志失败: {str(e)}")

    def get_pending_tasks(self):
        """获取待处理的任务"""
        try:
            return list(self.batch_tasks.find({"status": "PENDING"}))
        except Exception as e:
            logger.error(f"获取待处理任务失败: {str(e)}")
            return []

def main():
    """主函数 - 可以作为定时任务运行"""
    processor = BatchImageProcessor()
    
    # 获取待处理的任务
    pending_tasks = processor.get_pending_tasks()
    
    if not pending_tasks:
        logger.info("没有待处理的任务")
        return
    
    logger.info(f"找到 {len(pending_tasks)} 个待处理任务")
    
    # 处理每个任务
    for task in pending_tasks:
        task_id = task['task_id']
        logger.info(f"开始处理任务: {task_id}")
        
        success = processor.process_batch_task(task_id)
        
        if success:
            logger.info(f"任务 {task_id} 处理成功")
        else:
            logger.error(f"任务 {task_id} 处理失败")
        
        # 任务间隔
        sleep(2)

if __name__ == '__main__':
    # 可以传入特定的task_id进行处理
    import sys
    
    if len(sys.argv) > 1:
        task_id = sys.argv[1]
        processor = BatchImageProcessor()
        processor.process_batch_task(task_id)
    else:
        main()