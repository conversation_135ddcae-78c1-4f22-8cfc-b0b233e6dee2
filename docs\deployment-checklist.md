# 批量上传自动创建Version功能部署检查清单

## 部署前检查

### 1. 环境要求 ✅
- [ ] Java 8+ 运行环境
- [ ] MongoDB 4.0+ 数据库
- [ ] MySQL 5.7+ 数据库
- [ ] MinIO 对象存储服务
- [ ] 若依框架基础环境

### 2. 代码文件检查 ✅
- [ ] `CertVersion.java` - 实体类已更新
- [ ] `CertVersionRepository.java` - 数据访问层已扩展
- [ ] `ICertVersionService.java` - 服务接口已扩展
- [ ] `CertVersionServiceImpl.java` - 核心业务逻辑已实现
- [ ] `CertBatchUploadServiceImpl.java` - 批量上传已集成自动创建Version功能
- [ ] `CertBatchUploadController.java` - 测试接口已添加
- [ ] `MongoIndexConfig.java` - 数据库索引已配置
- [ ] `BatchImagesUpload.vue` - 批量上传前端页面（已存在）
- [ ] `cert-batch-upload.js` - API接口已定义

## 数据库配置

### 1. MySQL菜单配置 ⚠️
选择以下方案之一执行：

#### 方案A：批量上传菜单配置（推荐）
```sql
-- 执行 sql/menu-batch-upload-integration.sql
-- 将创建证件管理菜单体系，包含批量上传功能
```
- [ ] 执行SQL脚本
- [ ] 验证菜单创建成功：证件管理 → 批量上传
- [ ] 检查角色权限分配

#### 方案B：手动配置
通过系统管理界面手动添加：
- [ ] 菜单名称：批量上传
- [ ] 路由地址：batch-upload
- [ ] 组件路径：cert/batch/BatchImagesUpload
- [ ] 权限标识：cert:batch:upload
- [ ] 分配给相关角色

### 2. MongoDB索引 ✅
- [ ] 应用启动时自动创建索引
- [ ] 检查日志确认索引创建成功

## 应用部署

### 1. 编译打包
```bash
# 后端编译
mvn clean package -Dmaven.test.skip=true

# 前端编译
npm run build:prod
```
- [ ] 后端编译成功
- [ ] 前端编译成功
- [ ] 无编译错误

### 2. 部署启动
- [ ] 部署应用到服务器
- [ ] 启动应用服务
- [ ] 检查启动日志无错误
- [ ] 检查MongoDB索引创建日志

## 功能验证

### 1. 菜单访问测试
- [ ] 登录系统
- [ ] 在菜单中找到"证件管理 → 批量上传"
- [ ] 点击菜单能正常跳转到批量上传页面
- [ ] 页面加载无错误

### 2. 批量上传功能测试
准备测试文件夹：
- [ ] 创建文件夹：`2021年胡志明市签发的加拿大签证`
- [ ] 创建文件夹：`2020年北京签发的美国护照`
- [ ] 创建文件夹：`2022年英国工作许可`
- [ ] 在每个文件夹中放入测试图片

执行上传测试：
- [ ] 选择包含测试文件夹的目录
- [ ] 系统正确扫描文件夹结构
- [ ] 显示文件夹预览信息
- [ ] 点击"开始上传"
- [ ] 上传过程无错误
- [ ] 上传完成显示成功信息

### 3. 自动创建Version验证
- [ ] 检查上传日志中的Version创建信息
- [ ] 在Version管理界面查看自动创建的Version记录
- [ ] 验证Version信息正确：
  - [ ] versionId格式正确（V + 8位字符）
  - [ ] versionName格式正确（如：CAN Visa 2021胡志明市版）
  - [ ] 状态为"0"（待确认）
  - [ ] needManualCheck为true
  - [ ] 国家信息映射正确
  - [ ] 证件类型映射正确

### 4. 图片关联验证
- [ ] 检查上传的图片记录
- [ ] 验证图片的versionId字段已正确设置
- [ ] 通过Version ID能查询到关联的图片
- [ ] 图片与文件夹的关联关系正确

### 5. 数据库验证
检查MongoDB中的数据：
```javascript
// 查看创建的Version记录
db.cert_version.find({needManualCheck: true}).pretty()

// 查看关联的图片记录
db.image_repository.find({versionId: {$exists: true}}).pretty()

// 检查索引
db.cert_version.getIndexes()
```
- [ ] Version记录创建正确
- [ ] 图片记录versionId关联正确
- [ ] 索引创建完整
- [ ] 数据结构符合预期

## 性能验证

### 1. 查询性能
- [ ] 测试文件夹名称查询速度
- [ ] 测试Version联合查询性能
- [ ] 检查数据库查询计划

### 2. 并发测试
- [ ] 多用户同时上传测试
- [ ] 相同文件夹名称并发处理
- [ ] 检查数据一致性

### 3. 大批量上传测试
- [ ] 测试50+文件夹批量上传
- [ ] 测试100+图片批量上传
- [ ] 验证异步处理模式
- [ ] 检查内存和CPU使用情况

## 权限验证

### 1. 角色权限
- [ ] admin角色可以访问批量上传功能
- [ ] 普通用户权限控制正确
- [ ] 菜单显示权限正确

### 2. 功能权限
- [ ] 批量上传权限：`cert:batch:upload`
- [ ] 任务查看权限：`cert:task:view`
- [ ] Version查看权限：`cert:version:view`

## 日志检查

### 1. 应用日志
检查以下日志内容：
- [ ] MongoDB索引创建日志
- [ ] 批量上传开始日志
- [ ] 文件夹名称解析日志
- [ ] Version自动创建日志
- [ ] 图片上传和关联日志
- [ ] 错误处理日志

### 2. 数据库日志
- [ ] MongoDB连接日志
- [ ] 查询性能日志
- [ ] 索引使用情况

## 故障排除

### 常见问题及解决方案

#### 1. 菜单不显示
- 检查SQL脚本是否执行成功
- 检查角色权限是否分配
- 清除浏览器缓存重新登录

#### 2. 上传失败
- 检查MinIO服务是否正常
- 检查文件权限和磁盘空间
- 查看应用日志中的错误信息

#### 3. Version创建失败
- 检查文件夹命名格式
- 查看应用日志中的解析错误
- 验证国家和证件类型映射
- 检查MongoDB连接和权限

#### 4. 图片关联失败
- 检查versionId是否正确生成
- 验证ImageRepository记录创建
- 检查数据库索引是否正常

#### 5. 性能问题
- 检查数据库索引是否创建
- 分析查询执行计划
- 监控系统资源使用
- 考虑使用异步处理模式

## 用户培训验证

### 1. 操作流程培训
- [ ] 文件夹命名规范培训
- [ ] 批量上传操作培训
- [ ] Version管理界面使用培训
- [ ] 任务管理界面使用培训

### 2. 管理员培训
- [ ] Version审核流程培训
- [ ] 系统监控和维护培训
- [ ] 故障排除培训

## 部署完成确认

- [ ] 所有检查项目已完成
- [ ] 功能测试通过
- [ ] 性能验证通过
- [ ] 权限配置正确
- [ ] 日志记录正常
- [ ] 用户培训完成
- [ ] 自动创建Version功能正常工作
- [ ] 批量上传与Version创建无缝集成

---

**部署负责人**: _______________  
**部署日期**: _______________  
**验收人**: _______________  
**验收日期**: _______________  
**功能确认**: 批量上传自动创建Version功能已成功集成并正常工作 