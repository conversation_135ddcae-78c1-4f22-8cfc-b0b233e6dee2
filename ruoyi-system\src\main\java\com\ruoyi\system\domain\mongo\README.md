# MongoDB 实体类说明

本目录包含了证件样本库后端服务的MongoDB实体类和POJO类。

## 主要实体类

### 1. BatchUploadTask.java
映射到 `batch_upload_task` 集合，用于管理批量上传任务。

**主要字段：**
- `id`: MongoDB主键
- `taskId`: 业务任务ID
- `status`: 任务状态
- `totalFolders`: 总文件夹数
- `processedFolders`: 已处理文件夹数
- `totalFiles`: 总文件数
- `processedFiles`: 已处理文件数
- `deptInfo`: 部门信息（SysDept类型）
- `creatorInfo`: 创建者信息（SysUser类型）
- `createTime`: 创建时间
- `startTime`: 开始时间
- `endTime`: 结束时间
- `errors`: 错误日志列表

### 2. CertVersion.java
映射到 `cert_version` 集合，用于管理证件版本信息。

**主要字段：**
- `id`: MongoDB主键
- `versionId`: 业务版本ID
- `versionCode`: 版本代码
- `description`: 描述
- `countryInfo`: 国家信息（Country类型）
- `certInfo`: 证件类型信息（CertType类型）
- `issueYear`: 发行年份
- `status`: 状态
- `standardFolderId`: 关联的标准文件夹ID
- `trainingImage`: 训练图片信息
- `deptInfo`: 部门信息（SysDept类型）
- `creatorInfo`: 创建者信息（SysUser类型）
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 3. FolderInfo.java
映射到 `folder_info` 集合，用于管理文件夹信息。

**主要字段：**
- `id`: MongoDB主键
- `folderId`: 业务文件夹ID
- `taskId`: 关联任务ID
- `versionId`: 关联版本ID
- `folderType`: 文件夹类型（如 "standard", "regular"）
- `folderName`: 文件夹名称
- `countryInfo`: 国家信息（Country类型）
- `certInfo`: 证件类型信息（CertType类型）
- `issueYear`: 发行年份
- `fileCount`: 文件数量
- `processedFileCount`: 已处理文件数量
- `status`: 状态
- `deptInfo`: 部门信息（SysDept类型）
- `uploaderInfo`: 上传者信息（SysUser类型）
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 4. ImageRepository.java
映射到 `image_repository` 集合，用于管理图片仓库。

**主要字段：**
- `id`: MongoDB主键
- `imageId`: 业务图片ID
- `taskId`: 关联任务ID
- `folderId`: 关联文件夹ID
- `versionId`: 关联版本ID
- `originalFileName`: 原始文件名
- `minioPath`: MinIO存储路径
- `fileSize`: 文件大小
- `contentType`: 内容类型
- `imageWidth`: 图片宽度
- `imageHeight`: 图片高度
- `lightType`: 光照类型
- `isMainImage`: 是否为主图
- `processStatus`: 处理状态
- `processMessage`: 处理消息
- `imageMd5`: 图片MD5值
- `tags`: 标签列表
- `deptInfo`: 部门信息（SysDept类型）
- `annotations`: 标注信息列表
- `createTime`: 创建时间

## POJO类

### 业务POJO类
- `ErrorLog.java`: 错误日志（folderName, fileName, message, timestamp）
- `TrainingImage.java`: 训练图片信息（url, description）

### 标注相关类
- `Annotation.java`: 标注信息（id, type, body, target）
- `AnnotationBody.java`: 标注内容（value, purpose）
- `AnnotationTarget.java`: 标注目标（selector）
- `AnnotationSelector.java`: 标注选择器（value - 百分比坐标）

## 使用说明

1. 所有实体类都使用了Lombok的`@Data`注解，自动生成getter/setter方法
2. 使用`LocalDateTime`替代`Date`类型，更好地支持Java 8+时间API
3. 使用Spring Data MongoDB的`@Document`注解指定集合名称
4. 内嵌对象使用独立的POJO类，便于复用和维护
5. 遵循Task -> Version -> Folder -> Image的数据流设计
6. 集成了基于部门的数据权限控制
7. **重要**: MongoDB实体类直接使用MySQL实体类（SysDept、SysUser、Country、CertType），确保数据一致性和完整性

## 数据流关系

```
BatchUploadTask (任务)
    ↓
CertVersion (版本)
    ↓
FolderInfo (文件夹)
    ↓
ImageRepository (图片)
```

每个层级都包含相应的部门信息和权限控制字段。
