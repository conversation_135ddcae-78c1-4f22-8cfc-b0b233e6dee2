package com.ruoyi.system.controller.news;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.news.Categroy;
import com.ruoyi.system.service.news.ICategroyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 栏目信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/categroy/categroy")
public class CategroyController extends BaseController
{
    @Autowired
    private ICategroyService categroyService;

    /**
     * 查询栏目信息列表
     */

    
    //@PreAuthorize("@ss.hasPermi('categroy:categroy:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(Categroy categroy)
    {
        startPage();
        List<Categroy> list = categroyService.selectCategroyList(categroy);
        return getDataTable(list);
    }

    /**
     * 导出栏目信息列表
     */
    @PreAuthorize("@ss.hasPermi('categroy:categroy:export')")
    @Log(title = "栏目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Categroy categroy)
    {
        List<Categroy> list = categroyService.selectCategroyList(categroy);
        ExcelUtil<Categroy> util = new ExcelUtil<Categroy>(Categroy.class);
        util.exportExcel(response, list, "栏目信息数据");
    }

    /**
     * 获取栏目信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('categroy:categroy:query')")
    @GetMapping(value = "/{categroyID}")
    public AjaxResult getInfo(@PathVariable("categroyID") Long categroyID)
    {
        return success(categroyService.selectCategroyByCategroyID(categroyID));
    }

    /**
     * 新增栏目信息
     */
    @PreAuthorize("@ss.hasPermi('categroy:categroy:add')")
    @Log(title = "栏目信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Categroy categroy)
    {
        return toAjax(categroyService.insertCategroy(categroy));
    }

    /**
     * 修改栏目信息
     */
    @PreAuthorize("@ss.hasPermi('categroy:categroy:edit')")
    @Log(title = "栏目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Categroy categroy)
    {
        return toAjax(categroyService.updateCategroy(categroy));
    }

    /**
     * 删除栏目信息
     */
    @PreAuthorize("@ss.hasPermi('categroy:categroy:remove')")
    @Log(title = "栏目信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categroyIDs}")
    public AjaxResult remove(@PathVariable Long[] categroyIDs)
    {
        return toAjax(categroyService.deleteCategroyByCategroyIDs(categroyIDs));
    }
}
