<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.SysCommentMapper">
    
    <resultMap type="SysComment" id="SysCommentResult">
        <result property="commentId"    column="comment_id"    />
        <result property="newsId"    column="news_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userName"    column="user_name"    />
        <result property="newsTitle" column="news_title" />
        <result property="avatar"    column="avatar"    />
    </resultMap>

    <sql id="selectSysCommentVo">
        select c.comment_id, c.news_id, c.user_id, c.content, c.status, c.create_by, c.create_time, c.update_by, c.update_time, c.remark,
        u.nick_name as user_name, u.avatar, n.news_title
        from sys_comment c
        left join sys_user u on u.user_id = c.user_id
        left join sys_news n on n.news_id = c.news_id
    </sql>

    <select id="selectSysCommentList" parameterType="SysComment" resultMap="SysCommentResult">
        <include refid="selectSysCommentVo"/>
        <where>  
            <if test="newsId != null "> and c.news_id = #{newsId}</if>
            <if test="userId != null "> and c.user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and c.content like concat('%', #{content}, '%')</if>
            <if test="status != null  and status != ''"> and c.status = #{status}</if>
        </where>
        order by c.create_time desc
    </select>
    
    <select id="selectSysCommentByCommentId" parameterType="Long" resultMap="SysCommentResult">
        <include refid="selectSysCommentVo"/>
        where c.comment_id = #{commentId}
    </select>
    
    <select id="selectSysCommentByNewsId" parameterType="Long" resultMap="SysCommentResult">
        <include refid="selectSysCommentVo"/>
        where c.news_id = #{newsId} and c.status = '0'
        order by c.create_time desc
    </select>
        
    <insert id="insertSysComment" parameterType="SysComment" useGeneratedKeys="true" keyProperty="commentId">
        insert into sys_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="newsId != null">news_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="newsId != null">#{newsId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysComment" parameterType="SysComment">
        update sys_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsId != null">news_id = #{newsId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where comment_id = #{commentId}
    </update>

    <delete id="deleteSysCommentByCommentId" parameterType="Long">
        delete from sys_comment where comment_id = #{commentId}
    </delete>

    <delete id="deleteSysCommentByCommentIds" parameterType="String">
        delete from sys_comment where comment_id in 
        <foreach item="commentId" collection="array" open="(" separator="," close=")">
            #{commentId}
        </foreach>
    </delete>
</mapper>
