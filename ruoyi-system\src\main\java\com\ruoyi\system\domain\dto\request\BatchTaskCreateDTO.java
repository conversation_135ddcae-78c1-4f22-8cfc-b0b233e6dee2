package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 统一的批量任务创建DTO
 *
 * 支持单文件夹和多文件夹场景的统一接口。
 * 一次性创建BatchUploadTask和所有FolderInfo记录。
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class BatchTaskCreateDTO {

    /**
     * 任务名称（可选，如果不提供将由后端自动生成）
     * 格式：时间_部门名称_批量上传任务
     */
    @Size(max = 200, message = "任务名称长度不能超过200个字符")
    private String taskName;

    /**
     * 任务描述
     */
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    private String description;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    /**
     * 创建者用户名
     */
    @NotBlank(message = "创建者不能为空")
    private String createdBy;

    /**
     * 文件夹列表（可以是1个或多个）
     */
    @NotEmpty(message = "文件夹列表不能为空")
    @Valid
    private List<FolderInfoDTO> folders;

    /**
     * 文件夹信息DTO
     */
    @Data
    public static class FolderInfoDTO {
        /**
         * 文件夹名称
         */
        @NotBlank(message = "文件夹名称不能为空")
        private String folderName;

        /**
         * 文件夹路径
         */
        @NotBlank(message = "文件夹路径不能为空")
        private String folderPath;

        /**
         * 国家ID
         */
        @NotNull(message = "国家ID不能为空")
        private Long countryId;

        /**
         * 证件类型ID
         */
        @NotNull(message = "证件类型ID不能为空")
        private Long certTypeId;

        /**
         * 签发年份
         */
        @NotBlank(message = "签发年份不能为空")
        private String issueYear;

        /**
         * 签发地（可选）
         */
        private String issuePlace;

        /**
         * 证件号前缀（可选）
         */
        private String certNumberPrefix;

        /**
         * 文件数量
         */
        @NotNull(message = "文件数量不能为空")
        private Integer fileCount;
    }

    /**
     * 获取文件夹数量
     */
    public int getFolderCount() {
        return folders != null ? folders.size() : 0;
    }

    /**
     * 获取总文件数量
     */
    public int getTotalFileCount() {
        if (folders == null) {
            return 0;
        }
        return folders.stream()
                .mapToInt(f -> f.getFileCount() != null ? f.getFileCount() : 0)
                .sum();
    }

    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        if (createdBy == null || createdBy.trim().isEmpty() || deptId == null) {
            return false;
        }

        if (folders == null || folders.isEmpty()) {
            return false;
        }

        for (FolderInfoDTO folder : folders) {
            if (folder.getCountryId() == null ||
                folder.getCertTypeId() == null ||
                folder.getFileCount() == null ||
                folder.getFileCount() <= 0) {
                return false;
            }

            if (folder.getFolderName() == null || folder.getFolderName().trim().isEmpty()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断是否为单文件夹任务
     */
    public boolean isSingleFolder() {
        return getFolderCount() == 1;
    }

    /**
     * 判断是否为多文件夹任务
     */
    public boolean isMultiFolder() {
        return getFolderCount() > 1;
    }
}
