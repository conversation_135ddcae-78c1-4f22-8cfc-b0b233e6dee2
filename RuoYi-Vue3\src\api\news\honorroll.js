import request from '@/utils/request'

// 查询光荣榜信息表列表
export function listHonorroll(query) {
  return request({
    url: '/honorroll/honorroll/list',
    method: 'get',
    params: query
  })
}

export function PubliclistlistHonorroll(query) {
  return request({
    url: '/honorroll/honorroll/publiclist',
    method: 'get',
    params: query
  })
}

// 查询光荣榜信息表详细
export function getHonorroll(honorId) {
  return request({
    url: '/honorroll/honorroll/' + honorId,
    method: 'get'
  })
}

// 新增光荣榜信息表
export function addHonorroll(data) {
  return request({
    url: '/honorroll/honorroll',
    method: 'post',
    data: data
  })
}

// 修改光荣榜信息表
export function updateHonorroll(data) {
  return request({
    url: '/honorroll/honorroll',
    method: 'put',
    data: data
  })
}

// 删除光荣榜信息表
export function delHonorroll(honorId) {
  return request({
    url: '/honorroll/honorroll/' + honorId,
    method: 'delete'
  })
}
