# 简单版本创建界面实现文档

## 概述

本文档描述了基于选中文件夹创建新版本的简单界面实现，包括前端组件和后端API的修改。

## 实现的功能

### 1. SimpleVersionForm 组件

**位置**: `src/views/cert/components/common/SimpleVersionForm.vue`

**主要功能**:
- 预填充版本号（基于 `folderInfo.preParseVersionInfo.parsedVersionCode`）
- 使用 CountrySelect 和 CertTypeSelect 组件进行选择
- 实时版本号格式校验
- 版本号自动更新（当国家或证件类型选择变化时）
- 将 `folderInfo.mainPicPath` 传递给后端

**Props**:
```typescript
interface Props {
  visible: boolean
  folderInfo: FolderInfoVO | null
}
```

**Emits**:
```typescript
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
  'close': []
}>()
```

**版本号校验规则**:
- 格式：`国家代码_证件类型代码_年份_其他信息`
- 国家代码：2-3位大写字母（如：GBR、CN、USA）
- 证件类型代码：数字或字母数字组合（如：13、A1、PASSPORT）
- 年份：4位数字（如：2004、2024）
- 其他信息：可选，支持字母数字下划线

### 2. TaskWorkspaceView 集成

**修改内容**:
- 导入 SimpleVersionForm 组件
- 添加新的响应式数据：
  - `showVersionForm`: 控制版本创建弹窗显示
  - `selectedFolderForVersion`: 存储选中的文件夹信息
- 修改 `handleCreateVersionClicked` 方法
- 添加 `handleVersionCreated` 和 `handleVersionFormClose` 方法
- 移除旧的版本创建弹窗代码

### 3. 后端API调整

**CertVersion 实体修改**:
```java
// 添加主图路径字段
private String mainPicPath; // 主图路径，从FolderInfo继承
```

**CertVersionServiceImpl 修改**:
```java
// 在创建版本时从 FolderInfo 继承主图路径
newVersion.setMainPicPath(folderInfo.getMainPicPath());
```

**CertVersionVO 修改**:
```java
// 添加主图路径字段
private String mainPicPath;
```

## 数据流程

1. **用户选择文件夹**: 在 TaskWorkspaceView 中选择待处理的文件夹
2. **点击创建版本**: 触发 `handleCreateVersionClicked` 方法
3. **打开版本创建表单**: 显示 SimpleVersionForm 组件
4. **预填充数据**: 
   - 版本号：`folderInfo.preParseVersionInfo.parsedVersionCode`
   - 国家代码：`folderInfo.preParseVersionInfo.countryCode`
   - 证件类型代码：`folderInfo.preParseVersionInfo.certTypeCode`
5. **用户编辑**: 用户可以修改版本号、选择国家和证件类型
6. **实时校验**: 版本号格式实时校验，自动更新
7. **提交创建**: 调用 `createVersionFromFolder` API
8. **后端处理**: 
   - 创建 CertVersion 对象
   - 从 FolderInfo 继承 mainPicPath
   - 保存版本并关联文件夹
9. **成功反馈**: 显示成功消息，刷新数据

## 使用示例

### 前端使用

```vue
<template>
  <SimpleVersionForm
    v-model:visible="showVersionForm"
    :folder-info="selectedFolderForVersion"
    @success="handleVersionCreated"
    @close="handleVersionFormClose"
  />
</template>

<script setup>
import SimpleVersionForm from '@/views/cert/components/common/SimpleVersionForm.vue'

const showVersionForm = ref(false)
const selectedFolderForVersion = ref(null)

const handleVersionCreated = async () => {
  ElMessage.success('版本创建成功')
  // 刷新数据
  await refreshComponents()
}

const handleVersionFormClose = () => {
  selectedFolderForVersion.value = null
}
</script>
```

### 后端API调用

```typescript
// 前端调用
const requestData = {
  folderId: folderInfo.folderId,
  versionCode: 'GBR_13_2004_XDD85',
  description: '基于文件夹创建的版本'
}

const result = await createVersionFromFolder(requestData)
```

## 文件结构

```
src/
├── views/
│   └── cert/
│       ├── components/
│       │   └── common/
│       │       └── SimpleVersionForm.vue  # 新增组件
│       └── batch/
│           └── TaskWorkspaceView.vue      # 修改的文件
└── api/
    └── cert/
        └── version.ts                     # 更新的API接口

ruoyi-system/
└── src/main/java/com/ruoyi/system/
    ├── domain/
    │   ├── mongo/
    │   │   └── CertVersion.java           # 添加mainPicPath字段
    │   └── dto/response/
    │       └── CertVersionVO.java         # 添加mainPicPath字段
    └── service/impl/
        └── CertVersionServiceImpl.java    # 修改版本创建逻辑
```

## 注意事项

1. **数据一致性**: 确保前后端数据结构一致
2. **错误处理**: 完善各种错误场景的处理
3. **用户体验**: 提供清晰的提示和反馈
4. **性能优化**: 避免不必要的重复请求
5. **向后兼容**: 确保不影响现有功能

## 测试建议

1. **功能测试**:
   - 测试版本号预填充功能
   - 测试版本号格式校验
   - 测试国家和证件类型选择联动
   - 测试版本创建成功流程

2. **边界测试**:
   - 测试无效版本号格式
   - 测试网络错误情况
   - 测试空数据情况

3. **集成测试**:
   - 测试与 TaskWorkspaceView 的集成
   - 测试数据刷新功能
   - 测试后端API调用

## 后续优化

1. **版本号智能生成**: 基于文件夹信息自动生成更准确的版本号
2. **批量版本创建**: 支持同时为多个文件夹创建版本
3. **版本模板**: 支持保存和使用版本创建模板
4. **高级校验**: 添加更多版本号校验规则
