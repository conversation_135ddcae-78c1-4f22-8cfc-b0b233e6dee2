/**
 * 版本相关的TypeScript类型定义
 */

import type { Country, CertType } from './common'

/** 版本状态枚举 */
export enum VersionStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

/** 版本创建请求DTO */
export interface VersionCreateDTO {
  /** 版本名称 */
  versionName: string
  /** 版本描述 */
  description?: string
  /** 国家ID */
  countryId: number
  /** 证件类型ID */
  certTypeId: number
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
  /** 版本号 */
  versionNumber?: string
  /** 标准文件夹ID */
  standardFolderId?: string
}

/** 从文件夹创建版本DTO */
export interface VersionCreateFromFolderDTO {
  /** 版本名称 */
  versionName: string
  /** 版本描述 */
  description?: string
  /** 文件夹ID */
  folderId: string
  /** 版本号 */
  versionNumber?: string
}

/** 版本更新DTO */
export interface VersionUpdateDTO {
  /** 版本ID */
  versionId: string
  /** 版本名称 */
  versionName?: string
  /** 版本描述 */
  description?: string
  /** 签发年份 */
  issueYear?: string
  /** 签发地 */
  issuePlace?: string
  /** 版本号 */
  versionNumber?: string
  /** 标准文件夹ID */
  standardFolderId?: string
  /** 状态 */
  status?: VersionStatus
}

/** 版本查询参数 */
export interface VersionQueryParams {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 版本名称 */
  versionName?: string
  /** 国家代码 */
  countryCode?: string
  /** 证件类型代码 */
  certTypeCode?: string
  /** 签发年份 */
  issueYear?: string
  /** 状态 */
  status?: VersionStatus
  /** 创建者 */
  createdBy?: string
}

/** 版本信息VO */
export interface VersionVO {
  /** 版本ID */
  versionId: string
  /** 版本名称 */
  versionName: string
  /** 版本描述 */
  description?: string
  /** 版本号 */
  versionNumber?: string
  /** 状态 */
  status: VersionStatus
  /** 国家信息 */
  countryInfo?: Country
  /** 证件类型信息 */
  certTypeInfo?: CertType
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
  /** 标准文件夹ID */
  standardFolderId?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 创建者 */
  createdBy: string
  /** 更新者 */
  updatedBy?: string
  /** 文件夹数量 */
  folderCount?: number
  /** 图片数量 */
  imageCount?: number
}

/** 版本详情VO */
export interface VersionDetailVO extends VersionVO {
  /** 文件夹列表 */
  folders?: FolderInfo[]
  /** 标注模板列表 */
  annotationTemplates?: AnnotationTemplateInfo[]
  /** 统计信息 */
  statistics?: VersionStatistics
}

/** 文件夹信息 */
export interface FolderInfo {
  /** 文件夹ID */
  folderId: string
  /** 文件夹名称 */
  folderName: string
  /** 文件夹路径 */
  folderPath: string
  /** 图片数量 */
  imageCount: number
  /** 是否为标准样本 */
  isStandardSample: boolean
  /** 创建时间 */
  createTime: string
}

/** 标注模板信息 */
export interface AnnotationTemplateInfo {
  /** 模板ID */
  templateId: string
  /** 图片类型 */
  imageType: string
  /** 标注项数量 */
  annotationCount: number
  /** 创建时间 */
  createTime: string
}

/** 版本统计信息 */
export interface VersionStatistics {
  /** 总文件夹数 */
  totalFolders: number
  /** 总图片数 */
  totalImages: number
  /** 已标注图片数 */
  annotatedImages: number
  /** 标注模板数 */
  templateCount: number
}

/** 文件夹类型更新DTO */
export interface FolderTypeUpdateDTO {
  /** 文件夹ID */
  folderId: string
  /** 国家ID */
  countryId: number
  /** 证件类型ID */
  certTypeId: number
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
} 