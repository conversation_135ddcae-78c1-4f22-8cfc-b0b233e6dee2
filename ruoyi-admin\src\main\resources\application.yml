# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: C:/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    max-http-post-size: 1GB
    connection-timeout: 600000
    # AI 助手 - 新增：解决临时文件删除问题的Tomcat配置 - 2025-01-15
    # 临时文件目录
    basedir: ${java.io.tmpdir}/tomcat
    # 文件上传临时目录
    tmp-dir: ${java.io.tmpdir}/tomcat-upload
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 500MB
      # AI 助手 - 新增：解决临时文件删除问题 - 2025-01-15
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 上传文件的临时目录
      location: ${java.io.tmpdir}
      # 是否延迟解析文件
      resolve-lazily: false
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Minio配置
minio:
  url: ${MINIO_URL:http://localhost:9000}
  accessKey: ${MINIO_ACCESS_KEY:minioadmin}
  secretKey: ${MINIO_SECRET_KEY:minioadmin}
  bucketName: ${MINIO_BUCKET_NAME:xjlfiles}

# Tus断点续传配置
tus:
  upload:
    storage-path: ${ruoyi.profile}/tus-uploads
    expiration-period: 86400000  # 24小时过期
    max-upload-size: **********  # 1GB
    download-enabled: true
    # 强制清理过期的上传状态
    cleanup-on-startup: true

# 批量上传配置
batch:
  upload:
    # 是否启用外部处理服务
    external-processor:
      enabled: false
      # 处理服务检查URL（可选）
      health-check-url: http://localhost:8080/processor/health
    # 默认处理模式
    default-process-mode: internal # internal | external | async

# FastAPI服务配置 - greenfox增强版 - 2025-01-15
fastapi:
  # 基础配置
  base:
    url: ${FASTAPI_BASE_URL:http://localhost:8000}  # 支持环境变量覆盖
  # API认证配置
  api:
    key: ${FASTAPI_API_KEY:hVNImh-BAs2cIopy-rDntyD0fzvkCyyLWEiAcZDIGtc}  # API密钥
    header-name: X-API-Key  # API Key在Header中的名称
  # 服务配置
  service:
    enabled: ${FASTAPI_ENABLED:true}  # 是否启用FastAPI服务
    timeout: 30000  # 请求超时时间(ms)
    connect-timeout: 5000  # 连接超时时间(ms)
    read-timeout: 25000  # 读取超时时间(ms)
  # 重试配置
  retry:
    max-attempts: 3  # 最大重试次数
    delay: 1000  # 重试延迟(ms)
    backoff-multiplier: 2  # 退避倍数
  # 熔断器配置
  circuit-breaker:
    enabled: true  # 是否启用熔断器
    failure-threshold: 5  # 失败阈值
    recovery-timeout: 30000  # 恢复超时时间
  # 接口路径配置
  endpoints:
    folder-info: "/api/v1/cert/folder-info"  # 文件夹信息查询接口
    folder-detail: "/api/v1/cert/folder-info/{id}"  # 文件夹详情接口
    folder-stats: "/api/v1/cert/folder-info/stats"  # 文件夹统计接口
    health-check: "/health"  # 健康检查接口
  # 日志配置
  logging:
    enabled: true  # 是否启用请求日志
    level: INFO  # 日志级别
    include-request-body: false  # 是否包含请求体
    include-response-body: false  # 是否包含响应体
