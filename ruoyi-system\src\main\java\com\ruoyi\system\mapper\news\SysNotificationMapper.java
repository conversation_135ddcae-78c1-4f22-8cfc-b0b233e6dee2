package com.ruoyi.system.mapper.news;

import java.util.List;
import com.ruoyi.system.domain.news.SysNotification;

/**
 * 消息通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
public interface SysNotificationMapper
{
    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    public SysNotification selectSysNotificationByNotificationId(Long notificationId);

    /**
     * 查询消息通知列表
     * 
     * @param sysNotification 消息通知
     * @return 消息通知集合
     */
    public List<SysNotification> selectSysNotificationList(SysNotification sysNotification);

    /**
     * 查询用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    public int selectUnreadCount(Long userId);

    /**
     * 新增消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    public int insertSysNotification(SysNotification sysNotification);

    /**
     * 修改消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    public int updateSysNotification(SysNotification sysNotification);

    /**
     * 删除消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int deleteSysNotificationByNotificationId(Long notificationId);

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysNotificationByNotificationIds(Long[] notificationIds);

    /**
     * 将消息标记为已读
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int markAsRead(Long notificationId);

    /**
     * 将用户所有消息标记为已读
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int markAllAsRead(Long userId);
}
