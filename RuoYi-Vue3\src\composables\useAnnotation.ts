/**
 * 统一的标注管理 Composable
 * 合并了 useImageAnnotation 和 useAnnotationPermission 的功能
 * 
 * 提供完整的标注功能：
 * - 图片标注管理
 * - 权限检查
 * - 批量操作
 * - 模板管理
 */

import { ref, computed, watch, type Ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getImagesByFolder, 
  getImageDetails, 
  updateImageAnnotations 
} from '@/api/cert/image'
import { 
  checkAnnotationPermission, 
  getImageAnnotations,
  saveAnnotationTemplate,
  type AnnotationPermissionInfo,
  type ImageAnnotationVO 
} from '@/api/cert/annotation'
import type { ImageInfoVO } from '@/types'

/**
 * 图片标注管理
 */
export function useImageAnnotation() {
  // 响应式数据
  const imageList = ref<ImageInfoVO[]>([])
  const selectedImage = ref<ImageInfoVO | null>(null)
  const currentAnnotations = ref<any[]>([])
  const imageLoading = ref(false)
  const saving = ref(false)
  const hasAnnotationChanges = ref(false)
  const selectedAnnotation = ref<any>(null)

  // 计算属性
  const annotatedCount = computed(() => {
    return imageList.value.filter(image => image.isAnnotated).length
  })

  const annotationProgress = computed(() => {
    if (imageList.value.length === 0) return 0
    return Math.round((annotatedCount.value / imageList.value.length) * 100)
  })

  // 方法
  const loadImageList = async (folderId: string) => {
    if (!folderId) {
      throw new Error('文件夹ID不能为空')
    }

    imageLoading.value = true
    try {
      const response = await getImagesByFolder(folderId)
      console.log('getImagesByFolder API 响应:', response)

      if (response.code === 200) {
        imageList.value = response.rows || response.data || []
        console.log('图片列表加载成功，数量:', imageList.value.length)
        return imageList.value
      } else {
        throw new Error(response.msg || '获取图片列表失败')
      }
    } catch (error) {
      console.error('加载图片列表失败:', error)
      throw error
    } finally {
      imageLoading.value = false
    }
  }

  const selectImage = (image: ImageInfoVO) => {
    selectedImage.value = image
    selectedAnnotation.value = null
    hasAnnotationChanges.value = false
  }

  const selectAnnotation = (annotationId: string) => {
    selectedAnnotation.value = currentAnnotations.value.find(a => a.id === annotationId) || null
  }

  const clearAnnotations = () => {
    currentAnnotations.value = []
    selectedAnnotation.value = null
    hasAnnotationChanges.value = false
  }

  // 标注格式转换
  const convertAnnotationsToTemplate = (annotations: any[], imageWidth?: number, imageHeight?: number) => {
    return annotations.map(annotation => {
      const coordinate = annotation.target?.selector?.percentCoords
      return {
        annotationId: annotation.id,
        annotationType: 'rectangle',
        annotationName: annotation.bodies?.[0]?.value || '标注',
        coordinate: coordinate ? {
          x: coordinate.x,
          y: coordinate.y,
          width: coordinate.w,
          height: coordinate.h
        } : undefined,
        annotationValue: annotation.bodies?.[0]?.value || '',
        required: false,
        displayOrder: 0
      }
    })
  }

  const convertTemplateToAnnotations = (templateAnnotations: any[]) => {
    return templateAnnotations.map(annotation => {
      let bodies = []
      if (annotation.annotationValue) {
        bodies = [{
          type: 'TextualBody',
          value: annotation.annotationValue,
          purpose: 'commenting'
        }]
      }

      let target = null
      if (annotation.coordinate) {
        const coord = annotation.coordinate
        target = {
          selector: {
            type: 'RECTANGLE',
            percentCoords: {
              x: coord.x,
              y: coord.y,
              w: coord.width,
              h: coord.height
            },
            value: `xywh=percent:${coord.x},${coord.y},${coord.width},${coord.height}`
          }
        }
      }

      return {
        id: annotation.annotationId || `annotation_${Date.now()}`,
        type: 'Annotation',
        bodies: bodies,
        target: target
      }
    })
  }

  return {
    // 状态
    imageList,
    selectedImage,
    currentAnnotations,
    imageLoading,
    saving,
    hasAnnotationChanges,
    selectedAnnotation,
    
    // 计算属性
    annotatedCount,
    annotationProgress,
    
    // 方法
    loadImageList,
    selectImage,
    selectAnnotation,
    clearAnnotations,
    convertAnnotationsToTemplate,
    convertTemplateToAnnotations
  }
}

/**
 * 标注权限管理
 */
export function useAnnotationPermission(imageId?: Ref<string> | string) {
  // 响应式状态
  const loading = ref(false)
  const permissionInfo = ref<AnnotationPermissionInfo | null>(null)
  const annotationInfo = ref<ImageAnnotationVO | null>(null)
  const error = ref<string | null>(null)

  // 计算属性
  const canEdit = computed(() => permissionInfo.value?.canEdit ?? false)
  const canView = computed(() => permissionInfo.value?.canView ?? false)
  const isStandardSample = computed(() => permissionInfo.value?.isStandardSample ?? false)
  const isAnnotatableType = computed(() => permissionInfo.value?.isAnnotatableType ?? false)
  const reason = computed(() => permissionInfo.value?.reason ?? '')
  const imageType = computed(() => permissionInfo.value?.imageType ?? '')

  // 权限状态描述
  const permissionStatus = computed(() => {
    if (!permissionInfo.value) return 'unknown'
    
    if (canEdit.value) return 'editable'
    if (canView.value) return 'viewable'
    return 'restricted'
  })

  // 权限状态颜色
  const permissionColor = computed(() => {
    switch (permissionStatus.value) {
      case 'editable': return 'success'
      case 'viewable': return 'warning'
      case 'restricted': return 'danger'
      default: return 'info'
    }
  })

  // 权限状态文本
  const permissionText = computed(() => {
    switch (permissionStatus.value) {
      case 'editable': return '可编辑'
      case 'viewable': return '只读'
      case 'restricted': return '无权限'
      default: return '未知'
    }
  })

  /**
   * 检查图片标注权限
   */
  const checkPermission = async (targetImageId?: string) => {
    const currentImageId = targetImageId || (typeof imageId === 'string' ? imageId : imageId?.value)
    
    if (!currentImageId) {
      error.value = '图片ID不能为空'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const response = await checkAnnotationPermission(currentImageId)
      
      if (response.code === 200) {
        permissionInfo.value = response.data
        return true
      } else {
        error.value = response.msg || '获取权限信息失败'
        ElMessage.error(error.value)
        return false
      }
    } catch (err: any) {
      error.value = err.message || '网络请求失败'
      ElMessage.error(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取图片标注信息（包含权限检查）
   */
  const getAnnotationInfo = async (targetImageId?: string) => {
    const currentImageId = targetImageId || (typeof imageId === 'string' ? imageId : imageId?.value)
    
    if (!currentImageId) {
      error.value = '图片ID不能为空'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const response = await getImageAnnotations(currentImageId)
      
      if (response.code === 200) {
        annotationInfo.value = response.data
        
        // 同时更新权限信息
        permissionInfo.value = {
          canEdit: response.data.canEdit,
          canView: response.data.canView,
          reason: response.data.reason,
          imageType: response.data.imageType,
          isStandardSample: response.data.isStandardSample,
          isAnnotatableType: response.data.isAnnotatableType
        }
        
        return response.data
      } else {
        error.value = response.msg || '获取标注信息失败'
        ElMessage.error(error.value)
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络请求失败'
      ElMessage.error(error.value)
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 验证操作权限
   */
  const validateOperation = (operation: 'edit' | 'view' | 'save') => {
    if (!permissionInfo.value) {
      ElMessage.warning('请先检查权限信息')
      return false
    }

    switch (operation) {
      case 'edit':
      case 'save':
        if (!canEdit.value) {
          ElMessage.warning(`无法执行${operation === 'edit' ? '编辑' : '保存'}操作：${reason.value}`)
          return false
        }
        break
      case 'view':
        if (!canView.value) {
          ElMessage.warning(`无法查看标注：${reason.value}`)
          return false
        }
        break
    }

    return true
  }

  /**
   * 重置状态
   */
  const reset = () => {
    permissionInfo.value = null
    annotationInfo.value = null
    error.value = null
    loading.value = false
  }

  /**
   * 刷新权限信息
   */
  const refresh = async () => {
    if (typeof imageId === 'string' ? imageId : imageId?.value) {
      await checkPermission()
    }
  }

  // 监听imageId变化，自动检查权限
  if (imageId && typeof imageId !== 'string') {
    watch(imageId, (newImageId) => {
      if (newImageId) {
        checkPermission(newImageId)
      } else {
        reset()
      }
    }, { immediate: true })
  }

  return {
    // 状态
    loading,
    permissionInfo,
    annotationInfo,
    error,
    
    // 计算属性
    canEdit,
    canView,
    isStandardSample,
    isAnnotatableType,
    reason,
    imageType,
    permissionStatus,
    permissionColor,
    permissionText,
    
    // 方法
    checkPermission,
    getAnnotationInfo,
    validateOperation,
    reset,
    refresh
  }
}

/**
 * 批量标注权限管理
 */
export function useBatchAnnotationPermission() {
  const permissions = ref<Map<string, AnnotationPermissionInfo>>(new Map())
  const errors = ref<Map<string, string>>(new Map())
  const loading = ref(false)

  const checkBatchPermissions = async (imageIds: string[]) => {
    loading.value = true
    permissions.value.clear()
    errors.value.clear()

    try {
      const promises = imageIds.map(async (imageId) => {
        try {
          const response = await checkAnnotationPermission(imageId)
          if (response.code === 200) {
            permissions.value.set(imageId, response.data)
          } else {
            errors.value.set(imageId, response.msg || '获取权限失败')
          }
        } catch (error: any) {
          errors.value.set(imageId, error.message || '网络请求失败')
        }
      })

      await Promise.all(promises)
    } finally {
      loading.value = false
    }
  }

  const getPermission = (imageId: string) => {
    return permissions.value.get(imageId) || null
  }

  const getError = (imageId: string) => {
    return errors.value.get(imageId) || null
  }

  const canEdit = (imageId: string) => {
    return permissions.value.get(imageId)?.canEdit ?? false
  }

  const canView = (imageId: string) => {
    return permissions.value.get(imageId)?.canView ?? false
  }

  const getEditableImages = () => {
    return Array.from(permissions.value.entries())
      .filter(([_, permission]) => permission.canEdit)
      .map(([imageId, _]) => imageId)
  }

  const getViewableImages = () => {
    return Array.from(permissions.value.entries())
      .filter(([_, permission]) => permission.canView)
      .map(([imageId, _]) => imageId)
  }

  const reset = () => {
    permissions.value.clear()
    errors.value.clear()
    loading.value = false
  }

  return {
    permissions,
    errors,
    loading,
    checkBatchPermissions,
    getPermission,
    getError,
    canEdit,
    canView,
    getEditableImages,
    getViewableImages,
    reset
  }
}

/**
 * 标注模板管理
 */
export function useAnnotationTemplate() {
  const loading = ref(false)
  const saving = ref(false)
  const error = ref<string | null>(null)

  const saveTemplate = async (templateData: any) => {
    saving.value = true
    error.value = null

    try {
      const response = await saveAnnotationTemplate(templateData)
      
      if (response.code === 200) {
        ElMessage.success('标注模板保存成功')
        return response.data
      } else {
        error.value = response.msg || '保存模板失败'
        ElMessage.error(error.value)
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络请求失败'
      ElMessage.error(error.value)
      return null
    } finally {
      saving.value = false
    }
  }

  return {
    loading,
    saving,
    error,
    saveTemplate
  }
} 
