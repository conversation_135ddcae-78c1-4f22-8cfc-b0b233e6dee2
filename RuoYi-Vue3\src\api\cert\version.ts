import request from '@/utils/request'
import type { 
  Country, 
  CertType, 
  SysDept, 
  SysUser, 
  TrainingImageDTO 
} from '@/types/common'

// ==================== TypeScript 接口定义 ====================

/** 从文件夹创建版本请求DTO */
export interface VersionCreateFromFolderDTO {
  /** 文件夹ID */
  folderId: string
  /** 版本代码 */
  versionCode: string
  /** 版本描述 */
  description?: string
  /** 创建者用户ID */
  creatorUserId: number
  /** 创建者部门ID */
  creatorDeptId: number
}

/** 文件夹类型更新请求DTO */
export interface FolderTypeUpdateDTO {
  /** 新类型 ("standard" 或 "regular") */
  newType: 'standard' | 'regular'
}

/** 版本查询请求DTO */
export interface VersionQueryDTO {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 国家ID */
  countryId?: number
  /** 证件类型 */
  certType?: string
  /** 发行年份 */
  issueYear?: string
  /** 版本状态 */
  status?: string
  /** 是否需要人工确认 */
  needManualCheck?: boolean
  /** 关键词搜索 */
  keyword?: string
  /** 版本代码 */
  versionCode?: string
  /** 创建者部门ID */
  deptId?: number
}



/** 证件版本响应VO */
export interface CertVersionVO {
  /** MongoDB主键 */
  id: string
  /** 业务版本ID */
  versionId: string
  /** 版本代码 */
  versionCode: string
  /** 描述 */
  description?: string
  /** 国家信息 */
  countryInfo: Country
  /** 证件类型信息 */
  certInfo: CertType
  /** 发行年份 */
  issueYear: string
  /** 状态 */
  status: string
  /** 关联的标准文件夹ID */
  standardFolderId?: string
  /** 主图路径 */
  mainPicPath?: string
  /** 训练图片信息 */
  trainingImage?: TrainingImageDTO
  /** 部门信息 */
  deptInfo: SysDept
  /** 创建者信息 */
  creatorInfo: SysUser
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 关联的文件夹数量 */
  folderCount?: number
}

// ==================== API 函数 ====================

/**
 * 创建新版本 (从文件夹)
 * POST /api/versions
 */
export function createVersionFromFolder(data: VersionCreateFromFolderDTO) {
  return request({
    url: '/api/versions',
    method: 'post',
    data
  })
}

/**
 * 管理文件夹类型 (设置标准/普通)
 * PUT /api/versions/{versionId}/folders/{folderId}/type
 */
export function updateFolderType(versionId: string, folderId: string, data: FolderTypeUpdateDTO) {
  return request({
    url: `/api/versions/${versionId}/folders/${folderId}/type`,
    method: 'put',
    data
  })
}

export function getVersionsByTaskId(taskId: string) {
  return request({
    url: `/api/versions/task/${taskId}`,
    method: 'get'
  })
}

/**
 * 校验版本号是否重复
 * GET /api/versions/check-duplicate?versionCode={versionCode}
 */
export function checkVersionCodeDuplicate(versionCode: string) {
  return request({
    url: '/api/versions/check-duplicate',
    method: 'get',
    params: { versionCode }
  })
}

/**
 * 查询版本列表
 * GET /api/versions
 */
export function getVersionList(params?: VersionQueryDTO) {
  return request({
    url: '/api/versions',
    method: 'get',
    params
  })
}

/**
 * 获取版本下的文件夹列表
 * GET /api/versions/{versionId}/folders
 */
export function getFoldersByVersionId(versionId: string) {
  return request({
    url: `/api/versions/${versionId}/folders`,
    method: 'get'
  })
}

/**
 * 设置标准样本文件夹
 * PUT /api/versions/{versionId}/standard-folder
 */
export function setStandardFolder(versionId: string, data: { folderId: string }) {
  return request({
    url: `/api/versions/${versionId}/standard-folder`,
    method: 'put',
    data
  })
}

/**
 * 取消标准样本文件夹
 * DELETE /api/versions/{versionId}/standard-folder
 */
export function removeStandardFolder(versionId: string) {
  return request({
    url: `/api/versions/${versionId}/standard-folder`,
    method: 'delete'
  })
}

/**
 * 获取版本详情
 * GET /api/versions/{versionId}
 */
export function getVersionDetails(versionId: string) {
  return request({
    url: `/api/versions/${versionId}`,
    method: 'get'
  })
}

/**
 * 查找相似版本 (用于版本推荐)
 * GET /api/versions/similar
 */
export function findSimilarVersions(params: {
  countryCode?: string
  certType?: string
  issueYear?: string
  keyword?: string
}) {
  return request({
    url: '/api/versions/similar',
    method: 'get',
    params
  })
}

/**
 * 删除版本
 * DELETE /api/versions/{versionId}
 */
export function deleteVersion(versionId: string) {
  return request({
    url: `/api/versions/${versionId}`,
    method: 'delete'
  })
}

/**
 * 获取指定国家的版本信息
 * GET /api/versions/country/{countryId}
 */
export function getVersionsByCountry(countryId: number) {
  return request({
    url: `/api/versions/country/${countryId}`,
    method: 'get'
  })
}
