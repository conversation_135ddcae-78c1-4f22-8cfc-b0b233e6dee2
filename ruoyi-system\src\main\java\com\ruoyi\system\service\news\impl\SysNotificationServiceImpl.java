package com.ruoyi.system.service.news.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.SysNotificationMapper;
import com.ruoyi.system.domain.news.SysNotification;
import com.ruoyi.system.service.news.ISysNotificationService;
import com.ruoyi.system.service.websocket.WebSocketServer;

/**
 * 消息通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
@Service
public class SysNotificationServiceImpl implements ISysNotificationService 
{
    @Autowired
    private SysNotificationMapper sysNotificationMapper;
    


    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    @Override
    public SysNotification selectSysNotificationByNotificationId(Long notificationId)
    {
        return sysNotificationMapper.selectSysNotificationByNotificationId(notificationId);
    }

    /**
     * 查询消息通知列表
     * 
     * @param sysNotification 消息通知
     * @return 消息通知
     */
    @Override
    public List<SysNotification> selectSysNotificationList(SysNotification sysNotification)
    {
        return sysNotificationMapper.selectSysNotificationList(sysNotification);
    }

    /**
     * 查询用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    @Override
    public int selectUnreadCount(Long userId)
    {
        return sysNotificationMapper.selectUnreadCount(userId);
    }

    /**
     * 新增消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    @Override
    public int insertSysNotification(SysNotification sysNotification)
    {
        int rows = sysNotificationMapper.insertSysNotification(sysNotification);
        
        // 发送WebSocket消息
        if (rows > 0) {
            WebSocketServer.sendMessageToUser(sysNotification.getUserId().toString(), 
                    "{\"type\":\"notification\",\"title\":\"" + sysNotification.getTitle() + 
                    "\",\"content\":\"" + sysNotification.getContent() + 
                    "\",\"id\":" + sysNotification.getNotificationId() + "}");
        }
        
        return rows;
    }

    /**
     * 修改消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    @Override
    public int updateSysNotification(SysNotification sysNotification)
    {
        return sysNotificationMapper.updateSysNotification(sysNotification);
    }

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的消息通知主键
     * @return 结果
     */
    @Override
    public int deleteSysNotificationByNotificationIds(Long[] notificationIds)
    {
        return sysNotificationMapper.deleteSysNotificationByNotificationIds(notificationIds);
    }

    /**
     * 删除消息通知信息
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    @Override
    public int deleteSysNotificationByNotificationId(Long notificationId)
    {
        return sysNotificationMapper.deleteSysNotificationByNotificationId(notificationId);
    }

    /**
     * 将消息标记为已读
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    @Override
    public int markAsRead(Long notificationId)
    {
        return sysNotificationMapper.markAsRead(notificationId);
    }

    /**
     * 将用户所有消息标记为已读
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int markAllAsRead(Long userId)
    {
        return sysNotificationMapper.markAllAsRead(userId);
    }
    
    /**
     * 发送审核通知
     * 
     * @param userId 用户ID
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param isApproved 是否通过审核
     * @param auditComment 审核意见
     * @return 结果
     */
    @Override
    public int sendAuditNotification(Long userId, Long newsId, String newsTitle, boolean isApproved, String auditComment) {
        SysNotification notification = new SysNotification();
        notification.setUserId(userId);
        notification.setSourceId(newsId);
        notification.setType("2"); // 审核通知
        notification.setStatus("0"); // 未读状态

        if (isApproved) {
            notification.setTitle("文章审核通过通知");
            notification.setContent("您的文章《" + newsTitle + "》已通过审核，现已发布。审核意见：" + auditComment);
        } else {
            notification.setTitle("文章审核未通过通知");
            notification.setContent("您的文章《" + newsTitle + "》未通过审核，请修改后重新提交。审核意见：" + auditComment);
        }

        return insertSysNotification(notification);
    }

    /**
     * 发送评论通知
     * 
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param commentUserId 评论用户ID
     * @param commentUserName 评论用户名称
     * @param targetUserId 目标用户ID（文章作者）
     * @return 结果
     */
    @Override
    public int sendCommentNotification(Long newsId, String newsTitle, Long commentUserId, String commentUserName, Long targetUserId)
    {
        // 如果是给自己的文章评论，不发送通知
        if (commentUserId.equals(targetUserId)) {
            return 0;
        }
        
        SysNotification notification = new SysNotification();
        notification.setUserId(targetUserId);
        notification.setSourceId(newsId);
        notification.setType("3"); // 评论通知
        notification.setStatus("0"); // 未读状态
        notification.setTitle("新评论通知");
        notification.setContent("用户 " + commentUserName + " 评论了您的文章《" + newsTitle + "》");
        
        return insertSysNotification(notification);
    }

    /**
     * 发送评分通知
     * 
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param ratingUserId 评分用户ID
     * @param ratingUserName 评分用户名称
     * @param targetUserId 目标用户ID（文章作者）
     * @param score 评分
     * @return 结果
     */
    @Override
    public int sendRatingNotification(Long newsId, String newsTitle, Long ratingUserId, String ratingUserName, Long targetUserId, Integer score)
    {
        // 如果是给自己的文章评分，不发送通知
        if (ratingUserId.equals(targetUserId)) {
            return 0;
        }
        
        SysNotification notification = new SysNotification();
        notification.setUserId(targetUserId);
        notification.setSourceId(newsId);
        notification.setType("4"); // 评分通知
        notification.setStatus("0"); // 未读状态
        notification.setTitle("新评分通知");
        notification.setContent("用户 " + ratingUserName + " 给您的文章《" + newsTitle + "》评了 " + score + " 分");
        
        return insertSysNotification(notification);
    }
}
