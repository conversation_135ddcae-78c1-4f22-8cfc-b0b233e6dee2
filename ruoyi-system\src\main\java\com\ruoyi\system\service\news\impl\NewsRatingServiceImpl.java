package com.ruoyi.system.service.news.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.NewsRatingMapper;
import com.ruoyi.system.domain.news.NewsRating;
import com.ruoyi.system.service.news.INewsRatingService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 文章评分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
@Service
public class NewsRatingServiceImpl implements INewsRatingService 
{
    @Autowired
    private NewsRatingMapper newsRatingMapper;

    /**
     * 查询文章评分
     * 
     * @param ratingId 文章评分主键
     * @return 文章评分
     */
    @Override
    public NewsRating selectNewsRatingByRatingId(Long ratingId)
    {
        return newsRatingMapper.selectNewsRatingByRatingId(ratingId);
    }

    /**
     * 查询文章评分列表
     * 
     * @param newsRating 文章评分
     * @return 文章评分
     */
    @Override
    public List<NewsRating> selectNewsRatingList(NewsRating newsRating)
    {
        return newsRatingMapper.selectNewsRatingList(newsRating);
    }

    /**
     * 新增文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    @Override
    public int insertNewsRating(NewsRating newsRating)
    {
        return newsRatingMapper.insertNewsRating(newsRating);
    }

    /**
     * 修改文章评分
     * 
     * @param newsRating 文章评分
     * @return 结果
     */
    @Override
    public int updateNewsRating(NewsRating newsRating)
    {
        return newsRatingMapper.updateNewsRating(newsRating);
    }

    /**
     * 批量删除文章评分
     * 
     * @param ratingIds 需要删除的文章评分主键
     * @return 结果
     */
    @Override
    public int deleteNewsRatingByRatingIds(Long[] ratingIds)
    {
        return newsRatingMapper.deleteNewsRatingByRatingIds(ratingIds);
    }

    /**
     * 删除文章评分信息
     * 
     * @param ratingId 文章评分主键
     * @return 结果
     */
    @Override
    public int deleteNewsRatingByRatingId(Long ratingId)
    {
        return newsRatingMapper.deleteNewsRatingByRatingId(ratingId);
    }

    /**
     * 获取文章平均评分和评分人数
     * 
     * @param newsId 文章ID
     * @return 包含平均评分和评分人数的Map
     */
    @Override
    public Map<String, Object> getAvgRatingByNewsId(Long newsId)
    {
        Map<String, Object> result = new HashMap<>();
        Double avgRating = newsRatingMapper.selectAvgRatingByNewsId(newsId);
        Integer ratingCount = newsRatingMapper.selectRatingCountByNewsId(newsId);
        
        result.put("avgRating", avgRating != null ? avgRating : 0.0);
        result.put("ratingCount", ratingCount != null ? ratingCount : 0);
        
        // 获取当前用户的评分
        Long userId = SecurityUtils.getUserId();
        if (userId != null) {
            NewsRating userRating = newsRatingMapper.selectRatingByNewsIdAndUserId(newsId, userId);
            result.put("userRating", userRating != null ? userRating.getScore() : 0);
        } else {
            result.put("userRating", 0);
        }
        
        return result;
    }
    
    /**
     * 添加用户评分
     * 
     * @param newsRating 评分信息
     * @return 结果
     */
    @Override
    public int addUserRating(NewsRating newsRating)
    {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            return 0;
        }
        
        newsRating.setUserId(userId);
        newsRating.setCreatedAt(new Date());
        
        // 检查用户是否已经评分过该文章
        NewsRating existingRating = newsRatingMapper.selectRatingByNewsIdAndUserId(newsRating.getNewsId(), userId);
        
        if (existingRating != null) {
            // 如果已经评分过，则更新评分
            existingRating.setScore(newsRating.getScore());
            return newsRatingMapper.updateNewsRating(existingRating);
        } else {
            // 如果没有评分过，则添加新评分
            return newsRatingMapper.insertNewsRating(newsRating);
        }
    }

    @Override
    public int insertOrUpdateRating(NewsRating newsRating) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            return 0; // 用户未登录
        }

        newsRating.setUserId(userId);
        newsRating.setCreatedAt(new Date());

        // 检查用户是否已经评分过该文章
        NewsRating existingRating = newsRatingMapper.selectRatingByNewsIdAndUserId(newsRating.getNewsId(), userId);

        if (existingRating != null) {
            // 如果已经评分过，则更新评分
            existingRating.setScore(newsRating.getScore());
            return newsRatingMapper.updateNewsRating(existingRating);
        } else {
            // 如果没有评分过，则添加新评分
            return newsRatingMapper.insertNewsRating(newsRating);
        }
    }
}
