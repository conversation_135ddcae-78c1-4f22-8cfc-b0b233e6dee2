package com.ruoyi.system.repository;

import com.ruoyi.system.domain.mongo.ImageRepository;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 图片仓库Repository
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface ImageRepositoryRepo extends MongoRepository<ImageRepository, String> {
    
    /**
     * 根据图片ID查找
     * 
     * @param imageId 图片ID
     * @return 图片信息
     */
    ImageRepository findByImageId(String imageId);
    
    /**
     * 根据文件夹ID查找图片列表
     * 
     * @param folderId 文件夹ID
     * @return 图片列表
     */
    List<ImageRepository> findByFolderId(String folderId);
    
    /**
     * 根据版本ID查找图片列表
     * 
     * @param versionId 版本ID
     * @return 图片列表
     */
    List<ImageRepository> findByVersionId(String versionId);
    
    /**
     * 根据文件夹ID统计图片数量
     * 
     * @param folderId 文件夹ID
     * @return 图片数量
     */
    long countByFolderId(String folderId);
    
    /**
     * 根据任务ID查找图片列表
     * 
     * @param taskId 任务ID
     * @return 图片列表
     */
    List<ImageRepository> findByTaskId(String taskId);
    
    /**
     * 根据光照类型查找图片列表
     * 
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepository> findByLightType(String lightType);
    
    /**
     * 根据是否为主图查找
     * 
     * @param isMainImage 是否为主图
     * @return 图片列表
     */
    List<ImageRepository> findByIsMainImage(boolean isMainImage);
    
    /**
     * 根据处理状态查找
     * 
     * @param processStatus 处理状态
     * @return 图片列表
     */
    List<ImageRepository> findByProcessStatus(String processStatus);
    
    /**
     * 根据部门查找
     *
     * @param deptId 部门ID
     * @return 图片列表
     */
    @Query("{'deptInfo.deptId': ?0}")
    List<ImageRepository> findByDeptId(Long deptId);

    /**
     * 根据国家信息查找
     *
     * @param countryId 国家ID
     * @return 图片列表
     */
    @Query("{'countryInfo.id': ?0}")
    List<ImageRepository> findByCountryId(Long countryId);

    /**
     * 根据证件类型查找
     *
     * @param certTypeId 证件类型ID
     * @return 图片列表
     */
    @Query("{'certInfo.id': ?0}")
    List<ImageRepository> findByCertTypeId(Long certTypeId);

    /**
     * 根据创建者查找
     *
     * @param userId 用户ID
     * @return 图片列表
     */
    @Query("{'creatorInfo.userId': ?0}")
    List<ImageRepository> findByCreatorId(Long userId);
    
    /**
     * 根据文件夹ID和光照类型查找
     * 
     * @param folderId 文件夹ID
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepository> findByFolderIdAndLightType(String folderId, String lightType);
    
    /**
     * 根据文件夹ID和是否为主图查找
     * 
     * @param folderId 文件夹ID
     * @param isMainImage 是否为主图
     * @return 图片列表
     */
    List<ImageRepository> findByFolderIdAndIsMainImage(String folderId, boolean isMainImage);
    
    /**
     * 根据版本ID和光照类型查找
     * 
     * @param versionId 版本ID
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepository> findByVersionIdAndLightType(String versionId, String lightType);
    
    /**
     * 根据创建时间范围查询
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 图片列表
     */
    List<ImageRepository> findByCreateTimeBetween(Date startTime, Date endTime);
    
    /**
     * 根据文件夹ID查找主图
     * 
     * @param folderId 文件夹ID
     * @return 主图列表
     */
    @Query("{'folderId': ?0, 'isMainImage': true}")
    List<ImageRepository> findMainImagesByFolderId(String folderId);
    
    /**
     * 根据文件夹ID统计已处理图片数量
     * 
     * @param folderId 文件夹ID
     * @return 已处理图片数量
     */
    @Query(value = "{'folderId': ?0, 'processStatus': 'completed'}", count = true)
    long countProcessedImagesByFolderId(String folderId);
    
    /**
     * 根据版本ID统计图片数量
     * 
     * @param versionId 版本ID
     * @return 图片数量
     */
    long countByVersionId(String versionId);
    
    /**
     * 删除指定文件夹的所有图片
     * 
     * @param folderId 文件夹ID
     */
    void deleteByFolderId(String folderId);
    
    /**
     * 删除指定任务的所有图片
     * 
     * @param taskId 任务ID
     */
    void deleteByTaskId(String taskId);
}
