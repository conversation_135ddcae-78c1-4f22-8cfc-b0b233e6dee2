package com.ruoyi.system.domain.dto.response;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.mongo.ErrorLog;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量上传任务响应VO
 * 用于展示批量上传任务的详情
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class BatchUploadTaskVO {
    
    /** MongoDB主键 */
    private String id;
    
    /** 业务任务ID */
    private String taskId;
    
    /** 任务状态 */
    private String status;
    
    /** 总文件夹数 */
    private Integer totalFolders;
    
    /** 已处理文件夹数 */
    private Integer processedFolders;
    
    /** 总文件数 */
    private Integer totalFiles;
    
    /** 已处理文件数 */
    private Integer processedFiles;
    
    /** 部门信息 */
    private SysDept deptInfo;
    
    /** 创建者信息 */
    private SysUser creatorInfo;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 开始时间 */
    private LocalDateTime startTime;
    
    /** 结束时间 */
    private LocalDateTime endTime;
    
    /** 错误日志列表 */
    private List<ErrorLog> errors;
}
