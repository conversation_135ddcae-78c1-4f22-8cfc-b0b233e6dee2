<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CountryMapper">
    
    <resultMap type="Country" id="CountryResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="flagIcon"    column="icon_path"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCountryVo">
        select id, code, name_cn, name_en, icon_path, create_time, update_time from country
    </sql>

    <select id="selectCountryList" parameterType="Country" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name_cn like concat('%', #{name}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and name_en like concat('%', #{nameEn}, '%')</if>
            <if test="flagIcon != null  and flagIcon != ''"> and icon_path = #{flagIcon}</if>
        </where>
    </select>
    
    <select id="selectCountryById" parameterType="Long" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        where id = #{id}
    </select>
    
    <!-- 根据首字母查询国家列表 -->
    <select id="selectCountryByFirstLetter" parameterType="String" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        where UPPER(SUBSTRING(name_en, 1, 1)) = UPPER(#{letter})
        order by name_en
    </select>
    
    <!-- 获取所有首字母及对应的国家数量 -->
    <select id="selectCountryLetterCounts" resultType="java.util.Map">
        SELECT 
            UPPER(SUBSTRING(name_en, 1, 1)) as letter,
            COUNT(*) as count
        FROM 
            country
        WHERE 
            name_en IS NOT NULL AND name_en != ''
        GROUP BY 
            UPPER(SUBSTRING(name_en, 1, 1))
        ORDER BY 
            letter
    </select>

    <insert id="insertCountry" parameterType="Country" useGeneratedKeys="true" keyProperty="id">
        insert into country
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name_cn,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="flagIcon != null">icon_path,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="flagIcon != null">#{flagIcon},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCountry" parameterType="Country">
        update country
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name_cn = #{name},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="flagIcon != null">icon_path = #{flagIcon},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCountryById" parameterType="Long">
        delete from country where id = #{id}
    </delete>

    <delete id="deleteCountryByIds" parameterType="String">
        delete from country where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCountryByCode" parameterType="String" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        where code = #{code}
    </select>

    <!-- 批量查询国家信息 -->
    <select id="selectCountryByCodes" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        where code in
        <foreach item="code" collection="list" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <!-- 搜索国家（支持中英文名称模糊查询） -->
    <select id="searchCountries" parameterType="String" resultMap="CountryResult">
        <include refid="selectCountryVo"/>
        <where>
            <if test="keyword != null and keyword != ''">
                (name_cn like concat('%', #{keyword}, '%') 
                 or name_en like concat('%', #{keyword}, '%') 
                 or code like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by name_en
    </select>

    <!-- 批量更新国旗图标路径 -->
    <update id="batchUpdateFlagIcon">
        <foreach collection="list" item="country" separator=";">
            update country set icon_path = #{country.flagIcon}, update_time = #{country.updateTime}
            where id = #{country.id}
        </foreach>
    </update>

    <!-- 按条件统计数量 -->
    <select id="countByCondition" parameterType="Country" resultType="int">
        select count(*) from country
        <where>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name_cn like concat('%', #{name}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and name_en like concat('%', #{nameEn}, '%')</if>
            <if test="flagIcon != null  and flagIcon != ''"> and icon_path = #{flagIcon}</if>
        </where>
    </select>
</mapper>