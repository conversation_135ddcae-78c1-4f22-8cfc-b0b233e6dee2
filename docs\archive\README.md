# 历史文档归档

本目录包含项目开发过程中的历史文档，这些文档记录了功能实现的过程和技术细节，但不再是当前系统的主要参考文档。

## 📁 归档文档分类

### 早期架构设计文档
- `20250706-操作流程与前端代码结构说明.md` - 早期操作流程说明
- `20250707-前后端功能结构详细说明.md` - 功能结构详细说明
- `20250707-图片标注组件架构说明.md` - 图片标注组件架构

### 功能实现记录
- `20250707-多级文件夹上传功能完整实现报告.md` - 多级文件夹上传实现报告
- `multi-level-folder-upload-implementation-summary.md` - 技术实现总结
- `multi-version-modal-improvements.md` - 多版本模态框改进

### 重构项目记录
- `complete-refactoring-summary.md` - 后端重构完整记录
- `complete-frontend-migration-final-summary.md` - 前端迁移完整记录

## 📝 说明

这些文档保留用于：
- 了解功能的历史实现过程
- 技术决策的参考
- 问题排查时的历史记录
- 新团队成员了解项目演进历程

## 🔍 当前系统文档

如需了解当前系统架构和使用指南，请参考主docs目录下的最新文档：
- [系统架构说明](../cert-sample-management-system-architecture.md)
- [后端文件指南](../backend-cert-management-files-guide.md)
- [前端组件指南](../frontend-vue-components-guide.md)
- [项目概览](../project-overview.md)
