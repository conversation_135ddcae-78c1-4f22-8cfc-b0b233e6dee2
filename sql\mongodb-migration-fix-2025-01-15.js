// MongoDB 数据迁移修复脚本 - 版本标注模板系统
// 执行日期: 2025-01-15
// 目的: 修复索引冲突问题，完成剩余的索引创建

print("=== 开始执行版本标注模板系统数据迁移修复 ===");

// 1. 检查现有索引状态
print("1. 检查现有索引状态...");

print("图片仓库现有索引:");
var imageIndexes = db.image_repository.getIndexes();
imageIndexes.forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

// 2. 安全地创建缺失的索引
print("2. 创建缺失的索引...");

// 检查并创建isAnnotatableType索引
var hasAnnotatableTypeIndex = false;
for (var i = 0; i < imageIndexes.length; i++) {
    if (imageIndexes[i].key && imageIndexes[i].key.isAnnotatableType === 1) {
        hasAnnotatableTypeIndex = true;
        print("isAnnotatableType索引已存在: " + imageIndexes[i].name);
        break;
    }
}

if (!hasAnnotatableTypeIndex) {
    try {
        db.image_repository.createIndex(
            { "isAnnotatableType": 1 },
            { name: "is_annotatable_type_idx" }
        );
        print("✓ 创建isAnnotatableType索引成功");
    } catch (e) {
        print("✗ isAnnotatableType索引创建失败: " + e.message);
    }
} else {
    print("✓ isAnnotatableType索引已存在，跳过创建");
}

// 检查并创建复合索引 versionId + imageType
var hasVersionImageTypeIndex = false;
for (var i = 0; i < imageIndexes.length; i++) {
    var index = imageIndexes[i];
    if (index.key && index.key.versionId === 1 && index.key.imageType === 1) {
        hasVersionImageTypeIndex = true;
        print("versionId+imageType复合索引已存在: " + index.name);
        break;
    }
}

if (!hasVersionImageTypeIndex) {
    try {
        db.image_repository.createIndex(
            { "versionId": 1, "imageType": 1 },
            { name: "version_image_type_idx" }
        );
        print("✓ 创建versionId+imageType复合索引成功");
    } catch (e) {
        print("✗ 复合索引创建失败: " + e.message);
    }
} else {
    print("✓ versionId+imageType复合索引已存在，跳过创建");
}

// 3. 验证所有必需的索引
print("3. 验证所有必需的索引...");

var requiredIndexes = [
    { collection: "version_annotation_template", field: "versionId", name: "version_id_idx" },
    { collection: "version_annotation_template", field: "templateId", name: "template_id_idx" },
    { collection: "folder_info", field: "isStandardSample", name: "is_standard_sample_idx" },
    { collection: "image_repository", field: "isAnnotatableType", name: "is_annotatable_type_idx" }
];

requiredIndexes.forEach(function(req) {
    var indexes = db.getCollection(req.collection).getIndexes();
    var found = false;
    
    for (var i = 0; i < indexes.length; i++) {
        var index = indexes[i];
        if ((index.key[req.field] === 1) || (index.name === req.name)) {
            found = true;
            print("✓ " + req.collection + "." + req.field + " 索引存在");
            break;
        }
    }
    
    if (!found) {
        print("✗ " + req.collection + "." + req.field + " 索引缺失");
    }
});

// 4. 检查数据迁移状态
print("4. 检查数据迁移状态...");

// 检查版本标注模板集合
var templateCollectionExists = db.getCollectionNames().indexOf("version_annotation_template") !== -1;
print("版本标注模板集合存在: " + templateCollectionExists);

// 检查文件夹标准样本字段
var folderWithStandardFlag = db.folder_info.findOne({ "isStandardSample": { $exists: true } });
print("文件夹标准样本字段存在: " + (folderWithStandardFlag !== null));

// 检查图片可标注类型字段
var imageWithAnnotatableFlag = db.image_repository.findOne({ "isAnnotatableType": { $exists: true } });
print("图片可标注类型字段存在: " + (imageWithAnnotatableFlag !== null));

// 统计可标注图片数量
var annotatableImageCount = db.image_repository.countDocuments({ "isAnnotatableType": true });
print("可标注图片数量: " + annotatableImageCount);

// 5. 输出最终索引状态
print("5. 输出最终索引状态...");

print("版本标注模板集合索引:");
if (templateCollectionExists) {
    db.version_annotation_template.getIndexes().forEach(function(index) {
        print("  - " + index.name + ": " + JSON.stringify(index.key));
    });
} else {
    print("  集合不存在");
}

print("文件夹信息集合相关索引:");
db.folder_info.getIndexes().filter(function(index) {
    return index.name.indexOf("standard") !== -1 || index.name.indexOf("version") !== -1;
}).forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("图片仓库集合相关索引:");
db.image_repository.getIndexes().filter(function(index) {
    return index.name.indexOf("type") !== -1 || index.name.indexOf("annotatable") !== -1 || index.name.indexOf("version") !== -1;
}).forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

// 6. 提供手动修复建议
print("6. 手动修复建议...");

if (!hasAnnotatableTypeIndex) {
    print("手动创建isAnnotatableType索引:");
    print('db.image_repository.createIndex({"isAnnotatableType": 1}, {name: "is_annotatable_type_idx"})');
}

if (!hasVersionImageTypeIndex) {
    print("手动创建复合索引:");
    print('db.image_repository.createIndex({"versionId": 1, "imageType": 1}, {name: "version_image_type_idx"})');
}

print("=== 版本标注模板系统数据迁移修复完成 ===");
