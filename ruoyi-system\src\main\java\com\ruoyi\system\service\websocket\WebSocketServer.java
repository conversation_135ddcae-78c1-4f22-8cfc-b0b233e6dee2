package com.ruoyi.system.service.websocket;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * WebSocket服务
 * 
 * <AUTHOR>
 */
@ServerEndpoint("/websocket/{userId}")
@Component
public class WebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);
    
    /**
     * 用户ID和WebSocket连接的映射关系
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    
    /**
     * 接收userId
     */
    private String userId = "";
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.session = session;
        this.userId = userId;
        
        // 如果已存在相同用户的连接，先移除旧连接
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.remove(userId);
        }
        
        webSocketMap.put(userId, this);
        log.info("用户连接:" + userId + ",当前在线人数:" + webSocketMap.size());
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.remove(userId);
            log.info("用户退出:" + userId + ",当前在线人数:" + webSocketMap.size());
        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("用户消息:" + userId + ",报文:" + message);
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户错误:" + this.userId + ",原因:" + error.getMessage());
        error.printStackTrace();
    }
    
    /**
     * 发送消息
     */
    public void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            log.error("发送消息出错:", e);
        }
    }
    
    /**
     * 发送消息给指定用户
     */
    public static void sendMessageToUser(String userId, String message) {
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.get(userId).sendMessage(message);
        } else {
            log.warn("用户" + userId + "不在线，消息发送失败");
        }
    }
    
    /**
     * 发送消息给所有用户
     */
    public static void sendMessageToAll(String message) {
        for (WebSocketServer item : webSocketMap.values()) {
            item.sendMessage(message);
        }
    }
}
