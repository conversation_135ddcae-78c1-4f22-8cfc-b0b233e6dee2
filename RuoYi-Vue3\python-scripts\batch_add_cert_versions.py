import os
import requests
import uuid
from minio import Minio
from minio.error import S3Error
import logging
from datetime import datetime
import concurrent.futures
import time

# 导入配置
try:
    from config import (
        API_CONFIG, MINIO_CONFIG, PERFORMANCE_CONFIG, FOLDER_CONFIG,
        DEFAULT_CERT_SPECS, TRAINING_IMAGE_CONFIG, LOGGING_CONFIG,
        get_api_url, get_folder_root, validate_config, print_config_summary,
        load_config_from_file
    )
    CONFIG_AVAILABLE = True
except ImportError:
    print("⚠️  未找到config.py配置文件，使用默认配置")
    CONFIG_AVAILABLE = False

# 配置日志
if CONFIG_AVAILABLE:
    log_level = getattr(logging, LOGGING_CONFIG['level'].upper(), logging.INFO)
    logging.basicConfig(level=log_level, format=LOGGING_CONFIG['format'])
else:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

logger = logging.getLogger(__name__)

# 加载配置
if CONFIG_AVAILABLE:
    # 尝试从配置文件加载
    load_config_from_file()
    
    # 使用配置文件中的值
    API_URL = get_api_url()
    FOLDER_ROOT = get_folder_root()
    
    # MinIO配置
    MINIO_ENDPOINT = MINIO_CONFIG['endpoint']
    MINIO_ACCESS_KEY = MINIO_CONFIG['access_key']
    MINIO_SECRET_KEY = MINIO_CONFIG['secret_key']
    MINIO_BUCKET_NAME = MINIO_CONFIG['bucket_name']
    MINIO_SECURE = MINIO_CONFIG['secure']
    
    # 性能配置
    MAX_CONCURRENT_UPLOADS = PERFORMANCE_CONFIG['max_concurrent_uploads']
    MAX_CONCURRENT_REQUESTS = PERFORMANCE_CONFIG['max_concurrent_requests']
else:
    # 回退到硬编码配置（向后兼容）
    API_URL = 'http://localhost:8000/api/v1/cert/version/unified'
    FOLDER_ROOT = r'C:\Users\<USER>\Documents\工作文档\2025研发项目\证研平台\configs_upd\configs\personal'
    MINIO_ENDPOINT = 'localhost:9000'
    MINIO_ACCESS_KEY = 'minioadmin'
    MINIO_SECRET_KEY = 'minioadmin'
    MINIO_BUCKET_NAME = 'xjlfiles'
    MINIO_SECURE = False
    MAX_CONCURRENT_UPLOADS = 5
    MAX_CONCURRENT_REQUESTS = 3

# 初始化MinIO客户端
minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

def ensure_bucket_exists():
    """确保MinIO bucket存在"""
    try:
        if not minio_client.bucket_exists(MINIO_BUCKET_NAME):
            minio_client.make_bucket(MINIO_BUCKET_NAME)
            logger.info(f"创建MinIO bucket: {MINIO_BUCKET_NAME}")
        return True
    except Exception as e:
        logger.error(f"MinIO bucket检查失败: {e}")
        return False

def upload_training_image_to_minio(local_path, folder_name):
    """
    上传训练图片到MinIO
    
    Args:
        local_path: 本地图片路径
        folder_name: 原始文件夹名称，用于生成MinIO路径
    
    Returns:
        str: MinIO URL或None
    """
    try:
        if not os.path.exists(local_path):
            logger.error(f"训练图片文件不存在: {local_path}")
            return None
        
        # 确保bucket存在
        if not ensure_bucket_exists():
            return None
        
        # 生成MinIO路径：原始文件夹名/training/PER.jpg
        file_extension = os.path.splitext(local_path)[1].lower()
        minio_path = f"{folder_name}/training/PER{file_extension}"
        
        # 检查文件是否已存在（去重优化）
        try:
            minio_client.stat_object(MINIO_BUCKET_NAME, minio_path)
            logger.info(f"文件已存在，跳过上传: {minio_path}")
            minio_url = f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
            return minio_url
        except S3Error:
            # 文件不存在，继续上传
            pass
        
        # 上传文件
        minio_client.fput_object(
            bucket_name=MINIO_BUCKET_NAME,
            object_name=minio_path,
            file_path=local_path,
            content_type=f"image/{file_extension[1:]}" if file_extension in ['.jpg', '.jpeg', '.png'] else 'application/octet-stream'
        )
        
        # 生成访问URL
        minio_url = f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
        
        logger.info(f"训练图片上传成功: {local_path} -> {minio_url}")
        return minio_url
        
    except S3Error as e:
        logger.error(f"MinIO上传失败: {e}")
        return None
    except Exception as e:
        logger.error(f"上传训练图片异常: {e}")
        return None

def upload_image_with_retry(local_path, folder_name, max_retries=3):
    """带重试的图片上传"""
    for attempt in range(max_retries):
        try:
            result = upload_training_image_to_minio(local_path, folder_name)
            if result:
                return result
        except Exception as e:
            logger.warning(f"上传重试 {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                time.sleep(1)  # 等待1秒后重试
    return None

def generate_version_code(country_code, cert_type, issue_year):
    """
    生成版本代码
    格式: {countryCode}_{certType}_{year}_{timestamp}
    """
    timestamp = datetime.now().strftime("%Y%m%d")
    return f"{country_code}_{cert_type}_{issue_year}_{timestamp}"

def generate_version_id():
    """生成版本ID"""
    return "V" + str(uuid.uuid4()).replace("-", "")[:8]

def parse_folder_name(folder_name):
    """解析文件夹名称，提取信息"""
    parts = folder_name.split('_')
    if len(parts) >= 4:
        return {
            'countryCode': parts[0],
            'certType': parts[1],
            'startNo': parts[2],
            'issueYear': parts[3]
        }
    return None

def find_training_image(root_dir, country_code, folder_name):
    """查找训练图片"""
    if CONFIG_AVAILABLE:
        file_names = TRAINING_IMAGE_CONFIG['file_names']
    else:
        file_names = ["PER.JPG", "per.jpg", "PER.jpg", "per.JPG"]
    
    possible_paths = [
        os.path.join(root_dir, country_code, folder_name, file_name)
        for file_name in file_names
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def find_cert_folders(root_dir):
    """递归查找符合格式的文件夹"""
    result = []

    # 遍历根目录下的所有文件夹
    for country_code in os.listdir(root_dir):
        country_path = os.path.join(root_dir, country_code)

        # 检查是否是文件夹
        if os.path.isdir(country_path):
            # 遍历国家代码文件夹下的所有文件夹
            for folder in os.listdir(country_path):
                folder_path = os.path.join(country_path, folder)

                # 检查是否是文件夹且包含下划线
                if os.path.isdir(folder_path) and '_' in folder:
                    # 检查文件夹名是否符合格式
                    parts = folder.split('_')
                    if len(parts) == 4:
                        result.append({
                            'folderName': folder,
                            'countryCode': country_code,
                            'folderPath': folder_path
                        })

    return result

def create_unified_cert_version_request(folder_info):
    """创建统一证件版本请求（使用cert命名）"""
    folder_name = folder_info['folderName']
    country_code = folder_info['countryCode']
    folder_path = folder_info['folderPath']
    
    # 解析文件夹名称
    parsed = parse_folder_name(folder_name)
    if not parsed:
        raise ValueError(f"无法解析文件夹名称: {folder_name}")
    
    # 生成版本代码
    version_code = generate_version_code(
        country_code, 
        parsed['certType'], 
        parsed['issueYear']
    )
    
    # 查找并上传训练图片到MinIO
    training_image_path = find_training_image(FOLDER_ROOT, country_code, folder_name)
    training_image_url = None
    
    if training_image_path:
        logger.info(f"找到训练图片: {training_image_path}")
        training_image_url = upload_image_with_retry(training_image_path, folder_name)
        
        if not training_image_url:
            logger.warning(f"训练图片上传失败，将使用本地路径: {training_image_path}")
            training_image_url = f"local://{training_image_path}"
    else:
        logger.warning(f"未找到训练图片: {folder_name}")
    
    # 获取默认证件规格
    if CONFIG_AVAILABLE:
        cert_specs = DEFAULT_CERT_SPECS
        training_config = TRAINING_IMAGE_CONFIG
    else:
        # 回退到硬编码配置
        cert_specs = {
            'width': 85.6, 'height': 53.98, 'thickness': 0.76, 'material': 'PVC',
            'security_features': ['水印', '防伪线', '全息图'], 'number_format': 'D{4}',
            'end_no': 'D9999', 'light_types': ['visible']
        }
        training_config = {'light_type': 'visible', 'description_template': '标准样本训练图片 - {version_code}'}
    
    # 构建统一创建请求
    request = {
        "creationMode": "STANDARD_SAMPLE",
        "folderName": folder_name,
        "certType": parsed['certType'],
        "issueYear": parsed['issueYear'],
        "countryCode": country_code,
        "startNo": parsed['startNo'],
        "endNo": cert_specs['end_no'],
        "numberFormat": cert_specs['number_format'],
        "securityFeatures": cert_specs['security_features'],
        "width": cert_specs['width'],
        "height": cert_specs['height'],
        "thickness": cert_specs['thickness'],
        "material": cert_specs['material'],
        "versionCode": version_code,
        "trainingImageUrl": training_image_url,
        "trainingImageLightType": training_config['light_type'],
        "trainingImageDescription": training_config['description_template'].format(version_code=version_code),
        "imageCount": 1,
        "lightTypes": cert_specs['light_types']
    }
    
    return request

def batch_create_unified_cert_versions():
    """调用统一接口批量创建证件版本（使用cert命名）"""
    try:
        logger.info("开始批量创建证件版本")
        
        # 检查MinIO连接
        if not ensure_bucket_exists():
            print("❌ MinIO连接失败，请检查MinIO服务是否启动")
            return
        
        # 获取文件夹列表
        folder_infos = find_cert_folders(FOLDER_ROOT)

        if not folder_infos:
            print("未找到符合格式的文件夹")
            return

        print(f"找到 {len(folder_infos)} 个证件文件夹:")
        for folder_info in folder_infos:
            print(f"  - {folder_info['folderName']} ({folder_info['countryCode']})")

        # 确认是否继续
        confirm = input("是否继续处理这些文件夹? (y/n): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return

        # 设置请求头
        headers = {
            'Content-Type': 'application/json'
        }

        success_count = 0
        failed_count = 0
        failed_folders = []
        uploaded_images = []

        # 记录开始时间
        start_time = time.time()

        # 🚀 真正的并发处理 - 阶段1: 并发准备数据
        print(f"\n🔄 阶段1: 并发准备数据 (最大并发: {MAX_CONCURRENT_UPLOADS})")
        prepared_data = []
        
        def prepare_single_folder(folder_info):
            try:
                request_data = create_unified_cert_version_request(folder_info)
                return {'status': 'success', 'folder_info': folder_info, 'request_data': request_data}
            except Exception as e:
                return {'status': 'error', 'folder_info': folder_info, 'error': str(e)}
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_UPLOADS) as executor:
            future_to_folder = {executor.submit(prepare_single_folder, folder_info): folder_info 
                              for folder_info in folder_infos}
            
            completed = 0
            for future in concurrent.futures.as_completed(future_to_folder):
                completed += 1
                result = future.result()
                prepared_data.append(result)
                
                if completed % 100 == 0 or completed == len(folder_infos):
                    print(f"  进度: [{completed}/{len(folder_infos)}]")
        
        stage1_time = time.time() - start_time
        ready_count = sum(1 for d in prepared_data if d['status'] == 'success')
        print(f"✅ 阶段1完成: {ready_count}/{len(folder_infos)} 准备就绪, 用时: {stage1_time:.1f}秒")
        
        # 🚀 阶段2: 并发API请求
        print(f"\n🔄 阶段2: 并发API请求 (最大并发: {MAX_CONCURRENT_REQUESTS})")
        
        def send_single_request(prepared_item):
            if prepared_item['status'] != 'success':
                return {**prepared_item, 'api_status': 'skipped'}
            
            try:
                folder_info = prepared_item['folder_info']
                request_data = prepared_item['request_data']
                
                response = requests.post(API_URL, json=request_data, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 200:
                        version_data = result.get('data', {})
                        return {
                            **prepared_item,
                            'api_status': 'success',
                            'version_id': version_data.get('versionId', 'Unknown'),
                            'version_code': request_data.get('versionCode', 'Unknown')
                        }
                    else:
                        return {**prepared_item, 'api_status': 'failed', 'api_error': result.get('msg', '未知错误')}
                else:
                    return {**prepared_item, 'api_status': 'http_error', 'api_error': f"HTTP {response.status_code}"}
                    
            except Exception as e:
                return {**prepared_item, 'api_status': 'exception', 'api_error': str(e)}
        
        final_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_REQUESTS) as executor:
            future_to_prepared = {executor.submit(send_single_request, prepared): prepared 
                                for prepared in prepared_data}
            
            completed = 0
            for future in concurrent.futures.as_completed(future_to_prepared):
                completed += 1
                result = future.result()
                final_results.append(result)
                
                # 统计结果
                api_status = result.get('api_status', 'unknown')
                if api_status == 'success':
                    success_count += 1
                    # 记录上传的训练图片
                    request_data = result.get('request_data', {})
                    if request_data.get('trainingImageUrl') and 'http://' in request_data['trainingImageUrl']:
                        uploaded_images.append({
                            'version_id': result.get('version_id'),
                            'version_code': result.get('version_code'),
                            'training_image_url': request_data['trainingImageUrl']
                        })
                else:
                    failed_count += 1
                    folder_name = result['folder_info']['folderName']
                    error = result.get('api_error', result.get('error', 'Unknown'))
                    failed_folders.append(f"{folder_name}: {error}")
                
                if completed % 100 == 0 or completed == len(prepared_data):
                    success_rate = success_count / completed * 100
                    print(f"  进度: [{completed}/{len(prepared_data)}] 成功率: {success_rate:.1f}%")

        # 计算处理时间
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 输出结果统计
        print("\n" + "="*60)
        print("🎉 批量创建证件版本结果 (真正并发版):")
        print(f"📊 总数: {len(folder_infos)}")
        print(f"✅ 成功: {success_count}")
        print(f"❌ 失败: {failed_count}")
        print(f"📈 成功率: {success_count/len(folder_infos)*100:.1f}%")
        print(f"⏱️  处理时间: {elapsed_time:.1f} 秒")
        if elapsed_time > 0:
            current_speed = len(folder_infos)/elapsed_time
            print(f"🚀 平均速度: {current_speed:.2f} 个/秒")
            print(f"💡 相比之前提升: {current_speed/0.37:.1f} 倍 (之前0.37个/秒)")
        
        if uploaded_images:
            print(f"\n成功上传到MinIO的训练图片 ({len(uploaded_images)} 个):")
            for img in uploaded_images:
                print(f"  - {img['version_code']}: {img['training_image_url']}")
        
        if failed_folders:
            print("\n失败的文件夹:")
            for failed in failed_folders:
                print(f"  - {failed}")

    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到后端服务，请检查服务是否启动")
        logger.error("无法连接到后端服务")
    except requests.exceptions.Timeout:
        print("❌ 请求超时: 后端服务响应时间过长")
        logger.error("请求超时")
    except Exception as e:
        print(f"❌ 请求过程中发生错误: {str(e)}")
        logger.error(f"批量创建过程中发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 证件版本统一创建工具 (可配置并发版)")
    
    if CONFIG_AVAILABLE:
        # 验证配置
        validation = validate_config()
        if not validation['valid']:
            print("❌ 配置验证失败:")
            for issue in validation['issues']:
                print(f"  - {issue}")
            print("\n请检查config.py配置文件或设置环境变量")
            exit(1)
        
        print_config_summary()
    else:
        print("⚠️  使用默认硬编码配置 (建议创建config.py配置文件)")
        print(f"📁 文件夹路径: {FOLDER_ROOT}")
        print(f"🗄️  MinIO服务: http://{MINIO_ENDPOINT}")
        print(f"📦 MinIO存储桶: {MINIO_BUCKET_NAME}")
        print(f"🌐 统一创建接口: {API_URL}")
        print(f"⚙️  并发配置: 上传{MAX_CONCURRENT_UPLOADS}线程, API{MAX_CONCURRENT_REQUESTS}线程")
    
    print("-" * 60)
    batch_create_unified_cert_versions()
    print("-" * 60)
    input("按 Enter 键退出...")
