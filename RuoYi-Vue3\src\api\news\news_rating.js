import request from '@/utils/request'

// 查询文章评分列表
export function listNews_rating(query) {
  return request({
    url: '/news_rating/news_rating/list',
    method: 'get',
    params: query
  })
}

// 查询文章评分详细
export function getNews_rating(ratingId) {
  return request({
    url: '/news_rating/news_rating/' + ratingId,
    method: 'get'
  })
}

// 新增文章评分
export function addNews_rating(data) {
  return request({
    url: '/news_rating/news_rating',
    method: 'post',
    data: data
  })
}

// 修改文章评分
export function updateNews_rating(data) {
  return request({
    url: '/news_rating/news_rating',
    method: 'put',
    data: data
  })
}

// 删除文章评分
export function delNews_rating(ratingId) {
  return request({
    url: '/news_rating/news_rating/' + ratingId,
    method: 'delete'
  })
}

// 获取文章平均评分（匿名可访问）
export function getPublicAvgRating(newsId) {
  return request({
    url: '/news_rating/news_rating/public/avg/' + newsId,
    method: 'get',
    headers: {
      'isToken': false
    }
  })
}

// 获取用户对文章的评分（需要登录）
export function getUserRating(newsId) {
  return request({
    url: '/news_rating/news_rating/user/rating/' + newsId,
    method: 'get'
  })
}

// 原有的getAvgRating方法可以保留，也可以不使用
export function getAvgRating(newsId) {
  return request({
    url: '/news_rating/news_rating/avg/' + newsId,
    method: 'get'
  })
}

// 添加用户评分
export function addUserRating(data) {
  return request({
    url: '/news_rating/news_rating/rate',
    method: 'post',
    data: data
  })
}
