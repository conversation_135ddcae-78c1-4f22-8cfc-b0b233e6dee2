<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.SysNewsMapper">
    
    <resultMap type="SysNews" id="SysNewsResult">
        <result property="newsId"    column="news_id"    />
        <result property="newsTitle"    column="news_title"    />
        <result property="newsType"    column="news_type"    />
        <result property="newsContent"    column="news_content"    />
        <result property="status"    column="status"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="author"    column="author"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="headlinePic"    column="headline_pic"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectSysNewsVo">
        SELECT n.news_id, n.news_title, n.news_type, n.news_content, 
            n.status, n.reviewer, n.review_time, n.update_by, 
            n.update_time, n.remark, n.author, n.upload_time, 
            n.headline_pic, n.dept_id, d.dept_name, n.user_id 
        FROM sys_news n
        LEFT JOIN sys_user u ON n.user_id = u.user_id
        LEFT JOIN sys_dept d ON n.dept_id = d.dept_id
    </sql>

    <select id="selectSysNewsList" parameterType="SysNews" resultMap="SysNewsResult">
        <include refid="selectSysNewsVo"/>
        <where>  
            <if test="newsTitle != null and newsTitle != ''">
            <foreach item="keyword" index="index" collection="newsTitle.split(' ')" open="and (" separator=" or " close=")">
                n.news_title like concat('%', #{keyword}, '%')
            </foreach>
            </if>
            <if test="newsType != null  and newsType != ''"> and n.news_type = #{newsType}</if>
            <if test="newsContent != null and newsContent != ''">
            <foreach item="keyword" index="index" collection="newsContent.split(' ')" open="and (" separator=" or " close=")">
                n.news_content like concat('%', #{keyword}, '%')
            </foreach>
            </if>
            <if test="status != null  and status != ''"> and n.status = #{status}</if>
            <if test="reviewer != null  and reviewer != ''"> and n.reviewer = #{reviewer}</if>
            <if test="reviewTime != null "> and n.review_time = #{reviewTime}</if>
            <if test="author != null  and author != ''"> and n.author = #{author}</if>
            <if test="uploadTime != null "> and n.upload_time = #{uploadTime}</if>
            <if test="headlinePic != null  and headlinePic != ''"> and n.headline_pic = #{headlinePic}</if>
            <if test="deptId != null "> and n.dept_id = #{deptId}</if>
            <if test="userId != null "> and n.user_id = #{userId}</if>
            <if test="startTime != null"> and n.upload_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and n.upload_time &lt;= DATE_ADD(#{endTime}, INTERVAL 1 DAY)</if>
            
        </where>
        ORDER BY n.upload_time DESC
    </select>

    <select id="adminSysNewsList" parameterType="SysNews" resultMap="SysNewsResult">
        <include refid="selectSysNewsVo"/>
        <where>  
            <if test="newsTitle != null and newsTitle != ''">
            <foreach item="keyword" index="index" collection="newsTitle.split(' ')" open="and (" separator=" or " close=")">
                n.news_title like concat('%', #{keyword}, '%')
            </foreach>
            </if>
            <if test="newsType != null  and newsType != ''"> and n.news_type = #{newsType}</if>
            <if test="newsContent != null and newsContent != ''">
            <foreach item="keyword" index="index" collection="newsContent.split(' ')" open="and (" separator=" or " close=")">
                n.news_content like concat('%', #{keyword}, '%')
            </foreach>
            </if>
            <if test="status != null  and status != ''"> and n.status = #{status}</if>
            <if test="reviewer != null  and reviewer != ''"> and n.reviewer = #{reviewer}</if>
            <if test="reviewTime != null "> and n.review_time = #{reviewTime}</if>
            <if test="author != null  and author != ''"> and n.author = #{author}</if>
            <if test="uploadTime != null "> and n.upload_time = #{uploadTime}</if>
            <if test="headlinePic != null  and headlinePic != ''"> and n.headline_pic = #{headlinePic}</if>
            <if test="deptId != null "> and n.dept_id = #{deptId}</if>
            <if test="userId != null "> and n.user_id = #{userId}</if>
            <if test="startTime != null"> and n.upload_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and n.upload_time &lt;= DATE_ADD(#{endTime}, INTERVAL 1 DAY)</if>
            ${params.dataScope}
        </where>
        ORDER BY n.upload_time DESC
    </select>
    
    <select id="selectSysNewsByNewsId" parameterType="Long" resultMap="SysNewsResult">
        <include refid="selectSysNewsVo"/>
        where n.news_id = #{newsId}
    </select>
   <select id="countNewsByTimeAndType" parameterType="String" resultType="java.util.Map">
    <choose>
        <when test="type == 'year'">
            SELECT 
                YEAR(n.upload_time) as time,
                n.dept_id as deptId,
                MAX(n.author) as author,
                d.dept_name as deptName,
                COUNT(*) as count
            FROM 
                sys_news n
            LEFT JOIN sys_dept d ON n.dept_id = d.dept_id
            WHERE 
                n.status = '1'
                AND n.dept_id != 30
            GROUP BY 
                YEAR(n.upload_time), n.dept_id, d.dept_name
            ORDER BY 
                YEAR(n.upload_time);
        </when>
        <when test="type == 'quarter'">
            SELECT 
                time, deptId, MAX(author) as author, deptName, COUNT(*) as count
            FROM (
                SELECT 
                    CONCAT(YEAR(n.upload_time), '-Q', QUARTER(n.upload_time)) as time,
                    n.dept_id as deptId,
                    n.author as author,
                    d.dept_name as deptName
                FROM 
                    sys_news n
                LEFT JOIN sys_dept d ON n.dept_id = d.dept_id
                WHERE 
                    n.status = '1'
                    AND n.dept_id != 30
            ) t
            GROUP BY 
                time, deptId, deptName
            ORDER BY 
                time;
        </when>
        <when test="type == 'month'">
            SELECT 
                DATE_FORMAT(n.upload_time, '%Y-%m') as time,
                n.dept_id as deptId,
                MAX(n.author) as author,
                d.dept_name as deptName,
                COUNT(*) as count
            FROM 
                sys_news n
            LEFT JOIN sys_dept d ON n.dept_id = d.dept_id
            WHERE 
                n.status = '1'
                AND n.dept_id != 30
            GROUP BY 
                DATE_FORMAT(n.upload_time, '%Y-%m'), n.dept_id, d.dept_name
            ORDER BY 
                DATE_FORMAT(n.upload_time, '%Y-%m');
        </when>
    </choose>
</select>

    <insert id="insertSysNews" parameterType="SysNews" useGeneratedKeys="true" keyProperty="newsId">
        insert into sys_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="newsTitle != null and newsTitle != ''">news_title,</if>
            <if test="newsType != null and newsType != ''">news_type,</if>
            <if test="newsContent != null">news_content,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="author != null">author,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="headlinePic != null">headline_pic,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="newsTitle != null and newsTitle != ''">#{newsTitle},</if>
            <if test="newsType != null and newsType != ''">#{newsType},</if>
            <if test="newsContent != null">#{newsContent},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="author != null">#{author},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="headlinePic != null">#{headlinePic},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateSysNews" parameterType="SysNews">
        update sys_news n
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsTitle != null and newsTitle != ''">n.news_title = #{newsTitle},</if>
            <if test="newsType != null and newsType != ''">n.news_type = #{newsType},</if>
            <if test="newsContent != null">n.news_content = #{newsContent},</if>
            <if test="status != null and status != ''">n.status = #{status},</if>
            <if test="reviewer != null">n.reviewer = #{reviewer},</if>
            <if test="reviewTime != null">n.review_time = #{reviewTime},</if>
            <if test="updateBy != null">n.update_by = #{updateBy},</if>
            <if test="updateTime != null">n.update_time = #{updateTime},</if>
            <if test="remark != null">n.remark = #{remark},</if>
            <if test="author != null">n.author = #{author},</if>
            <if test="uploadTime != null">n.upload_time = #{uploadTime},</if>
            <if test="headlinePic != null">n.headline_pic = #{headlinePic},</if>
            <if test="deptId != null">n.dept_id = #{deptId},</if>
        </trim>
        where n.news_id = #{newsId}
    </update>

    <delete id="deleteSysNewsByNewsId" parameterType="Long">
        delete from sys_news n where n.news_id = #{newsId}
    </delete>

    <delete id="deleteSysNewsByNewsIds" parameterType="String">
        delete from sys_news n where n.news_id in 
        <foreach item="newsId" collection="array" open="(" separator="," close=")">
            #{newsId}
        </foreach>
    </delete>
</mapper>