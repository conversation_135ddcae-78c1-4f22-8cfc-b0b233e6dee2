package com.ruoyi.common.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * 图片处理工具类
 */
public class ImageUtils {
    
    /**
     * 生成缩略图
     * 
     * @param originalImageBytes 原图字节数组
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图字节数组
     */
    public static byte[] generateThumbnail(byte[] originalImageBytes, int width, int height) throws IOException {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(originalImageBytes);
        BufferedImage originalImage = ImageIO.read(inputStream);
        
        if (originalImage == null) {
            throw new IOException("无法读取图片");
        }
        
        // 计算缩放比例
        double scaleX = (double) width / originalImage.getWidth();
        double scaleY = (double) height / originalImage.getHeight();
        double scale = Math.min(scaleX, scaleY);
        
        int newWidth = (int) (originalImage.getWidth() * scale);
        int newHeight = (int) (originalImage.getHeight() * scale);
        
        // 创建缩略图
        BufferedImage thumbnailImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = thumbnailImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(thumbnailImage, "jpg", outputStream);
        
        return outputStream.toByteArray();
    }
} 