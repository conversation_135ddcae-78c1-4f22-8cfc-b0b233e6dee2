<template>
    <div class="app-container">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>国家列表</span>
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                placeholder="搜索国家名称（支持中文名、英文名、国家代码）"
                clearable
                @clear="handleSearch"
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="search-tips" v-if="searchQuery">
                <span>找到 {{ filteredCountries.length }} 个匹配结果</span>
                <span style="margin-left: 10px; color: #909399; font-size: 12px;">
                  支持：中文名、英文名、国家代码
                </span>
              </div>
            </div>
          </div>
        </template>

        <!-- 字母索引 -->
        <div class="letter-index">
          <el-button
            v-for="letter in availableLetters"
            :key="letter"
            :type="activeLetter === letter ? 'primary' : 'default'"
            size="small"
            @click="handleLetterClick(letter)"
            :disabled="!(letter in letterCounts)"
          >
            {{ letter }} <span v-if="letter in letterCounts">({{ letterCounts[letter] }})</span>
          </el-button>
        </div>

        <!-- 国家列表 -->
        <div v-loading="loading" class="country-container">
          <div v-if="filteredCountries.length > 0" class="letter-group">
            <div v-if="activeLetter" class="letter-header">{{ activeLetter }}</div>
            <div v-else-if="searchQuery" class="letter-header">
              搜索结果 ({{ filteredCountries.length }} 个)
            </div>
            <div class="country-grid">
              <el-card
                v-for="country in filteredCountries"
                :key="country.id"
                class="country-card"
                shadow="hover"
                :body-style="{ padding: '10px', cursor: 'pointer' }"
                @click="navigateToCountrySamples(country)"
              >
                <div class="country-content">
                  <div class="flag-container">
                    <img
                      :src="`/static/flags/${country.code.toUpperCase()}.png`"
                      :alt="country.name"
                      class="flag-image"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="country-info">
                    <h3 class="country-name">{{ country.name }}</h3>
                    <p class="country-name-en">{{ country.nameEn }}</p>
                    <p class="country-code">{{ country.code }}</p>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 无数据提示 -->
        <el-empty
          v-if="!loading && filteredCountries.length === 0"
          description="没有找到匹配的国家"
        ></el-empty>
      </el-card>
    </div>
  </template>

  <script setup>
  import { ref, computed, onMounted } from 'vue';
  import { getCountryList,listCountryByLetter, getCountryLetters } from '@/api/cert/country';
  import { Search } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';

  // 初始化 router
  const router = useRouter();

  // 字母索引
  const availableLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  const letterCounts = ref({});
  const activeLetter = ref('');
  const searchQuery = ref('');
  const countries = ref([]);
  const loading = ref(true);
  const allCountries = ref([]); // 存储所有国家数据，用于搜索

  // 处理国家数据，映射字段名称
  const processCountryData = (countryList) => {
    return countryList.map(country => ({
      ...country,
      // 统一字段名称映射
      name: country.name_cn || country.name || country.nameCn,
      nameEn: country.name_en || country.nameEn,
      code: country.code
    }));
  };

  // 获取所有字母及对应的国家数量
  const getLetters = async () => {
    try {
      console.log('开始获取字母统计...');
      const response = await getCountryLetters();
      console.log('获取字母统计响应:', response);
      letterCounts.value = response.data || {};
      console.log('处理后的字母统计:', letterCounts.value);
    } catch (error) {
      console.error('获取字母统计失败:', error);
      if (error.response) {
        console.error('错误响应数据:', error.response.data);
        console.error('错误状态码:', error.response.status);
      }
      ElMessage.error('获取字母统计失败: ' + (error.message || '未知错误'));
    }
  };

  // 根据字母获取国家列表
  const getCountriesByLetter = async (letter) => {
    loading.value = true;
    try {
      const response = await listCountryByLetter(letter);
      const rawCountries = response.data || [];
      countries.value = processCountryData(rawCountries);
    } catch (error) {
      console.error(`获取${letter}开头的国家列表失败:`, error);
      ElMessage.error(`获取${letter}开头的国家列表失败`);
      countries.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 处理字母点击
  const handleLetterClick = (letter) => {
    if (letter in letterCounts.value) {
      activeLetter.value = letter;
      searchQuery.value = ''; // 清空搜索框
      getCountriesByLetter(letter);
    }
  };

  // 简化的搜索过滤功能
  const filteredCountries = computed(() => {
    if (!searchQuery.value || !searchQuery.value.trim()) {
      return countries.value;
    }

    const query = searchQuery.value.toLowerCase().trim();

    // 如果正在搜索，使用所有国家数据进行搜索
    const searchPool = allCountries.value.length > 0 ? allCountries.value : countries.value;

    return searchPool.filter(country => {
      // 使用表中的三个字段进行模糊搜索
      return (country.name && country.name.toLowerCase().includes(query)) ||
             (country.nameEn && country.nameEn.toLowerCase().includes(query)) ||
             (country.code && country.code.toLowerCase().includes(query));
    });
  });

  // 处理搜索，支持实时搜索
  const handleSearch = async () => {
    if (searchQuery.value && searchQuery.value.trim()) {
      // 清除字母选择状态，因为现在是全局搜索
      activeLetter.value = '';

      // 如果有搜索内容，确保加载了所有国家数据
      if (allCountries.value.length === 0) {
        await getAllCountries();
      }

      // 设置countries.value为所有国家数据，这样filteredCountries才能正确工作
      countries.value = allCountries.value;
    } else {
      // 如果清空搜索，回到字母选择模式
      if (letterCounts.value && Object.keys(letterCounts.value).length > 0) {
        const firstLetter = Object.keys(letterCounts.value).sort()[0];
        if (firstLetter) {
          handleLetterClick(firstLetter);
        }
      }
    }
  };

  // 获取所有国家
  const getAllCountries = async () => {
    loading.value = true;
    try {
      const response = await getCountryList({ pageSize: 9999 }); // 获取所有国家
      const rawCountries = response.rows || response.data || [];
      allCountries.value = processCountryData(rawCountries);

      // 如果当前没有选中字母且有搜索内容，显示搜索结果
      if (!activeLetter.value && searchQuery.value) {
        countries.value = allCountries.value;
      }
    } catch (error) {
      console.error('获取所有国家列表失败:', error);
      ElMessage.error('获取国家列表失败');
      allCountries.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 处理图片加载错误
  const handleImageError = (e) => {
    console.warn('图片加载失败:', e.target.src);
    e.target.src = '/static/flags/default.png'; // 默认图片路径
  };

  // 导航到国家证件版本页面
  // 这个功能提供国家维度的版本查看入口，与全局版本管理形成互补
  const navigateToCountrySamples = (country) => {
    console.log('点击国家卡片:', country); // 添加调试日志
    router.push({
      name: 'CountryCertVersions',
      params: { countryId: country.id },
      query: {
        countryCode: country.code,
        countryName: country.name,
        countryNameEn: country.nameEn
      }
    });
  };

  // 组件挂载时获取数据
  onMounted(async () => {
    await getLetters();
    await getAllCountries(); // 预加载所有国家数据

    // 默认选择第一个有国家的字母
    if (Object.keys(letterCounts.value).length > 0) {
      const letters = Object.keys(letterCounts.value).sort();
      if (letters.length > 0) {
        handleLetterClick(letters[0]);
      }
    }
  });
  </script>

  <style scoped>
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-box {
    width: 350px;
  }

  .search-tips {
    margin-top: 5px;
    font-size: 12px;
    color: #606266;
    padding: 2px 0;
  }

  .letter-index {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .country-container {
    margin-top: 20px;
    min-height: 200px;
  }

  .letter-group {
    margin-bottom: 30px;
  }

  .letter-header {
    font-size: 24px;
    font-weight: bold;
    padding: 10px 0;
    border-bottom: 2px solid #409EFF;
    margin-bottom: 15px;
  }

  .country-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }

  .country-card {
    transition: transform 0.3s;
    cursor: pointer;
  }

  .country-card:hover {
    transform: translateY(-5px);
    border-color: #409EFF;
  }

  .country-content {
    display: flex;
    align-items: center;
  }

  .flag-container {
    width: 60px;
    height: 40px;
    margin-right: 15px;
    overflow: hidden;
    border: 1px solid #ebeef5;
  }

  .flag-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .country-info {
    flex: 1;
  }

  .country-name {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .country-name-en {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
  }

  .country-code {
    margin: 0;
    font-size: 12px;
    color: #909399;
    background-color: #f0f2f5;
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
  }
  </style>
