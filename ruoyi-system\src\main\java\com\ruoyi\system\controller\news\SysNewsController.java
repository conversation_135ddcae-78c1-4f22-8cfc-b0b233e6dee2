package com.ruoyi.system.controller.news;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.news.SysNews;
import com.ruoyi.system.service.news.ISysNewsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.news.ISysNotificationService;

/**
 * 文章管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/news/news")
public class SysNewsController extends BaseController
{
    @Autowired
    private ISysNewsService sysNewsService;

    @Autowired
    private ISysNotificationService notificationService;

    /**
     * 查询文章管理列表
     */
    //@PreAuthorize("@ss.hasPermi('news:news:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(SysNews sysNews)
    {
        sysNews.setStatus("1");
        startPage();
        List<SysNews> list = sysNewsService.selectSysNewsList(sysNews);
        return getDataTable(list);
    }

    /**
     * 导出文章管理列表
     */
    @PreAuthorize("@ss.hasPermi('news:news:export')")
    @Log(title = "文章管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysNews sysNews)
    {
        List<SysNews> list = sysNewsService.selectSysNewsList(sysNews);
        ExcelUtil<SysNews> util = new ExcelUtil<SysNews>(SysNews.class);
        util.exportExcel(response, list, "文章管理数据");
    }
    /**
     * 查询管理员文章管理列表
     */
    @PreAuthorize("@ss.hasPermi('news:news:adminList')")
    @GetMapping("/adminList")
    public TableDataInfo adminList(SysNews sysNews)
    {
        startPage();
        List<SysNews> list = sysNewsService.adminSysNewsList(sysNews);
        return getDataTable(list);
    }

    /**
     * 获取文章管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('news:news:query')")
    @GetMapping(value = "/{newsId}")
    @Anonymous
    public AjaxResult getInfo(@PathVariable("newsId") Long newsId)
    {
        return success(sysNewsService.selectSysNewsByNewsId(newsId));
    }

    /**
     * 新增文章管理
     */
    @PreAuthorize("@ss.hasPermi('news:news:add')")
    @Log(title = "文章管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysNews sysNews)
    {
        sysNews.setAuthor(getUsername());
        sysNews.setUserId(getUserId());
        sysNews.setDeptId(getDeptId());
        sysNews.setStatus("0");
        return toAjax(sysNewsService.insertSysNews(sysNews));
    }

    /**
     * 修改文章管理
     */
    @PreAuthorize("@ss.hasPermi('news:news:edit')")
    @Log(title = "文章管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysNews sysNews)
    {
        return toAjax(sysNewsService.updateSysNews(sysNews));
    }

    /**
     * 删除文章管理
     */
    @PreAuthorize("@ss.hasPermi('news:news:remove')")
    @Log(title = "文章管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{newsIds}")
    public AjaxResult remove(@PathVariable Long[] newsIds)
    {
        return toAjax(sysNewsService.deleteSysNewsByNewsIds(newsIds));
    }

    /**
     * 审核文章
     */
    @PreAuthorize("@ss.hasPermi('news:news:audit')")
    @Log(title = "文章管理", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{newsId}")
    public AjaxResult auditNews(@PathVariable("newsId") Long newsId, @RequestParam("auditComment") String auditComment) {
        SysNews sysNews = new SysNews();
        sysNews.setNewsId(newsId);
        sysNews.setStatus("1");
        sysNews.setReviewer(getUsername());
        sysNews.setReviewTime(DateUtils.getNowDate());
        sysNews.setAuditComment(auditComment); // 新增审核意见

        // 获取文章信息，用于发送通知
        SysNews news = sysNewsService.selectSysNewsByNewsId(newsId);
        if (news != null) {
            // 发送审核通过通知，包含审核意见
            notificationService.sendAuditNotification(news.getUserId(), newsId, news.getNewsTitle(), true, auditComment);
        }

        return toAjax(sysNewsService.updateSysNews(sysNews));
    }

    /**
     * 审核不通过文章
     */
    @PreAuthorize("@ss.hasPermi('news:news:audit')")
    @Log(title = "文章管理", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{newsId}")
    public AjaxResult rejectNews(@PathVariable("newsId") Long newsId, @RequestParam("auditComment") String auditComment) {
        SysNews sysNews = new SysNews();
        sysNews.setNewsId(newsId);
        sysNews.setStatus("2"); // 2表示审核不通过
        sysNews.setReviewer(getUsername());
        sysNews.setReviewTime(DateUtils.getNowDate());
        sysNews.setAuditComment(auditComment); // 新增审核意见

        // 获取文章信息，用于发送通知
        SysNews news = sysNewsService.selectSysNewsByNewsId(newsId);
        if (news != null) {
            // 发送审核不通过通知，包含审核意见
            notificationService.sendAuditNotification(news.getUserId(), newsId, news.getNewsTitle(), false, auditComment);
        }

        return toAjax(sysNewsService.updateSysNews(sysNews));
    }

    /**
     * 按年、季度、月统计部门和个人发布的文章数量
     */
    //@PreAuthorize("@ss.hasPermi('news:news:statistics')")
    @GetMapping("/statistics")
    @Anonymous
    public AjaxResult countNewsByTimeAndType(String type)
    {
        List<Map<String, Object>> result = sysNewsService.countNewsByTimeAndType(type);
        return success(result);
    }

}
