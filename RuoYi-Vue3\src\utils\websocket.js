import  useUserStore  from '@/store/modules/user'
import { ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'

let socket = null
let isConnected = false
let reconnectTimer = null
let reconnectAttempts = 0
const MAX_RECONNECT_ATTEMPTS = 5

export function setupWebSocket() {
  const userStore = useUserStore()
  const userId = userStore.userId
  const router = useRouter()

  if (!userId) {
    console.error('用户未登录，无法建立WebSocket连接')
    return
  }

  // 关闭之前的连接
  closeWebSocket()

  // 创建WebSocket连接
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.host
  const url = `${protocol}//${host}/websocket/${userId}`

  socket = new WebSocket(url)

  // 连接建立时触发
  socket.onopen = () => {
    console.log('WebSocket连接已建立')
    isConnected = true
    reconnectAttempts = 0
    clearTimeout(reconnectTimer)
  }

  // 接收到消息时触发
  socket.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'notification') {
        // 显示通知
        ElNotification({
          title: data.title,
          message: data.content,
          type: 'info',
          duration: 5000,
          onClick: () => {
            router.push('/system/notification')
          }
        })
        
        // 更新未读消息数量
        userStore.getUnreadMessageCount()
      }
    } catch (error) {
      console.error('处理WebSocket消息出错:', error)
    }
  }

  // 连接关闭时触发
  socket.onclose = () => {
    console.log('WebSocket连接已关闭')
    isConnected = false
    
    // 尝试重新连接
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000)
      console.log(`${delay / 1000}秒后尝试重新连接...`)
      
      reconnectTimer = setTimeout(() => {
        setupWebSocket()
      }, delay)
    }
  }

  // 连接发生错误时触发
  socket.onerror = (error) => {
    console.error('WebSocket连接发生错误:', error)
  }
}

export function closeWebSocket() {
  if (socket && (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)) {
    socket.close()
  }
  
  clearTimeout(reconnectTimer)
  isConnected = false
}

export function isWebSocketConnected() {
  return isConnected && socket && socket.readyState === WebSocket.OPEN
}

export default {
  setupWebSocket,
  closeWebSocket,
  isWebSocketConnected
}
