<template>
  <TopNavbar />
    <div class="news-detail-container">
      <el-card>
        <template #header>
          <div class="title-container">
            <h2 class="title">{{ newsDetail.newsTitle || '加载中...' }}</h2>
            <div class="meta-container">
              <div class="info">
                <span>发布单位：{{ newsDetail.deptName || '未知部门' }}</span>
                <span>发布时间：{{ newsDetail.uploadTime || '' }}</span>
                <span v-if="newsDetail.source">作者：{{ newsDetail.source }}</span>
              </div>
              <div class="rating-section">
                <div class="rating-info">
                  <span>文章评分：{{ avgRating.toFixed(1) }}</span>
                  <span>({{ ratingCount }}人评价)</span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="content" v-html="newsDetail.content || '加载中...'"></div>
      </el-card>

      <!-- 评论区 -->
      <el-card class="comment-card">
        <div slot="header" class="clearfix">
          <div class="comment-header">
            <div class="rating-stars">
              <span class="rating-label">文章评分：</span>
              <el-rate
                v-model="userRating"
                :colors="colors"
                @change="handleRateChange"
                :disabled="ratingDisabled"
                :show-score="true"
                text-color="#ff9900"
                score-template="{value}"
              ></el-rate>
            </div>
            <span class="comment-title">评论区</span>
          </div>
        </div>

        <!-- 评论列表 -->
        <div class="comment-list">
          <div v-if="commentList.length === 0" class="no-comment">
            暂无评论，快来发表您的看法吧！
          </div>
          <div v-for="comment in commentList" :key="comment.commentId" class="comment-item">
            <el-avatar :src="comment.avatar || defaultAvatar" :size="40"></el-avatar>
            <div class="comment-content">
              <div class="comment-user">
                <span class="user-name">{{ comment.userName }}</span>
                <span class="comment-time">{{ parseTime(comment.createTime) }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
            </div>
          </div>
        </div>

        <!-- 发表评论 -->
        <div class="comment-form">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入您的评论..."
            v-model="commentContent"
            maxlength="500"
            show-word-limit
          >
          </el-input>
          <div class="comment-btn">
            <el-button type="primary" @click="submitComment" :loading="submitting">发表评论</el-button>
          </div>
        </div>
      </el-card>
      <div class="btn-container">
        <el-button @click="goBack">返回</el-button>
      </div>
      <FooterComponent />
    </div>
  </template>

  <script setup>
  import { ref, onMounted} from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { getNews } from '@/api/news/news'
  import { ElMessage } from 'element-plus'
  import { getCommentsByNewsId, addUserComment } from "@/api/news/comment"
  import { getPublicAvgRating, getUserRating, addUserRating } from "@/api/news/news_rating"
  import { parseTime } from '@/utils/ruoyi'
  import defaultAvatarSrc from "@/assets/images/profile.jpg";
  import FooterComponent from '@/views/news/components/FooterComponent.vue'
  import TopNavbar from '@/views/news/components/TopNavbar.vue';
  import { getToken } from '@/utils/auth' // 导入 getToken 函数
  import useUserStore from '@/store/modules/user' // 导入 userStore

  const userStore = useUserStore() // 使用 userStore
  const defaultAvatar = ref(defaultAvatarSrc);

  // 检查用户登录状态的方法
  const checkUserLoginStatus = () => {
    return !!getToken() // 如果有 token，则认为用户已登录
  }

  // 评分相关
  const avgRating = ref(0)
  const ratingCount = ref(0)
  const userRating = ref(0)
  const ratingDisabled = ref(false)
  const colors = ['#99A9BF', '#F7BA2A', '#FF9900'] // 三色分级

  const route = useRoute()
  const router = useRouter()
  const newsDetail = ref({})
  const commentList = ref([])
  const commentContent = ref("")
  const submitting = ref(false)

  onMounted(() => {
    fetchNewsDetail()
    getComments()
    getNewsRating()
  })

  const fetchNewsDetail = async () => {
    try {
      const newsId = route.params.newsId
      if (!newsId) {
        throw new Error('缺少新闻ID')
      }

      const response = await getNews(newsId)
      let content = response.data.newsContent

      newsDetail.value = {
        newsId: response.data.newsId,
        newsTitle: response.data.newsTitle,
        uploadTime: response.data.uploadTime,
        source: response.data.author,
        deptName: response.data.deptName,
        content: content,
        userId: response.data.userId
      }
    } catch (error) {
      console.error('获取新闻详情失败', error)
      ElMessage.error('获取新闻详情失败')
    }
  }

  const goBack = () => {
    router.go(-1)
  }

  const getComments = async () => {
    try {
      const response = await getCommentsByNewsId(route.params.newsId)
      commentList.value = response.data
    } catch (error) {
      console.error('获取评论列表失败', error)
      ElMessage.error('获取评论列表失败')
    }
  }

  const submitComment = async () => {
    // 检查用户登录状态
    const isLoggedIn = checkUserLoginStatus();
    if (!isLoggedIn) {
      ElMessage.warning('您尚未登录，请登录后再评论');
      return; // 直接返回，不执行后续逻辑
    }

    if (!commentContent.value.trim()) {
      ElMessage.warning('评论内容不能为空');
      return;
    }

    submitting.value = true;
    try {
      const response = await addUserComment({
        newsId: route.params.newsId,
        content: commentContent.value,
        authorId: newsDetail.value.userId
      });
      if (response.code === 200) {
        ElMessage.success('评论发表成功');
        commentContent.value = '';
        getComments(); // 重新获取评论列表
      } else {
        ElMessage.error('评论发表失败，请重试');
      }
    } catch (error) {
      console.error('发表评论失败', error);
      ElMessage.error('发表评论失败，请重试');
    } finally {
      submitting.value = false;
    }
  }

  // 获取文章评分信息
  const getNewsRating = async () => {
    try {
      const newsId = route.params.newsId
      const isLoggedIn = checkUserLoginStatus(); // 检查用户是否登录

      // 获取平均评分和评分人数（匿名可访问）
      try {
        const response = await getPublicAvgRating(newsId)
        if (response.code === 200) {
          avgRating.value = response.data.avgRating || 0
          ratingCount.value = response.data.ratingCount || 0
        } else {
          console.error('获取平均评分失败:', response.msg)
          avgRating.value = 0
          ratingCount.value = 0
        }
      } catch (error) {
        console.error('获取平均评分失败', error)
        avgRating.value = 0
        ratingCount.value = 0
      }

      // 只有登录用户才获取自己的评分信息
      if (isLoggedIn) {
        try {
          const userRatingResponse = await getUserRating(newsId)
          if (userRatingResponse.code === 200) {
            userRating.value = userRatingResponse.data || 0
          }
          ratingDisabled.value = false // 登录用户可以评分
        } catch (userRatingError) {
          console.error('获取用户评分失败', userRatingError)
          userRating.value = 0
        }
      } else {
        // 未登录用户
        userRating.value = 0
        ratingDisabled.value = false // 允许显示评分组件
      }
    } catch (error) {
      console.error('获取评分信息失败', error)
      avgRating.value = 0
      ratingCount.value = 0
      userRating.value = 0
    }
  }

  // 用户评分变更
  const handleRateChange = async (value) => {
    const isLoggedIn = checkUserLoginStatus();
    if (!isLoggedIn) {
      ElMessage.warning('您尚未登录，请登录后再评分')
      userRating.value = 0 // 重置评分
      return
    }

    try {
      const newsId = route.params.newsId
      const response = await addUserRating({
        newsId: newsId,
        score: value,
      })

      if (response.code === 200) {
        ElMessage.success('评分成功')
        // 重新获取平均评分和用户评分
        getNewsRating()
      } else {
        ElMessage.error('评分失败，请重试')
        // 恢复用户之前的评分
        userRating.value = 0
      }
    } catch (error) {
      console.error('提交评分失败', error)
      ElMessage.error('提交评分失败，请重试')
      // 恢复用户之前的评分
      userRating.value = 0
    }
  }

  </script>

<style scoped lang="scss">
.news-detail-container {
  padding: 20px 15%;
  background-color: #f5f9fd;

  .title-container {
    text-align: center;
    margin-bottom: 20px;

    .title {
      margin-bottom: 20px;
      font-size: 26px;
      font-family: "Microsoft YaHei", "黑体", sans-serif;
      font-weight: bold;
      color: #333;
    }

    .meta-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 15px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #ccc;

      .info {
        text-align: left;
        color: #666;
        font-size: 16px;

        span {
          margin-right: 20px;
        }
      }

      .rating-section {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .rating-info {
          margin-bottom: 8px;
          font-size: 16px;
          color: #666;

          span {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .content {
    min-height: 300px;
    line-height: 1.8;
    font-size: 16px;
    padding: 20px 0;

    /* Quill editor content styles */
    :deep() {
      p {
        margin-bottom: 1.2em;
      }

      .ql-align-center {
        text-align: center;
      }

      .ql-align-justify {
        text-align: justify;
      }

      .ql-align-right {
        text-align: right;
      }
    }

    img {
      max-width: 100%;
      height: auto;
      margin: 20px 0;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .btn-container {
    margin-top: 30px;
    text-align: right;
  }

  .comment-card {
    margin-top: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .clearfix {
      display: block;
    }

    .comment-header {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 10px 0;
    }

    .rating-stars {
      margin-bottom: 10px;
      margin-left: 10px;
    }

    .comment-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-left: 10px;
      padding-bottom: 5px;
      border-bottom: 2px solid #409EFF;
    }

    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .rating-label {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-right: 15px;
    }
  }

  .comment-list {
    margin-bottom: 25px;
  }

  .no-comment {
    text-align: center;
    color: #999;
    padding: 30px 0;
    font-size: 16px;
  }

  .comment-item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f9f9f9;
    }
  }

  .comment-item:last-child {
    border-bottom: none;
  }

  .comment-content {
    margin-left: 15px;
    flex: 1;
  }

  .comment-user {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .user-name {
    font-weight: bold;
    color: #409EFF;
  }

  .comment-time {
    font-size: 14px;
    color: #999;
  }

  .comment-text {
    line-height: 1.6;
    color: #333;
  }

  .comment-form {
    margin-top: 25px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
  }

  .comment-btn {
    margin-top: 15px;
    text-align: right;
  }
}

@media (max-width: 768px) {
  .news-detail-container {
    padding: 15px 5%;

    .title-container {
      .title {
        font-size: 22px;
      }

      .meta-container {
        flex-direction: column;
        align-items: center;

        .info, .rating-section {
          width: 100%;
          text-align: center;
          margin-bottom: 15px;
        }

        .rating-section {
          align-items: center;
        }
      }
    }
  }
}
</style>
