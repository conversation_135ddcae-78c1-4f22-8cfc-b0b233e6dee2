package com.ruoyi.common.service.impl;

import com.ruoyi.common.config.MinioConfig;
import com.ruoyi.common.service.MinioService;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;

/**
 * MinIO 服务实现
 *
 * <AUTHOR>
 */
@Service
public class MinioServiceImpl implements MinioService {
    
    private static final Logger log = LoggerFactory.getLogger(MinioServiceImpl.class);
    
    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    /**
     * 初始化MinIO bucket策略
     */
    @PostConstruct
    public void initBucketPolicy() {
        try {
            String bucketName = minioConfig.getBucketName();
            log.info("初始化MinIO bucket策略: {}", bucketName);

            // 检查bucket是否存在，如果不存在则创建
            if (!isBucketExist(bucketName)) {
                log.info("Bucket不存在，创建bucket: {}", bucketName);
                createBucket(bucketName);
            }

            // 设置bucket策略为公共读取
            setBucketPolicy(bucketName);

            log.info("MinIO bucket策略初始化完成: {}", bucketName);
        } catch (Exception e) {
            log.error("初始化MinIO bucket策略失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置bucket策略为公共读取
     */
    private void setBucketPolicy(String bucketName) throws Exception {
        try {
            // 创建允许公共读取的策略
            String policy = "{\n" +
                    "  \"Version\": \"2012-10-17\",\n" +
                    "  \"Statement\": [\n" +
                    "    {\n" +
                    "      \"Effect\": \"Allow\",\n" +
                    "      \"Principal\": {\n" +
                    "        \"AWS\": \"*\"\n" +
                    "      },\n" +
                    "      \"Action\": [\n" +
                    "        \"s3:GetObject\"\n" +
                    "      ],\n" +
                    "      \"Resource\": [\n" +
                    "        \"arn:aws:s3:::" + bucketName + "/*\"\n" +
                    "      ]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";

            minioClient.setBucketPolicy(
                    SetBucketPolicyArgs.builder()
                            .bucket(bucketName)
                            .config(policy)
                            .build()
            );

            log.info("成功设置bucket策略为公共读取: {}", bucketName);
        } catch (Exception e) {
            log.warn("设置bucket策略失败，但不影响基本功能: {}", e.getMessage());
            // 不抛出异常，因为策略设置失败不应该影响基本的文件操作
        }
    }
    
    /**
     * 上传文件
     * 
     * @param file 文件
     * @param directory 目录
     * @return 文件URL
     */
    @Override
    public String uploadFile(MultipartFile file, String directory) throws Exception {
        // 检查存储桶是否存在
        boolean bucketExists = isBucketExist(minioConfig.getBucketName());
        if (!bucketExists) {
            createBucket(minioConfig.getBucketName());
        }
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String objectName = directory + "/" + UUID.randomUUID().toString() + fileExtension;
        
        // 上传文件
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(file.getInputStream(), file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build()
        );
        
        log.info("文件上传成功: {}", objectName);
        
        // 返回文件URL
        return getFileUrl(objectName);
    }
    
    /**
     * 上传文件
     * 
     * @param inputStream 输入流
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @return 文件URL
     */
    @Override
    public String uploadFile(InputStream inputStream, String objectName, String contentType) throws Exception {
        // 检查存储桶是否存在
        boolean bucketExists = isBucketExist(minioConfig.getBucketName());
        if (!bucketExists) {
            createBucket(minioConfig.getBucketName());
        }
        
        // 上传文件
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(inputStream, -1, 10485760)
                        .contentType(contentType)
                        .build()
        );
        
        log.info("文件上传成功: {}", objectName);
        
        // 返回文件URL
        return getFileUrl(objectName);
    }
    
    /**
     * 获取文件URL
     * 
     * @param objectName 对象名称
     * @return 文件URL
     */
    @Override
    public String getFileUrl(String objectName) throws Exception {
        // 使用预签名URL代替直接访问URL
        return getPresignedUrl(objectName, 7 * 24 * 3600); // 7天有效期
    }
    
    /**
     * 获取临时访问URL
     * 
     * @param objectName 对象名称
     * @param expirySeconds 过期时间（秒）
     * @return 临时访问URL
     */
    @Override
    public String getPresignedUrl(String objectName, int expirySeconds) throws Exception {
        return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .method(Method.GET)
                        .expiry(expirySeconds, TimeUnit.SECONDS)
                        .build()
        );
    }
    
    /**
     * 删除文件
     * 
     * @param objectName 对象名称
     */
    @Override
    public void deleteFile(String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .build()
        );
        
        log.info("文件删除成功: {}", objectName);
    }
    
    /**
     * 批量删除文件
     *
     * @param objectNames 对象名称列表
     */
    @Override
    public void deleteFiles(List<String> objectNames) throws Exception {
        if (objectNames == null || objectNames.isEmpty()) {
            log.info("没有文件需要删除");
            return;
        }

        log.info("开始批量删除文件，共{}个文件", objectNames.size());

        // 先列出存储桶中的所有文件，用于调试
        listAllFilesForDebug();

        // 使用更智能的删除策略：先列出实际存在的文件，然后删除
        List<DeleteObject> objectsToDelete = new ArrayList<>();

        for (String objectName : objectNames) {
            try {
                log.info("🔍 检查文件删除: 原始路径={}, 存储桶={}", objectName, minioConfig.getBucketName());

                // 检查文件是否存在（使用原始路径）
                boolean exists = checkFileExists(objectName);
                log.info("🔍 文件存在性检查结果: 路径={}, 存在={}", objectName, exists);

                if (exists) {
                    objectsToDelete.add(new DeleteObject(objectName));
                    log.info("✅ 文件存在，添加到删除列表: 完整路径={}://{}/{}",
                        "minio", minioConfig.getBucketName(), objectName);
                } else {
                    // 如果原始路径不存在，尝试查找可能的编码变体
                    log.info("🔍 原始路径不存在，尝试查找实际路径: {}", objectName);
                    String actualPath = findActualFilePath(objectName);
                    if (actualPath != null) {
                        objectsToDelete.add(new DeleteObject(actualPath));
                        log.info("✅ 找到实际文件路径，添加到删除列表: 原始={}, 实际={}, 完整路径={}://{}/{}",
                            objectName, actualPath, "minio", minioConfig.getBucketName(), actualPath);
                    } else {
                        log.warn("❌ 文件不存在，跳过删除: 完整路径={}://{}/{}",
                            "minio", minioConfig.getBucketName(), objectName);
                    }
                }
            } catch (Exception e) {
                log.error("❌ 检查文件存在性失败: 完整路径={}://{}/{}",
                    "minio", minioConfig.getBucketName(), objectName, e);
                // 即使检查失败，也尝试删除原始路径
                objectsToDelete.add(new DeleteObject(objectName));
                log.info("⚠️ 检查失败，仍添加到删除列表: {}", objectName);
            }
        }

        if (objectsToDelete.isEmpty()) {
            log.warn("❌ 没有找到需要删除的文件");
            return;
        }

        log.info("🗑️ 开始执行批量删除，存储桶={}, 文件数量={}", minioConfig.getBucketName(), objectsToDelete.size());
        log.info("🗑️ 删除列表详情:");
        for (DeleteObject obj : objectsToDelete) {
            log.info("  - 待删除文件: {}://{}/{}", "minio", minioConfig.getBucketName(),obj.toString());
        }

        // 执行批量删除
        Iterable<Result<DeleteError>> results = minioClient.removeObjects(
                RemoveObjectsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .objects(objectsToDelete)
                        .build()
        );

        // 检查删除结果
        int errorCount = 0;
        int successCount = 0;
        for (Result<DeleteError> result : results) {
            DeleteError error = result.get();
            log.error("❌ 删除文件失败: 完整路径={}://{}/{}, 错误代码={}, 错误信息={}",
                "minio", minioConfig.getBucketName(), error.objectName(),
                error.code(), error.message());
            errorCount++;
        }

        successCount = objectsToDelete.size() - errorCount;
        log.info("🗑️ 批量删除文件完成, 尝试删除{}个文件, 成功{}个, 失败{}个",
            objectsToDelete.size(), successCount, errorCount);

        if (successCount > 0) {
            log.info("✅ 成功删除的文件数量: {}", successCount);
        }
        if (errorCount > 0) {
            log.error("❌ 删除失败的文件数量: {}", errorCount);
        }
    }

    /**
     * 检查文件是否存在
     */
    private boolean checkFileExists(String objectName) {
        try {
            log.debug("🔍 检查文件存在性: 存储桶={}, 对象路径={}", minioConfig.getBucketName(), objectName);

            StatObjectResponse response = minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .build()
            );

            log.debug("✅ 文件存在: 路径={}, 大小={}, 最后修改={}",
                objectName, response.size(), response.lastModified());
            return true;
        } catch (Exception e) {
            log.debug("❌ 文件不存在或检查失败: 路径={}, 错误={}", objectName, e.getMessage());
            return false;
        }
    }

    /**
     * 查找文件的实际路径（处理编码问题）
     */
    private String findActualFilePath(String expectedPath) {
        try {
            log.debug("🔍 查找实际文件路径: 期望路径={}", expectedPath);

            // 提取路径前缀（任务名称部分）
            String prefix = expectedPath;
            int lastSlashIndex = expectedPath.lastIndexOf('/');
            if (lastSlashIndex > 0) {
                prefix = expectedPath.substring(0, lastSlashIndex + 1);
            }

            log.debug("🔍 使用前缀列出文件: 存储桶={}, 前缀={}", minioConfig.getBucketName(), prefix);

            // 列出该前缀下的所有文件
            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .prefix(prefix)
                    .build()
            );

            String expectedFileName = expectedPath.substring(lastSlashIndex + 1);
            log.debug("🔍 期望的文件名: {}", expectedFileName);

            int fileCount = 0;
            // 查找匹配的文件
            for (Result<Item> result : results) {
                Item item = result.get();
                String actualPath = item.objectName();
                String actualFileName = actualPath.substring(actualPath.lastIndexOf('/') + 1);
                fileCount++;

                log.debug("🔍 检查文件 #{}: 实际路径={}, 实际文件名={}", fileCount, actualPath, actualFileName);

                // 比较文件名（忽略编码差异）
                if (actualFileName.equals(expectedFileName) ||
                    URLEncoder.encode(actualFileName, StandardCharsets.UTF_8.toString()).equals(expectedFileName) ||
                    actualFileName.equals(URLEncoder.encode(expectedFileName, StandardCharsets.UTF_8.toString()))) {
                    log.info("✅ 找到匹配文件: 期望={}, 实际={}", expectedPath, actualPath);
                    return actualPath;
                }
            }

            log.warn("❌ 未找到匹配文件: 期望路径={}, 检查了{}个文件", expectedPath, fileCount);
        } catch (Exception e) {
            log.error("❌ 查找实际文件路径失败: 期望路径={}", expectedPath, e);
        }
        return null;
    }

    /**
     * 列出存储桶中的所有文件（用于调试）
     */
    private void listAllFilesForDebug() {
        try {
            log.info("🔍 调试：列出存储桶 {} 中的所有文件", minioConfig.getBucketName());

            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .recursive(true)
                    .build()
            );

            int fileCount = 0;
            for (Result<Item> result : results) {
                Item item = result.get();
                fileCount++;
                log.info("🔍 文件 #{}: 路径={}, 大小={}, 最后修改={}",
                    fileCount, item.objectName(), item.size(), item.lastModified());

                // 只显示前20个文件，避免日志过多
                if (fileCount >= 20) {
                    log.info("🔍 ... 还有更多文件，只显示前20个");
                    break;
                }
            }

            if (fileCount == 0) {
                log.warn("🔍 存储桶 {} 中没有文件", minioConfig.getBucketName());
            } else {
                log.info("🔍 存储桶 {} 中共有{}+个文件", minioConfig.getBucketName(), fileCount);
            }
        } catch (Exception e) {
            log.error("🔍 列出文件失败", e);
        }
    }

    /**
     * 检查字符串是否包含中文字符
     */
    private boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param objectName 对象名称
     * @return 是否存在
     */
    @Override
    public boolean isFileExist(String objectName) throws Exception {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(objectName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 创建存储桶
     * 
     * @param bucketName 存储桶名称
     */
    @Override
    public void createBucket(String bucketName) throws Exception {
        boolean bucketExists = isBucketExist(bucketName);
        if (!bucketExists) {
            minioClient.makeBucket(
                    MakeBucketArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
            log.info("存储桶创建成功: {}", bucketName);
        }
    }
    
    /**
     * 检查存储桶是否存在
     * 
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    @Override
    public boolean isBucketExist(String bucketName) throws Exception {
        return minioClient.bucketExists(
                BucketExistsArgs.builder()
                        .bucket(bucketName)
                        .build()
        );
    }
    
    /**
     * greenfox - 新增：获取文件对象流 - 2025-01-15
     *
     * @param objectName 对象名称
     * @return 文件输入流
     */
    @Override
    public InputStream getObject(String objectName) throws Exception {
        try {
            log.info("尝试获取MinIO对象: bucket={}, object={}", minioConfig.getBucketName(), objectName);

            // 首先检查bucket是否存在
            if (!isBucketExist(minioConfig.getBucketName())) {
                log.error("Bucket不存在: {}", minioConfig.getBucketName());
                throw new Exception("Bucket不存在: " + minioConfig.getBucketName());
            }

            // 检查文件是否存在
            if (!isFileExist(objectName)) {
                log.error("文件不存在: {}", objectName);
                throw new Exception("文件不存在: " + objectName);
            }

            // 获取文件对象
            InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(objectName)
                            .build()
            );

            log.info("成功获取MinIO对象流: {}", objectName);
            return inputStream;

        } catch (Exception e) {
            log.error("获取文件对象失败: bucket={}, object={}, error={}",
                    minioConfig.getBucketName(), objectName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * greenfox - 新增：列出文件 - 2025-01-15
     *
     * @param prefix 文件前缀
     * @param limit 限制数量
     * @return 文件列表
     */
    @Override
    public List<String> listObjects(String prefix, int limit) throws Exception {
        try {
            log.info("列出MinIO文件: bucket={}, prefix={}, limit={}", minioConfig.getBucketName(), prefix, limit);

            List<String> objectNames = new ArrayList<>();

            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            int count = 0;
            for (Result<Item> result : results) {
                if (count >= limit) {
                    break;
                }

                Item item = result.get();
                objectNames.add(item.objectName());
                log.info("找到文件: {}, 大小: {}, 修改时间: {}",
                        item.objectName(), item.size(), item.lastModified());
                count++;
            }

            log.info("列出文件完成，共找到 {} 个文件", count);
            return objectNames;

        } catch (Exception e) {
            log.error("列出MinIO文件失败: bucket={}, prefix={}, error={}",
                    minioConfig.getBucketName(), prefix, e.getMessage(), e);
            throw e;
        }
    }
}