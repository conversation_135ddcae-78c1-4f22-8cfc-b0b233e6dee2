package com.ruoyi.system.service;

import com.ruoyi.system.domain.BatchTask;
import com.ruoyi.system.domain.FolderInfo;
import com.ruoyi.system.domain.ImageRepository;
import com.ruoyi.system.dto.MultiVersionBatchTaskCreateDTO;
import com.ruoyi.system.dto.VersionFolderDTO;
import com.ruoyi.system.mapper.BatchTaskMapper;
import com.ruoyi.system.mapper.FolderInfoMapper;
import com.ruoyi.system.mapper.ImageRepositoryMapper;
import com.ruoyi.system.service.impl.BatchTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 批量任务服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class BatchTaskServiceTest {

    @Mock
    private BatchTaskMapper batchTaskMapper;

    @Mock
    private FolderInfoMapper folderInfoMapper;

    @Mock
    private ImageRepositoryMapper imageRepositoryMapper;

    @InjectMocks
    private BatchTaskServiceImpl batchTaskService;

    private MultiVersionBatchTaskCreateDTO testDTO;
    private List<VersionFolderDTO> testVersions;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testVersions = Arrays.asList(
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照", "2023", "A1234", "北京"),
            createVersionFolder("美国_护照_2022_B5678_纽约", "美国", "护照", "2022", "B5678", "纽约"),
            createVersionFolder("英国_驾照_2024_C9012_伦敦", "英国", "驾照", "2024", "C9012", "伦敦")
        );

        testDTO = new MultiVersionBatchTaskCreateDTO();
        testDTO.setTaskName("2024-01-15_技术部_多版本批量上传任务");
        testDTO.setDepartmentId(1L);
        testDTO.setVersionFolders(testVersions);
    }

    @Test
    void testCreateMultiVersionBatchTask_Success() {
        // Arrange
        String expectedTaskId = UUID.randomUUID().toString();
        when(batchTaskMapper.insert(any(BatchTask.class))).thenReturn(1);
        when(folderInfoMapper.insert(any(FolderInfo.class))).thenReturn(1);

        // 使用反射设置私有方法的返回值
        ReflectionTestUtils.setField(batchTaskService, "generateTaskId", expectedTaskId);

        // Act
        String actualTaskId = batchTaskService.createMultiVersionBatchTask(testDTO);

        // Assert
        assertNotNull(actualTaskId);
        verify(batchTaskMapper, times(1)).insert(any(BatchTask.class));
        verify(folderInfoMapper, times(3)).insert(any(FolderInfo.class));
    }

    @Test
    void testCreateMultiVersionBatchTask_WithSpecialCharacters() {
        // Arrange - 包含特殊字符的文件夹名
        List<VersionFolderDTO> specialCharVersions = Arrays.asList(
            createVersionFolder("España_Pasaporte_2023_D3456_Madrid", "España", "Pasaporte", "2023", "D3456", "Madrid"),
            createVersionFolder("test folder with spaces", "Test Country", "Test Type", "2023", "E7890", "Test Place")
        );

        testDTO.setVersionFolders(specialCharVersions);
        when(batchTaskMapper.insert(any(BatchTask.class))).thenReturn(1);
        when(folderInfoMapper.insert(any(FolderInfo.class))).thenReturn(1);

        // Act
        String taskId = batchTaskService.createMultiVersionBatchTask(testDTO);

        // Assert
        assertNotNull(taskId);
        verify(folderInfoMapper, times(2)).insert(argThat(folderInfo -> 
            folderInfo.getFolderName().contains("España") || 
            folderInfo.getFolderName().contains("spaces")
        ));
    }

    @Test
    void testFindFolderInfoByPath_ExactMatch() {
        // Arrange
        String taskId = "test-task-id";
        String folderPath = "中国_护照_2023_A1234_北京";
        
        FolderInfo expectedFolder = new FolderInfo();
        expectedFolder.setFolderId("folder-id-1");
        expectedFolder.setFolderPath(folderPath);
        expectedFolder.setFolderName("中国_护照_2023_A1234_北京");

        when(folderInfoMapper.selectByTaskIdAndPath(taskId, folderPath))
            .thenReturn(expectedFolder);

        // Act
        FolderInfo result = batchTaskService.findFolderInfoByPath(taskId, folderPath);

        // Assert
        assertNotNull(result);
        assertEquals(folderPath, result.getFolderPath());
        assertEquals("中国_护照_2023_A1234_北京", result.getFolderName());
    }

    @Test
    void testFindFolderInfoByPath_FuzzyMatch() {
        // Arrange
        String taskId = "test-task-id";
        String searchPath = "中国_护照_2023";
        
        List<FolderInfo> candidateFolders = Arrays.asList(
            createFolderInfo("中国_护照_2023_A1234_北京"),
            createFolderInfo("中国_护照_2023_B5678_上海")
        );

        when(folderInfoMapper.selectByTaskIdAndPath(taskId, searchPath))
            .thenReturn(null);
        when(folderInfoMapper.selectByTaskIdAndPathLike(taskId, searchPath + "%"))
            .thenReturn(candidateFolders);

        // Act
        FolderInfo result = batchTaskService.findFolderInfoByPath(taskId, searchPath);

        // Assert
        assertNotNull(result);
        assertTrue(result.getFolderPath().startsWith(searchPath));
    }

    @Test
    void testProcessFileUpload_OriginalFilename() {
        // Arrange
        String taskId = "test-task-id";
        String originalFilename = "身份证正面.jpg";
        String folderName = "中国_护照_2023_A1234_北京";
        
        FolderInfo folderInfo = createFolderInfo(folderName);
        when(folderInfoMapper.selectByTaskIdAndPath(eq(taskId), anyString()))
            .thenReturn(folderInfo);
        when(imageRepositoryMapper.insert(any(ImageRepository.class)))
            .thenReturn(1);

        // Act
        String imageId = batchTaskService.processFileUpload(taskId, folderName, originalFilename);

        // Assert
        assertNotNull(imageId);
        verify(imageRepositoryMapper).insert(argThat(image -> 
            originalFilename.equals(image.getFilename()) &&
            originalFilename.equals(image.getOriginalFilename())
        ));
    }

    @Test
    void testDeleteBatchTask_CascadeDelete() {
        // Arrange
        String taskId = "test-task-id";
        
        List<FolderInfo> folders = Arrays.asList(
            createFolderInfo("folder1"),
            createFolderInfo("folder2")
        );
        
        List<ImageRepository> images = Arrays.asList(
            createImageRepository("image1.jpg"),
            createImageRepository("image2.png")
        );

        when(folderInfoMapper.selectByTaskId(taskId)).thenReturn(folders);
        when(imageRepositoryMapper.selectByTaskId(taskId)).thenReturn(images);
        when(batchTaskMapper.deleteById(taskId)).thenReturn(1);
        when(folderInfoMapper.deleteByTaskId(taskId)).thenReturn(2);
        when(imageRepositoryMapper.deleteByTaskId(taskId)).thenReturn(2);

        // Act
        boolean result = batchTaskService.deleteBatchTask(taskId);

        // Assert
        assertTrue(result);
        verify(batchTaskMapper).deleteById(taskId);
        verify(folderInfoMapper).deleteByTaskId(taskId);
        verify(imageRepositoryMapper).deleteByTaskId(taskId);
        // 这里还应该验证 MinIO 文件删除，但需要 mock MinIO 客户端
    }

    @Test
    void testValidateVersionFolders_ValidData() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            batchTaskService.validateVersionFolders(testVersions);
        });
    }

    @Test
    void testValidateVersionFolders_DuplicateFolders() {
        // Arrange
        List<VersionFolderDTO> duplicateVersions = Arrays.asList(
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照", "2023", "A1234", "北京"),
            createVersionFolder("中国_护照_2023_A1234_北京", "中国", "护照", "2023", "A1234", "北京")
        );

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            batchTaskService.validateVersionFolders(duplicateVersions);
        });
    }

    @Test
    void testPerformanceWithLargeFileSet() {
        // Arrange - 模拟大量文件
        List<VersionFolderDTO> largeVersionSet = createLargeVersionSet(100);
        testDTO.setVersionFolders(largeVersionSet);
        
        when(batchTaskMapper.insert(any(BatchTask.class))).thenReturn(1);
        when(folderInfoMapper.insert(any(FolderInfo.class))).thenReturn(1);

        // Act
        long startTime = System.currentTimeMillis();
        String taskId = batchTaskService.createMultiVersionBatchTask(testDTO);
        long endTime = System.currentTimeMillis();

        // Assert
        assertNotNull(taskId);
        assertTrue(endTime - startTime < 5000, "处理100个版本应该在5秒内完成");
        verify(folderInfoMapper, times(100)).insert(any(FolderInfo.class));
    }

    // 辅助方法
    private VersionFolderDTO createVersionFolder(String folderName, String country, 
                                                String certType, String issueYear, 
                                                String certPrefix, String issuePlace) {
        VersionFolderDTO version = new VersionFolderDTO();
        version.setFolderName(folderName);
        version.setFolderPath(folderName);
        version.setCountryName(country);
        version.setCertTypeName(certType);
        version.setIssueYear(issueYear);
        version.setCertNumberPrefix(certPrefix);
        version.setIssuePlace(issuePlace);
        version.setFileCount(3);
        return version;
    }

    private FolderInfo createFolderInfo(String folderName) {
        FolderInfo folder = new FolderInfo();
        folder.setFolderId(UUID.randomUUID().toString());
        folder.setFileInfoId(UUID.randomUUID().toString()); // 设置唯一的fileInfoId
        folder.setFolderName(folderName);
        folder.setFolderPath(folderName);
        folder.setCreateTime(LocalDateTime.now());
        return folder;
    }

    private ImageRepository createImageRepository(String filename) {
        ImageRepository image = new ImageRepository();
        image.setImageId(UUID.randomUUID().toString());
        image.setFilename(filename);
        image.setOriginalFilename(filename);
        image.setCreateTime(LocalDateTime.now());
        return image;
    }

    private List<VersionFolderDTO> createLargeVersionSet(int count) {
        return java.util.stream.IntStream.range(0, count)
            .mapToObj(i -> createVersionFolder(
                "测试文件夹_" + i,
                "测试国家_" + i,
                "测试类型_" + i,
                "2024",
                "T" + String.format("%04d", i),
                "测试地点_" + i
            ))
            .collect(java.util.stream.Collectors.toList());
    }
}
