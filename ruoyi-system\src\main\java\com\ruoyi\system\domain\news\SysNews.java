package com.ruoyi.system.domain.news;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文章管理对象 sys_news
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public class SysNews extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文章ID */
    private Long newsId;

    /** 文章标题 */
    @Excel(name = "文章标题")
    private String newsTitle;

    /** 文章类型 */
    @Excel(name = "文章类型")
    private Integer newsType;

    /** 文章内容 */
    @Excel(name = "文章内容")
    private String newsContent;

    /** 审核状态（0待审核 1已发布） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已发布")
    private String status;

    /** 审核人员 */
    @Excel(name = "审核人员")
    private String reviewer;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 作者 */
    @Excel(name = "作者")
    private String author;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 头条图片地址 */
    @Excel(name = "头条图片地址")
    private String headlinePic;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    public void setNewsId(Long newsId) 
    {
        this.newsId = newsId;
    }

    public Long getNewsId() 
    {
        return newsId;
    }

    public void setNewsTitle(String newsTitle) 
    {
        this.newsTitle = newsTitle;
    }

    public String getNewsTitle() 
    {
        return newsTitle;
    }

    public void setNewsType(Integer newsType) 
    {
        this.newsType = newsType;
    }

    public Integer getNewsType() 
    {
        return newsType;
    }

    public void setNewsContent(String newsContent) 
    {
        this.newsContent = newsContent;
    }

    public String getNewsContent() 
    {
        return newsContent;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setReviewer(String reviewer) 
    {
        this.reviewer = reviewer;
    }

    public String getReviewer() 
    {
        return reviewer;
    }

    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }

    public void setAuthor(String author) 
    {
        this.author = author;
    }

    public String getAuthor() 
    {
        return author;
    }

    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setUploadTime(Date uploadTime) 
    {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() 
    {
        return uploadTime;
    }


    public void setHeadlinePic(String headlinePic) 
    {
        this.headlinePic = headlinePic;
    }

    public String getHeadlinePic() 
    {
        return headlinePic;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public String getDeptName() {
        return deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public String getAuditComment() {
        return auditComment;
    }
    public void setAuditComment(String auditComment) {
        this.auditComment = auditComment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("newsId", getNewsId())
            .append("newsTitle", getNewsTitle())
            .append("newsType", getNewsType())
            .append("newsContent", getNewsContent())
            .append("status", getStatus())
            .append("reviewer", getReviewer())
            .append("reviewTime", getReviewTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("author", getAuthor())
            .append("uploadTime", getUploadTime())
            .append("headlinePic", getHeadlinePic())
            .append("deptId", getDeptId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .toString();
    }
}
