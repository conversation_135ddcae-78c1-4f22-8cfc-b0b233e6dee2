package com.ruoyi.system.domain.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.util.Date;
import java.util.List;

/**
 * 版本标注模板实体
 * 存储每个版本每种图片类型的标注模板
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Document(collection = "version_annotation_template")
@CompoundIndexes({
    @CompoundIndex(name = "version_image_type_idx", def = "{'versionId': 1, 'imageType': 1}", unique = true),
    @CompoundIndex(name = "standard_folder_idx", def = "{'standardFolderId': 1}")
})
public class VersionAnnotationTemplate {
    
    @Id
    private String id;
    
    /** 模板ID */
    private String templateId;
    
    /** 版本ID */
    private String versionId;
    
    /** 图片类型 */
    private String imageType;
    
    /** 证件类型 */
    private String certType;
    
    /** 国家代码 */
    private String countryCode;
    
    /** 标注项列表 */
    private List<AnnotationItem> annotations;
    
    /** 创建此模板的标准文件夹ID */
    private String standardFolderId;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 创建人 */
    private String createdBy;
    
    /** 更新人 */
    private String updatedBy;
    
    /**
     * 标注项内嵌类
     */
    @Data
    public static class AnnotationItem {
        /** 标注项ID */
        private String annotationId;
        
        /** 标注类型 */
        private String annotationType;
        
        /** 标注名称 */
        private String annotationName;
        
        /** 坐标信息 */
        private AnnotationCoordinate coordinate;
        
        /** 标注值 */
        private String annotationValue;
        
        /** 是否必填 */
        private Boolean required = false;
        
        /** 显示顺序 */
        private Integer displayOrder = 0;
    }
    
    /**
     * 坐标信息内嵌类
     */
    @Data
    public static class AnnotationCoordinate {
        /** X坐标（百分比） */
        private Double x;
        
        /** Y坐标（百分比） */
        private Double y;
        
        /** 宽度（百分比） */
        private Double width;
        
        /** 高度（百分比） */
        private Double height;
    }
}
