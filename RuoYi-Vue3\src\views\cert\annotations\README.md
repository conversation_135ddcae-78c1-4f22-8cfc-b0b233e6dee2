# 标注组件库

这是一个基于 Vue 3 和 Element Plus 的图片标注组件库，封装了 `@recogito/annotorious` 库，提供完整的图片标注功能。

## 组件概览

### 1. AnnotationToolbar.vue
**职责**: 提供标注工具选择界面

**Props**: 无

**Emits**:
- `tool-selected`: 当用户选择工具时触发，传递工具名称

**功能**:
- 提供移动/选择、矩形、多边形三种工具
- 高亮显示当前选中的工具
- 工具提示增强用户体验

### 2. AnnotationList.vue
**职责**: 以列表形式展示和管理标注项

**Props**:
- `modelValue` (Array): 标注数组，支持 v-model 双向绑定

**Emits**:
- `update:modelValue`: 更新标注数组
- `select-annotation`: 选择标注项时触发

**功能**:
- 空状态处理
- 标注文本编辑
- 标注删除功能
- 标注选择功能

### 3. AnnotationCanvas.vue ⭐
**职责**: 核心标注画布，封装 Annotorious 库

**Props**:
- `imageUrl` (String, required): 图片URL
- `tool` (String): 当前选择的工具
- `modelValue` (Array): 标注数组，支持 v-model 双向绑定

**Emits**:
- `update:modelValue`: 更新标注数组
- `select-annotation`: 选择标注时触发

**功能**:
- 图片加载和显示
- 标注绘制和编辑
- 工具切换
- 标注数据同步
- 错误处理

## 安装依赖

```bash
npm install @recogito/annotorious
```

## 基本使用

```vue
<template>
  <div class="annotation-app">
    <!-- 工具栏 -->
    <AnnotationToolbar @tool-selected="handleToolSelected" />
    
    <div class="content">
      <!-- 标注画布 -->
      <AnnotationCanvas
        :imageUrl="imageUrl"
        :tool="currentTool"
        v-model="annotations"
        @select-annotation="handleAnnotationSelected"
        ref="canvasRef"
      />
      
      <!-- 标注列表 -->
      <AnnotationList 
        v-model="annotations" 
        @select-annotation="handleAnnotationSelected"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AnnotationToolbar from './AnnotationToolbar.vue'
import AnnotationCanvas from './AnnotationCanvas.vue'
import AnnotationList from './AnnotationList.vue'

const currentTool = ref('select')
const annotations = ref([])
const imageUrl = ref('your-image-url.jpg')
const canvasRef = ref(null)

const handleToolSelected = (toolName) => {
  currentTool.value = toolName
}

const handleAnnotationSelected = (annotationId) => {
  if (canvasRef.value && annotationId) {
    canvasRef.value.selectAnnotation(annotationId)
  }
}
</script>
```

## 标注数据格式

标注数据遵循 Annotorious 的标准格式：

```javascript
{
  id: "unique-id",
  type: "Annotation",
  body: [
    {
      type: "TextualBody",
      value: "标注文本内容",
      purpose: "commenting"
    }
  ],
  target: {
    selector: {
      type: "FragmentSelector",
      conformsTo: "http://www.w3.org/TR/media-frags/",
      value: "xywh=pixel:100,100,200,150"  // 矩形标注
    }
  }
}
```

## 工具类型

- `select`: 选择和移动工具
- `rectangle`: 矩形标注工具
- `polygon`: 多边形标注工具

## AnnotationCanvas 暴露的方法

```javascript
// 选择指定标注
canvasRef.value.selectAnnotation(annotationId)

// 清除选择
canvasRef.value.clearSelection()

// 适应画布大小
canvasRef.value.fitBounds()
```

## 样式自定义

组件使用了 Element Plus 的设计规范，并提供了深度样式覆盖来自定义 Annotorious 的外观：

```css
/* 标注样式 */
:deep(.a9s-annotation) {
  stroke: #409eff;
  stroke-width: 2;
  fill: rgba(64, 158, 255, 0.1);
}

/* 选中状态 */
:deep(.a9s-annotation.selected) {
  stroke: #f56c6c;
  stroke-width: 3;
}
```

## 事件流

1. **工具选择**: AnnotationToolbar → 父组件 → AnnotationCanvas
2. **标注创建**: AnnotationCanvas → 父组件 → AnnotationList
3. **标注选择**: AnnotationList → 父组件 → AnnotationCanvas
4. **标注编辑**: AnnotationList ↔ 父组件 ↔ AnnotationCanvas

## 注意事项

1. **图片跨域**: 确保图片URL支持跨域访问
2. **依赖版本**: 使用 Annotorious 3.x 版本
3. **浏览器兼容**: 支持现代浏览器，需要 ES6+ 支持
4. **性能优化**: 大图片建议使用缩略图预览

## 故障排除

### 1. Annotorious 加载失败
- 检查网络连接
- 确认依赖已正确安装
- 查看浏览器控制台错误信息

### 2. 图片无法显示
- 检查图片URL是否有效
- 确认图片支持跨域访问
- 检查图片格式是否支持

### 3. 标注无法保存
- 检查标注数据格式
- 确认事件监听器正确绑定
- 查看控制台错误信息

## 示例项目

参考 `ExampleUsage.vue` 文件查看完整的使用示例。
