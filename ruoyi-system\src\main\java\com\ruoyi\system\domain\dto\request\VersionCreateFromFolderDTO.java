package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 从文件夹创建版本请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class VersionCreateFromFolderDTO {
    
    /** 文件夹ID */
    @NotBlank(message = "文件夹ID不能为空")
    private String folderId;
    
    /** 版本代码 */
    @NotBlank(message = "版本代码不能为空")
    private String versionCode;
    
    /** 版本描述 */
    private String description;
}
