package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.dto.request.AnnotationTemplateDTO;
import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.service.IVersionAnnotationTemplateService;
import com.ruoyi.system.service.IVersionAnnotationTemplateService.TemplateStatistics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * 版本标注模板管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/annotation-templates")
@Validated
public class VersionAnnotationTemplateController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(VersionAnnotationTemplateController.class);
    
    @Autowired
    private IVersionAnnotationTemplateService templateService;
    
    /**
     * 创建或更新标注模板
     * POST /api/annotation-templates
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:save')")
    @Log(title = "标注模板管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult saveTemplate(@Valid @RequestBody AnnotationTemplateDTO templateDTO) {
        try {
            log.info("保存标注模板: versionId={}, imageType={}, standardFolderId={}", 
                    templateDTO.getVersionId(), templateDTO.getImageType(), templateDTO.getStandardFolderId());
            
            VersionAnnotationTemplate template = templateService.saveTemplate(
                    templateDTO.getVersionId(), 
                    templateDTO.getImageType(), 
                    templateDTO, 
                    templateDTO.getStandardFolderId()
            );
            
            log.info("保存标注模板成功: templateId={}", template.getTemplateId());
            return AjaxResult.success("保存标注模板成功", template);
            
        } catch (Exception e) {
            log.error("保存标注模板失败: {}", e.getMessage(), e);
            return AjaxResult.error("保存标注模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取版本的标注模板
     * GET /api/annotation-templates/version/{versionId}
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:query')")
    @GetMapping("/version/{versionId}")
    public TableDataInfo getVersionTemplates(@PathVariable String versionId) {
        try {
            log.info("获取版本标注模板: versionId={}", versionId);
            
            List<VersionAnnotationTemplate> templates = templateService.getVersionTemplates(versionId);
            
            log.info("获取版本标注模板成功: versionId={}, 模板数量={}", versionId, templates.size());
            return getDataTable(templates);
            
        } catch (Exception e) {
            log.error("获取版本标注模板失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return new TableDataInfo();
        }
    }
    
    /**
     * 获取指定版本和图片类型的模板
     * GET /api/annotation-templates/version/{versionId}/type/{imageType}
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:query')")
    @GetMapping("/version/{versionId}/type/{imageType}")
    public AjaxResult getTemplate(@PathVariable String versionId, @PathVariable String imageType) {
        try {
            log.info("获取指定类型标注模板: versionId={}, imageType={}", versionId, imageType);
            
            Optional<VersionAnnotationTemplate> template = templateService.getTemplate(versionId, imageType);
            
            if (template.isPresent()) {
                log.info("获取标注模板成功: templateId={}", template.get().getTemplateId());
                return AjaxResult.success("获取标注模板成功", template.get());
            } else {
                log.warn("标注模板不存在: versionId={}, imageType={}", versionId, imageType);
                return AjaxResult.error("标注模板不存在");
            }
            
        } catch (Exception e) {
            log.error("获取标注模板失败: versionId={}, imageType={}, error={}", 
                    versionId, imageType, e.getMessage(), e);
            return AjaxResult.error("获取标注模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除标注模板
     * DELETE /api/annotation-templates/{templateId}
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:remove')")
    @Log(title = "标注模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateId}")
    public AjaxResult deleteTemplate(@PathVariable String templateId) {
        try {
            log.info("删除标注模板: templateId={}", templateId);
            
            boolean success = templateService.deleteTemplate(templateId);
            
            if (success) {
                log.info("删除标注模板成功: templateId={}", templateId);
                return AjaxResult.success("删除标注模板成功");
            } else {
                log.warn("标注模板不存在或删除失败: templateId={}", templateId);
                return AjaxResult.error("标注模板不存在或删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除标注模板失败: templateId={}, error={}", templateId, e.getMessage(), e);
            return AjaxResult.error("删除标注模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除版本的所有模板
     * DELETE /api/annotation-templates/version/{versionId}
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:remove')")
    @Log(title = "标注模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/version/{versionId}")
    public AjaxResult deleteVersionTemplates(@PathVariable String versionId) {
        try {
            log.info("删除版本所有标注模板: versionId={}", versionId);
            
            templateService.deleteVersionTemplates(versionId);
            
            log.info("删除版本所有标注模板成功: versionId={}", versionId);
            return AjaxResult.success("删除版本所有标注模板成功");
            
        } catch (Exception e) {
            log.error("删除版本标注模板失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return AjaxResult.error("删除版本标注模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 复制模板到新版本
     * POST /api/annotation-templates/copy
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:save')")
    @Log(title = "标注模板管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copyTemplates(@RequestParam String sourceVersionId, 
                                   @RequestParam String targetVersionId,
                                   @RequestParam String newStandardFolderId) {
        try {
            log.info("复制标注模板: sourceVersionId={}, targetVersionId={}, newStandardFolderId={}", 
                    sourceVersionId, targetVersionId, newStandardFolderId);
            
            List<VersionAnnotationTemplate> newTemplates = templateService.copyTemplatesToVersion(
                    sourceVersionId, targetVersionId, newStandardFolderId);
            
            log.info("复制标注模板成功: 复制了{}个模板", newTemplates.size());
            return AjaxResult.success("复制标注模板成功", newTemplates);
            
        } catch (Exception e) {
            log.error("复制标注模板失败: sourceVersionId={}, targetVersionId={}, error={}", 
                    sourceVersionId, targetVersionId, e.getMessage(), e);
            return AjaxResult.error("复制标注模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取版本模板统计信息
     * GET /api/annotation-templates/version/{versionId}/statistics
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:query')")
    @GetMapping("/version/{versionId}/statistics")
    public AjaxResult getTemplateStatistics(@PathVariable String versionId) {
        try {
            log.info("获取版本模板统计信息: versionId={}", versionId);
            
            TemplateStatistics statistics = templateService.getTemplateStatistics(versionId);
            
            log.info("获取版本模板统计信息成功: versionId={}, 模板数量={}", 
                    versionId, statistics.getTotalTemplates());
            return AjaxResult.success("获取模板统计信息成功", statistics);
            
        } catch (Exception e) {
            log.error("获取版本模板统计信息失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return AjaxResult.error("获取模板统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证模板数据
     * POST /api/annotation-templates/validate
     */
    @PreAuthorize("@ss.hasPermi('cert:annotation:template:query')")
    @PostMapping("/validate")
    public AjaxResult validateTemplate(@Valid @RequestBody AnnotationTemplateDTO templateDTO) {
        try {
            log.info("验证标注模板数据: versionId={}, imageType={}", 
                    templateDTO.getVersionId(), templateDTO.getImageType());
            
            boolean isValid = templateService.validateTemplate(templateDTO);
            
            if (isValid) {
                log.info("标注模板数据验证通过");
                return AjaxResult.success("模板数据验证通过");
            } else {
                log.warn("标注模板数据验证失败");
                return AjaxResult.error("模板数据验证失败");
            }
            
        } catch (Exception e) {
            log.error("验证标注模板数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("验证模板数据失败: " + e.getMessage());
        }
    }
}
