package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 图片标注请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ImageAnnotationRequestDTO {
    
    /** 图片ID */
    @NotBlank(message = "图片ID不能为空")
    private String imageId;
    
    /** 标注项列表 */
    @Valid
    @NotEmpty(message = "标注项列表不能为空")
    private List<AnnotationItemRequestDTO> annotations;
    
    /** 操作类型：save-保存, apply-应用模板 */
    private String operation = "save";
    
    /**
     * 标注项请求DTO
     */
    @Data
    public static class AnnotationItemRequestDTO {
        /** 标注项ID */
        private String annotationId;
        
        /** 标注类型 */
        @NotBlank(message = "标注类型不能为空")
        private String annotationType;
        
        /** 标注名称 */
        @NotBlank(message = "标注名称不能为空")
        private String annotationName;
        
        /** 坐标信息 */
        @Valid
        private AnnotationCoordinateRequestDTO coordinate;
        
        /** 标注值 */
        private String annotationValue;
        
        /** 是否必填 */
        private Boolean required = false;
        
        /** 显示顺序 */
        private Integer displayOrder = 0;
    }
    
    /**
     * 坐标信息请求DTO
     */
    @Data
    public static class AnnotationCoordinateRequestDTO {
        /** X坐标（百分比） */
        private Double x;
        
        /** Y坐标（百分比） */
        private Double y;
        
        /** 宽度（百分比） */
        private Double width;
        
        /** 高度（百分比） */
        private Double height;
    }
}
