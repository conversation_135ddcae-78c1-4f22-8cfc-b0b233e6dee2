package com.ruoyi.system.domain.news;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 消息通知对象 sys_notification
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
public class SysNotification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 通知ID */
    private Long notificationId;

    /** 接收用户ID */
    @Excel(name = "接收用户ID")
    private Long userId;

    /** 通知标题 */
    @Excel(name = "通知标题")
    private String title;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String content;

    /** 通知类型（1：系统通知，2：审核通知） */
    @Excel(name = "通知类型", readConverterExp = "1=系统通知,2=审核通知")
    private String type;

    /** 状态（0：未读，1：已读） */
    @Excel(name = "状态", readConverterExp = "0=未读,1=已读")
    private String status;

    /** 关联的业务ID */
    @Excel(name = "关联的业务ID")
    private Long sourceId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public void setNotificationId(Long notificationId) 
    {
        this.notificationId = notificationId;
    }

    public Long getNotificationId() 
    {
        return notificationId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setSourceId(Long sourceId) 
    {
        this.sourceId = sourceId;
    }

    public Long getSourceId() 
    {
        return sourceId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("notificationId", getNotificationId())
            .append("userId", getUserId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("type", getType())
            .append("status", getStatus())
            .append("sourceId", getSourceId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
