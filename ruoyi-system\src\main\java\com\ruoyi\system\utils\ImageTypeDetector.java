package com.ruoyi.system.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 图片类型检测工具类
 * 基于文件名自动识别图片类型
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class ImageTypeDetector {
    
    private static final Logger log = LoggerFactory.getLogger(ImageTypeDetector.class);
    
    // 图片类型常量
    public static final String VISIBLE_DATA_PAGE = "VISIBLE_DATA_PAGE";
    public static final String INFRARED_DATA_PAGE = "INFRARED_DATA_PAGE";
    public static final String ULTRAVIOLET_DATA_PAGE = "ULTRAVIOLET_DATA_PAGE";
    public static final String OTHER = "OTHER";
    
    // 可见光数据页模式
    private static final Pattern VISIBLE_PATTERN = Pattern.compile(
        ".*[_\\-\\s](visible|常光|可见光|data[_\\-\\s]?page|正面|front).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 红外数据页模式
    private static final Pattern INFRARED_PATTERN = Pattern.compile(
        ".*[_\\-\\s](infrared|红外|ir|data[_\\-\\s]?page).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 紫外数据页模式
    private static final Pattern ULTRAVIOLET_PATTERN = Pattern.compile(
        ".*[_\\-\\s](ultraviolet|紫外|uv|data[_\\-\\s]?page).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 更精确的可见光模式（包含更多关键词）
    private static final Pattern VISIBLE_ENHANCED_PATTERN = Pattern.compile(
        ".*(visible|可见光|常光|白光|normal|regular|standard|正面|front|data.*page|主页|main.*page).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 红外增强模式
    private static final Pattern INFRARED_ENHANCED_PATTERN = Pattern.compile(
        ".*(infrared|红外|ir|heat|thermal|热成像).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 紫外增强模式
    private static final Pattern ULTRAVIOLET_ENHANCED_PATTERN = Pattern.compile(
        ".*(ultraviolet|紫外|uv|fluorescent|荧光).*\\.(jpg|jpeg|png|bmp|tiff)$", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 检测图片类型
     * 
     * @param fileName 文件名
     * @return 图片类型
     */
    public static String detectImageType(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            log.warn("文件名为空，返回OTHER类型");
            return OTHER;
        }
        
        String normalizedFileName = fileName.toLowerCase().trim();
        log.debug("检测图片类型: {}", fileName);
        
        // 优先检查精确模式
        if (INFRARED_PATTERN.matcher(normalizedFileName).matches() || 
            INFRARED_ENHANCED_PATTERN.matcher(normalizedFileName).matches()) {
            log.debug("检测为红外数据页: {}", fileName);
            return INFRARED_DATA_PAGE;
        }
        
        if (ULTRAVIOLET_PATTERN.matcher(normalizedFileName).matches() || 
            ULTRAVIOLET_ENHANCED_PATTERN.matcher(normalizedFileName).matches()) {
            log.debug("检测为紫外数据页: {}", fileName);
            return ULTRAVIOLET_DATA_PAGE;
        }
        
        if (VISIBLE_PATTERN.matcher(normalizedFileName).matches() || 
            VISIBLE_ENHANCED_PATTERN.matcher(normalizedFileName).matches()) {
            log.debug("检测为可见光数据页: {}", fileName);
            return VISIBLE_DATA_PAGE;
        }
        
        // 特殊规则：如果包含"data"或"page"但不匹配其他类型，默认为可见光
        if (normalizedFileName.contains("data") || normalizedFileName.contains("page")) {
            log.debug("包含data/page关键词，默认为可见光数据页: {}", fileName);
            return VISIBLE_DATA_PAGE;
        }
        
        log.debug("未匹配任何模式，返回OTHER类型: {}", fileName);
        return OTHER;
    }
    
    /**
     * 检查图片类型是否可标注
     * 
     * @param imageType 图片类型
     * @return 是否可标注
     */
    public static boolean isAnnotatableType(String imageType) {
        return VISIBLE_DATA_PAGE.equals(imageType) || 
               INFRARED_DATA_PAGE.equals(imageType) || 
               ULTRAVIOLET_DATA_PAGE.equals(imageType);
    }
    
    /**
     * 获取图片类型的中文名称
     * 
     * @param imageType 图片类型
     * @return 中文名称
     */
    public static String getImageTypeDisplayName(String imageType) {
        switch (imageType) {
            case VISIBLE_DATA_PAGE:
                return "可见光数据页";
            case INFRARED_DATA_PAGE:
                return "红外数据页";
            case ULTRAVIOLET_DATA_PAGE:
                return "紫外数据页";
            case OTHER:
                return "其他";
            default:
                return "未知类型";
        }
    }
    
    /**
     * 批量检测结果
     */
    public static class DetectionResult {
        private String fileName;
        private String imageType;
        private boolean isAnnotatable;
        private String displayName;
        
        public DetectionResult(String fileName) {
            this.fileName = fileName;
            this.imageType = detectImageType(fileName);
            this.isAnnotatable = isAnnotatableType(this.imageType);
            this.displayName = getImageTypeDisplayName(this.imageType);
        }
        
        // getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getImageType() { return imageType; }
        public void setImageType(String imageType) { this.imageType = imageType; }
        
        public boolean isAnnotatable() { return isAnnotatable; }
        public void setAnnotatable(boolean annotatable) { isAnnotatable = annotatable; }
        
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        
        @Override
        public String toString() {
            return String.format("DetectionResult{fileName='%s', imageType='%s', isAnnotatable=%s, displayName='%s'}", 
                    fileName, imageType, isAnnotatable, displayName);
        }
    }
    
    /**
     * 批量检测图片类型
     * 
     * @param fileNames 文件名列表
     * @return 检测结果列表
     */
    public static java.util.List<DetectionResult> batchDetect(java.util.List<String> fileNames) {
        return fileNames.stream()
                .map(DetectionResult::new)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取所有支持的图片类型
     * 
     * @return 图片类型列表
     */
    public static java.util.List<String> getSupportedImageTypes() {
        return java.util.Arrays.asList(VISIBLE_DATA_PAGE, INFRARED_DATA_PAGE, ULTRAVIOLET_DATA_PAGE, OTHER);
    }
    
    /**
     * 获取所有可标注的图片类型
     * 
     * @return 可标注图片类型列表
     */
    public static java.util.List<String> getAnnotatableImageTypes() {
        return java.util.Arrays.asList(VISIBLE_DATA_PAGE, INFRARED_DATA_PAGE, ULTRAVIOLET_DATA_PAGE);
    }
}
