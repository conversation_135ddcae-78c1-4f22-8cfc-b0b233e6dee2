package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.minio.MinioClient;
import com.ruoyi.common.utils.file.MinioUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.PostConstruct;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig
{
    private static final Logger log = LoggerFactory.getLogger(MinioConfig.class);
    
    /**
     * 服务地址
     */
    private String url;

    /**
     * 用户名
     */
    private String accessKey;

    /**
     * 密码
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 静态配置，用于非Spring管理的类访问
     */
    private static String staticUrl;
    private static String staticAccessKey;
    private static String staticSecretKey;
    private static String staticBucketName;

    public String getUrl()
    {
        return url;
    }

    public void setUrl(String url)
    {
        this.url = url;
        MinioConfig.staticUrl = url;
    }

    public String getAccessKey()
    {
        return accessKey;
    }

    public void setAccessKey(String accessKey)
    {
        this.accessKey = accessKey;
        MinioConfig.staticAccessKey = accessKey;
    }

    public String getSecretKey()
    {
        return secretKey;
    }

    public void setSecretKey(String secretKey)
    {
        this.secretKey = secretKey;
        MinioConfig.staticSecretKey = secretKey;
    }

    public String getBucketName()
    {
        return bucketName;
    }

    public void setBucketName(String bucketName)
    {
        this.bucketName = bucketName;
        MinioConfig.staticBucketName = bucketName;
    }

    /**
     * 获取静态URL
     */
    public static String getStaticUrl()
    {
        return staticUrl;
    }

    /**
     * 获取静态AccessKey
     */
    public static String getStaticAccessKey()
    {
        return staticAccessKey;
    }

    /**
     * 获取静态SecretKey
     */
    public static String getStaticSecretKey()
    {
        return staticSecretKey;
    }

    /**
     * 获取静态BucketName
     */
    public static String getStaticBucketName()
    {
        return staticBucketName;
    }

    @Bean
    public MinioClient getMinioClient()
    {
        MinioClient minioClient = MinioClient.builder()
                .endpoint(url)
                .credentials(accessKey, secretKey)
                .build();

        log.info("MinIO客户端创建成功，URL: {}", url);
        return minioClient;
    }
    
    /**
     * 初始化MinioUtils
     */
    @PostConstruct
    public void initMinioUtils()
    {
        try {
            log.info("初始化MinioUtils，URL: {}, AccessKey: {}, BucketName: {}", url, accessKey, bucketName);
            MinioUtils.init(url, accessKey, secretKey);
            log.info("MinioUtils初始化成功");
        } catch (Exception e) {
            log.error("MinioUtils初始化失败: {}", e.getMessage(), e);
        }
    }
}
