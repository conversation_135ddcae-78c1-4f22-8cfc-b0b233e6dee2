package com.ruoyi.system.mapper.news;

import java.util.List;
import com.ruoyi.system.domain.news.SysComment;

/**
 * 新闻评论Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
public interface SysCommentMapper 
{
    /**
     * 查询新闻评论
     * 
     * @param commentId 新闻评论主键
     * @return 新闻评论
     */
    public SysComment selectSysCommentByCommentId(Long commentId);

    /**
     * 查询新闻评论列表
     * 
     * @param sysComment 新闻评论
     * @return 新闻评论集合
     */
    public List<SysComment> selectSysCommentList(SysComment sysComment);

    /**
     * 新增新闻评论
     * 
     * @param sysComment 新闻评论
     * @return 结果
     */
    public int insertSysComment(SysComment sysComment);

    /**
     * 修改新闻评论
     * 
     * @param sysComment 新闻评论
     * @return 结果
     */
    public int updateSysComment(SysComment sysComment);

    /**
     * 删除新闻评论
     * 
     * @param commentId 新闻评论主键
     * @return 结果
     */
    public int deleteSysCommentByCommentId(Long commentId);

    /**
     * 批量删除新闻评论
     * 
     * @param commentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysCommentByCommentIds(Long[] commentIds);
    
    /**
     * 根据新闻ID查询评论列表
     * 
     * @param newsId 新闻ID
     * @return 评论列表
     */
    public List<SysComment> selectSysCommentByNewsId(Long newsId);
}
