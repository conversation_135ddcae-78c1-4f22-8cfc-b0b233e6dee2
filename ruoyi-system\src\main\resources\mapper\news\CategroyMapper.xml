<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.CategroyMapper">
    
    <resultMap type="Categroy" id="CategroyResult">
        <result property="categroyID"    column="categroyID"    />
        <result property="categroyName"    column="categroyName"    />
        <result property="categroyStatus"    column="categroyStatus"    />
        <result property="categroyAddress"    column="categroyAddress"    />
        <result property="pictureStatus"    column="pictureStatus"    />
    </resultMap>

    <sql id="selectCategroyVo">
        select categroyID, categroyName, categroyStatus, categroyAddress, pictureStatus, display_order as displayOrder from categroy
    </sql>

    <select id="selectCategroyList" parameterType="Categroy" resultMap="CategroyResult">
        <include refid="selectCategroyVo"/>
        <where>  
            <if test="categroyName != null  and categroyName != ''"> and categroyName like concat('%', #{categroyName}, '%')</if>
            <if test="categroyStatus != null  and categroyStatus != ''"> and categroyStatus = #{categroyStatus}</if>
            <if test="categroyAddress != null  and categroyAddress != ''"> and categroyAddress = #{categroyAddress}</if>
            <if test="pictureStatus != null  and pictureStatus != ''"> and pictureStatus = #{pictureStatus}</if>
            <if test="displayOrder != null">
                AND display_order = #{displayOrder}
            </if>
        </where>
    </select>
    
    <select id="selectCategroyByCategroyID" parameterType="Long" resultMap="CategroyResult">
        <include refid="selectCategroyVo"/>
        where categroyID = #{categroyID}
    </select>

    <insert id="insertCategroy" parameterType="Categroy" useGeneratedKeys="true" keyProperty="categroyID">
        insert into categroy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categroyName != null and categroyName != ''">categroyName,</if>
            <if test="categroyStatus != null">categroyStatus,</if>
            <if test="categroyAddress != null">categroyAddress,</if>
            <if test="pictureStatus != null">pictureStatus,</if>
            <if test="displayOrder != null">display_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categroyName != null and categroyName != ''">#{categroyName},</if>
            <if test="categroyStatus != null">#{categroyStatus},</if>
            <if test="categroyAddress != null">#{categroyAddress},</if>
            <if test="pictureStatus != null">#{pictureStatus},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
         </trim>
    </insert>

    <update id="updateCategroy" parameterType="Categroy">
        update categroy
        <trim prefix="SET" suffixOverrides=",">
            <if test="categroyName != null and categroyName != ''">categroyName = #{categroyName},</if>
            <if test="categroyStatus != null">categroyStatus = #{categroyStatus},</if>
            <if test="categroyAddress != null">categroyAddress = #{categroyAddress},</if>
            <if test="pictureStatus != null">pictureStatus = #{pictureStatus},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
        </trim>
        where categroyID = #{categroyID}
    </update>

    <delete id="deleteCategroyByCategroyID" parameterType="Long">
        delete from categroy where categroyID = #{categroyID}
    </delete>

    <delete id="deleteCategroyByCategroyIDs" parameterType="String">
        delete from categroy where categroyID in 
        <foreach item="categroyID" collection="array" open="(" separator="," close=")">
            #{categroyID}
        </foreach>
    </delete>
</mapper>