-- =====================================================
-- 修复菜单路由配置 SQL 脚本
-- 日期: 2025-07-08
-- 说明: 修复路由路径与组件不匹配的问题
-- =====================================================

-- 1. 修复样本管理菜单组件路径
UPDATE sys_menu
SET component = 'cert/sample/SampleManagementView',  -- 修正组件路径
    update_time = NOW(),
    update_by = 'admin',
    remark = '修复组件路径配置'
WHERE menu_id = 2101;

-- 2. 修复批量上传任务菜单组件路径
UPDATE sys_menu
SET component = 'cert/batch/BatchTaskManagement',  -- 修正组件路径
    update_time = NOW(),
    update_by = 'admin',
    remark = '修复组件路径配置'
WHERE menu_id = 2229;

-- 3. 修复文件夹详情菜单组件路径
UPDATE sys_menu
SET component = 'cert/sample/FolderDetailView',  -- 修正组件路径
    update_time = NOW(),
    update_by = 'admin',
    remark = '修复组件路径配置'
WHERE menu_id = 2100;

-- 4. 验证修复结果
SELECT
    menu_id,
    menu_name,
    path,
    component,
    CASE
        WHEN component IS NULL OR component = '' THEN '❌ 组件路径为空'
        ELSE '✅ 组件路径已配置'
    END as component_status
FROM sys_menu m
WHERE menu_id IN (2100, 2101, 2229)
ORDER BY menu_id;

-- 5. 查看证件管理下所有菜单的路径配置
SELECT 
    m.menu_id,
    m.menu_name,
    m.path,
    m.component,
    p.path as parent_path,
    CASE 
        WHEN p.path IS NOT NULL THEN CONCAT(p.path, '/', m.path)
        ELSE m.path
    END as full_route_path
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.parent_id = 2000 OR m.menu_id = 2000
ORDER BY m.order_num;

-- 执行完成提示
SELECT '菜单路由修复完成！' as message;
SELECT '请刷新页面或重新登录查看效果' as notice;
