<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CertWarehouseMapper">
    
    <resultMap type="CertWarehouse" id="CertWarehouseResult">
        <result property="id"    column="id"    />
        <result property="witnessNumber"    column="witness_number"    />
        <result property="certTypeId"    column="cert_type_id"    />
        <result property="countryId"    column="country_id"    />
        <result property="collectionSource"    column="collection_source"    />
        <result property="imagePath"    column="image_path"    />
        <result property="isSample"    column="is_sample"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCertWarehouseVo">
        select id, witness_number, cert_type_id, country_id, collection_source, image_path, is_sample, create_time, update_time from cert_warehouse
    </sql>

    <select id="selectCertWarehouseList" parameterType="CertWarehouse" resultMap="CertWarehouseResult">
        <include refid="selectCertWarehouseVo"/>
        <where>  
            <if test="witnessNumber != null  and witnessNumber != ''"> and witness_number = #{witnessNumber}</if>
            <if test="certTypeId != null "> and cert_type_id = #{certTypeId}</if>
            <if test="countryId != null "> and country_id = #{countryId}</if>
            <if test="collectionSource != null  and collectionSource != ''"> and collection_source = #{collectionSource}</if>
            <if test="imagePath != null  and imagePath != ''"> and image_path = #{imagePath}</if>
            <if test="isSample != null "> and is_sample = #{isSample}</if>
        </where>
    </select>
    
    <select id="selectCertWarehouseById" parameterType="Long" resultMap="CertWarehouseResult">
        <include refid="selectCertWarehouseVo"/>
        where id = #{id}
    </select>

    <insert id="insertCertWarehouse" parameterType="CertWarehouse" useGeneratedKeys="true" keyProperty="id">
        insert into cert_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="witnessNumber != null and witnessNumber != ''">witness_number,</if>
            <if test="certTypeId != null">cert_type_id,</if>
            <if test="countryId != null">country_id,</if>
            <if test="collectionSource != null and collectionSource != ''">collection_source,</if>
            <if test="imagePath != null and imagePath != ''">image_path,</if>
            <if test="isSample != null">is_sample,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="witnessNumber != null and witnessNumber != ''">#{witnessNumber},</if>
            <if test="certTypeId != null">#{certTypeId},</if>
            <if test="countryId != null">#{countryId},</if>
            <if test="collectionSource != null and collectionSource != ''">#{collectionSource},</if>
            <if test="imagePath != null and imagePath != ''">#{imagePath},</if>
            <if test="isSample != null">#{isSample},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCertWarehouse" parameterType="CertWarehouse">
        update cert_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="witnessNumber != null and witnessNumber != ''">witness_number = #{witnessNumber},</if>
            <if test="certTypeId != null">cert_type_id = #{certTypeId},</if>
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="collectionSource != null and collectionSource != ''">collection_source = #{collectionSource},</if>
            <if test="imagePath != null and imagePath != ''">image_path = #{imagePath},</if>
            <if test="isSample != null">is_sample = #{isSample},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCertWarehouseById" parameterType="Long">
        delete from cert_warehouse where id = #{id}
    </delete>

    <delete id="deleteCertWarehouseByIds" parameterType="String">
        delete from cert_warehouse where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>