package com.ruoyi.system.domain.mongo;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * 文件夹详细信息
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Document(collection = "folder_info")
public class FolderInfo {

    @Id
    private String id;

    private String folderId; // 业务ID
    private String fileInfoId; // 文件信息ID，用于唯一索引
    private String taskId;
    private String folderType; // e.g., "standard", "regular"
    private String folderName;

    // 主图路径字段
    @Field("mainPicPath")
    private String mainPicPath; // 主图文件的存储路径 (可见光主图.jpg)

    // 新增字段：文件夹原始名称和预解析版本信息
    @Field("filename")
    private String filename; // 原始文件夹名称 (如: 西班牙_公务普通护照_2001_XDD85_哈瓦那)

    @Field("preParseVersionInfo")
    private PreParseVersionInfo preParseVersionInfo; // 预解析的版本信息

    // 多级文件夹支持字段
    @Field("parentTaskId")
    private String parentTaskId; // 父任务ID，用于多版本批量任务

    @Field("folderPath")
    private String folderPath; // 完整文件夹路径 (如: 测试文件夹/中国_护照_2020_A1000_北京)

    @Field("relativePath")
    private String relativePath; // 相对路径 (如: 中国_护照_2020_A1000_北京)

    @Field("uploadedFileCount")
    private Integer uploadedFileCount = 0; // 已上传文件数量

    @Field("totalFileCount")
    private Integer totalFileCount = 0; // 总文件数量

    @Field("uploadProgress")
    private Double uploadProgress = 0.0; // 上传进度百分比

    @Field("isMultiVersion")
    private Boolean isMultiVersion = false; // 是否为多版本任务的一部分

    @Field("versionIndex")
    private Integer versionIndex; // 在多版本任务中的版本索引
    private Country countryInfo;
    private CertType certInfo;
    private String issueYear;
    private Integer fileCount;
    private Integer processedFileCount;
    private String status;
    private SysDept deptInfo;
    private SysUser uploaderInfo;
    private Date createTime;
    private Date updateTime;

    // 关联版本信息（聚合对象）
    @Field("associationInfo")
    private AssociationVersionInfo associationInfo;

    // 新增字段 - 版本审核和样本分类相关
    @Field("reviewStatus")
    private String reviewStatus; // 审核状态，初始为null，关联版本后设置为pending

    @Field("versionAssociationMethod")
    private String versionAssociationMethod; // 版本关联方式，初始为null，关联时设置

    @Field("thirdPartyDetectionResult")
    private ThirdPartyDetectionResult thirdPartyDetectionResult; // 第三方检测结果

    @Field("reviewInfo")
    private ReviewInfo reviewInfo; // 审核信息

    @Field("sampleClassification")
    private SampleClassification sampleClassification; // 样本分类信息



    /**
     * 预解析版本信息
     */
    @Data
    public static class PreParseVersionInfo {
        @Field("originalFolderName")
        private String originalFolderName; // 原始文件夹名称

        @Field("parsedVersionCode")
        private String parsedVersionCode; // 解析后的版本代码 (如: ESP_PG_2001_XDD85_Havana)

        @Field("countryName")
        private String countryName; // 解析的国家名称 (如: 西班牙)

        @Field("countryCode")
        private String countryCode; // 匹配的国家代码 (如: ESP)

        @Field("certTypeName")
        private String certTypeName; // 解析的证件类型名称 (如: 公务普通护照)

        @Field("certTypeCode")
        private String certTypeCode; // 匹配的证件类型代码 (如: PG)

        @Field("issueYear")
        private String issueYear; // 签发年份 (如: 2001)

        @Field("certNumberPrefix")
        private String certNumberPrefix; // 证件号前缀 (如: XDD85)

        @Field("issuePlace")
        private String issuePlace; // 签发地 (如: 哈瓦那 -> Havana)

        @Field("parseStatus")
        private String parseStatus; // 解析状态: SUCCESS, PARTIAL, FAILED

        @Field("parseErrors")
        private List<String> parseErrors; // 解析错误信息

        @Field("parseTime")
        private Date parseTime; // 解析时间


    }

    /**
     * 第三方检测结果
     */
    @Data
    public static class ThirdPartyDetectionResult {
        @Field("detectionId")
        private String detectionId; // 检测ID

        @Field("detectionTime")
        private Date detectionTime; // 检测时间

        @Field("detectionStatus")
        private String detectionStatus; // 检测状态

        @Field("confidence")
        private Double confidence; // 置信度

        @Field("matchedVersions")
        private List<VersionMatch> matchedVersions; // 匹配的版本列表

        @Field("errorMessage")
        private String errorMessage; // 错误信息
    }

    /**
     * 版本匹配信息
     */
    @Data
    public static class VersionMatch {
        @Field("versionId")
        private String versionId; // 版本ID

        @Field("versionCode")
        private String versionCode; // 版本编码

        @Field("matchScore")
        private Double matchScore; // 匹配分数

        @Field("matchReason")
        private String matchReason; // 匹配原因
    }

    /**
     * 审核信息
     */
    @Data
    public static class ReviewInfo {
        @Field("reviewerId")
        private String reviewerId; // 审核人ID

        @Field("reviewerName")
        private String reviewerName; // 审核人姓名

        @Field("reviewTime")
        private Date reviewTime; // 审核时间

        @Field("reviewComments")
        private String reviewComments; // 审核意见

        @Field("reviewHistory")
        private List<ReviewRecord> reviewHistory; // 审核历史
    }

    /**
     * 审核记录
     */
    @Data
    public static class ReviewRecord {
        @Field("action")
        private String action; // 操作动作

        @Field("reviewerId")
        private String reviewerId; // 审核人ID

        @Field("actionTime")
        private Date actionTime; // 操作时间

        @Field("comments")
        private String comments; // 操作意见
    }

    /**
     * 样本分类信息
     */
    @Data
    public static class SampleClassification {
        @Field("standardImageId")
        private String standardImageId; // 标准样本图片ID

        @Field("classificationTime")
        private Date classificationTime; // 分类时间

        @Field("classifiedBy")
        private String classifiedBy; // 分类人

        @Field("classificationReason")
        private String classificationReason; // 分类原因
    }

    // 手动添加 createTime 的 setter 方法（如果 Lombok 没有生成）
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 判断是否已关联版本
     */
    public boolean isAssociated() {
        return "associated".equals(status);
    }

    /**
     * 判断是否为新建版本关联
     */
    public boolean isCreatedNewVersion() {
        return isAssociated() && associationInfo != null && "CREATED_NEW".equals(associationInfo.getAssociationType());
    }

    /**
     * 判断是否为关联已有版本
     */
    public boolean isLinkedExistingVersion() {
        return isAssociated() && associationInfo != null && "LINKED_EXISTING".equals(associationInfo.getAssociationType());
    }

    /**
     * 关联版本信息内嵌类
     */
    @Data
    public static class AssociationVersionInfo {
        /** 关联的版本ID */
        @Field("versionId")
        private String versionId;

        /** 关联的版本编码 */
        @Field("versionCode")
        private String versionCode;

        /** 关联类型：CREATED_NEW（新建版本关联）、LINKED_EXISTING（关联已有版本） */
        @Field("associationType")
        private String associationType;

        /** 关联操作的用户ID */
        @Field("userId")
        private Long userId;

        /** 用户名 */
        @Field("userName")
        private String userName;

        /** 部门ID */
        @Field("deptId")
        private Long deptId;

        /** 部门名称 */
        @Field("deptName")
        private String deptName;

        /** 首次关联时间 */
        @Field("associationTime")
        private Date associationTime;

        /** 最后修改人ID */
        @Field("lastModifiedBy")
        private Long lastModifiedBy;

        /** 最后修改人姓名 */
        @Field("lastModifiedByName")
        private String lastModifiedByName;

        /** 最后修改时间 */
        @Field("lastModifiedTime")
        private Date lastModifiedTime;
    }
}