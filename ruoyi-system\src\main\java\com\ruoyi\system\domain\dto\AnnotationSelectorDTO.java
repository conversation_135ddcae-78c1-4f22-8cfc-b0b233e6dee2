package com.ruoyi.system.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 标注选择器DTO
 * 用于请求和响应中的标注选择器信息
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AnnotationSelectorDTO {
    
    /** 
     * 百分比坐标值 
     * 格式: "xywh=percent:10,20,30,10"
     * 表示: x=10%, y=20%, width=30%, height=10%
     */
    @NotBlank(message = "坐标值不能为空")
    private String value;
}
