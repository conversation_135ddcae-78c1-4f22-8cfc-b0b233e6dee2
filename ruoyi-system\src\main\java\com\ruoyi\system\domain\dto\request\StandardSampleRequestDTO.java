package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 标准样本管理请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class StandardSampleRequestDTO {
    
    /** 版本ID */
    @NotBlank(message = "版本ID不能为空")
    private String versionId;
    
    /** 文件夹ID */
    @NotBlank(message = "文件夹ID不能为空")
    private String folderId;
    
    /** 是否保留现有标注（替换标准样本时使用） */
    private Boolean keepAnnotations = false;
    
    /** 操作原因（可选） */
    private String reason;
}
