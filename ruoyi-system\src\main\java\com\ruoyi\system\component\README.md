# MinioTusDataStore 组件说明

## 概述

`MinioTusDataStore` 是连接Tus服务和MinIO的核心桥梁组件，它将Tus协议的上传操作（创建、写入分块、完成等）直接翻译成MinIO的分块上传（Multipart Upload）API调用。

## 功能特性

### 1. 核心功能
- **分块上传管理**: 支持大文件的分块上传，提高上传效率和可靠性
- **断点续传**: 通过Tus协议实现文件上传的断点续传功能
- **MinIO集成**: 直接与MinIO对象存储服务集成，无需额外的文件系统存储
- **内存缓存**: 使用ConcurrentHashMap提供高性能的上传信息缓存

### 2. 实现的接口方法

#### create(UploadInfo info)
- 在MinIO上启动一个分块上传任务
- 将MinIO返回的uploadId保存到info.setStorageName()中
- 将上传信息存储到内存缓存中

#### write(InputStream, long, UploadId)
- 执行分块数据写入操作
- 自动计算分块号（partNumber）
- 调用MinIO的uploadPart API

#### getOffset(UploadId)
- 获取当前上传进度偏移量
- 通过listParts API计算已上传的总字节数

#### finish(UploadId)
- 完成分块上传
- 合并所有分块为最终文件
- 清理上传信息缓存

#### terminate(UploadId)
- 中止未完成的上传任务
- 清理MinIO上的临时数据
- 清理内存缓存

#### getUploadInfo(UploadId) / update(UploadInfo)
- 提供上传信息的查询和更新功能
- 支持Tus服务器的状态管理需求

## 配置要求

### 1. 依赖注入
```java
@Autowired
private MinioClient minioClient;

@Value("${minio.bucketName}")
private String bucketName;
```

### 2. 配置文件
确保在`application.yml`中配置了MinIO相关参数：
```yaml
minio:
  url: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: xjlfiles
```

## 技术实现

### 1. 临时文件管理
- 使用`TEMP_PREFIX = "tus-temp/"`前缀存放未完成的上传任务
- 临时对象名格式：`tus-temp/{uploadId}`

### 2. 分块管理
- 自动计算分块号，从1开始递增
- 支持任意大小的分块上传
- 确保分块按序号正确排序

### 3. 错误处理
- 完善的异常处理和日志记录
- 上传失败时自动清理资源
- 提供详细的错误信息

### 4. 内存管理
- 使用ConcurrentHashMap确保线程安全
- 上传完成或终止后自动清理内存
- 避免内存泄漏

## 使用示例

### 1. Spring Boot集成
```java
@Configuration
public class TusConfig {
    
    @Autowired
    private MinioTusDataStore minioTusDataStore;
    
    @Bean
    public TusFileUploadService tusFileUploadService() {
        return new TusFileUploadService()
            .withDataStore(minioTusDataStore)
            .withDownloadFeature()
            .withUploadURI("/api/upload");
    }
}
```

### 2. 控制器使用
```java
@RestController
@RequestMapping("/api")
public class UploadController {
    
    @Autowired
    private TusFileUploadService tusService;
    
    @RequestMapping(value = "/upload/**", method = {RequestMethod.POST, RequestMethod.PATCH, RequestMethod.HEAD, RequestMethod.DELETE, RequestMethod.OPTIONS})
    public void upload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        tusService.process(request, response);
    }
}
```

## 注意事项

### 1. 生产环境建议
- 考虑将UploadInfo存储从内存改为Redis或数据库，以支持集群部署
- 添加定期清理机制，清理长时间未完成的上传任务
- 配置合适的MinIO连接池参数

### 2. 性能优化
- 根据网络环境调整分块大小
- 配置合适的并发上传数量
- 监控MinIO存储空间使用情况

### 3. 安全考虑
- 验证上传文件的类型和大小
- 实现用户权限验证
- 防止恶意上传攻击

## 相关文档

- [Tus协议规范](https://tus.io/protocols/resumable-upload.html)
- [MinIO Java SDK文档](https://docs.min.io/docs/java-client-quickstart-guide.html)
- [Spring Boot集成指南](https://spring.io/projects/spring-boot)
