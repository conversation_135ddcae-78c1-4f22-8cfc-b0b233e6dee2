<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.news.SysHonorrollMapper">
    
    <resultMap type="SysHonorroll" id="SysHonorrollResult">
        <result property="honorId"    column="honor_id"    />
        <result property="honorName"    column="honor_name"    />
        <result property="honorImaglUrl"    column="honor_imagl_url"    />
        <result property="honorStatus"    column="honor_status"    />
        <result property="honorType"    column="honor_type"    />
    </resultMap>

    <sql id="selectSysHonorrollVo">
        select honor_id, honor_name, honor_imagl_url, honor_status, honor_type from sys_honorroll
    </sql>

    <select id="selectSysHonorrollList" parameterType="SysHonorroll" resultMap="SysHonorrollResult">
        <include refid="selectSysHonorrollVo"/>
        <where>  
            <if test="honorName != null  and honorName != ''"> and honor_name like concat('%', #{honorName}, '%')</if>
            <if test="honorImaglUrl != null  and honorImaglUrl != ''"> and honor_imagl_url = #{honorImaglUrl}</if>
            <if test="honorStatus != null  and honorStatus != ''"> and honor_status = #{honorStatus}</if>
            <if test="honorType != null "> and honor_type = #{honorType}</if>
        </where>
    </select>
    
    <select id="selectSysHonorrollByHonorId" parameterType="Long" resultMap="SysHonorrollResult">
        <include refid="selectSysHonorrollVo"/>
        where honor_id = #{honorId}
    </select>

    <insert id="insertSysHonorroll" parameterType="SysHonorroll" useGeneratedKeys="true" keyProperty="honorId">
        insert into sys_honorroll
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="honorName != null">honor_name,</if>
            <if test="honorImaglUrl != null">honor_imagl_url,</if>
            <if test="honorStatus != null">honor_status,</if>
            <if test="honorType != null">honor_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="honorName != null">#{honorName},</if>
            <if test="honorImaglUrl != null">#{honorImaglUrl},</if>
            <if test="honorStatus != null">#{honorStatus},</if>
            <if test="honorType != null">#{honorType},</if>
         </trim>
    </insert>

    <update id="updateSysHonorroll" parameterType="SysHonorroll">
        update sys_honorroll
        <trim prefix="SET" suffixOverrides=",">
            <if test="honorName != null">honor_name = #{honorName},</if>
            <if test="honorImaglUrl != null">honor_imagl_url = #{honorImaglUrl},</if>
            <if test="honorStatus != null">honor_status = #{honorStatus},</if>
            <if test="honorType != null">honor_type = #{honorType},</if>
        </trim>
        where honor_id = #{honorId}
    </update>

    <delete id="deleteSysHonorrollByHonorId" parameterType="Long">
        delete from sys_honorroll where honor_id = #{honorId}
    </delete>

    <delete id="deleteSysHonorrollByHonorIds" parameterType="String">
        delete from sys_honorroll where honor_id in 
        <foreach item="honorId" collection="array" open="(" separator="," close=")">
            #{honorId}
        </foreach>
    </delete>
</mapper>