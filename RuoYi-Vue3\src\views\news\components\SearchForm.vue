<template>
  <el-card class="search-card">
    <template #header>
      <div class="card-header">站内搜索</div>
    </template>
    <!-- 修改此处，添加类名 -->
    <el-form :model="queryParams" ref="queryRef" :label-width="labelWidth"  :class="[formClass, 'uniform-search-items']">
      <el-form-item label="发布时间段">
        <el-date-picker
          v-model="dateRangeValue"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="文章标题" prop="newsTitle">
        <el-tooltip placement="top">
          <template #content>
            如需输入多个文章标题关键词，关键词之间用空格分隔
          </template>
          <el-input
            v-model="queryParams.newsTitle"
            placeholder="输入标题关键词"
            clearable
            @keyup.enter="handleQuery"
            style="width: 165px;"
          />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="文章内容" prop="newsContent">
        <el-tooltip placement="top">
        <template #content>
            如需输入多个文章内容关键词，关键词之间用空格分隔
        </template>
        <el-input
          v-model="queryParams.newsContent"
          placeholder="输入内容关键词"
          clearable
          @keyup.enter="handleQuery"
          style="width: 165px;"
        />
      </el-tooltip>
      </el-form-item>
      <el-form-item label="作者" prop="author">
        <el-input
          v-model="queryParams.author"
          placeholder="请输入作者"
          clearable
          @keyup.enter="handleQuery"
          style="width: 165px;"
        />
      </el-form-item>
      
      <el-form-item label="文章栏目" prop="newsType">
        <el-select v-model="queryParams.newsType" placeholder="请选择文章栏目" clearable style="width: 165px">
          <el-option
            v-for="option in categroyList"
            :key="option.categroyID"
            :label="option.categroyName"
            :value="option.categroyID">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-select v-model="queryParams.deptId" placeholder="请选择部门名称" clearable style="width: 165px">
          <el-option
            v-for="option in deptOptions"
            :key="option.deptId"
            :label="option.deptName"
            :value="option.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  },
  dateRange: {
    type: Array,
    default: () => []
  },
  categroyList: {
    type: Array,
    default: () => []
  },
  deptOptions: {
    type: Array,
    default: () => []
  },
  labelWidth: {
    type: String,
    default: '120px'
  },
  formClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['query', 'reset', 'update:dateRange']);

// 创建本地日期范围值，用于双向绑定
const dateRangeValue = ref(props.dateRange || []);

// 监听父组件传入的日期范围变化
watch(() => props.dateRange, (newValue) => {
  dateRangeValue.value = newValue;
}, { deep: true });

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  emit('update:dateRange', val);
};

// 搜索按钮操作
const handleQuery = () => {
  emit('query');
};

// 重置按钮操作
const handleReset = () => {
  emit('reset');
};
</script>

<style scoped lang="scss">
.search-card {
  .card-header {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 15px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background-color: #409EFF;
      border-radius: 2px;
    }
  }

  :deep(.el-form-item__label) {
    font-size: 16px;
    width: auto;
    color: #333;
  }
  
  :deep(.el-input__inner) {
    border-radius: 4px;
    transition: all 0.3s ease;
    
    &:focus {
      border-color: #409EFF;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  :deep(.el-button) {
    margin-top: 15px;
    border-radius: 4px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  margin-bottom: 25px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 530px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}

.uniform-search-items {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  padding: 18px;
}
</style>
