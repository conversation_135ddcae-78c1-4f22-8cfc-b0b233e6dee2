<template>
  <div class="annotation-list">
    <el-card shadow="never" class="annotation-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">标注列表 ({{ modelValue?.length || 0 }})</span>
          <el-button
            v-if="modelValue && modelValue.length > 0"
            size="small"
            type="danger"
            @click="handleClearAll"
          >
            清空全部
          </el-button>
        </div>
      </template>

      <!-- 空状态处理 -->
      <el-empty
        v-if="!modelValue || modelValue.length === 0"
        description="暂无标注"
        :image-size="60"
      />

      <!-- 标注列表 -->
      <div v-else class="annotation-items">
      <el-card
        v-for="(annotation, index) in modelValue"
        :key="annotation.id"
        :class="[
          'annotation-card',
          { 'annotation-card--selected': selectedAnnotationId === annotation.id }
        ]"
        shadow="hover"
        @click="handleCardClick(annotation.id)"
      >
        <div class="annotation-content">
          <!-- 标注序号 -->
          <div class="annotation-index">{{ index + 1 }}</div>

          <!-- 标注文本内容 -->
          <el-input
            :model-value="getAnnotationText(annotation)"
            @update:model-value="(value) => handleTextChange(annotation.id, value)"
            placeholder="请输入标注内容"
            @click.stop
            @keyup.enter="$event.target.blur()"
          />

          <!-- 删除按钮 -->
          <el-button
            type="danger"
            :icon="Delete"
            circle
            size="small"
            class="delete-btn"
            @click.stop="handleDelete(annotation.id)"
            title="删除标注"
          />
        </div>
      </el-card>
    </div>
    </el-card>
  </div>
</template>

<script setup>
import { Delete } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'

// 定义 props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  selectedAnnotationId: {
    type: String,
    default: null
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'select-annotation'])

// 获取标注文本内容
const getAnnotationText = (annotation) => {
  // 兼容两种格式：数组格式和对象格式
  if (Array.isArray(annotation.bodies) && annotation.bodies.length > 0) {
    return annotation.bodies[0]?.value || ''
  } else if (annotation.body) {
    // 如果是对象格式
    return annotation.body.value || ''
  }
  return ''
}

// 处理卡片点击事件
const handleCardClick = (annotationId) => {
  emit('select-annotation', annotationId)
}

// 处理文本内容变化
const handleTextChange = (annotationId, newValue) => {
  // 创建新的标注数组，更新指定标注的文本内容
  const updatedAnnotations = props.modelValue.map(annotation => {
    if (annotation.id === annotationId) {
      // 创建新的标注对象，避免直接修改原对象
      const updatedAnnotation = { ...annotation }

      // 更新bodies数组格式（Annotorious格式）
      updatedAnnotation.bodies = [
        {
          type: 'TextualBody',
          value: newValue,
          purpose: 'commenting'
        }
      ]

      return updatedAnnotation
    }
    return annotation
  })

  // 触发双向绑定更新
  emit('update:modelValue', updatedAnnotations)
}

// 处理删除标注
const handleDelete = async (annotationId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个标注吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 从数组中移除指定的标注项
    const newArray = props.modelValue.filter(annotation => annotation.id !== annotationId)
    // 触发双向绑定更新
    emit('update:modelValue', newArray)
    ElMessage.success('标注已删除')
  } catch {
    // 用户取消删除，不做任何操作
  }
}

// 处理清空全部标注
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除全部 ${props.modelValue.length} 个标注吗？此操作不可撤销。`,
      '清空确认',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 清空所有标注
    emit('update:modelValue', [])
    ElMessage.success('已清空所有标注')
  } catch {
    // 用户取消清空，不做任何操作
  }
}
</script>

<style scoped>
.annotation-list {
  height: 100%;
  overflow-y: auto;
}

.annotation-list-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__header) {
    padding: 12px 16px;
  }

  :deep(.el-card__body) {
    padding: 12px 16px;
    flex: 1;
    overflow-y: auto;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-weight: 500;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
}

.annotation-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.annotation-card {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.annotation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.annotation-card--selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.annotation-card--selected:hover {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.annotation-content {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
}

/* 列表头部样式 */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.annotation-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 标注序号样式 */
.annotation-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

/* 确保输入框占据剩余空间 */
.annotation-content .el-input {
  flex: 1;
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .annotation-content {
    flex-wrap: wrap;
    gap: 8px;
  }

  .annotation-index {
    order: -1;
  }

  .delete-btn {
    position: static;
    order: 1;
  }
}
</style>
