import request from '@/utils/request'

// 查询栏目信息列表
export function listCategroy(query) {
  return request({
    url: '/categroy/categroy/list',
    method: 'get',
    params: query
  })
}

// 查询栏目信息详细
export function getCategroy(categroyID) {
  return request({
    url: '/categroy/categroy/' + categroyID,
    method: 'get'
  })
}

// 新增栏目信息
export function addCategroy(data) {
  return request({
    url: '/categroy/categroy',
    method: 'post',
    data: data
  })
}

// 修改栏目信息
export function updateCategroy(data) {
  return request({
    url: '/categroy/categroy',
    method: 'put',
    data: data
  })
}

// 删除栏目信息
export function delCategroy(categroyID) {
  return request({
    url: '/categroy/categroy/' + categroyID,
    method: 'delete'
  })
}
