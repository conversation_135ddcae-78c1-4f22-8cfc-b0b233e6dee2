<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="文章标题" prop="newsId">
        <el-input
          v-model="queryParams.newsId"
          placeholder="请输入文章标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评分用户" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入评分用户"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="1-5分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入1-5分"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时间" prop="createdAt">
        <el-date-picker clearable
          v-model="queryParams.createdAt"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['news_rating:news_rating:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['news_rating:news_rating:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['news_rating:news_rating:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['news_rating:news_rating:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="news_ratingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="ratingId" />
      <el-table-column label="文章标题" align="center" prop="newsTitle" />
      <el-table-column label="评分用户" align="center" prop="userName" />
      <el-table-column label="分数" align="center" prop="score" />
      <el-table-column label="时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['news_rating:news_rating:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['news_rating:news_rating:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改文章评分对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="news_ratingRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文章标题" prop="newsId">
          <el-input v-model="form.newsId" placeholder="请输入关联文章ID" />
        </el-form-item>
        <el-form-item label="评分用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入评分用户ID" />
        </el-form-item>
        <el-form-item label="分数" prop="score">
          <el-input v-model="form.score" placeholder="请输入1-5分" />
        </el-form-item>
        <el-form-item label="时间" prop="createdAt">
          <el-date-picker clearable
            v-model="form.createdAt"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="News_rating">
import { listNews_rating, getNews_rating, delNews_rating, addNews_rating, updateNews_rating } from "@/api/news/news_rating"

const { proxy } = getCurrentInstance()

const news_ratingList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    newsId: null,
    userId: null,
    score: null,
    createdAt: null
  },
  rules: {
    newsId: [
      { required: true, message: "关联文章ID不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "评分用户ID不能为空", trigger: "blur" }
    ],
    score: [
      { required: true, message: "1-5分不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询文章评分列表 */
function getList() {
  loading.value = true
  listNews_rating(queryParams.value).then(response => {
    news_ratingList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    ratingId: null,
    newsId: null,
    userId: null,
    score: null,
    createdAt: null
  }
  proxy.resetForm("news_ratingRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.ratingId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加文章评分"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _ratingId = row.ratingId || ids.value
  getNews_rating(_ratingId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改文章评分"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["news_ratingRef"].validate(valid => {
    if (valid) {
      if (form.value.ratingId != null) {
        updateNews_rating(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addNews_rating(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ratingIds = row.ratingId || ids.value
  proxy.$modal.confirm('是否确认删除文章评分编号为"' + _ratingIds + '"的数据项？').then(function() {
    return delNews_rating(_ratingIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('news_rating/news_rating/export', {
    ...queryParams.value
  }, `news_rating_${new Date().getTime()}.xlsx`)
}

getList()
</script>
