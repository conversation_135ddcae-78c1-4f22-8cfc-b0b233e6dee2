package com.ruoyi.system.domain.dto.request;

import com.ruoyi.system.domain.dto.AnnotationDTO;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 图片标注更新请求DTO
 * 用于 PUT /api/images/{imageId}/annotations
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ImageAnnotationUpdateDTO {
    
    /** 完整的标注列表，将覆盖旧数据 */
    @Valid
    private List<AnnotationDTO> annotations;
}
