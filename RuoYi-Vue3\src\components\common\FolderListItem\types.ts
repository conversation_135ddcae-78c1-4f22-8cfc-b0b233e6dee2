/**
 * FolderListItem 组件的 Props 接口
 * 只包含与UI展示直接相关的简单类型
 */
export interface FolderListItemProps {
  /** 文件夹名称 */
  folderName: string
  
  /** 缩略图URL */
  thumbnailUrl?: string
  
  /** 是否显示复选框 */
  showCheckbox?: boolean
  
  /** 复选框是否被选中 */
  isChecked?: boolean
  
  /** 是否显示比对按钮 */
  showCompareButton?: boolean
  
  /** 是否显示标准样本徽章 */
  showStandardBadge?: boolean
  
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * FolderListItem 组件的事件接口
 * 只包含通用的用户交互事件
 */
export interface FolderListItemEmits {
  /** 列表项被点击 */
  'item-clicked': []
  
  /** 复选框状态变化 */
  'check-change': [checked: boolean]
  
  /** 比对按钮被点击 */
  'compare-clicked': []
} 