package com.ruoyi.system.domain.news;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 栏目信息对象 categroy
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public class Categroy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 栏目编号 */
    private Long categroyID;

    /** 栏目名称 */
    @Excel(name = "栏目名称")
    private String categroyName;

    /** 栏目状态（0未启用，1已启用） */
    @Excel(name = "栏目状态", readConverterExp = "0=未启用，1已启用")
    private String categroyStatus;

    /** 栏目地址 */
    @Excel(name = "栏目地址")
    private String categroyAddress;

    /** 图片需求（0不需要，1需要） */
    @Excel(name = "图片需求", readConverterExp = "0=不需要，1需要")
    private String pictureStatus;
    
    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer displayOrder;

    public void setCategroyID(Long categroyID) 
    {
        this.categroyID = categroyID;
    }

    public Long getCategroyID() 
    {
        return categroyID;
    }

    public void setCategroyName(String categroyName) 
    {
        this.categroyName = categroyName;
    }

    public String getCategroyName() 
    {
        return categroyName;
    }

    public void setCategroyStatus(String categroyStatus) 
    {
        this.categroyStatus = categroyStatus;
    }

    public String getCategroyStatus() 
    {
        return categroyStatus;
    }

    public void setCategroyAddress(String categroyAddress) 
    {
        this.categroyAddress = categroyAddress;
    }

    public String getCategroyAddress() 
    {
        return categroyAddress;
    }

    public void setPictureStatus(String pictureStatus) 
    {
        this.pictureStatus = pictureStatus;
    }

    public String getPictureStatus() 
    {
        return pictureStatus;
    }
    
    public void setDisplayOrder(Integer displayOrder) 
    {
        this.displayOrder = displayOrder;
    }

    public Integer getDisplayOrder() 
    {
        return displayOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("categroyID", getCategroyID())
            .append("categroyName", getCategroyName())
            .append("categroyStatus", getCategroyStatus())
            .append("categroyAddress", getCategroyAddress())
            .append("pictureStatus", getPictureStatus())
            .append("displayOrder", getDisplayOrder())
            .toString();
    }
}
