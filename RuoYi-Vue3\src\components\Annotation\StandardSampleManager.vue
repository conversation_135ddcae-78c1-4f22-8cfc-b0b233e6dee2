<template>
  <div class="standard-sample-manager">
    <el-card shadow="never">
      <template #header>
        <div class="manager-header">
          <span class="manager-title">标准样本管理</span>
          <el-button
            size="small"
            @click="handleRefresh"
            :loading="loading"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 当前状态显示 -->
      <div class="current-status" v-if="standardSampleInfo">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="文件夹名称">
            {{ standardSampleInfo.folderName || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="版本ID">
            {{ standardSampleInfo.versionId }}
          </el-descriptions-item>
          <el-descriptions-item label="标准样本状态">
            <el-tag
              :type="standardSampleInfo.isStandardSample ? 'success' : 'info'"
              size="small"
            >
              {{ standardSampleInfo.isStandardSample ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态说明">
            {{ standardSampleInfo.reason }}
          </el-descriptions-item>
          <el-descriptions-item label="模板数量">
            {{ standardSampleInfo.templateCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="可标注图片">
            {{ standardSampleInfo.annotatableImageCount || 0 }} / {{ standardSampleInfo.totalImageCount || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="standardSampleInfo">
        <!-- 设置为标准样本 -->
        <el-button
          v-if="!standardSampleInfo.isStandardSample && standardSampleInfo.canSetAsStandard"
          type="primary"
          @click="handleSetStandard"
          :loading="operating"
        >
          <el-icon><Star /></el-icon>
          设置为标准样本
        </el-button>

        <!-- 取消标准样本 -->
        <el-button
          v-if="standardSampleInfo.isStandardSample && standardSampleInfo.canRemoveStandard"
          type="danger"
          @click="handleRemoveStandard"
          :loading="operating"
        >
          <el-icon><StarFilled /></el-icon>
          取消标准样本
        </el-button>

        <!-- 替换标准样本 -->
        <el-button
          v-if="!standardSampleInfo.isStandardSample && !standardSampleInfo.canSetAsStandard"
          type="warning"
          @click="handleReplaceStandard"
          :loading="operating"
        >
          <el-icon><Switch /></el-icon>
          替换为标准样本
        </el-button>

        <!-- 查看版本标准样本 -->
        <el-button
          size="small"
          @click="handleViewVersionStandard"
          :loading="loadingVersionStandard"
        >
          <el-icon><View /></el-icon>
          查看版本标准样本
        </el-button>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!standardSampleInfo && !loading"
        description="无法获取标准样本信息"
        :image-size="80"
      />
    </el-card>

    <!-- 替换确认对话框 -->
    <el-dialog
      v-model="replaceDialogVisible"
      title="替换标准样本"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="replace-content">
        <el-alert
          title="注意"
          type="warning"
          :closable="false"
          show-icon
        >
          <p>替换标准样本将会影响现有的标注模板，请选择处理方式：</p>
        </el-alert>

        <el-radio-group v-model="replaceOption" style="margin-top: 16px;">
          <el-radio :label="true">保留现有标注模板</el-radio>
          <el-radio :label="false">删除现有标注模板</el-radio>
        </el-radio-group>

        <el-input
          v-model="replaceReason"
          type="textarea"
          placeholder="请输入替换原因（可选）"
          :rows="3"
          style="margin-top: 16px;"
        />
      </div>

      <template #footer>
        <el-button @click="replaceDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmReplace"
          :loading="operating"
        >
          确认替换
        </el-button>
      </template>
    </el-dialog>

    <!-- 版本标准样本信息对话框 -->
    <el-dialog
      v-model="versionStandardDialogVisible"
      title="版本标准样本信息"
      width="600px"
    >
      <div v-if="versionStandardFolder">
        <el-descriptions :column="1" size="small" border>
          <el-descriptions-item label="文件夹ID">
            {{ versionStandardFolder.folderId }}
          </el-descriptions-item>
          <el-descriptions-item label="文件夹名称">
            {{ versionStandardFolder.folderName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ versionStandardFolder.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ versionStandardFolder.updateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="图片数量">
            {{ versionStandardFolder.imageCount || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-empty
        v-else
        description="该版本暂无标准样本"
        :image-size="60"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Star, StarFilled, Switch, View } from '@element-plus/icons-vue'
import {
  getStandardSampleInfo,
  setStandardFolder,
  removeStandardFolder,
  replaceStandardFolder,
  getVersionStandardFolder,
  type StandardSampleInfoVO,
  type FolderInfoVO
} from '@/api/cert/folder'

// 导入工具函数
import {
  isStandardSampleFolder,
  isStandardSampleByVersion,
  canAnnotateImage,
  getFolderTypeDisplayName,
  getFolderTypeTagType
} from '@/utils/folderUtils'

// Props
interface Props {
  /** 文件夹ID */
  folderId: string
  /** 版本ID */
  versionId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  standardChanged: [isStandard: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const operating = ref(false)
const loadingVersionStandard = ref(false)
const standardSampleInfo = ref<StandardSampleInfoVO | null>(null)
const replaceDialogVisible = ref(false)
const replaceOption = ref(true) // true: 保留标注, false: 删除标注
const replaceReason = ref('')
const versionStandardDialogVisible = ref(false)
const versionStandardFolder = ref<FolderInfoVO | null>(null)

// 方法
const loadStandardSampleInfo = async () => {
  if (!props.folderId) return

  loading.value = true
  try {
    const response = await getStandardSampleInfo(props.folderId)
    if (response.code === 200) {
      standardSampleInfo.value = response.data
    } else {
      ElMessage.error(response.msg || '获取标准样本信息失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '网络请求失败')
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  loadStandardSampleInfo()
  emit('refresh')
}

const handleSetStandard = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要将此文件夹设置为标准样本吗？',
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    operating.value = true
    const response = await setStandardFolder(props.folderId, {
      versionId: props.versionId,
      folderId: props.folderId
    })

    if (response.code === 200) {
      ElMessage.success('设置标准样本成功')
      await loadStandardSampleInfo()
      emit('standardChanged', true)
    } else {
      ElMessage.error(response.msg || '设置标准样本失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败')
    }
  } finally {
    operating.value = false
  }
}

const handleRemoveStandard = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消此文件夹的标准样本状态吗？这将删除相关的标注模板。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    operating.value = true
    const response = await removeStandardFolder(props.folderId, {
      versionId: props.versionId,
      folderId: props.folderId
    })

    if (response.code === 200) {
      ElMessage.success('取消标准样本成功')
      await loadStandardSampleInfo()
      emit('standardChanged', false)
    } else {
      ElMessage.error(response.msg || '取消标准样本失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败')
    }
  } finally {
    operating.value = false
  }
}

const handleReplaceStandard = () => {
  replaceDialogVisible.value = true
  replaceOption.value = true
  replaceReason.value = ''
}

const handleConfirmReplace = async () => {
  operating.value = true
  try {
    const response = await replaceStandardFolder(props.folderId, {
      versionId: props.versionId,
      folderId: props.folderId,
      keepAnnotations: replaceOption.value,
      reason: replaceReason.value
    })

    if (response.code === 200) {
      ElMessage.success('替换标准样本成功')
      replaceDialogVisible.value = false
      await loadStandardSampleInfo()
      emit('standardChanged', true)
    } else {
      ElMessage.error(response.msg || '替换标准样本失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    operating.value = false
  }
}

const handleViewVersionStandard = async () => {
  loadingVersionStandard.value = true
  try {
    const response = await getVersionStandardFolder(props.versionId)
    if (response.code === 200) {
      versionStandardFolder.value = response.data
    } else {
      versionStandardFolder.value = null
      if (response.code !== 404) {
        ElMessage.error(response.msg || '获取版本标准样本失败')
      }
    }
    versionStandardDialogVisible.value = true
  } catch (error: any) {
    ElMessage.error(error.message || '网络请求失败')
  } finally {
    loadingVersionStandard.value = false
  }
}

// 监听props变化
watch(() => [props.folderId, props.versionId], () => {
  if (props.folderId && props.versionId) {
    loadStandardSampleInfo()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.folderId && props.versionId) {
    loadStandardSampleInfo()
  }
})
</script>

<style scoped lang="scss">
.standard-sample-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .manager-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .current-status {
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 16px;
  }

  .replace-content {
    .el-alert {
      margin-bottom: 16px;
    }
  }
}
</style>
