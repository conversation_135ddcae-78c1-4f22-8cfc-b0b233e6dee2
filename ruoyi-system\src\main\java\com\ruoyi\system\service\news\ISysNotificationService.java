package com.ruoyi.system.service.news;

import java.util.List;
import com.ruoyi.system.domain.news.SysNotification;

/**
 * 消息通知Service接口
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
public interface ISysNotificationService
{
    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    public SysNotification selectSysNotificationByNotificationId(Long notificationId);

    /**
     * 查询消息通知列表
     * 
     * @param sysNotification 消息通知
     * @return 消息通知集合
     */
    public List<SysNotification> selectSysNotificationList(SysNotification sysNotification);

    /**
     * 查询用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    public int selectUnreadCount(Long userId);

    /**
     * 新增消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    public int insertSysNotification(SysNotification sysNotification);

    /**
     * 修改消息通知
     * 
     * @param sysNotification 消息通知
     * @return 结果
     */
    public int updateSysNotification(SysNotification sysNotification);

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的消息通知主键集合
     * @return 结果
     */
    public int deleteSysNotificationByNotificationIds(Long[] notificationIds);

    /**
     * 删除消息通知信息
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int deleteSysNotificationByNotificationId(Long notificationId);

    /**
     * 将消息标记为已读
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int markAsRead(Long notificationId);

    /**
     * 将用户所有消息标记为已读
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int markAllAsRead(Long userId);
    
    /**
     * 发送审核通知
     * 
     * @param userId 用户ID
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param isApproved 是否通过审核
     * @param auditComment 审核意见
     * @return 结果
     */
    public int sendAuditNotification(Long userId, Long newsId, String newsTitle, boolean isApproved, String auditComment);

    /**
     * 发送评论通知
     * 
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param commentUserId 评论用户ID
     * @param commentUserName 评论用户名称
     * @param targetUserId 目标用户ID（文章作者）
     * @return 结果
     */
    public int sendCommentNotification(Long newsId, String newsTitle, Long commentUserId, String commentUserName, Long targetUserId);

    /**
     * 发送评分通知
     * 
     * @param newsId 文章ID
     * @param newsTitle 文章标题
     * @param ratingUserId 评分用户ID
     * @param ratingUserName 评分用户名称
     * @param targetUserId 目标用户ID（文章作者）
     * @param score 评分
     * @return 结果
     */
    public int sendRatingNotification(Long newsId, String newsTitle, Long ratingUserId, String ratingUserName, Long targetUserId, Integer score);
}
