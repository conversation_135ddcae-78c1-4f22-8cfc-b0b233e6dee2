# RuoYi-Vue 证件样本管理系统

## 项目简介

基于RuoYi-Vue框架开发的证件样本管理系统，支持证件图片的批量上传、标注、审核和管理。

## 开发环境

- **后端**: Java 17 + Spring Boot 3 + Maven
- **前端**: Vue 3 + TypeScript + Element Plus
- **数据库**: MySQL 8.0 + MongoDB 4.4+
- **存储**: MinIO
- **开发工具**: IntelliJ IDEA Community Edition

## 快速开始

### 1. 环境准备

确保已安装：
- Java 17+
- Node.js 16+
- MySQL 8.0
- MongoDB 4.4+
- IntelliJ IDEA

### 2. 使用开发工具脚本

项目提供了便捷的开发工具脚本：

#### Windows批处理版本
```bash
# 运行开发工具菜单
dev-tools.bat

# 或直接使用Maven命令
mvn.bat compile
mvn.bat test
```

#### PowerShell版本（推荐）
```powershell
# 运行开发工具菜单
.\dev-tools.ps1

# 如果遇到执行策略问题，先运行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 3. 开发工具功能

开发工具脚本提供以下功能：
- ✅ 编译整个项目
- ✅ 编译特定模块 (ruoyi-system, ruoyi-admin)
- ✅ 运行测试
- ✅ 清理项目
- ✅ 检查依赖
- ✅ 查看Maven版本
- ✅ 检查应用运行状态
- ✅ 快速编译检查

### 4. 手动启动

如果不使用脚本，也可以在IntelliJ IDEA中：
1. 导入项目
2. 等待Maven依赖下载完成
3. 运行 `RuoYiApplication.main()` 方法

## 项目结构

```
RuoYi-Vue/
├── ruoyi-admin/          # 主启动模块
├── ruoyi-common/         # 通用模块
├── ruoyi-framework/      # 框架模块
├── ruoyi-generator/      # 代码生成模块
├── ruoyi-quartz/         # 定时任务模块
├── ruoyi-system/         # 系统模块
├── RuoYi-Vue3/          # 前端Vue3项目
├── docs/                # 项目文档
├── scripts/             # 脚本文件
├── mvn.bat             # Maven便捷脚本
├── dev-tools.bat       # 开发工具(批处理版)
├── dev-tools.ps1       # 开发工具(PowerShell版)
└── README.md           # 项目说明
```

## 常用命令

```bash
# 编译项目
mvn.bat compile

# 编译特定模块
mvn.bat compile -pl ruoyi-system

# 运行测试
mvn.bat test

# 清理项目
mvn.bat clean

# 检查应用状态
netstat -ano | findstr :8080
```

## 开发规范

- 使用IntelliJ IDEA进行开发
- 遵循阿里巴巴Java开发手册
- 前端使用Vue 3 + TypeScript + Element Plus
- 数据库字段命名使用下划线分隔
- API接口遵循RESTful规范

## 故障排除

### Maven命令无法识别
使用项目提供的 `mvn.bat` 脚本，它会自动使用IntelliJ IDEA内置的Maven。

### 应用启动失败
1. 检查端口8080是否被占用
2. 确认数据库连接配置
3. 查看控制台错误日志

### 编译错误
运行开发工具脚本中的"快速编译检查"功能，查看具体错误信息。

## 联系方式

如有问题，请查看项目文档或联系开发团队。
