/**
 * 批量任务管理API (统一版)
 *
 * 提供批量上传任务的完整管理功能，包括：
 * - 任务创建和管理
 * - 多版本批量任务支持
 * - 任务状态跟踪
 * - 文件上传完成回调
 * - 统一的API路径结构
 *
 * API路径规范：
 * - 批量任务管理: /batch/tasks/*
 * - 基础数据查询: /cert/country/*, /cert/type/* (保持不变)
 *
 * <AUTHOR>
 * @date 2025-01-15
 * @updated 2025-07-08 - API路径统一更新，类型定义迁移，清理重复接口
 */

import request from '@/utils/request'
import {BatchTaskQueryParams} from "../../types";

// 统一的批量任务创建DTO
export interface BatchTaskCreateDTO {
  taskName?: string
  description?: string
  deptId: number
  createdBy: string
  folders: Array<{
    folderName: string
    folderPath: string
    countryId: number
    certTypeId: number
    issueYear: string
    issuePlace?: string
    certNumberPrefix?: string
    fileCount: number
  }>
}

// ==================== API 函数 ====================

/**
 * 创建批量上传任务（统一接口）
 * POST /batch/tasks/create
 */
export function createBatchTask(data: BatchTaskCreateDTO): Promise<any> {
  return request({
    url: '/batch/tasks/create',
    method: 'post',
    data
  })
}



/**
 * 获取批量上传任务列表
 * GET /batch/tasks/list
 */
export function getBatchTaskList(params: BatchTaskQueryParams): Promise<any> {
  return request({
    url: '/batch/tasks/list',
    method: 'get',
    params
  })
}

/**
 * 获取批量上传任务详情
 * GET /batch/tasks/{taskId}
 */
export function getBatchTaskDetail(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}`,
    method: 'get'
  })
}

/**
 * 删除批量上传任务
 * DELETE /batch/tasks/{taskId}
 */
export function deleteBatchTask(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}`,
    method: 'delete'
  })
}

/**
 * 重启批量上传任务
 * POST /batch/tasks/{taskId}/restart
 */
export function restartBatchTask(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/restart`,
    method: 'post'
  })
}

/**
 * 获取任务统计信息
 * GET /batch/tasks/stats
 */
export function getBatchTaskStats(): Promise<any> {
  return request({
    url: '/batch/tasks/stats',
    method: 'get'
  })
}

/**
 * 获取用户的任务列表
 * GET /batch/tasks/user/{userId}
 */
export function getBatchTasksByUserId(userId: number): Promise<any> {
  return request({
    url: `/batch/tasks/user/${userId}`,
    method: 'get'
  })
}

/**
 * 获取部门的任务列表
 * GET /batch/tasks/dept/{deptId}
 */
export function getBatchTasksByDeptId(deptId: number): Promise<any> {
  return request({
    url: `/batch/tasks/dept/${deptId}`,
    method: 'get'
  })
}

/**
 * 获取指定状态的任务列表
 * GET /batch/tasks/status/{status}
 */
export function getBatchTasksByStatus(status: string): Promise<any> {
  return request({
    url: `/batch/tasks/status/${status}`,
    method: 'get'
  })
}

/**
 * 更新任务状态
 * PUT /batch/tasks/{taskId}/status
 */
export function updateBatchTaskStatus(taskId: string, status: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 更新任务进度
 * PUT /batch/tasks/{taskId}/progress
 */
export function updateBatchTaskProgress(taskId: string, processedFolders: number, processedFiles: number): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/progress`,
    method: 'put',
    params: { processedFolders, processedFiles }
  })
}

/**
 * 重试失败的任务
 * POST /batch/tasks/{taskId}/retry
 */
export function retryBatchTask(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/retry`,
    method: 'post'
  })
}

/**
 * 暂停任务
 * POST /batch/tasks/{taskId}/pause
 */
export function pauseBatchTask(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/pause`,
    method: 'post'
  })
}

/**
 * 恢复任务
 * POST /batch/tasks/{taskId}/resume
 */
export function resumeBatchTask(taskId: string): Promise<any> {
  return request({
    url: `/batch/tasks/${taskId}/resume`,
    method: 'post'
  })
}

/**
 * 根据名称查询国家列表
 * GET /cert/country/search?keyword={name}
 */
export function getCountryListByName(name: string): Promise<any> {
  return request({
    url: '/cert/country/search',
    method: 'get',
    params: {
      keyword: name
    }
  })
}

/**
 * 根据名称查询证件类型列表
 * GET /cert/type/listAll?zjlbmc={name}
 */
export function getCertTypeListByName(name: string): Promise<any> {
  return request({
    url: '/cert/type/listAll',
    method: 'get',
    params: {
      zjlbmc: name
    }
  })
}

/**
 * 获取所有国家列表（不分页）
 * GET /cert/country/listAll
 */
export function getAllCountries(): Promise<any> {
  return request({
    url: '/cert/country/listAll',
    method: 'get'
  })
}

/**
 * 获取所有证件类型列表（不分页）
 * GET /cert/type/listAll
 */
export function getAllCertTypes(): Promise<any> {
  return request({
    url: '/cert/type/listAll',
    method: 'get'
  })
}
