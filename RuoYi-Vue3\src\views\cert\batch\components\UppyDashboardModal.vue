<template>
  <el-dialog
    v-model="dialogVisible"
    title="文件上传进度"
    width="900px"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="!isUploading"
    @close="handleClose"
  >
    <!-- 自定义文件列表显示（替代 Dashboard） -->
    <div class="custom-file-list-container">
      <div v-if="fileList.length === 0" class="empty-state">
        <el-empty description="暂无文件" />
      </div>

      <div v-else class="file-list">
        <div class="file-list-header">
          <span>文件列表 ({{ fileList.length }} 个文件)</span>
          <el-button
            v-if="!isUploading"
            type="primary"
            size="small"
            @click="startUpload"
          >
            开始上传
          </el-button>
        </div>

        <div class="file-items">
          <div
            v-for="(file, index) in fileList"
            :key="file.id"
            class="file-item"
            :class="{
              'uploading': file.status === 'uploading',
              'success': file.status === 'success',
              'error': file.status === 'error'
            }"
          >
            <div class="file-info">
              <el-icon class="file-icon">
                <Document />
              </el-icon>
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size) }}</div>
              </div>
            </div>

            <div class="file-status">
              <div v-if="file.status === 'uploading'" class="upload-progress">
                <el-progress
                  :percentage="file.progress || 0"
                  :stroke-width="4"
                  :show-text="false"
                />
                <span class="progress-text">{{ file.progress || 0 }}%</span>
              </div>

              <el-icon v-else-if="file.status === 'success'" class="status-icon success">
                <Check />
              </el-icon>

              <el-icon v-else-if="file.status === 'error'" class="status-icon error">
                <Close />
              </el-icon>

              <span v-else class="status-text">等待上传</span>
            </div>

            <div class="file-actions">
              <el-button
                v-if="!isUploading && file.status !== 'success'"
                type="danger"
                size="small"
                text
                @click="removeFile(file.id)"
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传完成提示 -->
    <div v-if="uploadCompleted" class="upload-completion-section">
      <el-result
        icon="success"
        title="上传完成！"
        :sub-title="completionMessage"
      >
        <template #extra>
          <el-button type="primary" @click="handleClose">
            关闭窗口
          </el-button>
          <el-button @click="handleReset">
            重新上传
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 上传失败提示 -->
    <div v-if="uploadFailed" class="upload-failure-section">
      <el-result
        icon="error"
        title="上传失败"
        :sub-title="failureMessage"
      >
        <template #extra>
          <el-button type="primary" @click="handleRetry">
            重试上传
          </el-button>
          <el-button type="warning" @click="clearTusState">
            清理状态重传
          </el-button>
          <el-button type="info" @click="runNetworkDiagnostics">
            网络诊断
          </el-button>
          <el-button type="success" @click="testTusUpload">
            测试上传
          </el-button>
          <el-button @click="handleClose">
            关闭窗口
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 上传统计信息 -->
    <div v-if="showStats && !uploadCompleted && !uploadFailed" class="upload-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总文件数" :value="uploadStats.totalFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已上传" :value="uploadStats.uploadedFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败文件" :value="uploadStats.failedFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="上传进度" :value="uploadStats.progress" suffix="%" />
        </el-col>
      </el-row>
    </div>

    <template #footer v-if="!uploadCompleted && !uploadFailed">
      <span class="dialog-footer">
        <el-button
          v-if="!isUploading"
          @click="handleClose"
        >
          取消
        </el-button>
        <el-button
          v-if="isUploading"
          type="warning"
          @click="handlePauseResume"
        >
          {{ isPaused ? '继续上传' : '暂停上传' }}
        </el-button>
        <el-button
          v-if="isUploading"
          type="danger"
          @click="handleCancelUpload"
        >
          取消上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Check, Close } from '@element-plus/icons-vue'
import { runConnectionDiagnostics, checkLoginStatus, redirectToLogin } from '@/utils/connectionTest.js'
import { testTusUpload as testTusUploadFunction } from '@/utils/uploadTest.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  uppy: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'upload-complete', 'upload-failed'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

// 上传状态
const isUploading = ref(false)
const isPaused = ref(false)
const uploadCompleted = ref(false)
const uploadFailed = ref(false)
const showStats = ref(false)

// 消息文本
const completionMessage = ref('')
const failureMessage = ref('')

// 上传统计
const uploadStats = ref({
  totalFiles: 0,
  uploadedFiles: 0,
  failedFiles: 0,
  progress: 0
})

// 文件列表（替代 Dashboard）
const fileList = ref([])

// Dashboard 实例引用（保留以防需要）
let dashboardInstance = null

/**
 * 初始化文件列表（替代 Dashboard）
 */
const initializeFileList = () => {
  if (!props.uppy) {
    console.error('Uppy 实例不存在，无法初始化文件列表')
    return
  }

  console.log('初始化自定义文件列表')

  // 从 Uppy 获取文件并转换为我们的格式
  const uppyFiles = props.uppy.getFiles()
  fileList.value = uppyFiles.map(file => ({
    id: file.id,
    name: file.name || 'unnamed-file',
    size: file.size || 0,
    type: file.type || '',
    status: 'pending', // pending, uploading, success, error
    progress: 0,
    error: null
  }))

  console.log(`✅ 已初始化 ${fileList.value.length} 个文件`)
}

// 文件操作方法
const startUpload = () => {
  if (!props.uppy) {
    ElMessage.error('上传组件未初始化')
    return
  }

  console.log('开始上传文件...')
  props.uppy.upload()
}

const removeFile = (fileId) => {
  if (!props.uppy) return

  props.uppy.removeFile(fileId)
  // 同时从我们的文件列表中移除
  fileList.value = fileList.value.filter(file => file.id !== fileId)
  console.log(`已移除文件: ${fileId}`)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 安全卸载 Uppy Dashboard
 */
const unmountDashboard = () => {
  if (!props.uppy) {
    console.warn('Uppy 实例不存在，跳过卸载')
    dashboardInstance = null
    return
  }

  try {
    // 检查 Dashboard 插件是否存在
    const existingDashboard = props.uppy.getPlugin('Dashboard')
    if (existingDashboard) {
      console.log('正在卸载 Dashboard 插件...')
      props.uppy.removePlugin('Dashboard')
      console.log('Dashboard 插件已成功卸载')
    } else {
      console.log('Dashboard 插件不存在，无需卸载')
    }

    // 清理实例引用
    dashboardInstance = null

  } catch (error) {
    console.error('卸载 Dashboard 失败:', error)
    // 强制清理引用，防止内存泄漏
    dashboardInstance = null
  }
}

/**
 * 设置事件监听器
 */
const setupEventListeners = () => {
  if (!props.uppy) return

  // 上传开始
  props.uppy.on('upload', () => {
    console.log('开始上传...')
    isUploading.value = true
    isPaused.value = false
    uploadCompleted.value = false
    uploadFailed.value = false
    showStats.value = true

    // 重置统计数据
    uploadStats.value = {
      totalFiles: props.uppy.getFiles().length,
      uploadedFiles: 0,
      failedFiles: 0,
      progress: 0
    }

    // 更新文件状态为上传中
    fileList.value.forEach(file => {
      file.status = 'uploading'
      file.progress = 0
    })
  })

  // 上传进度
  props.uppy.on('progress', (progress) => {
    uploadStats.value.progress = Math.round(progress)
  })

  // 单个文件上传进度
  props.uppy.on('upload-progress', (file, progress) => {
    const fileItem = fileList.value.find(f => f.id === file.id)
    if (fileItem) {
      fileItem.progress = Math.round((progress.bytesUploaded / progress.bytesTotal) * 100)
    }
  })

  // 单个文件上传成功
  props.uppy.on('upload-success', (file) => {
    uploadStats.value.uploadedFiles++
    console.log(`文件上传成功: ${file.name}`)

    // 更新文件状态
    const fileItem = fileList.value.find(f => f.id === file.id)
    if (fileItem) {
      fileItem.status = 'success'
      fileItem.progress = 100
    }
  })

  // 单个文件上传失败
  props.uppy.on('upload-error', (file, error) => {
    uploadStats.value.failedFiles++
    console.error(`文件上传失败: ${file.name}`, error)

    // 更新文件状态
    const fileItem = fileList.value.find(f => f.id === file.id)
    if (fileItem) {
      fileItem.status = 'error'
      fileItem.error = error.message || '上传失败'
    }
  })

  // 单个文件上传失败
  props.uppy.on('upload-error', (file, error) => {
    uploadStats.value.failedFiles++
    console.error(`文件上传失败: ${file.name}`, error)

    // 检查是否是网络连接错误
    if (error.message && (
      error.message.includes('network error') ||
      error.message.includes('Failed to fetch') ||
      error.message.includes('blocked by an internet provider') ||
      error.message.includes('endpoint might be blocked')
    )) {
      console.warn(`文件 ${file.name} 遇到网络连接错误`)

      ElMessage.error({
        message: `文件 ${file.name} 上传失败：网络连接异常。请检查网络连接和防火墙设置，或点击"网络诊断"按钮进行详细检查。`,
        duration: 10000,
        showClose: true
      })

      // 建议运行网络诊断
      setTimeout(() => {
        ElMessageBox.confirm(
          '检测到网络连接问题，是否运行网络诊断以找出具体原因？',
          '网络诊断建议',
          {
            confirmButtonText: '运行诊断',
            cancelButtonText: '稍后再说',
            type: 'warning'
          }
        ).then(() => {
          runNetworkDiagnostics()
        }).catch(() => {
          // 用户选择不运行诊断
        })
      }, 2000)

    } else if (error.message && (
      error.message.includes('locked') ||
      error.message.includes('already locked') ||
      error.message.includes('UploadAlreadyLockedException')
    )) {
      // 处理上传锁定冲突错误
      console.warn(`文件 ${file.name} 遇到上传锁定冲突，准备重试`)

      // 延迟重试，避免立即冲突
      setTimeout(() => {
        console.log(`重试锁定的上传文件: ${file.name}`)
        props.uppy.retryUpload(file.id)
      }, 1000 + Math.random() * 1000) // 1-2秒随机延迟

      // 显示友好的提示信息
      ElMessage.warning({
        message: `文件 ${file.name} 遇到并发上传冲突，正在自动重试...`,
        duration: 3000
      })

      return // 不执行后续的错误处理

    } else if (error.message && error.message.includes('offset')) {
      // 原有的 offset 错误处理
      console.warn(`文件 ${file.name} 遇到offset错误，尝试清理状态`)

      ElMessage.error({
        message: `文件 ${file.name} 上传失败：断点续传状态异常。建议重新选择文件上传。`,
        duration: 8000,
        showClose: true
      })

      // 尝试清理该文件的 tus 状态
      try {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('tus::') && key.includes(file.id)) {
            localStorage.removeItem(key)
            console.log(`清理Tus状态: ${key}`)
          }
        })
      } catch (cleanupError) {
        console.error('清理Tus状态失败:', cleanupError)
      }
    } else {
      // 普通错误处理
      ElMessage.error({
        message: `文件 ${file.name} 上传失败: ${error.message}`,
        duration: 5000
      })
    }
  })

  // 上传完成
  props.uppy.on('complete', (result) => {
    console.log('上传完成:', result)
    isUploading.value = false
    showStats.value = false

    const { successful, failed } = result

    if (failed.length === 0) {
      // 全部成功
      uploadCompleted.value = true
      completionMessage.value = `成功上传 ${successful.length} 个文件`

      ElMessage.success({
        message: '所有文件上传完成！',
        duration: 3000
      })

      // 发送完成事件
      emit('upload-complete', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })

      // 3秒后自动关闭弹窗
      setTimeout(() => {
        if (uploadCompleted.value) {
          handleClose()
        }
      }, 3000)

    } else if (successful.length === 0) {
      // 全部失败
      uploadFailed.value = true
      failureMessage.value = `${failed.length} 个文件上传失败`

      ElMessage.error({
        message: '文件上传失败，请检查网络连接或文件格式',
        duration: 5000
      })

      // 发送失败事件
      emit('upload-failed', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })

    } else {
      // 部分成功
      uploadCompleted.value = true
      completionMessage.value = `成功上传 ${successful.length} 个文件，${failed.length} 个文件失败`

      ElMessage.warning({
        message: `部分文件上传完成：成功 ${successful.length} 个，失败 ${failed.length} 个`,
        duration: 5000
      })

      // 发送完成事件（包含失败信息）
      emit('upload-complete', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })
    }
  })

  // 上传暂停
  props.uppy.on('upload-pause', () => {
    isPaused.value = true
    console.log('上传已暂停')
  })

  // 上传恢复
  props.uppy.on('upload-resume', () => {
    isPaused.value = false
    console.log('上传已恢复')
  })

  // 上传取消
  props.uppy.on('cancel-all', () => {
    isUploading.value = false
    isPaused.value = false
    showStats.value = false
    console.log('上传已取消')
  })
}

/**
 * 处理暂停/继续上传
 */
const handlePauseResume = () => {
  if (!props.uppy) return

  if (isPaused.value) {
    props.uppy.resumeAll()
  } else {
    props.uppy.pauseAll()
  }
}

/**
 * 处理取消上传
 */
const handleCancelUpload = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前上传吗？已上传的文件不会被删除。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '继续上传',
        type: 'warning'
      }
    )

    if (props.uppy) {
      props.uppy.cancelAll()
    }

    ElMessage.info('上传已取消')

  } catch {
    // 用户取消确认，继续上传
  }
}

/**
 * 处理重试上传
 */
const handleRetry = () => {
  if (props.uppy) {
    uploadFailed.value = false
    failureMessage.value = ''
    props.uppy.retryAll()
  }
}

/**
 * 处理重新上传
 */
const handleReset = () => {
  if (props.uppy) {
    uploadCompleted.value = false
    completionMessage.value = ''

    // 清理所有 tus 相关的本地存储状态
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('tus::')) {
          localStorage.removeItem(key)
          console.log(`清理Tus状态: ${key}`)
        }
      })
      console.log('已清理所有TUS本地状态')

      ElMessage.success({
        message: '已清理上传状态，可以重新选择文件上传',
        duration: 3000
      })
    } catch (error) {
      console.error('清理TUS状态失败:', error)
    }

    props.uppy.reset()
    showStats.value = false
  }
}

/**
 * 运行网络诊断
 */
const runNetworkDiagnostics = async () => {
  ElMessage.info('正在运行网络诊断，请稍候...')

  try {
    const diagnostics = await runConnectionDiagnostics()

    // 检查是否需要登录
    if (!diagnostics.loginStatus.isLoggedIn) {
      await ElMessageBox.alert(
        '检测到您未登录系统，这是导致上传失败的主要原因。\n\n请先登录系统，然后重新尝试上传。',
        '需要登录',
        {
          confirmButtonText: '去登录',
          type: 'warning'
        }
      )

      // 重定向到登录页面
      redirectToLogin()
      return
    }

    // 格式化诊断结果
    const results = []

    // 登录状态
    results.push('=== 登录状态 ===')
    results.push(`✅ 用户已登录: ${diagnostics.loginStatus.userInfo?.userName || '未知用户'}`)
    results.push(`🔑 Token状态: ${diagnostics.loginStatus.token ? '有效' : '无效'}`)

    // 基本连接测试结果
    results.push('\n=== 基本连接测试 ===')
    if (diagnostics.basicTests) {
      diagnostics.basicTests.forEach(test => {
        const status = test.success ? '✅' : '❌'
        results.push(`${status} ${test.name}: ${test.status || 'N/A'} ${test.statusText || test.error || ''}`)
      })
    }

    // TUS协议测试结果
    results.push('\n=== TUS协议测试 ===')
    if (diagnostics.tusTests) {
      if (diagnostics.tusTests.success) {
        results.push('✅ TUS协议连接正常')
        results.push(`📡 状态端点: 正常`)
        results.push(`📡 OPTIONS预检: ${diagnostics.tusTests.optionsResponse.status}`)
        results.push(`📡 创建上传: ${diagnostics.tusTests.createResponse.status}`)
      } else {
        results.push(`❌ TUS协议连接失败: ${diagnostics.tusTests.error}`)
      }
    }

    // 诊断建议
    results.push('\n=== 诊断建议 ===')
    diagnostics.recommendations.forEach(rec => {
      results.push(`💡 ${rec}`)
    })

    // 显示详细的诊断结果
    await ElMessageBox.alert(
      results.join('\n'),
      '网络诊断结果',
      {
        confirmButtonText: '确定',
        type: diagnostics.basicTests?.every(t => t.success) && diagnostics.tusTests?.success ? 'success' : 'warning',
        customStyle: {
          'white-space': 'pre-line',
          'font-family': 'monospace',
          'text-align': 'left'
        }
      }
    )

    // 在控制台输出完整诊断信息
    console.log('🔍 完整网络诊断结果:', diagnostics)

  } catch (error) {
    console.error('💥 网络诊断失败:', error)
    ElMessage.error(`网络诊断失败: ${error.message}`)
  }
}

/**
 * 清理 TUS 上传状态
 */
const clearTusState = () => {
  try {
    // 清理 localStorage 中的 tus 状态
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('tus::')) {
        localStorage.removeItem(key)
        console.log(`清理Tus状态: ${key}`)
      }
    })

    // 重置所有状态
    uploadCompleted.value = false
    uploadFailed.value = false
    failureMessage.value = ''
    completionMessage.value = ''
    showStats.value = false

    if (props.uppy) {
      props.uppy.reset()
    }

    ElMessage.success({
      message: '上传状态已清理，请重新选择文件上传',
      duration: 3000
    })

    console.log('TUS状态清理完成')
  } catch (error) {
    console.error('清理TUS状态失败:', error)
    ElMessage.error('清理上传状态失败，请刷新页面重试')
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  // 如果正在上传，需要确认
  if (isUploading.value) {
    ElMessageBox.confirm(
      '上传正在进行中，确定要关闭吗？',
      '确认关闭',
      {
        confirmButtonText: '确定关闭',
        cancelButtonText: '继续上传',
        type: 'warning'
      }
    ).then(() => {
      if (props.uppy) {
        props.uppy.cancelAll()
      }
      emit('close')
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    emit('close')
  }
}

/**
 * 测试 TUS 上传功能
 */
const testTusUpload = async () => {
  ElMessage.info('正在测试TUS上传功能，请稍候...')

  try {
    const result = await testTusUploadFunction()

    if (result.success) {
      ElMessage.success('TUS上传测试成功！')

      await ElMessageBox.alert(
        `测试结果：
✅ 上传成功
📁 文件大小: ${result.fileSize} 字节
📡 上传位置: ${result.uploadLocation}
📊 上传进度: ${result.uploadOffset}/${result.uploadLength}
🎯 完成状态: ${result.completed ? '已完成' : '未完成'}`,
        'TUS上传测试结果',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )
    } else {
      ElMessage.error(`TUS上传测试失败: ${result.error}`)

      await ElMessageBox.alert(
        `测试失败：
❌ 错误信息: ${result.error}
💡 建议: 请检查网络连接和服务器配置`,
        'TUS上传测试失败',
        {
          confirmButtonText: '确定',
          type: 'error'
        }
      )
    }

  } catch (error) {
    console.error('💥 TUS上传测试异常:', error)
    ElMessage.error(`测试异常: ${error.message}`)
  }
}

// 监听弹窗显示状态
watch(() => props.visible, async (newValue, oldValue) => {
  console.log(`弹窗状态变化: ${oldValue} -> ${newValue}`)

  if (newValue) {
    // 弹窗打开时初始化文件列表
    console.log('弹窗打开，准备初始化文件列表')
    await nextTick()
    initializeFileList()
    setupEventListeners()
  } else {
    // 弹窗关闭时重置状态
    console.log('弹窗关闭，重置组件状态')
    resetComponentState()
  }
})

// 监听 uppy 实例变化
watch(() => props.uppy, (newUppy, oldUppy) => {
  console.log('Uppy 实例变化:', {
    old: oldUppy ? 'exists' : 'null',
    new: newUppy ? 'exists' : 'null',
    visible: props.visible
  })

  if (oldUppy && oldUppy !== newUppy) {
    // 如果有旧实例，先清理
    console.log('清理旧的 Uppy 实例')
    fileList.value = []
  }

  if (newUppy && props.visible) {
    // 新实例且弹窗可见时，初始化文件列表
    console.log('新 Uppy 实例可用，初始化文件列表')
    nextTick(() => {
      initializeFileList()
      setupEventListeners()
    })
  }
})

/**
 * 重置组件状态
 */
const resetComponentState = () => {
  isUploading.value = false
  isPaused.value = false
  uploadCompleted.value = false
  uploadFailed.value = false
  fileList.value = []
  showStats.value = false
  completionMessage.value = ''
  failureMessage.value = ''

  // 重置统计数据
  uploadStats.value = {
    totalFiles: 0,
    uploadedFiles: 0,
    failedFiles: 0,
    progress: 0
  }

  console.log('组件状态已重置')
}

// 组件卸载时安全清理
onUnmounted(() => {
  console.log('组件卸载，执行清理操作')
  unmountDashboard()
  resetComponentState()
})
</script>

<style scoped>
.uppy-dashboard-wrapper {
  min-height: 400px;
}

.upload-completion-section,
.upload-failure-section {
  margin-top: 20px;
  text-align: center;
}

.upload-stats {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Uppy Dashboard 样式优化 */
:deep(.uppy-Dashboard) {
  border: none;
  border-radius: 6px;
}

:deep(.uppy-Dashboard-inner) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

:deep(.uppy-Dashboard-AddFiles) {
  border-color: #dcdfe6;
}

:deep(.uppy-Dashboard-AddFiles:hover) {
  border-color: #409eff;
}

:deep(.uppy-Dashboard-AddFiles-title) {
  color: #606266;
}

:deep(.uppy-Dashboard-note) {
  color: #909399;
}

/* 隐藏图片预览，只显示文件名和大小 */
:deep(.uppy-Dashboard-Item-preview) {
  display: none !important;
}

:deep(.uppy-Dashboard-Item-previewIcon) {
  display: none !important;
}

/* 自定义文件列表样式 */
.custom-file-list-container {
  min-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.file-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.file-item.uploading {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.file-item.success {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.file-item.error {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  color: #909399;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status {
  display: flex;
  align-items: center;
  margin: 0 16px;
  min-width: 120px;
}

.upload-progress {
  display: flex;
  align-items: center;
  width: 100%;
}

.upload-progress .el-progress {
  flex: 1;
  margin-right: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
}

.status-icon {
  font-size: 18px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.error {
  color: #f56c6c;
}

.status-text {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  min-width: 60px;
  text-align: right;
}

:deep(.uppy-Dashboard-Item-name) {
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
}

:deep(.uppy-Dashboard-Item-status) {
  font-size: 12px !important;
  color: #909399 !important;
}

/* 文件大小显示 */
:deep(.uppy-Dashboard-Item-statusSize) {
  font-size: 12px !important;
  color: #606266 !important;
  margin-left: 8px !important;
}

/* 进度条样式 */
:deep(.uppy-Dashboard-Item-progress) {
  height: 4px !important;
  margin-top: 8px !important;
}

/* 文件列表容器优化 */
:deep(.uppy-Dashboard-files) {
  max-height: 300px !important;
  overflow-y: auto !important;
}

/* 文件项布局优化 */
:deep(.uppy-Dashboard-Item-fileInfo) {
  flex: 1 !important;
  min-width: 0 !important;
}

/* 隐藏不必要的图标 */
:deep(.uppy-Dashboard-Item-previewIconWrap) {
  display: none !important;
}
  

</style>
