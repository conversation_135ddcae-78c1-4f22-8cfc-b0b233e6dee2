package com.ruoyi.system.repository;

import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 版本标注模板Repository
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface VersionAnnotationTemplateRepository extends MongoRepository<VersionAnnotationTemplate, String> {
    
    /**
     * 根据版本ID和图片类型查找模板
     */
    Optional<VersionAnnotationTemplate> findByVersionIdAndImageType(String versionId, String imageType);
    
    /**
     * 根据版本ID查找所有模板
     */
    List<VersionAnnotationTemplate> findByVersionId(String versionId);
    
    /**
     * 根据标准文件夹ID查找模板
     */
    List<VersionAnnotationTemplate> findByStandardFolderId(String standardFolderId);
    
    /**
     * 检查版本是否已有指定类型的模板
     */
    boolean existsByVersionIdAndImageType(String versionId, String imageType);
    
    /**
     * 删除指定版本的所有模板
     */
    void deleteByVersionId(String versionId);
    
    /**
     * 删除指定标准文件夹的所有模板
     */
    void deleteByStandardFolderId(String standardFolderId);
    
    /**
     * 根据证件类型查找模板
     */
    List<VersionAnnotationTemplate> findByCertType(String certType);
    
    /**
     * 根据国家代码查找模板
     */
    List<VersionAnnotationTemplate> findByCountryCode(String countryCode);
    
    /**
     * 根据国家代码和证件类型查找模板
     */
    List<VersionAnnotationTemplate> findByCountryCodeAndCertType(String countryCode, String certType);
    
    /**
     * 根据模板ID查找
     */
    Optional<VersionAnnotationTemplate> findByTemplateId(String templateId);
    
    /**
     * 根据创建人查找模板
     */
    List<VersionAnnotationTemplate> findByCreatedBy(String createdBy);
    
    /**
     * 统计版本的模板数量
     */
    long countByVersionId(String versionId);
    
    /**
     * 统计标准文件夹的模板数量
     */
    long countByStandardFolderId(String standardFolderId);
}
