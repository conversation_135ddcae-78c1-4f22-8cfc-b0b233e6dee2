package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.mongo.CertVersion;
import com.ruoyi.system.domain.dto.request.FolderTypeUpdateDTO;
import com.ruoyi.system.domain.dto.request.VersionCreateFromFolderDTO;
import com.ruoyi.system.domain.dto.request.VersionQueryDTO;
import com.ruoyi.system.service.ICertVersionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 证件版本管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/versions")
@Validated
public class CertVersionController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(CertVersionController.class);
    
    @Autowired
    private ICertVersionService certVersionService;
    
    /**
     * 创建新版本 (从文件夹)
     * POST /api/versions
     */
    @PreAuthorize("@ss.hasPermi('cert:version:add')")
    @Log(title = "证件版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult createVersionFromFolder(@Valid @RequestBody VersionCreateFromFolderDTO dto) {
        try {
            log.info("开始从文件夹创建新版本: {}", dto);
            CertVersion version = certVersionService.createVersionFromFolder(dto);
            return AjaxResult.success("创建版本成功", version);
        } catch (Exception e) {
            log.error("从文件夹创建版本失败: {}", e.getMessage(), e);
            return AjaxResult.error("创建版本失败: " + e.getMessage());
        }
    }

    /**
     * 校验版本号是否重复
     * GET /api/versions/check-duplicate?versionCode={versionCode}
     */
    @GetMapping("/check-duplicate")
    public AjaxResult checkVersionCodeDuplicate(@RequestParam String versionCode) {
        try {
            log.info("校验版本号是否重复: {}", versionCode);
            CertVersion existingVersion = certVersionService.findByVersionCode(versionCode);

            if (existingVersion != null) {
                // 返回现有版本信息，供用户选择关联
                Map<String, Object> result = new HashMap<>();
                result.put("isDuplicate", true);
                result.put("existingVersion", existingVersion);
                log.info("版本号已存在: {}", versionCode);
                return AjaxResult.success("版本号已存在", result);
            } else {
                Map<String, Object> result = new HashMap<>();
                result.put("isDuplicate", false);
                log.info("版本号可用: {}", versionCode);
                return AjaxResult.success("版本号可用", result);
            }
        } catch (Exception e) {
            log.error("校验版本号失败: {}", e.getMessage(), e);
            return AjaxResult.error("校验失败: " + e.getMessage());
        }
    }

    /**
     * 管理文件夹类型 (设置标准/普通)
     * PUT /api/versions/{versionId}/folders/{folderId}/type
     */
    @PreAuthorize("@ss.hasPermi('cert:version:edit')")
    @Log(title = "证件版本", businessType = BusinessType.UPDATE)
    @PutMapping("/{versionId}/folders/{folderId}/type")
    public AjaxResult updateFolderType(
            @PathVariable String versionId,
            @PathVariable String folderId,
            @Valid @RequestBody FolderTypeUpdateDTO dto) {
        try {
            log.info("开始更新版本[{}]中文件夹[{}]的类型为[{}]", versionId, folderId, dto.getNewType());
            certVersionService.updateFolderType(versionId, folderId, dto.getNewType());
            return AjaxResult.success("更新文件夹类型成功");
        } catch (Exception e) {
            log.error("更新文件夹类型失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新文件夹类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询版本列表
     * GET /api/versions
     */
    @PreAuthorize("@ss.hasPermi('cert:version:list')")
    @GetMapping
    public TableDataInfo listVersions(VersionQueryDTO query) {
        try {
            log.info("查询版本列表: {}", query);
            startPage();
            
            // 统一使用 getVersionsWithSamplesPaged 方法，确保返回的数据包含 folderCount 字段
            Map<String, Object> result = certVersionService.getVersionsWithSamplesPaged(
                query.getCountryId() != null ? query.getCountryId().intValue() : null,
                query.getPageNum(),
                query.getPageSize(),
                query.getCertType(),
                query.getStatus(),
                query.getKeyword()
            );
            
            // 使用若依标准的 getDataTable 方法
            List<?> rows = (List<?>) result.get("rows");
            return getDataTable(rows);
        } catch (Exception e) {
            log.error("查询版本列表失败: {}", e.getMessage(), e);
            return getDataTable(new ArrayList<>());
        }
    }
    
    /**
     * 获取版本详情
     * GET /api/versions/{versionId}
     */
    @PreAuthorize("@ss.hasPermi('cert:version:query')")
    @GetMapping("/{versionId}")
    public AjaxResult getVersionDetails(@PathVariable String versionId) {
        try {
            log.info("获取版本详情: {}", versionId);
            CertVersion version = certVersionService.getVersionByVersionId(versionId);
            if (version != null) {
                return AjaxResult.success("获取版本详情成功", version);
            } else {
                return AjaxResult.error("版本不存在");
            }
        } catch (Exception e) {
            log.error("获取版本详情失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取版本详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除版本
     * DELETE /api/versions/{versionId}
     */
    @PreAuthorize("@ss.hasPermi('cert:version:remove')")
    @Log(title = "证件版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{versionId}")
    public AjaxResult deleteVersion(@PathVariable String versionId) {
        try {
            log.info("删除版本: {}", versionId);
            CertVersion version = certVersionService.getVersionByVersionId(versionId);
            if (version != null) {
                certVersionService.deleteVersion(version.getId());
                return AjaxResult.success("删除版本成功");
            } else {
                return AjaxResult.error("版本不存在");
            }
        } catch (Exception e) {
            log.error("删除版本失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除版本失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定国家的版本信息
     * GET /api/versions/country/{countryId}
     */
    @PreAuthorize("@ss.hasPermi('cert:version:list')")
    @GetMapping("/country/{countryId}")
    public AjaxResult getVersionsByCountry(@PathVariable Integer countryId) {
        try {
            log.info("获取国家[{}]的版本信息", countryId);
            List<Map<String, Object>> versions = certVersionService.getVersionsWithSamples(countryId);
            return AjaxResult.success("获取版本信息成功", versions);
        } catch (Exception e) {
            log.error("获取国家版本信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本下的文件夹列表
     * GET /api/versions/{versionId}/folders
     */
    @PreAuthorize("@ss.hasPermi('cert:version:list')")
    @GetMapping("/{versionId}/folders")
    public AjaxResult getVersionFolders(@PathVariable String versionId) {
        try {
            log.info("获取版本[{}]下的文件夹列表", versionId);
            List<Map<String, Object>> folders = certVersionService.getVersionFolders(versionId);
            return AjaxResult.success("获取文件夹列表成功", folders);
        } catch (Exception e) {
            log.error("获取版本文件夹列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取文件夹列表失败: " + e.getMessage());
        }
    }

    /**
     * 设置标准样本文件夹
     * PUT /api/versions/{versionId}/standard-folder
     */
    @PreAuthorize("@ss.hasPermi('cert:version:edit')")
    @Log(title = "证件版本", businessType = BusinessType.UPDATE)
    @PutMapping("/{versionId}/standard-folder")
    public AjaxResult setStandardFolder(@PathVariable String versionId,
                                       @Valid @RequestBody Map<String, String> request) {
        try {
            String folderId = request.get("folderId");
            if (folderId == null || folderId.trim().isEmpty()) {
                return AjaxResult.error("文件夹ID不能为空");
            }

            log.info("设置版本[{}]的标准样本文件夹: {}", versionId, folderId);
            certVersionService.setStandardFolder(versionId, folderId);
            return AjaxResult.success("设置标准样本文件夹成功");
        } catch (Exception e) {
            log.error("设置标准样本文件夹失败: {}", e.getMessage(), e);
            return AjaxResult.error("设置标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 取消标准样本文件夹
     * DELETE /api/versions/{versionId}/standard-folder
     */
    @PreAuthorize("@ss.hasPermi('cert:version:edit')")
    @Log(title = "证件版本", businessType = BusinessType.UPDATE)
    @DeleteMapping("/{versionId}/standard-folder")
    public AjaxResult removeStandardFolder(@PathVariable String versionId) {
        try {
            log.info("取消版本[{}]的标准样本文件夹设置", versionId);
            certVersionService.removeStandardFolder(versionId);
            return AjaxResult.success("取消标准样本文件夹成功");
        } catch (Exception e) {
            log.error("取消标准样本文件夹失败: {}", e.getMessage(), e);
            return AjaxResult.error("取消标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务ID获取版本列表
     * GET /api/versions/task/{taskId}
     */
    @PreAuthorize("@ss.hasPermi('cert:version:list')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getVersionsByTaskId(@PathVariable String taskId) {
        try {
            log.info("根据任务ID获取版本列表: taskId={}", taskId);
            List<CertVersion> versions = certVersionService.getVersionsByTaskId(taskId);
            return AjaxResult.success("获取版本列表成功", versions);
        } catch (Exception e) {
            log.error("根据任务ID获取版本列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取版本列表失败: " + e.getMessage());
        }
    }

    /**
     * 修复标准样本数据
     * POST /api/versions/{versionId}/fix-standard-data
     */
    @PreAuthorize("@ss.hasPermi('cert:version:edit')")
    @Log(title = "证件版本", businessType = BusinessType.UPDATE)
    @PostMapping("/{versionId}/fix-standard-data")
    public AjaxResult fixStandardData(@PathVariable String versionId) {
        try {
            log.info("修复版本[{}]的标准样本数据", versionId);
            certVersionService.fixStandardFolderData(versionId);
            return AjaxResult.success("修复标准样本数据成功");
        } catch (Exception e) {
            log.error("修复标准样本数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("修复标准样本数据失败: " + e.getMessage());
        }
    }
}
