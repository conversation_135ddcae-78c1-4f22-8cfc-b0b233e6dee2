# 🎨 证件样本版本管理系统 - 前端组件设计方案

## 📋 1. 设计原则与约束

### 1.1 代码复用原则
- ✅ **保持现有组件不变** - 不修改任何现有组件，避免影响其他功能
- ✅ **最大化代码复用** - 通过组合、继承、抽取等方式复用现有逻辑
- ✅ **渐进式开发** - 新功能独立开发，不影响现有系统稳定性

### 1.2 业务规则约束
- 🔒 **标注权限控制** - 只有标准样本文件夹可以进行标注操作
- 👁️ **标注信息查看** - 非标准样本可以查看标准样本的标注信息叠加显示
- 🎯 **特定图片标注** - 只有特定命名的图片支持标注功能
  - `可见光主图.jpg`
  - `红外光主图.jpg` 
  - `紫外光主图.jpg`

## 📦 2. 组件架构设计

### 2.1 整体架构图
```
VersionDetailView.vue (新建主页面)
├── VersionInfoCard.vue (版本信息卡片 - 新建)
└── VersionWorkspace.vue (三列工作区 - 新建)
    ├── VersionFolderList.vue (左侧文件夹列表 - 新建，基于现有组件)
    ├── VersionImagePreview.vue (中间预览区 - 新建，组合现有组件)
    └── VersionThumbnailGrid.vue (右侧缩略图 - 新建，抽取现有代码)
```

### 2.2 组件复用策略

| 现有组件 | 复用方式 | 新组件 | 说明 |
|---------|---------|--------|------|
| `UnassignedFolderList.vue` | 📋 参考设计 | `VersionFolderList.vue` | 新建组件，参考现有设计模式 |
| `FolderListItem/` | ✅ 直接复用 | - | 作为子组件使用 |
| `AssociatedFolderItem.vue` | ✅ 直接复用 | - | 作为子组件使用 |
| `StandardSampleManager.vue` | 🔧 扩展使用 | - | 添加叠加显示模式 |
| `AnnotationCanvas.vue` | ✅ 直接复用 | - | 标注画布功能 |
| `ImageTypeAnnotator.vue` | ✅ 直接复用 | - | 图片类型标注 |
| `FolderDetailView.vue` 图片列表 | 📦 代码抽取 | `VersionThumbnailGrid.vue` | 抽取缩略图网格逻辑 |

## 🧩 3. 详细组件设计

### 3.1 VersionDetailView.vue (主页面容器)

#### 功能职责
- **路由管理** - 处理页面路由参数，加载对应版本数据
- **状态管理** - 管理版本信息、文件夹列表、选中状态等全局数据
- **事件协调** - 协调三个子组件之间的数据传递和事件通信
- **权限控制** - 根据文件夹类型和图片名称控制操作权限

#### 核心数据结构
```javascript
const state = reactive({
  // 基础数据
  versionInfo: null,           // 版本基本信息
  folderList: [],             // 版本下的文件夹列表
  
  // 选中状态
  selectedFolder: null,        // 当前选中的文件夹
  selectedImage: null,         // 当前选中的图片
  
  // 图片数据
  currentFolderImages: [],     // 当前文件夹的图片列表
  standardAnnotations: new Map(), // 标准样本标注数据缓存
  
  // 加载状态
  loading: false,
  imageLoading: false
})
```

#### 权限控制逻辑
```javascript
// 是否可以标注
const canAnnotate = computed(() => {
  return selectedFolder.value?.folderId === versionInfo.value?.standardFolderId &&
         isAnnotatableImage(selectedImage.value)
})

// 是否可以查看标注叠加
const canViewAnnotationOverlay = computed(() => {
  return isAnnotatableImage(selectedImage.value) && 
         hasStandardAnnotations(selectedImage.value?.imageType)
})

// 判断是否为可标注图片
const isAnnotatableImage = (image) => {
  if (!image) return false
  const annotatableNames = ['可见光主图.jpg', '红外光主图.jpg', '紫外光主图.jpg']
  return annotatableNames.includes(image.imageName)
}
```

### 3.2 VersionFolderList.vue (左侧文件夹列表)

#### 设计思路
- **新建组件** - 不修改 `UnassignedFolderList.vue`，创建专门的版本文件夹列表
- **复用子组件** - 使用 `FolderListItem` 和 `AssociatedFolderItem` 作为列表项
- **简化交互** - 专注于文件夹选择和状态展示，移除复杂的关联操作

#### 功能特性
```javascript
// 组件接口
interface Props {
  folders: FolderInfo[]        // 文件夹列表
  selectedFolderId: string     // 当前选中文件夹ID
  standardFolderId: string     // 标准样本文件夹ID
  loading: boolean            // 加载状态
}

const emit = defineEmits([
  'folder-select',            // 文件夹选择事件
  'folder-detail'             // 查看文件夹详情事件
])
```

#### 显示逻辑
```javascript
// 文件夹分组显示
const folderGroups = computed(() => {
  return {
    standard: folders.value.filter(f => f.folderId === standardFolderId.value),
    ordinary: folders.value.filter(f => f.folderId !== standardFolderId.value)
  }
})

// 文件夹状态标识
const getFolderStatus = (folder) => {
  if (folder.folderId === standardFolderId.value) {
    return { type: 'success', text: '标准样本', icon: 'Star' }
  }
  return { type: 'info', text: '普通样本', icon: '' }
}
```

### 3.3 VersionImagePreview.vue (中间图片预览区)

#### 功能职责
- **图片展示** - 高清图片预览，支持缩放拖拽
- **标注叠加** - 对可标注图片显示标准样本的标注信息
- **操作入口** - 提供标准样本设置、标注编辑等操作按钮
- **权限控制** - 根据文件夹类型和图片类型控制操作可见性

#### 核心功能设计
```javascript
// 组件模式
const previewModes = {
  VIEW_ONLY: 'view-only',           // 仅查看模式
  ANNOTATION_OVERLAY: 'overlay',    // 标注叠加模式  
  ANNOTATION_EDIT: 'edit'           // 标注编辑模式
}

// 当前模式计算
const currentMode = computed(() => {
  if (canAnnotate.value) return previewModes.ANNOTATION_EDIT
  if (canViewAnnotationOverlay.value) return previewModes.ANNOTATION_OVERLAY
  return previewModes.VIEW_ONLY
})
```

#### 标注叠加功能
```javascript
// 标注叠加逻辑
const overlayAnnotations = computed(() => {
  if (currentMode.value !== previewModes.ANNOTATION_OVERLAY) return []
  
  // 获取标准样本对应图片类型的标注数据
  const imageType = selectedImage.value?.imageType
  return standardAnnotations.value.get(imageType) || []
})

// 叠加显示配置
const overlayConfig = {
  opacity: 0.7,              // 叠加透明度
  color: '#409eff',          // 叠加颜色
  showLabels: true,          // 显示标签
  interactive: false         // 不可交互
}
```

#### 操作按钮设计
```javascript
// 按钮显示逻辑
const actionButtons = computed(() => {
  const buttons = []
  
  // 查看详情按钮 - 始终显示
  buttons.push({
    text: '查看详情',
    type: 'info',
    action: 'view-detail',
    visible: true
  })
  
  // 标准样本操作按钮
  if (selectedFolder.value) {
    if (isStandardFolder.value) {
      buttons.push({
        text: '取消标准样本',
        type: 'warning', 
        action: 'remove-standard',
        visible: true
      })
      
      if (canAnnotate.value) {
        buttons.push({
          text: '开始标注',
          type: 'primary',
          action: 'start-annotation', 
          visible: true
        })
      }
    } else if (canSetAsStandard.value) {
      buttons.push({
        text: '设为标准样本',
        type: 'success',
        action: 'set-standard',
        visible: true
      })
    }
  }
  
  return buttons
})
```

### 3.4 VersionThumbnailGrid.vue (右侧缩略图网格)

#### 代码抽取策略
从 `FolderDetailView.vue` 抽取以下部分：
- **模板代码** - 第185-240行的图片列表模板
- **样式代码** - 第1114-1180行的图片列表样式
- **工具方法** - `getImageThumbnail()`, `handleImageError()` 等

#### 功能增强
```javascript
// 图片筛选功能
const filterOptions = {
  imageType: ['', 'VISIBLE', 'INFRARED', 'ULTRAVIOLET'], // 图片类型筛选
  annotationStatus: ['', 'annotated', 'unannotated'],    // 标注状态筛选
  annotatableOnly: false                                 // 仅显示可标注图片
}

// 图片状态标识
const getImageStatus = (image) => {
  const status = {
    isAnnotatable: isAnnotatableImage(image),
    isAnnotated: image.isAnnotated,
    hasOverlay: canViewAnnotationOverlay.value && isAnnotatableImage(image)
  }
  return status
}
```

#### 视觉标识设计
```javascript
// 图片标识配置
const imageIndicators = {
  // 可标注图片标识
  annotatable: {
    icon: 'EditPen',
    color: '#409eff',
    position: 'top-right'
  },
  
  // 已标注标识  
  annotated: {
    icon: 'Check',
    color: '#67c23a', 
    position: 'top-right'
  },
  
  // 有叠加标注标识
  hasOverlay: {
    icon: 'View',
    color: '#e6a23c',
    position: 'bottom-right'
  }
}
```

### 3.5 VersionInfoCard.vue (版本信息卡片)

#### 设计目标
- **紧凑展示** - 单行显示关键信息，节省垂直空间
- **状态突出** - 重点展示标准样本状态和统计信息
- **快速定位** - 提供版本基本信息的快速查看

#### 信息布局
```javascript
// 信息项配置
const infoItems = [
  { label: '版本代码', field: 'versionCode', span: 2, type: 'primary' },
  { label: '国家', field: 'countryInfo.name', span: 1 },
  { label: '证件类型', field: 'certInfo.zjlbmc', span: 1 },
  { label: '发行年份', field: 'issueYear', span: 1 },
  { label: '标准样本', field: 'standardStatus', span: 2, type: 'status' },
  { label: '文件夹数', field: 'folderCount', span: 1, type: 'count' }
]
```

## 🔄 4. 数据流与交互设计

### 4.1 组件通信机制
```mermaid
graph TD
    A[VersionDetailView] --> B[VersionFolderList]
    A --> C[VersionImagePreview] 
    A --> D[VersionThumbnailGrid]
    
    B --> |folder-select| A
    D --> |image-select| A
    C --> |set-standard| A
    C --> |start-annotation| A
    
    A --> |selectedFolder| C
    A --> |selectedFolder| D
    A --> |currentImages| D
    A --> |selectedImage| C
    A --> |standardAnnotations| C
```

### 4.2 标注数据管理
```javascript
// 标注数据缓存策略
class AnnotationCache {
  constructor() {
    this.cache = new Map() // imageType -> annotations
  }
  
  // 加载标准样本标注数据
  async loadStandardAnnotations(versionId, standardFolderId) {
    const annotatableTypes = ['VISIBLE', 'INFRARED', 'ULTRAVIOLET']
    
    for (const type of annotatableTypes) {
      const annotations = await api.getStandardAnnotations(standardFolderId, type)
      this.cache.set(type, annotations)
    }
  }
  
  // 获取指定类型的标注数据
  getAnnotations(imageType) {
    return this.cache.get(imageType) || []
  }
  
  // 清空缓存
  clear() {
    this.cache.clear()
  }
}
```

### 4.3 权限控制流程
```mermaid
graph TD
    A[选择图片] --> B{是否为可标注图片?}
    B -->|否| C[普通查看模式]
    B -->|是| D{是否为标准样本文件夹?}
    D -->|是| E[标注编辑模式]
    D -->|否| F{是否有标准样本标注?}
    F -->|是| G[标注叠加查看模式]
    F -->|否| H[普通查看模式]
```

## 📱 5. 响应式设计方案

### 5.1 布局断点策略
```scss
// 断点定义
$breakpoints: (
  'mobile': 768px,
  'tablet': 1024px, 
  'desktop': 1200px,
  'large': 1440px
);

// 布局适配
.version-workspace {
  // 大屏：三列并排
  @media (min-width: 1200px) {
    display: flex;
    .left-panel { width: 300px; }
    .center-panel { flex: 1; }
    .right-panel { width: 320px; }
  }
  
  // 中屏：上下布局
  @media (max-width: 1199px) and (min-width: 768px) {
    .left-panel { height: 200px; order: 1; }
    .right-panel { height: 150px; order: 2; }
    .center-panel { order: 3; }
  }
  
  // 小屏：单列堆叠
  @media (max-width: 767px) {
    .left-panel { height: 150px; }
    .right-panel { height: 120px; }
  }
}
```

### 5.2 移动端优化
- **触摸友好** - 增大点击区域至44px以上
- **滑动操作** - 支持左右滑动切换图片
- **简化界面** - 隐藏次要信息，突出核心功能
- **快速操作** - 提供快捷操作按钮

## 🎯 6. 开发实施计划

### Phase 1: 基础组件开发 (3-4天)
1. **VersionThumbnailGrid.vue** - 抽取并增强缩略图网格功能
2. **VersionInfoCard.vue** - 创建紧凑版本信息卡片
3. **VersionFolderList.vue** - 创建专用文件夹列表组件

### Phase 2: 核心功能开发 (4-5天)  
1. **VersionImagePreview.vue** - 实现图片预览和标注叠加功能
2. **VersionDetailView.vue** - 创建主页面和数据管理逻辑
3. **权限控制系统** - 实现基于文件夹和图片类型的权限控制

### Phase 3: 集成测试和优化 (2-3天)
1. **功能集成测试** - 验证组件间交互和数据流
2. **响应式测试** - 测试不同屏幕尺寸的适配效果
3. **性能优化** - 图片懒加载、标注数据缓存等
4. **用户体验优化** - 动画效果、加载状态等

## 📋 7. 技术实现要点

### 7.1 标注叠加技术方案
```javascript
// 标注叠加组件设计
const AnnotationOverlay = {
  props: {
    annotations: Array,      // 标注数据
    imageSize: Object,       // 图片尺寸
    overlayConfig: Object    // 叠加配置
  },
  
  // 坐标转换逻辑
  methods: {
    convertCoordinates(annotation, imageSize) {
      // 将标注坐标转换为当前图片尺寸下的坐标
    },
    
    renderAnnotation(annotation) {
      // 渲染单个标注项
    }
  }
}
```

### 7.2 图片加载优化
```javascript
// 图片懒加载策略
const useImageLazyLoad = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        loadImage(entry.target)
      }
    })
  })
  
  return { observer }
}

// 缩略图预加载
const preloadThumbnails = (images) => {
  const preloadQueue = images.slice(0, 10) // 预加载前10张
  preloadQueue.forEach(image => {
    const img = new Image()
    img.src = image.thumbnailUrl
  })
}
```

### 7.3 状态管理优化
```javascript
// 使用 Pinia 进行状态管理
const useVersionStore = defineStore('version', {
  state: () => ({
    currentVersion: null,
    folderList: [],
    selectedFolder: null,
    selectedImage: null,
    annotationCache: new Map()
  }),
  
  actions: {
    async loadVersionData(versionId) {
      // 加载版本数据
    },
    
    async loadStandardAnnotations(standardFolderId) {
      // 加载标准样本标注数据
    }
  }
})
```

## 🔍 8. 质量保证

### 8.1 单元测试覆盖
- **组件渲染测试** - 验证组件正确渲染
- **交互测试** - 验证用户交互功能
- **权限控制测试** - 验证权限逻辑正确性
- **数据流测试** - 验证组件间数据传递

### 8.2 集成测试场景
- **完整工作流测试** - 从版本选择到标注操作的完整流程
- **权限边界测试** - 测试各种权限边界情况
- **响应式测试** - 测试不同设备和屏幕尺寸
- **性能测试** - 测试大量数据下的性能表现

---

## 📝 总结

本设计方案在保持现有代码稳定性的前提下，通过新建组件和代码抽取的方式，实现了您需求文档中描述的三列协同布局和标注叠加功能。主要特点：

1. **零影响原则** - 不修改任何现有组件，确保系统稳定性
2. **最大化复用** - 充分利用现有组件和代码逻辑
3. **权限精确控制** - 基于文件夹类型和图片名称的精确权限控制
4. **标注叠加创新** - 非标准样本可查看标准样本标注信息
5. **响应式友好** - 适配不同屏幕尺寸的使用场景

请您仔细审查此方案，如有任何需要调整或补充的地方，请告知我。
