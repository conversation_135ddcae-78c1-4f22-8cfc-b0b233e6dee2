import os
import requests
import uuid
from minio import Minio
from minio.error import S3Error
import logging
from datetime import datetime
import concurrent.futures
import time
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 配置
API_URL = 'http://localhost:8000/api/v1/cert/version/unified'
FOLDER_ROOT = r'C:\Users\<USER>\Documents\工作文档\2025研发项目\证研平台\configs_upd\configs\personal'

# MinIO配置
MINIO_ENDPOINT = 'localhost:9000'
MINIO_ACCESS_KEY = 'minioadmin'
MINIO_SECRET_KEY = 'minioadmin'
MINIO_BUCKET_NAME = 'xjlfiles'
MINIO_SECURE = False

# 性能优化配置 - 大幅提升并发数
MAX_CONCURRENT_UPLOADS = 10   # 图片上传并发数
MAX_CONCURRENT_REQUESTS = 8   # API请求并发数
BATCH_SIZE = 50              # 批处理大小

# 初始化MinIO客户端
minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

def ensure_bucket_exists():
    """确保MinIO bucket存在"""
    try:
        if not minio_client.bucket_exists(MINIO_BUCKET_NAME):
            minio_client.make_bucket(MINIO_BUCKET_NAME)
            logger.info(f"创建MinIO bucket: {MINIO_BUCKET_NAME}")
        return True
    except Exception as e:
        logger.error(f"MinIO bucket检查失败: {e}")
        return False

def upload_training_image_to_minio(local_path: str, folder_name: str) -> str:
    """上传训练图片到MinIO（优化版）"""
    try:
        if not os.path.exists(local_path):
            logger.warning(f"训练图片文件不存在: {local_path}")
            return None
        
        # 生成MinIO路径：原始文件夹名/training/PER.jpg
        file_extension = os.path.splitext(local_path)[1].lower()
        minio_path = f"{folder_name}/training/PER{file_extension}"
        
        # 检查文件是否已存在（去重优化）
        try:
            minio_client.stat_object(MINIO_BUCKET_NAME, minio_path)
            minio_url = f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
            return minio_url
        except S3Error:
            # 文件不存在，继续上传
            pass
        
        # 上传文件
        minio_client.fput_object(
            bucket_name=MINIO_BUCKET_NAME,
            object_name=minio_path,
            file_path=local_path,
            content_type=f"image/{file_extension[1:]}" if file_extension in ['.jpg', '.jpeg', '.png'] else 'application/octet-stream'
        )
        
        # 生成访问URL
        minio_url = f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
        return minio_url
        
    except Exception as e:
        logger.error(f"上传训练图片异常: {folder_name}, {e}")
        return None

def upload_image_with_retry(local_path: str, folder_name: str, max_retries: int = 2) -> str:
    """带重试的图片上传（减少重试次数提升速度）"""
    for attempt in range(max_retries):
        try:
            result = upload_training_image_to_minio(local_path, folder_name)
            if result:
                return result
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(0.5)  # 减少等待时间
    return None

def parse_folder_name(folder_name: str) -> Dict[str, str]:
    """解析文件夹名称，提取信息"""
    parts = folder_name.split('_')
    if len(parts) >= 4:
        return {
            'countryCode': parts[0],
            'certType': parts[1],
            'startNo': parts[2],
            'issueYear': parts[3]
        }
    return None

def find_training_image(root_dir: str, country_code: str, folder_name: str) -> str:
    """查找训练图片"""
    possible_paths = [
        os.path.join(root_dir, country_code, folder_name, "PER.JPG"),
        os.path.join(root_dir, country_code, folder_name, "per.jpg"),
        os.path.join(root_dir, country_code, folder_name, "PER.jpg"),
        os.path.join(root_dir, country_code, folder_name, "per.JPG")
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def find_cert_folders(root_dir: str) -> List[Dict[str, str]]:
    """递归查找符合格式的文件夹"""
    result = []
    for country_code in os.listdir(root_dir):
        country_path = os.path.join(root_dir, country_code)
        if os.path.isdir(country_path):
            for folder in os.listdir(country_path):
                folder_path = os.path.join(country_path, folder)
                if os.path.isdir(folder_path) and '_' in folder:
                    parts = folder.split('_')
                    if len(parts) == 4:
                        result.append({
                            'folderName': folder,
                            'countryCode': country_code,
                            'folderPath': folder_path
                        })
    return result

def generate_version_code(country_code: str, cert_type: str, issue_year: str) -> str:
    """生成版本代码"""
    timestamp = datetime.now().strftime("%Y%m%d")
    return f"{country_code}_{cert_type}_{issue_year}_{timestamp}"

def process_single_folder(folder_info: Dict[str, str]) -> Dict[str, Any]:
    """处理单个文件夹（包含图片上传和请求数据准备）"""
    try:
        folder_name = folder_info['folderName']
        country_code = folder_info['countryCode']
        
        # 解析文件夹名称
        parsed = parse_folder_name(folder_name)
        if not parsed:
            return {
                'folder_info': folder_info,
                'status': 'error',
                'error': f'无法解析文件夹名称: {folder_name}'
            }
        
        # 生成版本代码
        version_code = generate_version_code(
            country_code, 
            parsed['certType'], 
            parsed['issueYear']
        )
        
        # 查找并上传训练图片
        training_image_path = find_training_image(FOLDER_ROOT, country_code, folder_name)
        training_image_url = None
        
        if training_image_path:
            training_image_url = upload_image_with_retry(training_image_path, folder_name)
            if not training_image_url:
                training_image_url = f"local://{training_image_path}"
        
        # 构建请求数据
        request_data = {
            "creationMode": "STANDARD_SAMPLE",
            "folderName": folder_name,
            "certType": parsed['certType'],
            "issueYear": parsed['issueYear'],
            "countryCode": country_code,
            "startNo": parsed['startNo'],
            "endNo": "D9999",
            "numberFormat": "D{4}",
            "securityFeatures": ["水印", "防伪线", "全息图"],
            "width": 85.6,
            "height": 53.98,
            "thickness": 0.76,
            "material": "PVC",
            "versionCode": version_code,
            "trainingImageUrl": training_image_url,
            "trainingImageLightType": "visible",
            "trainingImageDescription": f"标准样本训练图片 - {version_code}",
            "imageCount": 1,
            "lightTypes": ["visible"]
        }
        
        return {
            'folder_info': folder_info,
            'request_data': request_data,
            'training_image_url': training_image_url,
            'status': 'ready'
        }
        
    except Exception as e:
        return {
            'folder_info': folder_info,
            'status': 'error',
            'error': str(e)
        }

def send_api_request(processed_folder: Dict[str, Any]) -> Dict[str, Any]:
    """发送API请求创建证件版本"""
    try:
        if processed_folder['status'] != 'ready':
            return {**processed_folder, 'api_status': 'skipped'}
        
        folder_name = processed_folder['folder_info']['folderName']
        request_data = processed_folder['request_data']
        
        headers = {'Content-Type': 'application/json'}
        
        # 发送请求（减少超时时间）
        response = requests.post(API_URL, json=request_data, headers=headers, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return {
                    **processed_folder,
                    'api_status': 'success',
                    'version_id': result.get('data', {}).get('versionId'),
                    'version_code': result.get('data', {}).get('versionCode')
                }
            else:
                return {
                    **processed_folder,
                    'api_status': 'failed',
                    'error': result.get('msg', '未知错误')
                }
        else:
            return {
                **processed_folder,
                'api_status': 'http_error',
                'error': f"HTTP {response.status_code}"
            }
            
    except Exception as e:
        return {
            **processed_folder,
            'api_status': 'exception',
            'error': str(e)
        }

def batch_create_unified_cert_versions_concurrent():
    """真正的并发版本批量创建证件版本"""
    try:
        print("🚀 证件版本统一创建工具 (真正并发版)")
        print("=" * 60)
        
        # 检查MinIO连接
        if not ensure_bucket_exists():
            print("❌ MinIO连接失败，请检查MinIO服务是否启动")
            return
        
        # 获取文件夹列表
        folder_infos = find_cert_folders(FOLDER_ROOT)
        if not folder_infos:
            print("未找到符合格式的文件夹")
            return

        print(f"📁 找到 {len(folder_infos)} 个证件文件夹")
        print(f"⚙️  并发配置: 上传{MAX_CONCURRENT_UPLOADS}线程, API{MAX_CONCURRENT_REQUESTS}线程")
        
        # 确认是否继续
        confirm = input("\n是否继续处理? (y/n): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return

        start_time = time.time()
        
        # 阶段1：并发处理文件夹（图片上传+数据准备）
        print(f"\n🔄 阶段1: 并发处理文件夹 (最大并发: {MAX_CONCURRENT_UPLOADS})")
        processed_folders = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_UPLOADS) as executor:
            # 提交所有任务
            future_to_folder = {
                executor.submit(process_single_folder, folder_info): folder_info 
                for folder_info in folder_infos
            }
            
            # 获取结果
            completed = 0
            for future in concurrent.futures.as_completed(future_to_folder):
                completed += 1
                folder_info = future_to_folder[future]
                
                try:
                    result = future.result()
                    processed_folders.append(result)
                    
                    status_icon = "✅" if result['status'] == 'ready' else "❌"
                    if completed % 50 == 0 or completed == len(folder_infos):  # 每50个显示一次进度
                        print(f"  进度: [{completed}/{len(folder_infos)}] {status_icon} 最新: {folder_info['folderName']}")
                        
                except Exception as e:
                    processed_folders.append({
                        'folder_info': folder_info,
                        'status': 'error',
                        'error': str(e)
                    })

        stage1_time = time.time() - start_time
        ready_count = sum(1 for p in processed_folders if p['status'] == 'ready')
        print(f"  ✅ 阶段1完成: {ready_count}/{len(folder_infos)} 准备就绪, 用时: {stage1_time:.1f}秒")

        # 阶段2：并发发送API请求
        print(f"\n🔄 阶段2: 并发API请求 (最大并发: {MAX_CONCURRENT_REQUESTS})")
        final_results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_REQUESTS) as executor:
            # 提交API请求任务
            future_to_processed = {
                executor.submit(send_api_request, processed): processed 
                for processed in processed_folders
            }
            
            # 获取结果
            completed = 0
            for future in concurrent.futures.as_completed(future_to_processed):
                completed += 1
                processed = future_to_processed[future]
                
                try:
                    result = future.result()
                    final_results.append(result)
                    
                    folder_name = result['folder_info']['folderName']
                    api_status = result.get('api_status', 'unknown')
                    
                    if completed % 50 == 0 or completed == len(processed_folders):  # 每50个显示一次进度
                        status_icon = "✅" if api_status == 'success' else "❌"
                        print(f"  进度: [{completed}/{len(processed_folders)}] {status_icon} 最新: {folder_name}")
                        
                except Exception as e:
                    logger.error(f"API请求异常: {processed['folder_info']['folderName']}, 错误: {str(e)}")

        # 统计结果
        end_time = time.time()
        total_time = end_time - start_time
        stage2_time = total_time - stage1_time
        
        success_count = sum(1 for r in final_results if r.get('api_status') == 'success')
        failed_count = len(final_results) - success_count
        uploaded_images = [r for r in final_results if r.get('training_image_url') and 'http://' in r['training_image_url']]

        # 输出详细统计
        print("\n" + "="*60)
        print("🎉 批量创建证件版本结果 (真正并发版):")
        print(f"📊 总数: {len(folder_infos)}")
        print(f"✅ 成功: {success_count}")
        print(f"❌ 失败: {failed_count}")
        print(f"📈 成功率: {success_count/len(folder_infos)*100:.1f}%")
        print(f"⏱️  总处理时间: {total_time:.1f} 秒")
        print(f"   - 阶段1(处理): {stage1_time:.1f} 秒")
        print(f"   - 阶段2(API): {stage2_time:.1f} 秒")
        print(f"🚀 平均速度: {len(folder_infos)/total_time:.2f} 个/秒")
        print(f"💡 性能提升: 约 {(len(folder_infos)/total_time)/0.37:.1f} 倍")
        
        if uploaded_images:
            print(f"\n📁 成功上传到MinIO的训练图片: {len(uploaded_images)} 个")
        
        failed_results = [r for r in final_results if r.get('api_status') != 'success']
        if failed_results and len(failed_results) <= 20:  # 只显示前20个失败
            print(f"\n❌ 失败的文件夹 (显示前{min(len(failed_results), 20)}个):")
            for result in failed_results[:20]:
                folder_name = result['folder_info']['folderName']
                error = result.get('error', 'Unknown error')
                print(f"  - {folder_name}: {error}")

        print("\n" + "="*60)
        print("🎯 优化建议:")
        if success_count/len(folder_infos) < 0.95:
            print("- 成功率较低，建议检查网络连接和服务状态")
        if len(folder_infos)/total_time < 2:
            print("- 速度仍可优化，可考虑增加并发数或优化网络")
        else:
            print("- 性能表现良好！")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        logger.error(f"批量创建过程中发生错误: {str(e)}")

if __name__ == "__main__":
    batch_create_unified_cert_versions_concurrent() 