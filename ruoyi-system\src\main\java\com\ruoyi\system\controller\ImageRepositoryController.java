package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.dto.request.ImageAnnotationUpdateDTO;
import com.ruoyi.system.domain.dto.request.ImageAnnotationRequestDTO;
import com.ruoyi.system.domain.dto.response.ImageRepositoryVO;
import com.ruoyi.system.domain.dto.response.ImageAnnotationVO;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.service.IImageRepositoryService;
import com.ruoyi.system.service.IVersionAnnotationTemplateService;
import com.ruoyi.system.service.IAnnotationPermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 图片仓库管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/images")
@Validated
public class ImageRepositoryController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(ImageRepositoryController.class);
    
    @Autowired
    private IImageRepositoryService imageRepositoryService;

    @Autowired
    private IVersionAnnotationTemplateService templateService;

    @Autowired
    private IAnnotationPermissionService permissionService;
    
    /**
     * 更新图片标注
     * PUT /api/images/{imageId}/annotations
     */
    @PreAuthorize("@ss.hasPermi('cert:image:edit')")
    @Log(title = "图片标注", businessType = BusinessType.UPDATE)
    @PutMapping("/{imageId}/annotations")
    public AjaxResult updateAnnotations(
            @PathVariable String imageId,
            @Valid @RequestBody ImageAnnotationUpdateDTO dto) {
        try {
            log.info("开始更新图片[{}]的标注信息", imageId);
            boolean success = imageRepositoryService.updateImageAnnotations(imageId, dto.getAnnotations());
            if (success) {
                return AjaxResult.success("更新图片标注成功");
            } else {
                return AjaxResult.error("更新图片标注失败");
            }
        } catch (Exception e) {
            log.error("更新图片标注失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新图片标注失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取图片详情
     * GET /api/images/{imageId}
     */
    @PreAuthorize("@ss.hasPermi('cert:image:query')")
    @GetMapping("/{imageId}")
    public AjaxResult getImageDetails(@PathVariable String imageId) {
        try {
            log.info("获取图片详情: {}", imageId);
            ImageRepositoryVO image = imageRepositoryService.getImageById(imageId);
            if (image != null) {
                return AjaxResult.success("获取图片详情成功", image);
            } else {
                return AjaxResult.error("图片不存在");
            }
        } catch (Exception e) {
            log.error("获取图片详情失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取图片详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据文件夹ID查询图片列表
     * GET /api/images/folder/{folderId}
     */
    @PreAuthorize("@ss.hasPermi('cert:image:list')")
    @GetMapping("/folder/{folderId}")
    public TableDataInfo getImagesByFolder(@PathVariable String folderId) {
        try {
            log.info("查询文件夹[{}]的图片列表", folderId);
            startPage();
            List<ImageRepositoryVO> images = imageRepositoryService.getImagesByFolderId(folderId);

            // 记录详细的返回值日志
            TableDataInfo result = getDataTable(images);
            log.info("查询文件夹[{}]的图片列表完成，返回数据: 总记录数={}, 当前页数据量={}",
                    folderId, result.getTotal(), images.size());

            if (!images.isEmpty()) {
                log.info("图片列表详细信息:");
                for (int i = 0; i < Math.min(images.size(), 5); i++) { // 只记录前5条详细信息
                    ImageRepositoryVO image = images.get(i);
                    log.info("  [{}] imageId={}, fileName={}, fileSize={}, lightType={}, isMainImage={}, processStatus={}",
                            i + 1,
                            image.getImageId(),
                            image.getOriginalFileName(),
                            image.getFileSize(),
                            image.getLightType(),
                            image.getIsMainImage(),
                            image.getProcessStatus());
                }
                if (images.size() > 5) {
                    log.info("  ... 还有{}条记录未显示", images.size() - 5);
                }
            } else {
                log.info("文件夹[{}]中没有找到任何图片", folderId);
            }

            return result;
        } catch (Exception e) {
            log.error("查询文件夹图片列表失败: {}", e.getMessage(), e);
            return new TableDataInfo();
        }
    }
    
    /**
     * 根据版本ID查询图片列表
     * GET /api/images/version/{versionId}
     */
    @PreAuthorize("@ss.hasPermi('cert:image:list')")
    @GetMapping("/version/{versionId}")
    public TableDataInfo getImagesByVersion(@PathVariable String versionId) {
        try {
            log.info("查询版本[{}]的图片列表", versionId);
            startPage();
            List<ImageRepositoryVO> images = imageRepositoryService.getImagesByVersionId(versionId);
            return getDataTable(images);
        } catch (Exception e) {
            log.error("查询版本图片列表失败: {}", e.getMessage(), e);
            return new TableDataInfo();
        }
    }
    
    /**
     * 获取图片统计信息
     * GET /api/images/{imageId}/statistics
     */
    @PreAuthorize("@ss.hasPermi('cert:image:query')")
    @GetMapping("/{imageId}/statistics")
    public AjaxResult getImageStatistics(@PathVariable String imageId) {
        try {
            log.info("获取图片[{}]的统计信息", imageId);
            Map<String, Object> statistics = imageRepositoryService.getImageStatistics(imageId);
            return AjaxResult.success("获取图片统计信息成功", statistics);
        } catch (Exception e) {
            log.error("获取图片统计信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取图片统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取文件夹图片统计
     * GET /api/images/folder/{folderId}/statistics
     */
    @PreAuthorize("@ss.hasPermi('cert:image:query')")
    @GetMapping("/folder/{folderId}/statistics")
    public AjaxResult getFolderImageStatistics(@PathVariable String folderId) {
        try {
            log.info("获取文件夹[{}]的图片统计", folderId);
            Map<String, Object> statistics = imageRepositoryService.getFolderImageStatistics(folderId);
            return AjaxResult.success("获取文件夹图片统计成功", statistics);
        } catch (Exception e) {
            log.error("获取文件夹图片统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取文件夹图片统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除图片
     * DELETE /api/images/{imageId}
     */
    @PreAuthorize("@ss.hasPermi('cert:image:remove')")
    @Log(title = "图片管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{imageId}")
    public AjaxResult deleteImage(@PathVariable String imageId) {
        try {
            log.info("删除图片: {}", imageId);
            boolean success = imageRepositoryService.deleteImage(imageId);
            if (success) {
                return AjaxResult.success("删除图片成功");
            } else {
                return AjaxResult.error("删除图片失败");
            }
        } catch (Exception e) {
            log.error("删除图片失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除图片
     * DELETE /api/images/batch
     */
    @PreAuthorize("@ss.hasPermi('cert:image:remove')")
    @Log(title = "图片管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchDeleteImages(@RequestBody List<String> imageIds) {
        try {
            log.info("批量删除图片: {}", imageIds);
            boolean success = imageRepositoryService.batchDeleteImages(imageIds);
            if (success) {
                return AjaxResult.success("批量删除图片成功");
            } else {
                return AjaxResult.error("批量删除图片失败");
            }
        } catch (Exception e) {
            log.error("批量删除图片失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量删除图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新图片状态
     * PUT /api/images/{imageId}/status
     */
    @PreAuthorize("@ss.hasPermi('cert:image:edit')")
    @Log(title = "图片管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{imageId}/status")
    public AjaxResult updateImageStatus(
            @PathVariable String imageId,
            @RequestParam String status) {
        try {
            log.info("更新图片[{}]状态为[{}]", imageId, status);
            boolean success = imageRepositoryService.updateImageStatus(imageId, status);
            if (success) {
                return AjaxResult.success("更新图片状态成功");
            } else {
                return AjaxResult.error("更新图片状态失败");
            }
        } catch (Exception e) {
            log.error("更新图片状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新图片状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新图片信息
     * PUT /api/images/{imageId}/info
     */
    @PreAuthorize("@ss.hasPermi('cert:image:edit')")
    @Log(title = "图片管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{imageId}/info")
    public AjaxResult updateImageInfo(
            @PathVariable String imageId,
            @RequestBody Map<String, Object> updateData) {
        try {
            log.info("更新图片[{}]信息: {}", imageId, updateData);
            boolean success = imageRepositoryService.updateImageInfo(imageId, updateData);
            if (success) {
                return AjaxResult.success("更新图片信息成功");
            } else {
                return AjaxResult.error("更新图片信息失败");
            }
        } catch (Exception e) {
            log.error("更新图片信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新图片信息失败: " + e.getMessage());
        }
    }

    // ==================== 标注相关接口 ====================

    /**
     * 获取图片标注信息
     * GET /api/images/{imageId}/annotations
     * 从版本模板加载标注数据和权限信息
     */
    @PreAuthorize("@ss.hasPermi('cert:image:annotation:query')")
    @GetMapping("/{imageId}/annotations")
    public AjaxResult getImageAnnotations(@PathVariable String imageId) {
        try {
            log.info("获取图片标注信息（从版本模板）: imageId={}", imageId);

            // 调用Service层的新方法，从版本模板加载标注数据
            IImageRepositoryService.ImageAnnotationInfo result = imageRepositoryService.getImageAnnotations(imageId);

            log.info("获取图片标注信息成功: imageId={}, canEdit={}, canView={}, 标注数量={}",
                    imageId, result.getCanEdit(), result.getCanView(),
                    result.getAnnotations() != null ? result.getAnnotations().size() : 0);

            return AjaxResult.success("获取图片标注信息成功", result);

        } catch (Exception e) {
            log.error("获取图片标注信息失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return AjaxResult.error("获取图片标注信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存图片标注
     * POST /api/images/{imageId}/annotations
     * 重定向到版本模板保存逻辑
     */
    @PreAuthorize("@ss.hasPermi('cert:image:annotation:save')")
    @Log(title = "图片标注管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{imageId}/annotations")
    public AjaxResult saveImageAnnotations(@PathVariable String imageId,
                                          @Valid @RequestBody ImageAnnotationRequestDTO requestDTO) {
        try {
            log.info("保存图片标注到版本模板: imageId={}, 标注项数量={}", imageId,
                    requestDTO.getAnnotations() != null ? requestDTO.getAnnotations().size() : 0);

            // 转换DTO为Service层需要的格式
            List<IImageRepositoryService.AnnotationItem> annotations = convertToAnnotationItems(requestDTO.getAnnotations());

            // 重定向到版本模板保存逻辑
            // Service层会进行权限检查和版本模板保存
            boolean success = imageRepositoryService.saveImageAnnotations(imageId, annotations);

            if (success) {
                log.info("保存图片标注到版本模板成功: imageId={}", imageId);
                return AjaxResult.success("保存标注成功");
            } else {
                log.warn("保存图片标注到版本模板失败: imageId={}", imageId);
                return AjaxResult.error("保存标注失败");
            }

        } catch (Exception e) {
            log.error("保存图片标注到版本模板失败: imageId={}, error={}", imageId, e.getMessage(), e);

            // 处理权限相关的错误
            if (e.getMessage() != null && e.getMessage().contains("权限")) {
                return AjaxResult.error(403, e.getMessage());
            } else if (e.getMessage() != null && e.getMessage().contains("数据格式")) {
                return AjaxResult.error(400, e.getMessage());
            } else {
                return AjaxResult.error("保存标注失败: " + e.getMessage());
            }
        }
    }

    /**
     * 检查图片标注权限
     * GET /api/images/{imageId}/annotation-permission
     */
    @PreAuthorize("@ss.hasPermi('cert:image:annotation:query')")
    @GetMapping("/{imageId}/annotation-permission")
    public AjaxResult checkAnnotationPermission(@PathVariable String imageId) {
        try {
            log.info("检查图片标注权限: imageId={}", imageId);

            IVersionAnnotationTemplateService.AnnotationPermissionInfo permissionInfo = templateService.getAnnotationPermission(imageId);

            log.info("检查图片标注权限完成: imageId={}, canEdit={}, canView={}",
                    imageId, permissionInfo.isCanEdit(), permissionInfo.isCanView());
            return AjaxResult.success("获取标注权限成功", permissionInfo);

        } catch (Exception e) {
            log.error("检查图片标注权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return AjaxResult.error("检查标注权限失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新文件夹图片类型
     * POST /api/images/folder/{folderId}/update-types
     */
    @PreAuthorize("@ss.hasPermi('cert:image:edit')")
    @Log(title = "图片管理", businessType = BusinessType.UPDATE)
    @PostMapping("/folder/{folderId}/update-types")
    public AjaxResult updateFolderImageTypes(@PathVariable String folderId) {
        try {
            log.info("批量更新文件夹图片类型: folderId={}", folderId);

            imageRepositoryService.updateImageTypes(folderId);

            log.info("批量更新文件夹图片类型成功: folderId={}", folderId);
            return AjaxResult.success("批量更新图片类型成功");

        } catch (Exception e) {
            log.error("批量更新文件夹图片类型失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return AjaxResult.error("批量更新图片类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件夹可标注图片列表
     * GET /api/images/folder/{folderId}/annotatable
     */
    @PreAuthorize("@ss.hasPermi('cert:image:list')")
    @GetMapping("/folder/{folderId}/annotatable")
    public TableDataInfo getAnnotatableImages(@PathVariable String folderId) {
        try {
            log.info("获取文件夹可标注图片列表: folderId={}", folderId);
            startPage();

            List<ImageRepositoryVO> images = imageRepositoryService.getAnnotatableImages(folderId);

            log.info("获取文件夹可标注图片列表成功: folderId={}, 图片数量={}", folderId, images.size());
            return getDataTable(images);

        } catch (Exception e) {
            log.error("获取文件夹可标注图片列表失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return new TableDataInfo();
        }
    }

    /**
     * 根据类型获取文件夹图片列表
     * GET /api/images/folder/{folderId}/type/{imageType}
     */
    @PreAuthorize("@ss.hasPermi('cert:image:list')")
    @GetMapping("/folder/{folderId}/type/{imageType}")
    public TableDataInfo getImagesByType(@PathVariable String folderId, @PathVariable String imageType) {
        try {
            log.info("根据类型获取文件夹图片列表: folderId={}, imageType={}", folderId, imageType);
            startPage();

            List<ImageRepositoryVO> images = imageRepositoryService.getImagesByType(folderId, imageType);

            log.info("根据类型获取文件夹图片列表成功: folderId={}, imageType={}, 图片数量={}",
                    folderId, imageType, images.size());
            return getDataTable(images);

        } catch (Exception e) {
            log.error("根据类型获取文件夹图片列表失败: folderId={}, imageType={}, error={}",
                    folderId, imageType, e.getMessage(), e);
            return new TableDataInfo();
        }
    }

    /**
     * 转换请求DTO为Service层的标注项格式
     */
    private List<IImageRepositoryService.AnnotationItem> convertToAnnotationItems(
            List<ImageAnnotationRequestDTO.AnnotationItemRequestDTO> requestItems) {

        if (requestItems == null) {
            return new ArrayList<>();
        }

        return requestItems.stream().map(item -> {
            IImageRepositoryService.AnnotationItem serviceItem = new IImageRepositoryService.AnnotationItem();
            serviceItem.setAnnotationId(item.getAnnotationId());
            serviceItem.setAnnotationType(item.getAnnotationType());
            serviceItem.setAnnotationName(item.getAnnotationName());
            serviceItem.setAnnotationValue(item.getAnnotationValue());
            serviceItem.setRequired(item.getRequired());
            serviceItem.setDisplayOrder(item.getDisplayOrder());

            // 转换坐标信息
            if (item.getCoordinate() != null) {
                IImageRepositoryService.AnnotationCoordinate coordinate = new IImageRepositoryService.AnnotationCoordinate();
                coordinate.setX(item.getCoordinate().getX());
                coordinate.setY(item.getCoordinate().getY());
                coordinate.setWidth(item.getCoordinate().getWidth());
                coordinate.setHeight(item.getCoordinate().getHeight());
                serviceItem.setCoordinate(coordinate);
            }

            return serviceItem;
        }).collect(java.util.stream.Collectors.toList());
    }
}
