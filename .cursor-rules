# Cursor AI Assistant 规则配置

## 文件编辑权限
- 允许编辑 .env 文件用于配置修改
- 允许编辑配置文件 (.json, .yaml, .yml, .toml)
- 允许编辑启动脚本和批处理文件

## 环境配置优先级
- 优先使用用户提供的环境变量配置
- 如果用户明确说明已有配置文件，不要假设文件不存在
- 修改配置前先确认当前配置状态

## MongoDB和服务管理
- 不要尝试启动用户已经运行的服务（MongoDB, MinIO等）
- 首先询问或检查服务状态再进行操作
- 提供检查命令而不是直接执行启动命令

## FastAPI项目特殊处理
- 当用户抱怨watchfiles资源消耗时，优先建议关闭reload
- 提供生产模式和开发模式的不同配置选项
- 对于认证错误，先检查连接字符串格式

## Vue项目规则（用户自定义）
- 当修改或重构 Vue 组件时，按区域分开修改（template、script、style）
- 超过500行的组件建议拆分
- 添加变更注释说明
- 保持语义化和规范化 