#!/usr/bin/env python3
"""
批量证件版本创建工具 - 配置文件
将硬编码参数移到配置文件中，便于不同环境使用
"""

import os
from typing import Dict, Any

# ============ 基础配置 ============

# API配置
API_CONFIG = {
    'base_url': 'http://localhost:8000',
    'endpoint': '/api/v1/cert/version/unified',
    'timeout': 15
}

# 文件夹配置 - 支持环境变量或默认值
FOLDER_CONFIG = {
    'root_path': os.getenv('CERT_FOLDER_ROOT', r'C:\cert_configs\personal'),  # 可通过环境变量配置
    'country_subfolder': True,  # 是否按国家代码分子文件夹
    'folder_name_pattern': r'^[A-Z]{2,3}_[A-Z]{1,3}_\d+_\d{4}$'  # 文件夹名称格式
}

# MinIO配置 - 支持环境变量
MINIO_CONFIG = {
    'endpoint': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
    'access_key': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
    'secret_key': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
    'bucket_name': os.getenv('MINIO_BUCKET_NAME', 'xjlfiles'),
    'secure': os.getenv('MINIO_SECURE', 'false').lower() == 'true'
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_concurrent_uploads': int(os.getenv('MAX_CONCURRENT_UPLOADS', '5')),
    'max_concurrent_requests': int(os.getenv('MAX_CONCURRENT_REQUESTS', '3')),
    'retry_attempts': int(os.getenv('RETRY_ATTEMPTS', '3')),
    'retry_delay': float(os.getenv('RETRY_DELAY', '1.0'))
}

# ============ 证件默认配置 ============

# 默认证件规格 - 可以被具体参数覆盖
DEFAULT_CERT_SPECS = {
    'width': 85.6,
    'height': 53.98,
    'thickness': 0.76,
    'material': 'PVC',
    'security_features': ['水印', '防伪线', '全息图'],
    'number_format': 'D{4}',
    'end_no': 'D9999',
    'light_types': ['visible']
}

# 训练图片配置
TRAINING_IMAGE_CONFIG = {
    'file_names': ['PER.JPG', 'per.jpg', 'PER.jpg', 'per.JPG'],  # 按优先级排序
    'upload_path_template': '{folder_name}/training/PER{extension}',
    'light_type': 'visible',
    'description_template': '标准样本训练图片 - {version_code}'
}

# ============ 日志配置 ============

LOGGING_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file_enabled': os.getenv('LOG_TO_FILE', 'false').lower() == 'true',
    'file_path': os.getenv('LOG_FILE_PATH', 'batch_cert_creation.log')
}

# ============ 环境检测 ============

def get_api_url() -> str:
    """获取完整的API URL"""
    return f"{API_CONFIG['base_url']}{API_CONFIG['endpoint']}"

def get_folder_root() -> str:
    """获取文件夹根路径"""
    root_path = FOLDER_CONFIG['root_path']
    if not os.path.exists(root_path):
        # 尝试一些常见路径
        possible_paths = [
            r'C:\cert_configs\personal',
            r'D:\cert_configs\personal',
            r'.\configs\personal',
            r'..\configs\personal'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都不存在，返回当前目录
        return os.getcwd()
    
    return root_path

def validate_config() -> Dict[str, Any]:
    """验证配置有效性"""
    issues = []
    
    # 检查文件夹路径
    folder_root = get_folder_root()
    if not os.path.exists(folder_root):
        issues.append(f"文件夹路径不存在: {folder_root}")
    
    # 检查并发配置
    if PERFORMANCE_CONFIG['max_concurrent_uploads'] <= 0:
        issues.append("并发上传数必须大于0")
    
    if PERFORMANCE_CONFIG['max_concurrent_requests'] <= 0:
        issues.append("并发请求数必须大于0")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'folder_root': folder_root,
        'api_url': get_api_url()
    }

# ============ 配置显示 ============

def print_config_summary():
    """打印配置摘要"""
    print("📋 当前配置:")
    print(f"  🌐 API地址: {get_api_url()}")
    print(f"  📁 文件夹路径: {get_folder_root()}")
    print(f"  🗄️  MinIO服务: {MINIO_CONFIG['endpoint']}")
    print(f"  📦 MinIO存储桶: {MINIO_CONFIG['bucket_name']}")
    print(f"  ⚙️  并发配置: 上传{PERFORMANCE_CONFIG['max_concurrent_uploads']}线程, API{PERFORMANCE_CONFIG['max_concurrent_requests']}线程")

def print_environment_setup():
    """打印环境变量设置说明"""
    print("\n💡 环境变量配置 (可选):")
    print("  set CERT_FOLDER_ROOT=D:\\your\\cert\\path")
    print("  set MINIO_ENDPOINT=your-minio-server:9000")
    print("  set MINIO_ACCESS_KEY=your-access-key")
    print("  set MINIO_SECRET_KEY=your-secret-key")
    print("  set MAX_CONCURRENT_UPLOADS=10")
    print("  set MAX_CONCURRENT_REQUESTS=8")

# ============ 特定环境配置 ============

def get_development_config():
    """开发环境配置"""
    config = {
        'api_url': 'http://localhost:8000/api/v1/cert/version/unified',
        'folder_root': r'.\test_data',
        'max_concurrent_uploads': 2,
        'max_concurrent_requests': 1
    }
    return config

def get_production_config():
    """生产环境配置"""
    config = {
        'api_url': 'http://prod-server:8000/api/v1/cert/version/unified',
        'folder_root': r'\\server\cert_configs\personal',
        'max_concurrent_uploads': 10,
        'max_concurrent_requests': 8
    }
    return config

# ============ 配置加载器 ============

def load_config_from_file(config_file: str = 'batch_config.json'):
    """从JSON文件加载配置"""
    import json
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 更新配置
            if 'api' in file_config:
                API_CONFIG.update(file_config['api'])
            if 'folder' in file_config:
                FOLDER_CONFIG.update(file_config['folder'])
            if 'minio' in file_config:
                MINIO_CONFIG.update(file_config['minio'])
            if 'performance' in file_config:
                PERFORMANCE_CONFIG.update(file_config['performance'])
            
            return True
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
            return False
    
    return False

def create_sample_config_file(config_file: str = 'batch_config.json'):
    """创建示例配置文件"""
    import json
    
    sample_config = {
        "api": {
            "base_url": "http://localhost:8000",
            "endpoint": "/api/v1/cert/version/unified",
            "timeout": 15
        },
        "folder": {
            "root_path": "C:\\cert_configs\\personal",
            "country_subfolder": True
        },
        "minio": {
            "endpoint": "localhost:9000",
            "access_key": "minioadmin",
            "secret_key": "minioadmin",
            "bucket_name": "xjlfiles",
            "secure": False
        },
        "performance": {
            "max_concurrent_uploads": 5,
            "max_concurrent_requests": 3,
            "retry_attempts": 3,
            "retry_delay": 1.0
        }
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        print(f"✅ 已创建示例配置文件: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

if __name__ == "__main__":
    # 配置测试
    print("🔧 配置文件测试")
    print("=" * 50)
    
    validation = validate_config()
    if validation['valid']:
        print("✅ 配置验证通过")
        print_config_summary()
    else:
        print("❌ 配置验证失败:")
        for issue in validation['issues']:
            print(f"  - {issue}")
    
    print_environment_setup()
    
    # 创建示例配置文件
    if not os.path.exists('batch_config.json'):
        create_sample_config_file() 