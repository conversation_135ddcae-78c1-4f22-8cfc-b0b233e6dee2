<template>
    <div class="count-news-container"style="width: 100%;">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title-large">文章发布排行榜</span>
          </div>
        </template>
        <div class="rank-type-tabs">
          <el-tooltip
            v-for="type in ['year', 'quarter', 'month']"
            :key="type"
            :content="getTabTitle(type)"
            placement="top"
          >
            <div
              class="rank-type-tab"
              :class="{ active: activeType === type }"
              @mouseenter="activeType = type"
            >
              {{ getTabTitle(type) }}
            </div>
          </el-tooltip>
        </div>
        <div v-if="activeType" class="rank-list">
          <div
            v-for="(item, index) in getSortedList(activeType)"
            :key="item.deptId"
            class="rank-item"
            :class="{ 'top-1': index === 0, 'top-2': index === 1, 'top-3': index === 2 }"
          >
            <span class="rank-number">{{ index + 1 }}</span>
            <span class="dept-name">{{ item.deptName }}</span>
            <span class="article-count">{{ item.count }} 篇文章</span>
          </div>
        </div>
      </el-card>
    </div>
  </template>

  <script setup>
  import { ref, onMounted } from 'vue';
  import { countNewsByTimeAndType } from '@/api/news/news.js';


  const activeType = ref('year');
  const newsStatistics = ref({
    year: [],
    quarter: [],
    month: []
  });

  const getTabTitle = (type) => {
    switch (type) {
      case 'year':
        return '年度排行榜';
      case 'quarter':
        return '季度排行榜';
      case 'month':
        return '月度排行榜';
      default:
        return '';
    }
  };

  const fetchData = async () => {
    try {
        const [yearResponse, quarterResponse, monthResponse] = await Promise.all([
            countNewsByTimeAndType('year'),
            countNewsByTimeAndType('quarter'),
            countNewsByTimeAndType('month')
        ]);

        newsStatistics.value.year = yearResponse.data;
        newsStatistics.value.quarter = quarterResponse.data;
        newsStatistics.value.month = monthResponse.data;
    } catch (error) {
        console.error('获取数据失败:', error);
    }
  };
  const getSortedList = (type) => {
    let filteredData = newsStatistics.value[type];

        // 如果是按年统计，需要获取当前年份
        if (type === 'year') {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            filteredData = filteredData.filter(item => item.time === currentYear);
        }
        // 如果是按月统计，需要获取当前月份
        else if (type === 'month') {
            const currentDate = new Date();
            const currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
            filteredData = filteredData.filter(item => item.time === currentMonth);
        }
        // 如果是按季度统计，需要获取当前季度
        else if (type === 'quarter') {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentQuarter = Math.floor((currentDate.getMonth() + 3) / 3);
            const targetQuarter = `${currentYear}-Q${currentQuarter}`;
            filteredData = filteredData.filter(item => item.time === targetQuarter);
        }

        const groupedData = {};
        filteredData.forEach(item => {
            if (!groupedData[item.deptId]) {
                groupedData[item.deptId] = {
                    deptId: item.deptId,
                    deptName: item.deptName,
                    count: 0
                };
            }
            groupedData[item.deptId].count += item.count;
        });

    // 将分组后的数据转换为数组
      const result = Object.values(groupedData)
      return result.sort((a, b) => b.count - a.count);
  };

  onMounted(() => {
    fetchData();
  });
  </script>

  <style scoped lang="scss">
  .count-news-container {
    .box-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      height: 530px;

      &:hover {
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      }
    }

    .rank-type-tabs {
      font-size: 16px;
      display: flex;
      margin-bottom: 20px;
      border-radius: 4px;
      overflow: hidden;
    }

    .rank-type-tab {
      padding: 10px 16px;
      cursor: pointer;
      border: 1px solid #dcdfe6;
      margin-right: -1px;
      transition: all 0.3s ease;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &.active {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
      }

      &:hover:not(.active) {
        background-color: #f5f7fa;
      }
    }

    .rank-list {
      .rank-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px dashed #eaeaea;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f9f9f9;
          transform: translateX(5px);
        }

        .rank-number {
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          margin-right: 15px;
          background-color: #f0f0f0;
          border-radius: 50%;
          font-weight: bold;
        }

        .dept-name {
          flex: 1;
          font-size: 16px;
        }

        .article-count {
          width: 100px;
          text-align: right;
          font-size: 16px;
          color: #666;
        }
      }

      .top-1 {
        .rank-number {
          background-color: #f7ba2a;
          color: white;
        }
        color: #333;
        font-weight: bold;
      }

      .top-2 {
        .rank-number {
          background-color: #909399;
          color: white;
        }
        color: #333;
        font-weight: bold;
      }

      .top-3 {
        .rank-number {
          background-color: #d1a884;
          color: white;
        }
        color: #333;
        font-weight: bold;
      }
    }

    .title-large {
      font-size: 22px;
      font-weight: bold;
      color: #333;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background-color: #409EFF;
        border-radius: 2px;
      }
    }
  }
  </style>
