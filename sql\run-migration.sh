#!/bin/bash

echo "========================================"
echo "MongoDB 迁移脚本执行工具"
echo "========================================"

# 设置变量
MONGO_URI="******************************************************"
AUTH_SOURCE="admin"
SCRIPT_FILE="sql/mongodb-migration-2025-01-15.js"

echo
echo "1. 检查MongoDB连接..."
mongo "$MONGO_URI" --authSource "$AUTH_SOURCE" --eval "print('MongoDB连接成功: ' + db.getName())"

if [ $? -ne 0 ]; then
    echo "错误: 无法连接到MongoDB，请检查服务是否运行"
    exit 1
fi

echo
echo "2. 创建备份目录..."
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo
echo "3. 备份数据库..."
echo "正在备份到: $BACKUP_DIR"
mongodump --uri="$MONGO_URI?authSource=$AUTH_SOURCE" --out="$BACKUP_DIR"

if [ $? -ne 0 ]; then
    echo "警告: 备份失败，是否继续执行迁移？"
    read -p "输入 y 继续，其他键退出: " continue
    if [ "$continue" != "y" ] && [ "$continue" != "Y" ]; then
        echo "迁移已取消"
        exit 1
    fi
else
    echo "备份完成: $BACKUP_DIR"
fi

echo
echo "4. 执行迁移脚本..."
echo "正在执行: $SCRIPT_FILE"
mongo "$MONGO_URI" --authSource "$AUTH_SOURCE" "$SCRIPT_FILE"

if [ $? -ne 0 ]; then
    echo "错误: 迁移脚本执行失败"
    echo "请检查脚本文件是否存在: $SCRIPT_FILE"
    exit 1
fi

echo
echo "5. 验证迁移结果..."
mongo "$MONGO_URI" --authSource "$AUTH_SOURCE" --eval "
print('=== 迁移结果验证 ===');
print('版本标注模板集合存在: ' + (db.getCollectionNames().indexOf('version_annotation_template') !== -1));
print('文件夹标准样本字段数量: ' + db.folder_info.countDocuments({'isStandardSample': {\$exists: true}}));
print('图片可标注类型字段数量: ' + db.image_repository.countDocuments({'isAnnotatableType': {\$exists: true}}));
print('可标注图片数量: ' + db.image_repository.countDocuments({'isAnnotatableType': true}));
print('=== 验证完成 ===');
"

echo
echo "========================================"
echo "迁移脚本执行完成！"
echo "备份位置: $BACKUP_DIR"
echo "========================================"
