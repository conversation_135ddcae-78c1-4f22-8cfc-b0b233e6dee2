<template>
  <div class="annotation-canvas" :data-tool="tool">
    <!-- 图片容器 -->
    <div class="image-container" ref="imageContainer">
      <img
        ref="imageElement"
        :src="imageUrl"
        alt="标注图片"
        @load="handleImageLoad"
        @error="handleImageError"
        class="annotation-image"
      />
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-icon class="is-loading" size="40">
          <Loading />
        </el-icon>
        <p>正在加载图片...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <el-icon size="40">
          <Picture />
        </el-icon>
        <p>图片加载失败</p>
      </div>
    </div>
    
    <!-- 工具提示 -->
    <div v-if="tool && tool !== 'select'" class="tool-hint">
      <el-alert
        :title="getToolHint()"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
    

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// UUID生成函数
const generateUUID = () => {
  // 使用crypto.randomUUID()如果可用，否则使用fallback方法
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }

  // Fallback UUID生成方法
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}
import { Loading, Picture } from '@element-plus/icons-vue'

// 使用正确的 Annotorious 3.0 导入方式
let createImageAnnotator = null
let annotoriousInstance = null

// Props
const props = defineProps({
  imageUrl: {
    type: String,
    required: true
  },
  tool: {
    type: String,
    default: 'select'
  },
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'select-annotation'])

// 响应式数据
const imageContainer = ref(null)
const imageElement = ref(null)
const loading = ref(true)
const error = ref(false)
const annotoriousLoaded = ref(false)

// 添加状态管理，防止循环更新
const isInternalUpdate = ref(false)
const isEditingMode = ref(false)  // 标记是否正在编辑
const currentEditingId = ref(null)  // 当前编辑的标注ID
const updateInProgress = ref(false)  // 防止并发更新
const lastUpdateTime = ref(0)  // 记录最后更新时间



// 生命周期
onMounted(async () => {
  await loadAnnotorious()
})

onUnmounted(() => {
  destroyAnnotorious()
})

// 监听工具变化
watch(() => props.tool, (newTool) => {
  if (annotoriousInstance && annotoriousLoaded.value) {
    setAnnotoriousTool(newTool)
  }
})

// 监听标注数据变化
watch(() => props.modelValue, (newAnnotations, oldAnnotations) => {
  
  const now = Date.now()
  const isInitialLoad = lastUpdateTime.value === 0
  
  // 防止过于频繁的更新
  if (!isInitialLoad && now - lastUpdateTime.value < 500) {
    return
  }

  // 防止内部更新循环
  if (isInternalUpdate.value) {
    return
  }

  // 防止编辑模式下的更新
  if (isEditingMode.value) {
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newAnnotations) === JSON.stringify(oldAnnotations)) {
    return
  }

  if (annotoriousInstance) {
    lastUpdateTime.value = now
    updateAnnotations(newAnnotations)
  }
}, { deep: true })

// 监听图片URL变化
watch(() => props.imageUrl, (newUrl, oldUrl) => {
  if (newUrl !== oldUrl && newUrl) {
    if (annotoriousInstance) {
      destroyAnnotorious()
    }
    loading.value = true
    error.value = false
  }
})

// 监听 Annotorious 加载状态
watch(() => annotoriousLoaded.value, (loaded) => {
  if (loaded && imageElement.value && !loading.value && !annotoriousInstance) {
    nextTick(() => {
      initializeAnnotorious()
    })
  }
})

// 方法
const loadAnnotorious = async () => {
  try {
    const module = await import('@annotorious/annotorious')

    createImageAnnotator = module.createImageAnnotator

    // 导入 CSS
    await import('@annotorious/annotorious/annotorious.css')

    annotoriousLoaded.value = true
  } catch (err) {
    ElMessage.error('标注工具加载失败: ' + err.message)
  }
}

const initializeAnnotorious = () => {
  if (!createImageAnnotator || !imageElement.value) {
    return
  }

  try {
    // 使用 Annotorious 3.0 的正确初始化方式
    annotoriousInstance = createImageAnnotator(imageElement.value)

    // 添加事件监听器
    annotoriousInstance.on('createAnnotation', handleCreateAnnotation)
    annotoriousInstance.on('updateAnnotation', handleUpdateAnnotation)
    annotoriousInstance.on('deleteAnnotation', handleDeleteAnnotation)
    annotoriousInstance.on('selectAnnotation', handleSelectAnnotation)

    // 设置初始工具
    nextTick(() => {
      setAnnotoriousTool(props.tool)

      // 重置防抖状态，允许初始加载
      lastUpdateTime.value = 0
      updateInProgress.value = false
      isInternalUpdate.value = false

      // 设置初始标注
      if (props.modelValue.length > 0) {
        // 延迟一点时间确保图片完全加载
        setTimeout(() => {
          loadAnnotations()
        }, 100)
      }
    })

  } catch (err) {
    ElMessage.error('标注工具初始化失败: ' + err.message)
  }
}

// 简化的数据转换函数
const convertAnnotationToBackendFormat = (annotation) => {
  try {

    // 处理bodies数组
    let bodies = []
    if (annotation.bodies && annotation.bodies.length > 0) {
      bodies = annotation.bodies.map(body => ({
        id: body.id,
        purpose: body.purpose || 'commenting',
        value: body.value || '',
        creator: body.creator,
        created: body.created
      }))
    } else {
      // 如果没有bodies，创建一个默认的
      bodies = [{
        id: generateUUID(),
        purpose: 'commenting',
        value: '',
        creator: null,
        created: new Date()
      }]
    }

    // 处理target和selector
    let target = {
      annotation: annotation.id,
      creator: annotation.target?.creator,
      created: annotation.target?.created,
      updatedBy: annotation.target?.updatedBy,
      updated: annotation.target?.updated
    }

    // 转换Shape格式回后端格式
    if (annotation.target && annotation.target.selector) {
      const selector = annotation.target.selector
      if (selector.type === 'RECTANGLE' && selector.geometry) {
        const { x, y, w, h } = selector.geometry
        // 转换为百分比坐标（如果图片已加载）
        const imageEl = imageElement.value
        if (imageEl && imageEl.naturalWidth > 0) {
          const percentX = (x / imageEl.naturalWidth) * 100
          const percentY = (y / imageEl.naturalHeight) * 100
          const percentW = (w / imageEl.naturalWidth) * 100
          const percentH = (h / imageEl.naturalHeight) * 100

          target.selector = {
            type: 'RECTANGLE',
            percentCoords: {
              x: Math.round(percentX * 100) / 100,
              y: Math.round(percentY * 100) / 100,
              w: Math.round(percentW * 100) / 100,
              h: Math.round(percentH * 100) / 100
            },
            value: `xywh=percent:${Math.round(percentX)},${Math.round(percentY)},${Math.round(percentW)},${Math.round(percentH)}`
          }
        } else {
          // 如果图片未加载，保持像素坐标
          target.selector = {
            type: 'RECTANGLE',
            geometry: { x, y, w, h },
            value: `xywh=pixel:${x},${y},${w},${h}`
          }
        }
      } else {
        target.selector = selector
      }
    }

    const converted = {
      id: annotation.id,
      type: annotation.type || 'Annotation',
      bodies: bodies,
      target: target
    }

    return converted
  } catch (error) {
    return annotation
  }
}

const convertAnnotationFromBackendFormat = (annotation) => {
  try {
    let selector = null;

    // 从后端格式转换
    if (annotation.target && annotation.target.selector) {
      const backendSelector = annotation.target.selector

      // 检查是否已经有转换好的百分比坐标
      if (backendSelector.type === 'RECTANGLE' && backendSelector.percentCoords) {
        const { x, y, w, h } = backendSelector.percentCoords

        const imageEl = imageElement.value
        if (imageEl && imageEl.naturalWidth > 0) {
          const natural = { w: imageEl.naturalWidth, h: imageEl.naturalHeight }

          const pixelX = x / 100 * natural.w
          const pixelY = y / 100 * natural.h
          const pixelW = w / 100 * natural.w
          const pixelH = h / 100 * natural.h

          // 使用Annotorious期望的Shape格式
          selector = {
            type: 'RECTANGLE',
            geometry: {
              x: Math.round(pixelX),
              y: Math.round(pixelY),
              w: Math.round(pixelW),
              h: Math.round(pixelH),
              bounds: {
                minX: Math.round(pixelX),
                minY: Math.round(pixelY),
                maxX: Math.round(pixelX + pixelW),
                maxY: Math.round(pixelY + pixelH)
              }
            }
          }
        } else {
          // 使用百分比坐标创建临时的像素坐标（假设图片尺寸）
          const tempW = 1000, tempH = 1000
          selector = {
            type: 'RECTANGLE',
            geometry: {
              x: Math.round(x / 100 * tempW),
              y: Math.round(y / 100 * tempH),
              w: Math.round(w / 100 * tempW),
              h: Math.round(h / 100 * tempH),
              bounds: {
                minX: Math.round(x / 100 * tempW),
                minY: Math.round(y / 100 * tempH),
                maxX: Math.round((x + w) / 100 * tempW),
                maxY: Math.round((y + h) / 100 * tempH)
              }
            }
          }
        }
      } else if (backendSelector.type === 'RECTANGLE' && backendSelector.geometry) {
        const { x, y, w, h } = backendSelector.geometry;
        // 直接使用几何坐标创建Shape
        selector = {
          type: 'RECTANGLE',
          geometry: {
            x: Math.round(x),
            y: Math.round(y),
            w: Math.round(w),
            h: Math.round(h),
            bounds: {
              minX: Math.round(x),
              minY: Math.round(y),
              maxX: Math.round(x + w),
              maxY: Math.round(y + h)
            }
          }
        };
      } else if (typeof backendSelector === 'string' && backendSelector.startsWith('xywh=')) {
        // 解析xywh字符串并转换为Shape格式
        const match = backendSelector.match(/xywh=(pixel|percent):(\d+),(\d+),(\d+),(\d+)/)
        if (match) {
          const [, unit, x, y, w, h] = match
          const coords = { x: parseInt(x), y: parseInt(y), w: parseInt(w), h: parseInt(h) }

          if (unit === 'percent') {
            // 转换百分比为像素
            const imageEl = imageElement.value
            if (imageEl && imageEl.naturalWidth > 0) {
              coords.x = Math.round(coords.x / 100 * imageEl.naturalWidth)
              coords.y = Math.round(coords.y / 100 * imageEl.naturalHeight)
              coords.w = Math.round(coords.w / 100 * imageEl.naturalWidth)
              coords.h = Math.round(coords.h / 100 * imageEl.naturalHeight)
            }
          }

          selector = {
            type: 'RECTANGLE',
            geometry: {
              x: coords.x,
              y: coords.y,
              w: coords.w,
              h: coords.h,
              bounds: {
                minX: coords.x,
                minY: coords.y,
                maxX: coords.x + coords.w,
                maxY: coords.y + coords.h
              }
            }
          }
        }
      }
    }
    
    // 确保 bodies 是一个包含对象的数组
    const bodies = (annotation.bodies || []).map(b => ({
      id: b.id || generateUUID(),
      annotation: annotation.id, // 关键修复：添加对顶层ID的引用
      purpose: b.purpose || 'commenting',
      value: b.value,
      creator: b.creator,
      created: b.created
    }))

    // 最终返回给 Annotorious 的格式
    const annotoriousFormat = {
      id: annotation.id,
      bodies: bodies,
      target: {
        annotation: annotation.id,
        selector: selector,
        creator: annotation.target?.creator,
        created: annotation.target?.created,
        updatedBy: annotation.target?.updatedBy,
        updated: annotation.target?.updated
      }
    }
    
    return annotoriousFormat
  } catch (err) {
    return annotation
  }
}

// 事件处理函数
const handleCreateAnnotation = (annotation) => {
  // 防止循环更新
  if (isInternalUpdate.value || updateInProgress.value) {
    return
  }
  
  try {
    isInternalUpdate.value = true
    const backendFormat = convertAnnotationToBackendFormat(annotation)
    const newAnnotations = [...props.modelValue, backendFormat]
    emit('update:modelValue', newAnnotations)
  } catch (err) {
    // 处理错误
  } finally {
    // 延迟重置状态，防止快速连续更新
    setTimeout(() => {
      isInternalUpdate.value = false
    }, 300)
  }
}

const handleUpdateAnnotation = (annotation) => {
  // 防止循环更新
  if (isInternalUpdate.value || updateInProgress.value) {
    return
  }
  
  try {
    isInternalUpdate.value = true
    isEditingMode.value = true
    currentEditingId.value = annotation.id
    
    const backendFormat = convertAnnotationToBackendFormat(annotation)
    const newAnnotations = props.modelValue.map(item => 
      item.id === annotation.id ? backendFormat : item
    )
    emit('update:modelValue', newAnnotations)
  } catch (err) {
    // 处理错误
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      isInternalUpdate.value = false
      isEditingMode.value = false
      currentEditingId.value = null
    }, 300)
  }
}

const handleDeleteAnnotation = (annotation) => {
  // 防止循环更新
  if (isInternalUpdate.value || updateInProgress.value) {
    return
  }
  
  try {
    isInternalUpdate.value = true
    const newAnnotations = props.modelValue.filter(item => item.id !== annotation.id)
    emit('update:modelValue', newAnnotations)
  } catch (err) {
    // 处理错误
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      isInternalUpdate.value = false
      isEditingMode.value = false
      currentEditingId.value = null
    }, 300)
  }
}

const handleSelectAnnotation = (annotation) => {
  isEditingMode.value = true
  currentEditingId.value = annotation.id
  emit('select-annotation', annotation)
}

// 加载标注数据
const loadAnnotations = () => {
  if (!annotoriousInstance) {
    return
  }

  if (!props.modelValue || props.modelValue.length === 0) {
    return
  }

  // 确保图片完全加载后再加载标注
  const imageEl = imageElement.value
  if (imageEl && imageEl.complete && imageEl.naturalWidth > 0) {
    updateAnnotations(props.modelValue)
  } else {
    // 等待图片加载完成
    const checkImageLoaded = () => {
      if (imageEl && imageEl.complete && imageEl.naturalWidth > 0) {
        updateAnnotations(props.modelValue)
      } else {
        setTimeout(checkImageLoaded, 100)
      }
    }
    checkImageLoaded()
  }
}

const setAnnotoriousTool = (tool) => {
  if (!annotoriousInstance) {
    return
  }

  try {

    switch (tool) {
      case 'select':
        // Annotorious 3.0 中，禁用绘制模式即为选择模式
        try {
          // 先尝试禁用绘制
          if (typeof annotoriousInstance.setDrawingEnabled === 'function') {
            annotoriousInstance.setDrawingEnabled(false)
          }
          // 清除当前工具
          if (typeof annotoriousInstance.setDrawingTool === 'function') {
            annotoriousInstance.setDrawingTool(null)
          }
        } catch (e) {
          // 选择模式设置失败
        }
        break
        
      case 'rectangle':
        try {
          // 首先启用绘制模式
          if (typeof annotoriousInstance.setDrawingEnabled === 'function') {
            annotoriousInstance.setDrawingEnabled(true)
          }
          
          // 然后设置绘制工具
          if (typeof annotoriousInstance.setDrawingTool === 'function') {
            // 尝试不同的工具名称
            const rectToolNames = ['rectangle', 'rect', 'RECTANGLE']
            let toolSet = false
            
            for (const toolName of rectToolNames) {
              try {
                annotoriousInstance.setDrawingTool(toolName)
                toolSet = true
                break
              } catch (e) {
                // 尝试下一个工具名称
              }
            }
            
            if (!toolSet) {
              // 如果设置工具失败，至少确保绘制已启用
              annotoriousInstance.setDrawingEnabled(true)
            }
          }
        } catch (e) {
          // 矩形工具设置失败
        }
        break
        
      case 'polygon':
        try {
          // 首先启用绘制模式
          if (typeof annotoriousInstance.setDrawingEnabled === 'function') {
            annotoriousInstance.setDrawingEnabled(true)
          }
          
          // 然后设置绘制工具
          if (typeof annotoriousInstance.setDrawingTool === 'function') {
            annotoriousInstance.setDrawingTool('polygon')
          }
        } catch (e) {
          // 多边形工具设置失败
        }
        break
        
      default:
        // 未知工具
    }

    // 手动设置光标样式
    setTimeout(() => {
      if (imageElement.value) {
        if (tool === 'select') {
          imageElement.value.style.cursor = 'default'
        } else {
          imageElement.value.style.cursor = 'crosshair'
        }
      }
    }, 100)

  } catch (err) {
    ElMessage.error(`设置${tool}工具失败`)
  }
}

const updateAnnotations = (annotations) => {
  if (!annotoriousInstance || isEditingMode.value) {
    return
  }

  try {
    // 防止重复更新
    if (updateInProgress.value) {
      return
    }
    
    updateInProgress.value = true
    
    annotoriousInstance.clearAnnotations();

    annotations.forEach(annotation => {
      const formatted = convertAnnotationFromBackendFormat(annotation);
      annotoriousInstance.addAnnotation(formatted);
    });


    // 验证标注是否成功添加
    // 确保SVG元素可见
    setTimeout(() => {
      try {
        const imageEl = imageElement.value
        if (imageEl) {
          const svgElements = imageEl.parentElement?.querySelectorAll('svg') || []

          // 修复不可见的SVG元素
          svgElements.forEach((svg) => {
            if (svg.style.visibility === 'hidden' || svg.style.display === 'none' || svg.style.opacity === '0') {
              svg.style.visibility = 'visible'
              svg.style.display = 'block'
              svg.style.opacity = '1'
            }
          })
        }
      } catch (err) {
        // 忽略错误
      }
    }, 300)
  } catch (err) {
    // 更新标注失败
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      updateInProgress.value = false
    }, 200)
  }
}

const destroyAnnotorious = () => {
  if (annotoriousInstance) {
    try {
      // 重置状态
      isEditingMode.value = false
      currentEditingId.value = null
      
      annotoriousInstance.destroy()
      annotoriousInstance = null
    } catch (err) {
      // 销毁失败
    }
  }
}

const handleImageLoad = () => {
  loading.value = false
  error.value = false

  nextTick(() => {
    if (annotoriousLoaded.value) {
      initializeAnnotorious()
    } else {
      // 添加重试机制
      setTimeout(() => {
        if (annotoriousLoaded.value && !annotoriousInstance) {
          initializeAnnotorious()
        }
      }, 1000)
    }
  })
}

const handleImageError = () => {
  loading.value = false
  error.value = true
  ElMessage.error('图片加载失败')
}





const getToolHint = () => {
  const hints = {
    rectangle: '点击并拖拽绘制矩形标注',
    polygon: '点击创建多边形顶点，双击完成绘制'
  }
  return hints[props.tool] || ''
}



// 暴露方法给父组件
defineExpose({
  // 暴露图片元素引用，用于获取图片尺寸
  imageElement,
  selectAnnotation: (annotationId) => {
    if (annotoriousInstance && annotationId) {
      try {
        annotoriousInstance.setSelected(annotationId, true)
        isEditingMode.value = true
        currentEditingId.value = annotationId
      } catch (err) {
        // 选择标注失败
      }
    }
  },
  clearSelection: () => {
    if (annotoriousInstance) {
      try {
        annotoriousInstance.cancelSelected()
        isEditingMode.value = false
        currentEditingId.value = null
      } catch (err) {
        // 清除选择失败
      }
    }
  },
  fitBounds: () => {
    if (annotoriousInstance) {
      try {
        annotoriousInstance.fitBounds()
      } catch (err) {
        // 适应边界失败
      }
    }
  },
  getAnnotations: () => {
    if (annotoriousInstance) {
      try {
        return annotoriousInstance.getAnnotations()
      } catch (err) {
        return []
      }
    }
    return []
  },
  isReady: () => {
    return !!annotoriousInstance
  }
})
</script>

<style scoped>
.annotation-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  min-height: 400px;
}

.annotation-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
  cursor: crosshair; /* 默认显示十字光标 */
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-overlay p,
.error-overlay p {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.tool-hint {
  margin-top: 10px;
}

/* Annotorious 样式覆盖 */
:deep(.a9s-annotationlayer) {
  /* 确保标注层正确显示 */
  pointer-events: auto !important;
}

:deep(.a9s-annotation) {
  /* 标注样式 */
  cursor: pointer;
}

/* 强制设置正确的光标样式 */
:deep(.annotation-image) {
  cursor: crosshair !important;
}

/* 当选择工具时，使用默认光标 */
.annotation-canvas[data-tool="select"] :deep(.annotation-image) {
  cursor: default !important;
}

/* 当绘制工具激活时，使用十字光标 */
.annotation-canvas[data-tool="rectangle"] :deep(.annotation-image),
.annotation-canvas[data-tool="polygon"] :deep(.annotation-image) {
  cursor: crosshair !important;
  pointer-events: auto;
}

:deep(.a9s-annotation) {
  /* 标注样式 */
  stroke: #409eff;
  stroke-width: 2;
  fill: rgba(64, 158, 255, 0.1);
}

:deep(.a9s-annotation.selected) {
  /* 选中状态样式 */
  stroke: #f56c6c;
  stroke-width: 3;
  fill: rgba(245, 108, 108, 0.2);
}

:deep(.a9s-annotation:hover) {
  /* 悬停状态样式 */
  stroke: #67c23a;
  stroke-width: 2.5;
  fill: rgba(103, 194, 58, 0.15);
}

/* 标注编辑器样式 - 增强稳定性 */
:deep(.a9s-editor) {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;  /* 提高层级 */
  position: absolute;  /* 确保正确定位 */
}

:deep(.a9s-editor .a9s-editor-inner) {
  padding: 10px;
}

:deep(.a9s-editor textarea) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 60px;
  width: 100%;  /* 确保宽度 */
  box-sizing: border-box;  /* 盒模型 */
}

:deep(.a9s-editor textarea:focus) {
  border-color: #409eff;
  outline: none;
}

/* 防止编辑器意外消失 */
:deep(.a9s-editor:focus-within) {
  display: block !important;
  visibility: visible !important;
}

/* 确保编辑器在交互时保持可见 */
:deep(.a9s-annotation.editable .a9s-editor) {
  display: block !important;
  opacity: 1 !important;
}

/* 工具栏样式 */
:deep(.a9s-toolbar) {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.a9s-toolbar button) {
  background: transparent;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 2px;
  transition: background-color 0.3s;
}

:deep(.a9s-toolbar button:hover) {
  background-color: #f5f7fa;
}

:deep(.a9s-toolbar button.selected) {
  background-color: #409eff;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-container {
    min-height: 300px;
  }

  :deep(.a9s-editor) {
    max-width: 90vw;
  }
}
</style>
