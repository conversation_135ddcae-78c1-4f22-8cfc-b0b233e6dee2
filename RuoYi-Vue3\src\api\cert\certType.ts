import request from '@/utils/request'
import type { CertType } from '@/types/common'

// ==================== TypeScript 接口定义 ====================

/** 证件类型查询请求DTO */
export interface CertTypeQueryDTO {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 国家代码 */
  countryCode?: string
  /** 证件类型代码 */
  certType?: string
  /** 证件类型名称 */
  name?: string
  /** 状态 */
  status?: string
}

/** 证件类型创建/更新请求DTO */
export interface CertTypeCreateUpdateDTO {
  /** 国家代码 */
  countryCode: string
  /** 证件类型代码 */
  certType: string
  /** 证件类型名称 */
  name: string
  /** 证件类型描述 */
  description?: string
  /** 状态 */
  status?: string
  /** 排序 */
  orderNum?: number
}

// ==================== API 函数 ====================

/**
 * 查询证件类型列表（分页）
 * GET /cert/type/list
 */
export function getCertTypeList(params?: CertTypeQueryDTO) {
  return request({
    url: '/cert/type/list',
    method: 'get',
    params
  })
}

/**
 * 查询证件类型列表（不分页）
 * GET /cert/type/listAll
 */
export function getAllCertTypes(params?: CertTypeQueryDTO) {
  return request({
    url: '/cert/type/listAll',
    method: 'get',
    params
  })
}

/**
 * 获取证件类型详细信息
 * GET /cert/type/{id}
 */
export function getCertTypeDetails(id: number) {
  return request({
    url: `/cert/type/${id}`,
    method: 'get'
  })
}

/**
 * 根据国家代码和证件类型代码查询
 * GET /cert/type/code/{countryCode}/{certType}
 */
export function getCertTypeByCode(countryCode: string, certType: string) {
  return request({
    url: `/cert/type/code/${countryCode}/${certType}`,
    method: 'get'
  })
}

/**
 * 新增证件类型
 * POST /cert/type
 */
export function createCertType(data: CertTypeCreateUpdateDTO) {
  return request({
    url: '/cert/type',
    method: 'post',
    data
  })
}

/**
 * 修改证件类型
 * PUT /cert/type
 */
export function updateCertType(data: CertTypeCreateUpdateDTO & { id: number }) {
  return request({
    url: '/cert/type',
    method: 'put',
    data
  })
}

/**
 * 删除证件类型
 * DELETE /cert/type/{ids}
 */
export function deleteCertTypes(ids: number[]) {
  return request({
    url: `/cert/type/${ids.join(',')}`,
    method: 'delete'
  })
}

/**
 * 导出证件类型列表
 * POST /cert/type/export
 */
export function exportCertTypes(params?: CertTypeQueryDTO) {
  return request({
    url: '/cert/type/export',
    method: 'post',
    params,
    responseType: 'blob'
  })
} 