package com.ruoyi.system.service;

import com.ruoyi.common.config.FastApiConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.Map;

/**
 * FastAPI客户端服务
 * greenfox - 新增FastAPI服务调用封装 - 2025-01-15
 */
@Service
public class FastApiClientService {

    private static final Logger logger = LoggerFactory.getLogger(FastApiClientService.class);

    @Autowired
    private FastApiConfig fastApiConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询文件夹信息列表
     */
    @Retryable(value = {ResourceAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Map<String, Object> getFolderInfoList(Map<String, Object> params) {
        try {
            // 构建请求URL
            String url = fastApiConfig.getBase().getUrl() + fastApiConfig.getEndpoints().getFolderInfo();
            
            // 构建请求参数
            Map<String, Object> requestParams = buildFolderQueryParams(params);
            
            // 发送请求
            HttpEntity<Map<String, Object>> requestEntity = createRequestEntity(requestParams);
            
            if (fastApiConfig.getLogging().isEnabled()) {
                logger.info("调用FastAPI查询文件夹信息: URL={}, 参数={}", url, requestParams);
            }
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseData = response.getBody();
                
                if (fastApiConfig.getLogging().isEnabled()) {
                    logger.info("FastAPI调用成功: {}", responseData.get("msg"));
                }
                
                return responseData;
            } else {
                logger.error("FastAPI接口调用失败，状态码：{}", response.getStatusCode());
                return createErrorResponse("FastAPI调用失败");
            }
            
        } catch (HttpClientErrorException e) {
            logger.error("FastAPI客户端错误: {}", e.getMessage());
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                return createErrorResponse("API Key认证失败");
            }
            return createErrorResponse("客户端请求错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("调用FastAPI服务失败", e);
            return createErrorResponse("服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件夹详情
     */
    @Retryable(value = {ResourceAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Map<String, Object> getFolderDetail(String folderId) {
        try {
            String url = fastApiConfig.getBase().getUrl() + 
                        fastApiConfig.getEndpoints().getFolderDetail().replace("{id}", folderId);
            
            HttpEntity<?> requestEntity = createRequestEntity(null);
            
            if (fastApiConfig.getLogging().isEnabled()) {
                logger.info("调用FastAPI获取文件夹详情: URL={}, folderId={}", url, folderId);
            }
            
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            } else {
                logger.error("获取文件夹详情失败，状态码：{}", response.getStatusCode());
                return createErrorResponse("获取文件夹详情失败");
            }
            
        } catch (Exception e) {
            logger.error("获取文件夹详情失败", e);
            return createErrorResponse("获取文件夹详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件夹统计信息
     */
    @Retryable(value = {ResourceAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Map<String, Object> getFolderStats() {
        try {
            String url = fastApiConfig.getBase().getUrl() + fastApiConfig.getEndpoints().getFolderStats();
            
            HttpEntity<?> requestEntity = createRequestEntity(null);
            
            if (fastApiConfig.getLogging().isEnabled()) {
                logger.info("调用FastAPI获取文件夹统计: URL={}", url);
            }
            
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            } else {
                logger.error("获取文件夹统计失败，状态码：{}", response.getStatusCode());
                return createErrorResponse("获取文件夹统计失败");
            }
            
        } catch (Exception e) {
            logger.error("获取文件夹统计失败", e);
            return createErrorResponse("获取文件夹统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件夹标注状态
     */
    @Retryable(value = {ResourceAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Map<String, Object> updateAnnotationStatus(String folderId, Map<String, Object> statusData) {
        try {
            String url = fastApiConfig.getBase().getUrl() + 
                        fastApiConfig.getEndpoints().getFolderDetail().replace("{id}", folderId) + "/annotation";
            
            HttpEntity<Map<String, Object>> requestEntity = createRequestEntity(statusData);
            
            if (fastApiConfig.getLogging().isEnabled()) {
                logger.info("调用FastAPI更新标注状态: URL={}, folderId={}, data={}", url, folderId, statusData);
            }
            
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            } else {
                logger.error("更新标注状态失败，状态码：{}", response.getStatusCode());
                return createErrorResponse("更新标注状态失败");
            }
            
        } catch (Exception e) {
            logger.error("更新标注状态失败", e);
            return createErrorResponse("更新标注状态失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    public boolean healthCheck() {
        try {
            String url = fastApiConfig.getBase().getUrl() + fastApiConfig.getEndpoints().getHealthCheck();
            
            HttpEntity<?> requestEntity = createRequestEntity(null);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            logger.warn("FastAPI健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 构建文件夹查询参数
     */
    private Map<String, Object> buildFolderQueryParams(Map<String, Object> params) {
        Map<String, Object> requestParams = new HashMap<>();
        
        // 分页参数
        requestParams.put("page_num", Integer.parseInt(params.getOrDefault("pageNum", "1").toString()));
        requestParams.put("page_size", Integer.parseInt(params.getOrDefault("pageSize", "20").toString()));
        
        // 查询条件
        if (params.containsKey("taskId") && StringUtils.hasText(params.get("taskId").toString())) {
            requestParams.put("zjhm", params.get("taskId"));  // 映射到FastAPI的参数名
        }
        if (params.containsKey("folderName") && StringUtils.hasText(params.get("folderName").toString())) {
            requestParams.put("folder_name", params.get("folderName"));
        }
        if (params.containsKey("country") && StringUtils.hasText(params.get("country").toString())) {
            requestParams.put("country_code", params.get("country"));
        }
        if (params.containsKey("documentType") && StringUtils.hasText(params.get("documentType").toString())) {
            requestParams.put("document_type", params.get("documentType"));
        }
        if (params.containsKey("year") && StringUtils.hasText(params.get("year").toString())) {
            requestParams.put("year", params.get("year"));
        }
        if (params.containsKey("status") && StringUtils.hasText(params.get("status").toString())) {
            requestParams.put("status", params.get("status"));
        }
        if (params.containsKey("annotationStatus") && StringUtils.hasText(params.get("annotationStatus").toString())) {
            requestParams.put("annotation_status", params.get("annotationStatus"));
        }
        if (params.containsKey("createTimeStart") && StringUtils.hasText(params.get("createTimeStart").toString())) {
            requestParams.put("start_time", params.get("createTimeStart"));
        }
        if (params.containsKey("createTimeEnd") && StringUtils.hasText(params.get("createTimeEnd").toString())) {
            requestParams.put("end_time", params.get("createTimeEnd"));
        }
        if (params.containsKey("deptName") && StringUtils.hasText(params.get("deptName").toString())) {
            requestParams.put("upload_dept", params.get("deptName"));
        }
        
        return requestParams;
    }

    /**
     * 创建请求实体
     */
    private <T> HttpEntity<T> createRequestEntity(T body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 添加API Key认证
        if (StringUtils.hasText(fastApiConfig.getApi().getKey())) {
            headers.set(fastApiConfig.getApi().getHeaderName(), fastApiConfig.getApi().getKey());
        }
        
        return new HttpEntity<>(body, headers);
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("msg", message);
        response.put("data", null);
        response.put("total", 0);
        return response;
    }
}