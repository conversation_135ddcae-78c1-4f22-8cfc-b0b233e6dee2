<template>
  <el-dialog
    v-model="dialogVisible"
    title="多版本批量上传 - 元数据确认"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 任务信息区域 -->
    <el-card class="task-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>任务信息</span>
          <el-tag type="success" size="small">自动生成</el-tag>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称">
            <el-input
              value="系统将自动生成任务名称"
              readonly
              placeholder="系统自动生成任务名称"
            >
              <template #prepend>
                <el-icon><Calendar /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="版本数量">
            <el-input
              :value="`${versionList.length} 个版本`"
              readonly
            >
              <template #prepend>
                <el-icon><FolderOpened /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 版本列表表格 -->
    <el-table
      :data="versionList"
      border
      stripe
      style="margin-top: 20px"
      max-height="400px"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column label="文件夹名称" min-width="200">
        <template #default="{ row }">
          <div class="folder-info">
            <div class="folder-name">{{ row.folderName }}</div>
            <div class="folder-path">{{ row.folderPath }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="文件数量" width="100" align="center">
        <template #default="{ row }">
          <el-tag type="info">{{ row.fileCount }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="解析状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag
            :type="getParseStatusType(row.parseStatus)"
            size="small"
          >
            {{ getParseStatusText(row.parseStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="国家" width="150">
        <template #default="{ row }">
          <div class="parse-result">
            <div v-if="row.parsedInfo && row.parsedInfo.country" class="parsed-value">
              <el-tag type="success" size="small">
                {{ row.parsedInfo.country }}
              </el-tag>
            </div>
            <div v-else class="parse-failed">
              <el-tag type="warning" size="small">
                解析失败
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="证件类型" width="150">
        <template #default="{ row }">
          <div class="parse-result">
            <div v-if="row.parsedInfo && row.parsedInfo.certType" class="parsed-value">
              <el-tag type="success" size="small">
                {{ row.parsedInfo.certType }}
              </el-tag>
            </div>
            <div v-else class="parse-failed">
              <el-tag type="warning" size="small">
                解析失败
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="签发年份" width="120">
        <template #default="{ row, $index }">
          <el-input
            v-model="row.metadata.issueYear"
            placeholder="年份"
            maxlength="4"
            size="small"
            @blur="validateRow($index)"
          />
        </template>
      </el-table-column>

      <el-table-column label="证号前缀" width="120">
        <template #default="{ row, $index }">
          <el-input
            v-model="row.metadata.certNumberPrefix"
            placeholder="证号前缀"
            maxlength="20"
            size="small"
            @blur="validateRow($index)"
          />
        </template>
      </el-table-column>

      <el-table-column label="签发地" width="120">
        <template #default="{ row, $index }">
          <el-input
            v-model="row.metadata.issuePlace"
            placeholder="签发地"
            maxlength="50"
            size="small"
            @blur="validateRow($index)"
          />
        </template>
      </el-table-column>

      <el-table-column label="验证状态" width="100" align="center">
        <template #default="{ row }">
          <el-icon
            :color="row.isValid ? '#67C23A' : '#F56C6C'"
            size="16"
          >
            <Check v-if="row.isValid" />
            <Close v-else />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>

    <!-- 统计信息 -->
    <div class="summary-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总版本数" :value="versionList.length" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总文件数" :value="totalFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已验证" :value="validVersions" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="待完善" :value="invalidVersions" />
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          :disabled="invalidVersions > 0"
        >
          开始批量上传 ({{ validVersions }}/{{ versionList.length }})
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, FolderOpened, Check, Close } from '@element-plus/icons-vue'
import {
  getCountryListByName,
  getCertTypeListByName,
  getAllCountries,
  getAllCertTypes
} from '@/api/cert/batchTask'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  folderStructure: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close', 'submit'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})



// 版本列表
const versionList = ref([])

// 选项数据（保留用于解析验证）
const countryOptions = ref([])
const certTypeOptions = ref([])

// 加载状态
const countryLoading = ref(false)
const certTypeLoading = ref(false)
const submitLoading = ref(false)

// 计算属性
const totalFiles = computed(() => {
  return versionList.value.reduce((sum, version) => sum + version.fileCount, 0)
})

const validVersions = computed(() => {
  return versionList.value.filter(version => version.isValid).length
})

const invalidVersions = computed(() => {
  return versionList.value.length - validVersions.value
})

/**
 * 获取解析状态类型
 */
const getParseStatusType = (status) => {
  const statusMap = {
    success: 'success',
    warning: 'warning',
    error: 'danger',
    pending: 'info'
  }
  return statusMap[status] || 'info'
}

/**
 * 获取解析状态文本
 */
const getParseStatusText = (status) => {
  const statusMap = {
    success: '已解析',
    warning: '部分解析',
    error: '解析失败',
    pending: '待解析'
  }
  return statusMap[status] || '未知'
}



/**
 * 解析文件夹名称
 */
const parseFolderName = (folderName) => {
  try {
    // 解析格式：国家_证件类型_年份_编号_地区
    // 例如：中国_护照_2020_A1000_北京
    const parts = folderName.split('_')

    if (parts.length >= 3) {
      return {
        country: parts[0],
        certType: parts[1],
        year: parts[2],
        number: parts[3] || '',
        region: parts[4] || ''
      }
    }

    return null
  } catch (error) {
    console.error('解析文件夹名称失败:', error)
    return null
  }
}

/**
 * 验证单行数据（基于解析结果）
 */
const validateRow = (index) => {
  const row = versionList.value[index]

  // 基于解析结果验证
  row.isValid = !!(
    row.parsedInfo &&
    row.parsedInfo.country &&
    row.parsedInfo.certType &&
    row.parsedInfo.year
  )
}

/**
 * 根据国家名称查找国家ID
 */
const findCountryIdByName = (countryName) => {
  if (!countryName || !countryOptions.value.length) {
    console.log(`无法映射国家: ${countryName}, 选项数量: ${countryOptions.value.length}`)
    return null
  }

  const country = countryOptions.value.find(c =>
    c.nameCn === countryName ||
    c.nameEn === countryName ||
    c.name === countryName ||
    c.name_cn === countryName ||
    c.name_en === countryName
  )

  if (country) {
    console.log(`成功映射国家: ${countryName} -> ID: ${country.id}`)
  } else {
    console.log(`未找到国家映射: ${countryName}, 可用选项:`, countryOptions.value.map(c => c.nameCn || c.name_cn || c.name))
  }

  return country ? country.id : null
}

/**
 * 根据证件类型名称查找证件类型ID
 */
const findCertTypeIdByName = (certTypeName) => {
  if (!certTypeName || !certTypeOptions.value.length) {
    console.log(`无法映射证件类型: ${certTypeName}, 选项数量: ${certTypeOptions.value.length}`)
    return null
  }

  const certType = certTypeOptions.value.find(ct =>
    ct.zjlbmc === certTypeName ||
    ct.name === certTypeName ||
    certTypeName.includes(ct.zjlbmc) ||
    ct.zjlbmc.includes(certTypeName)
  )

  if (certType) {
    console.log(`成功映射证件类型: ${certTypeName} -> ID: ${certType.id}`)
  } else {
    console.log(`未找到证件类型映射: ${certTypeName}, 可用选项:`, certTypeOptions.value.map(ct => ct.zjlbmc || ct.name))
  }

  return certType ? certType.id : null
}

/**
 * 初始化版本数据
 */
const initializeVersionData = () => {
  versionList.value = props.folderStructure.map(folder => {
    // 解析文件夹名称
    const parsedInfo = parseFolderName(folder.folderName)

    // 尝试映射国家和证件类型到ID
    const countryId = parsedInfo ? findCountryIdByName(parsedInfo.country) : null
    const certTypeId = parsedInfo ? findCertTypeIdByName(parsedInfo.certType) : null

    return {
      folderName: folder.folderName,
      folderPath: folder.folderPath,
      fileCount: folder.fileCount,
      files: folder.files, // 保留文件数组
      parseStatus: parsedInfo ? 'success' : 'error',
      parsedInfo: parsedInfo,
      metadata: {
        countryId: countryId,
        certTypeId: certTypeId,
        issueYear: parsedInfo ? parsedInfo.year : '',
        certNumberPrefix: parsedInfo ? parsedInfo.number : '',
        issuePlace: parsedInfo ? parsedInfo.region : ''
      },
      isValid: !!(parsedInfo && countryId && certTypeId)
    }
  })



  console.log('初始化版本数据:', versionList.value)
}

/**
 * 远程搜索国家
 */
const searchCountries = async (query) => {
  if (!query) {
    try {
      countryLoading.value = true
      const response = await getAllCountries()
      countryOptions.value = response.data || response.rows || []

      // 重新初始化版本数据以映射ID
      if (versionList.value.length > 0) {
        initializeVersionData()
      }
    } catch (error) {
      console.error('加载国家列表失败:', error)
      ElMessage.error('加载国家列表失败')
    } finally {
      countryLoading.value = false
    }
    return
  }

  try {
    countryLoading.value = true
    const response = await getCountryListByName(query)
    countryOptions.value = response.data || response.rows || []
  } catch (error) {
    console.error('搜索国家失败:', error)
    ElMessage.error('搜索国家失败')
  } finally {
    countryLoading.value = false
  }
}

/**
 * 远程搜索证件类型
 */
const searchCertTypes = async (query) => {
  if (!query) {
    try {
      certTypeLoading.value = true
      const response = await getAllCertTypes()
      certTypeOptions.value = response.data || response.rows || []

      // 重新初始化版本数据以映射ID
      if (versionList.value.length > 0) {
        initializeVersionData()
      }
    } catch (error) {
      console.error('加载证件类型列表失败:', error)
      ElMessage.error('加载证件类型列表失败')
    } finally {
      certTypeLoading.value = false
    }
    return
  }

  try {
    certTypeLoading.value = true
    const response = await getCertTypeListByName(query)
    certTypeOptions.value = response.data || response.rows || []
  } catch (error) {
    console.error('搜索证件类型失败:', error)
    ElMessage.error('搜索证件类型失败')
  } finally {
    certTypeLoading.value = false
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  emit('close')
}

/**
 * 处理提交
 */
const handleSubmit = async () => {
  if (invalidVersions.value > 0) {
    ElMessage.error('存在解析失败的版本，请检查文件夹命名格式')
    return
  }

  try {
    submitLoading.value = true

    // 构建提交数据
    const submitData = {
      versions: versionList.value.map(version => ({
        folderPath: version.folderPath,
        folderName: version.folderName,
        fileCount: version.fileCount,
        files: version.files,
        parsedInfo: version.parsedInfo,
        metadata: {
          // 保留映射的ID
          countryId: version.metadata.countryId,
          certTypeId: version.metadata.certTypeId,
          issueYear: version.metadata.issueYear || version.parsedInfo?.year,
          certNumberPrefix: version.metadata.certNumberPrefix || version.parsedInfo?.number,
          issuePlace: version.metadata.issuePlace || version.parsedInfo?.region,
          // 保留解析的信息用于显示
          country: version.parsedInfo?.country,
          certType: version.parsedInfo?.certType
        }
      }))
    }

    console.log('多版本提交数据:', submitData)

    // 验证每个版本的文件数据
    submitData.versions.forEach((version, index) => {
      console.log(`版本 ${index + 1} (${version.folderName}):`, {
        fileCount: version.fileCount,
        hasFiles: !!version.files,
        filesLength: version.files ? version.files.length : 'undefined',
        metadata: version.metadata
      })
    })

    // 发送提交事件
    emit('submit', submitData)

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败: ' + error.message)
  } finally {
    submitLoading.value = false
  }
}

// 监听文件夹结构变化
watch(() => props.folderStructure, (newStructure) => {
  if (newStructure && newStructure.length > 0) {
    initializeVersionData()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    console.log('MultiVersionMetadataModal 弹窗打开，开始加载数据')
    // 确保选项数据已加载
    await Promise.all([
      searchCountries(''),
      searchCertTypes('')
    ])
    // 重新初始化版本数据以确保ID映射正确
    if (props.folderStructure && props.folderStructure.length > 0) {
      initializeVersionData()
    }
  }
})

// 组件挂载时初始化
onMounted(() => {
  console.log('MultiVersionMetadataModal 组件挂载')
  searchCountries('')
  searchCertTypes('')
})
</script>

<style scoped>
.task-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.folder-info {
  line-height: 1.4;
}

.folder-name {
  font-weight: 500;
  color: #303133;
}

.folder-path {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.parse-result {
  display: flex;
  align-items: center;
  justify-content: center;
}

.parsed-value {
  display: flex;
  align-items: center;
}

.parse-failed {
  display: flex;
  align-items: center;
}

.summary-info {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-statistic__content) {
  font-size: 18px;
  font-weight: 600;
}
</style>
