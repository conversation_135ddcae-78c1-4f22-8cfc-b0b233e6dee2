package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.FolderInfo.PreParseVersionInfo;
import com.ruoyi.common.core.domain.AjaxResult;
import java.util.Map;

/**
 * 版本名称解析服务接口
 */
public interface IVersionNameParseService {

    /**
     * 解析文件夹名称生成版本信息
     * 输入格式: 西班牙_公务普通护照_2001_XDD85_哈瓦那
     * 输出格式: ESP_PG_2001_XDD85_Havana
     */
    PreParseVersionInfo parseFolderNameToVersionInfo(String folderName);

    /**
     * 异步解析任务下的所有文件夹
     */
    void parseAllFoldersAsync(String taskId);

    /**
     * 根据国家中文名称查找国家代码
     */
    String findCountryCodeByNameCn(String countryNameCn);

    /**
     * 根据证件类型名称查找证件类型代码
     */
    String findCertTypeCodeByName(String certTypeName);

    /**
     * 将中文地名转换为英文地名
     */
    String translatePlaceNameToEnglish(String chinesePlaceName);

    /**
     * 生成任务名称 (格式: 时间_部门名称_批量上传任务)
     */
    String generateTaskName(String deptName);

    /**
     * 重新解析文件夹版本信息
     */
    AjaxResult reparseFolder(String folderId);

    /**
     * 批量重新解析任务下的所有文件夹
     */
    AjaxResult reparseAllFolders(String taskId);

    /**
     * 获取解析状态统计
     */
    Map<String, Object> getParseStatistics(String taskId);
}
