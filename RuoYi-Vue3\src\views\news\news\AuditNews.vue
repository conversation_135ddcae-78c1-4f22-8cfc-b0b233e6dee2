<template>
  <div>
    <el-dialog
      :title="title"
      :model-value="dialogVisible"
      @update:model-value="val => dialogVisible = val"
      width="1000px"
      append-to-body
      @close="handleClose"
      @closed="handleClosed"
    >
      <el-form ref="newsRef" :model="formData" label-width="80px">
        <el-form-item label="文章标题" prop="newsTitle">
          <el-input v-model="formData.newsTitle" placeholder="请输入文章标题" :readonly="isAudit" />
        </el-form-item>
        <el-form-item label="文章栏目" prop="categroyID">
          <el-select v-model="formData.newsType" placeholder="请选择文章栏目" :disabled="isAudit">
            <el-option
              v-for="categroy in categroyList"
              :key="categroy.categroyID"
              :label="categroy.categroyName"
              :value="categroy.categroyID"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="showPicture" label="封面图片">
          <!-- 修改这里，确保 limit 是数值类型 -->
          <image-upload v-model="formData.headlinePic" :width="250" :height="250" :limit="1" />
        </el-form-item>
        <el-form-item label="文章内容">
          <editor v-model="formData.newsContent" :min-height="250" />
        </el-form-item>
        <el-form-item label="审核意见" prop="auditComment" v-if="isAudit">
          <el-input v-model="formData.auditComment" type="textarea" placeholder="请输入审核意见"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-if="isAudit">通 过</el-button>
          <el-button type="danger" @click="rejectForm" v-if="isAudit">不通过</el-button>
          <el-button type="primary" @click="submitForm" v-else>提 交</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, inject, readonly } from 'vue';
import { auditNews, rejectNews,updateNews } from '@/api/news/news.js';
import { ElMessage } from 'element-plus';


const categroyList = inject('categroyList', []); // 默认值为空数组

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isAudit: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:visible', 'close', 'success']);

// 使用本地变量控制对话框显示状态
const dialogVisible = ref(props.visible);

// 监听父组件传递的 visible 属性变化
watch(() => props.visible, (newValue) => {

  dialogVisible.value = newValue;
});

// 监听本地变量变化，同步更新父组件
watch(dialogVisible, (newValue) => {
  emits('update:visible', newValue);
});

// 处理关闭事件
const handleClose = () => {
  dialogVisible.value = false;
};

// 对话框完全关闭后触发
const handleClosed = () => {
  emits('close');
};

// 审核通过
const submitForm = async () => {
  if (props.isAudit && !props.formData.auditComment) {
    ElMessage.warning('审核意见不能为空');
    return;
  }
  try {
    if (props.isAudit) {
      // 审核模式下调用审核通过接口，并传递审核意见
      await auditNews({ newsId: props.formData.newsId, auditComment: props.formData.auditComment });
    } else {
      // 非审核模式下调用更新接口
      await updateNews(props.formData);
    }
    emits('success'); // 通知父组件提交成功
    handleClose();
    console.log(props.isAudit ? '文章审核通过' : '文章修改成功');
  } catch (error) {
    console.error(props.isAudit ? '文章审核失败' : '文章修改失败', error);
    handleClose();
  }
};

// 审核不通过
const rejectForm = async () => {
  if (!props.formData.auditComment) {
    ElMessage.warning('审核意见不能为空');
    return;
  }
  try {
    // 调用审核不通过接口，并传递审核意见
    await rejectNews({ newsId: props.formData.newsId, auditComment: props.formData.auditComment });
    emits('success'); // 通知父组件提交成功
    handleClose();
    console.log('文章审核不通过');
  } catch (error) {
    console.error('文章审核不通过失败', error);
    handleClose();
  }
};


// 控制图片显示
const showPicture = ref(false);

// 监听栏目选择变化
watch(() => props.formData.newsType, (newValue) => {
  const selectedCategroy = categroyList.value.find(item => item.categroyID === newValue);
  if (selectedCategroy && selectedCategroy.pictureStatus == 1) {
    showPicture.value = true;
  } else {
    showPicture.value = false;
  }
});
</script>
