package com.ruoyi.system.controller.news;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.news.SysHonorroll;
import com.ruoyi.system.service.news.ISysHonorrollService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 光荣榜信息表Controller
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
@RestController
@RequestMapping("/honorroll/honorroll")
public class SysHonorrollController extends BaseController
{
    @Autowired
    private ISysHonorrollService sysHonorrollService;

    /**
     * 查询光荣榜信息表列表
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysHonorroll sysHonorroll)
    {
        startPage();
        List<SysHonorroll> list = sysHonorrollService.selectSysHonorrollList(sysHonorroll);
        return getDataTable(list);
    }

    /**
     * 公开查询光荣榜信息表列表
     */
    //@PreAuthorize("@ss.hasPermi('honorroll:honorroll:publiclist')")
    @GetMapping("/publiclist")
    @Anonymous
    public TableDataInfo publiclist(SysHonorroll sysHonorroll)
    {
        sysHonorroll.setHonorStatus("1");
        List<SysHonorroll> list = sysHonorrollService.selectSysHonorrollList(sysHonorroll);
        return getDataTable(list);
    }


    /**
     * 导出光荣榜信息表列表
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:export')")
    @Log(title = "光荣榜信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysHonorroll sysHonorroll)
    {
        List<SysHonorroll> list = sysHonorrollService.selectSysHonorrollList(sysHonorroll);
        ExcelUtil<SysHonorroll> util = new ExcelUtil<SysHonorroll>(SysHonorroll.class);
        util.exportExcel(response, list, "光荣榜信息表数据");
    }

    /**
     * 获取光荣榜信息表详细信息
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:query')")
    @GetMapping(value = "/{honorId}")
    public AjaxResult getInfo(@PathVariable("honorId") Long honorId)
    {
        return success(sysHonorrollService.selectSysHonorrollByHonorId(honorId));
    }

    /**
     * 新增光荣榜信息表
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:add')")
    @Log(title = "光荣榜信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysHonorroll sysHonorroll)
    {
        return toAjax(sysHonorrollService.insertSysHonorroll(sysHonorroll));
    }

    /**
     * 修改光荣榜信息表
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:edit')")
    @Log(title = "光荣榜信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysHonorroll sysHonorroll)
    {
        return toAjax(sysHonorrollService.updateSysHonorroll(sysHonorroll));
    }

    /**
     * 删除光荣榜信息表
     */
    @PreAuthorize("@ss.hasPermi('honorroll:honorroll:remove')")
    @Log(title = "光荣榜信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{honorIds}")
    public AjaxResult remove(@PathVariable Long[] honorIds)
    {
        return toAjax(sysHonorrollService.deleteSysHonorrollByHonorIds(honorIds));
    }
}
