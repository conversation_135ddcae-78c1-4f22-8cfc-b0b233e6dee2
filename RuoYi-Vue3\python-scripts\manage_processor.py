#!/usr/bin/env python3
"""
批量处理器管理脚本
"""

import argparse
import sys
from batch_image_processor import BatchImageProcessor
from pymongo import MongoClient
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def list_tasks():
    """列出所有任务"""
    client = MongoClient('mongodb://localhost:27017/')
    db = client['cert_database']
    tasks = db['batch_upload_task']
    
    print("任务列表:")
    print("-" * 80)
    print(f"{'任务ID':<20} {'文件夹名称':<30} {'状态':<10} {'进度':<10}")
    print("-" * 80)
    
    for task in tasks.find().sort("create_time", -1):
        progress = f"{task.get('processed_images', 0)}/{task.get('total_images', 0)}"
        print(f"{task['task_id']:<20} {task['folder_name']:<30} {task['status']:<10} {progress:<10}")

def process_task(task_id):
    """处理指定任务"""
    processor = BatchImageProcessor()
    success = processor.process_batch_task(task_id)
    if success:
        print(f"任务 {task_id} 处理成功")
    else:
        print(f"任务 {task_id} 处理失败")

def reset_task(task_id):
    """重置任务状态"""
    client = MongoClient('mongodb://localhost:27017/')
    db = client['cert_database']
    tasks = db['batch_upload_task']
    
    result = tasks.update_one(
        {"task_id": task_id},
        {
            "$set": {
                "status": "PENDING",
                "processed_images": 0,
                "error_message": None
            }
        }
    )
    
    if result.modified_count > 0:
        print(f"任务 {task_id} 状态已重置为 PENDING")
    else:
        print(f"任务 {task_id} 不存在")

def main():
    parser = argparse.ArgumentParser(description='批量处理器管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出任务
    subparsers.add_parser('list', help='列出所有任务')
    
    # 处理任务
    process_parser = subparsers.add_parser('process', help='处理指定任务')
    process_parser.add_argument('task_id', help='任务ID')
    
    # 重置任务
    reset_parser = subparsers.add_parser('reset', help='重置任务状态')
    reset_parser.add_argument('task_id', help='任务ID')
    
    args = parser.parse_args()
    
    if args.command == 'list':
        list_tasks()
    elif args.command == 'process':
        process_task(args.task_id)
    elif args.command == 'reset':
        reset_task(args.task_id)
    else:
        parser.print_help()

if __name__ == '__main__':
    main() 