package com.ruoyi.framework.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import javax.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MongoDB 配置类
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.ruoyi.system.repository")
public class MongoConfig {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    @PostConstruct
    public void init() {
        System.out.println("MongoDB配置初始化完成，Date/LocalDateTime转换器已注册");
        System.out.println("MongoDB URI: " + mongoUri);
    }

    /**
     * 自定义 MongoClient，支持副本集和 retryWrites
     */
    @Bean
    public MongoClient mongoClient() {
        // 副本集连接字符串，启用 retryWrites
        String connectionString = "*****************************************************************************************************************************************";

        System.out.println("=== MongoDB 副本集配置调试信息 ===");
        System.out.println("原始 URI: " + mongoUri);
        System.out.println("最终连接字符串: " + connectionString);

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
                .retryWrites(true)  // 启用 retryWrites（副本集支持）
                .build();

        MongoClient client = MongoClients.create(settings);
        System.out.println("MongoDB 副本集客户端创建成功，retryWrites 已启用");
        return client;
    }

    /**
     * 自定义 MongoDatabaseFactory
     */
    @Bean
    public MongoDatabaseFactory mongoDatabaseFactory() {
        return new SimpleMongoClientDatabaseFactory(mongoClient(), "docu_research");
    }

    /**
     * 自定义转换器，处理Date和LocalDateTime之间的转换
     */
    @Bean
    public MongoCustomConversions mongoCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new DateToLocalDateTimeConverter());
        converters.add(new LocalDateTimeToDateConverter());
        return new MongoCustomConversions(converters);
    }

    /**
     * 自定义 MappingMongoConverter，移除 _class 字段并设置自定义转换器
     */
    @Bean
    public MappingMongoConverter mappingMongoConverter(MongoDatabaseFactory factory, MongoMappingContext context) {
        MappingMongoConverter converter = new MappingMongoConverter(new DefaultDbRefResolver(factory), context);
        converter.setCustomConversions(mongoCustomConversions());
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        // 强制初始化转换器
        converter.afterPropertiesSet();
        return converter;
    }

    /**
     * 自定义 MongoTemplate，使用配置好的转换器
     */
    @Bean
    public MongoTemplate mongoTemplate(MongoMappingContext context) {
        return new MongoTemplate(mongoDatabaseFactory(), mappingMongoConverter(mongoDatabaseFactory(), context));
    }

    /**
     * 开启事务支持
     */
    @Bean
    MongoTransactionManager transactionManager() {
        return new MongoTransactionManager(mongoDatabaseFactory());
    }



    /**
     * Date转LocalDateTime转换器
     */
    public static class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            if (source == null) {
                return null;
            }
            return source.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    /**
     * LocalDateTime转Date转换器
     */
    public static class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            if (source == null) {
                return null;
            }
            return Date.from(source.atZone(ZoneId.systemDefault()).toInstant());
        }
    }
}