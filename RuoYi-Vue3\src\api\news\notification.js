import request from '@/utils/request'

// 查询消息通知列表
export function listNotification(query) {
  return request({
    url: '/system/notification/list',
    method: 'get',
    params: query
  })
}

// 查询消息通知详细
export function getNotification(notificationId) {
  return request({
    url: '/system/notification/' + notificationId,
    method: 'get'
  })
}

// 获取未读消息数量
export function getUnreadCount() {
  return request({
    url: '/system/notification/unread',
    method: 'get'
  })
}

// 标记消息为已读
export function markAsRead(notificationId) {
  return request({
    url: '/system/notification/read/' + notificationId,
    method: 'put'
  })
}

// 标记所有消息为已读
export function markAllAsRead() {
  return request({
    url: '/system/notification/readAll',
    method: 'put'
  })
}

// 删除消息通知
export function delNotification(notificationId) {
  return request({
    url: '/system/notification/' + notificationId,
    method: 'delete'
  })
}
