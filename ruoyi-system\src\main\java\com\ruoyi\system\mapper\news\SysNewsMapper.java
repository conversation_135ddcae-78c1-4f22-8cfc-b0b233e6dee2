package com.ruoyi.system.mapper.news;

import java.util.List;
import com.ruoyi.system.domain.news.SysNews;
import java.util.Map;

/**
 * 文章管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface SysNewsMapper 
{
    /**
     * 查询文章管理
     * 
     * @param newsId 文章管理主键
     * @return 文章管理
     */
    public SysNews selectSysNewsByNewsId(Long newsId);

    /**
     * 查询文章管理列表
     * 
     * @param sysNews 文章管理
     * @return 文章管理集合
     */
    public List<SysNews> selectSysNewsList(SysNews sysNews);

    /**
     * 查询管理员文章列表
     * 
     * @param sysNews 文章管理
     * @return 文章管理集合
     */
    public List<SysNews> adminSysNewsList(SysNews sysNews);

    /**
     * 新增文章管理
     * 
     * @param sysNews 文章管理
     * @return 结果
     */
    public int insertSysNews(SysNews sysNews);

    /**
     * 修改文章管理
     * 
     * @param sysNews 文章管理
     * @return 结果
     */
    public int updateSysNews(SysNews sysNews);

    /**
     * 删除文章管理
     * 
     * @param newsId 文章管理主键
     * @return 结果
     */
    public int deleteSysNewsByNewsId(Long newsId);

    /**
     * 批量删除文章管理
     * 
     * @param newsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysNewsByNewsIds(Long[] newsIds);

    /**
     * 按年、季度、月统计部门和个人发布的文章数量
     * @param type 统计类型，year, quarter, month
     * @return 统计结果
     */
    List<Map<String, Object>> countNewsByTimeAndType(String type);
}
