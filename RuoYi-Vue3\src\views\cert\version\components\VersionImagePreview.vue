<template>
  <el-card class="image-preview-card" shadow="never">
    <template #header>
      <div class="preview-header">
        <div class="folder-info">
          <span v-if="selectedFolder" class="current-folder">
            当前文件夹: {{ selectedFolder.folderName }}
          </span>
          <span v-else class="no-selection">请选择文件夹</span>
          
          <!-- 文件夹状态标识 -->
          <div v-if="selectedFolder" class="folder-status">
            <el-tag
              v-if="isStandardFolder"
              type="success"
              size="small"
              effect="dark"
            >
              <el-icon><Star /></el-icon>
              标准样本
            </el-tag>
            <el-tag v-else type="info" size="small" effect="plain">
              普通样本
            </el-tag>
          </div>
        </div>
        
        <!-- 预览模式指示器 -->
        <div v-if="selectedImage" class="preview-mode">
          <el-tag :type="getModeTagType(currentMode)" size="small">
            {{ getModeText(currentMode) }}
          </el-tag>
        </div>
      </div>
    </template>

    <!-- 图片预览区 -->
    <div class="preview-content">
      <div v-if="selectedImage" class="image-viewer">
        <!-- 图片信息栏 -->
        <div class="image-info-bar">
          <div class="image-details">
            <span class="image-name">{{ selectedImage.imageName }}</span>
            <el-tag :type="getImageTypeTagType(selectedImage.imageType)" size="small">
              {{ getImageTypeText(selectedImage.imageType) }}
            </el-tag>
            <span v-if="isAnnotatableImage(selectedImage)" class="annotatable-badge">
              <el-icon><EditPen /></el-icon>
              可标注
            </span>
          </div>
          
          <!-- 缩放控制 -->
          <div class="zoom-controls">
            <el-button-group size="small">
              <el-button @click="zoomOut" :disabled="zoomLevel <= 0.1">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom">
                {{ Math.round(zoomLevel * 100) }}%
              </el-button>
              <el-button @click="zoomIn" :disabled="zoomLevel >= 3">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 图片容器 -->
        <div class="image-container" ref="imageContainer">
          <!-- 基础图片 -->
          <img
            ref="previewImage"
            :src="getImageUrl(selectedImage)"
            :alt="selectedImage.imageName"
            class="preview-image"
            :style="imageStyle"
            @wheel="handleImageZoom"
            @mousedown="handleImageDragStart"
            @load="handleImageLoad"
            @error="handleImageError"
          />
          
          <!-- 标注叠加层 -->
          <div
            v-if="currentMode === 'ANNOTATION_OVERLAY' && overlayAnnotations.length > 0"
            class="annotation-overlay"
            :style="imageStyle"
          >
            <div
              v-for="annotation in overlayAnnotations"
              :key="annotation.id"
              class="overlay-annotation"
              :style="getAnnotationStyle(annotation)"
            >
              <div class="annotation-shape" :class="annotation.type"></div>
              <div v-if="annotation.label" class="annotation-label">
                {{ annotation.label }}
              </div>
            </div>
          </div>
          
          <!-- 标注编辑层 -->
          <AnnotationCanvas
            v-if="currentMode === 'ANNOTATION_EDIT'"
            ref="annotationCanvasRef"
            :imageUrl="getImageUrl(selectedImage)"
            :tool="currentTool"
            v-model="currentAnnotations"
            @select-annotation="handleAnnotationSelect"
            class="annotation-canvas"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-preview">
        <el-empty description="请从右侧选择图片进行预览" />
      </div>
    </div>

    <!-- 操作按钮区 -->
    <div v-if="selectedFolder" class="action-buttons">
      <!-- 查看详情按钮 - 始终显示 -->
      <el-button @click="handleViewDetails">
        <el-icon><View /></el-icon>
        查看详情
      </el-button>

      <!-- 标准样本操作按钮 -->
      <template v-if="!isStandardFolder && canSetAsStandard">
        <el-button type="success" @click="handleSetStandard">
          <el-icon><Star /></el-icon>
          设为标准样本
        </el-button>
      </template>
      
      <template v-if="isStandardFolder">
        <el-button type="warning" @click="handleRemoveStandard">
          <el-icon><Remove /></el-icon>
          取消标准样本
        </el-button>
        
        <!-- 标注操作按钮 -->
        <el-button
          v-if="canAnnotate"
          type="primary"
          @click="handleStartAnnotation"
        >
          <el-icon><EditPen /></el-icon>
          开始标注
        </el-button>
      </template>
      
      <!-- 标注工具栏 -->
      <div v-if="currentMode === 'ANNOTATION_EDIT'" class="annotation-tools">
        <el-divider direction="vertical" />
        <AnnotationToolbar
          @tool-selected="handleToolChange"
          :current-tool="currentTool"
        />
        
        <el-button
          type="primary"
          @click="handleSaveAnnotation"
          :loading="saving"
          size="small"
        >
          保存标注
        </el-button>
        
        <el-button
          type="danger"
          @click="handleClearAnnotation"
          :disabled="currentAnnotations.length === 0"
          size="small"
        >
          清空标注
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Star, Remove, View, EditPen, Edit, ZoomIn, ZoomOut
} from '@element-plus/icons-vue'

// 导入组件
import AnnotationCanvas from '@/views/cert/annotations/AnnotationCanvas.vue'
import AnnotationToolbar from '@/views/cert/annotations/AnnotationToolbar.vue'

// Props
const props = defineProps({
  selectedImage: {
    type: Object,
    default: null
  },
  selectedFolder: {
    type: Object,
    default: null
  },
  versionInfo: {
    type: Object,
    default: null
  },
  standardAnnotations: {
    type: Map,
    default: () => new Map()
  }
})

// Emits
const emit = defineEmits([
  'set-standard',
  'remove-standard', 
  'start-annotation',
  'view-details',
  'save-annotation'
])

// 响应式数据
const imageContainer = ref(null)
const previewImage = ref(null)
const annotationCanvasRef = ref(null)

// 图片缩放和拖拽
const zoomLevel = ref(1)
const imagePosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 标注相关
const currentTool = ref('select')
const currentAnnotations = ref([])
const saving = ref(false)

// 预览模式枚举
const previewModes = {
  VIEW_ONLY: 'VIEW_ONLY',           // 仅查看模式
  ANNOTATION_OVERLAY: 'ANNOTATION_OVERLAY',    // 标注叠加模式  
  ANNOTATION_EDIT: 'ANNOTATION_EDIT'           // 标注编辑模式
}

// 计算属性
const isStandardFolder = computed(() => {
  return props.selectedFolder?.folderId === props.versionInfo?.standardFolderId
})

const canSetAsStandard = computed(() => {
  return props.selectedFolder?.status === 'associated' && 
         props.selectedFolder?.reviewStatus === 'approved'
})

// 判断是否为可标注图片
const isAnnotatableImage = (image) => {
  if (!image) return false
  const annotatableNames = ['可见光主图.jpg', '红外光主图.jpg', '紫外光主图.jpg']
  return annotatableNames.includes(image.imageName)
}

const canAnnotate = computed(() => {
  return isStandardFolder.value && isAnnotatableImage(props.selectedImage)
})

const canViewAnnotationOverlay = computed(() => {
  return !isStandardFolder.value && 
         isAnnotatableImage(props.selectedImage) && 
         props.versionInfo?.standardFolderId
})

// 当前预览模式
const currentMode = computed(() => {
  if (canAnnotate.value) return previewModes.ANNOTATION_EDIT
  if (canViewAnnotationOverlay.value) return previewModes.ANNOTATION_OVERLAY
  return previewModes.VIEW_ONLY
})

// 叠加标注数据
const overlayAnnotations = computed(() => {
  if (currentMode.value !== previewModes.ANNOTATION_OVERLAY) return []
  
  const imageType = props.selectedImage?.imageType
  return props.standardAnnotations.get(imageType) || []
})

// 图片样式
const imageStyle = computed(() => {
  return {
    transform: `scale(${zoomLevel.value}) translate(${imagePosition.value.x}px, ${imagePosition.value.y}px)`,
    transformOrigin: 'center center',
    transition: isDragging.value ? 'none' : 'transform 0.3s ease'
  }
})

// 监听图片变化，重置缩放和位置
watch(() => props.selectedImage, () => {
  resetZoom()
})

// 方法
const resetZoom = () => {
  zoomLevel.value = 1
  imagePosition.value = { x: 0, y: 0 }
}

const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.2)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.1) {
    zoomLevel.value = Math.max(0.1, zoomLevel.value - 0.2)
  }
}

// 鼠标滚轮缩放
const handleImageZoom = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newZoom = Math.max(0.1, Math.min(3, zoomLevel.value + delta))
  zoomLevel.value = newZoom
}

// 图片拖拽
const handleImageDragStart = (event) => {
  if (zoomLevel.value <= 1) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - imagePosition.value.x,
    y: event.clientY - imagePosition.value.y
  }

  document.addEventListener('mousemove', handleImageDrag)
  document.addEventListener('mouseup', handleImageDragEnd)
}

const handleImageDrag = (event) => {
  if (!isDragging.value) return

  imagePosition.value = {
    x: event.clientX - dragStart.value.x,
    y: event.clientY - dragStart.value.y
  }
}

const handleImageDragEnd = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleImageDrag)
  document.removeEventListener('mouseup', handleImageDragEnd)
}

const handleImageLoad = () => {
  console.log('图片加载完成')
}

// 在 script setup 部分添加 getImageUrl 方法
const getImageUrl = (image) => {
  if (!image || !image.minioPath) {
    console.warn('图片对象或minioPath为空:', image)
    return ''
  }

  console.log('生成图片URL，minioPath:', image.minioPath)

  // 临时方案：先尝试直接访问MinIO，看看文件是否存在
  const directMinioUrl = `http://localhost:9000/xjlfiles/${image.minioPath}`
  console.log('直接MinIO URL:', directMinioUrl)

  // 如果直接访问失败，再尝试代理
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`
  console.log('代理URL:', proxyUrl)

  // 先返回代理URL，如果失败会在图片加载错误时处理
  return proxyUrl
}

const handleImageError = (event) => {
  const img = event.target
  const currentSrc = img.src

  console.error('图片加载失败:', currentSrc)

  // 如果当前是代理URL失败，尝试直接访问MinIO
  if (currentSrc.includes('/dev-api/common/image/proxy')) {
    // 提取原始MinIO URL
    const urlMatch = currentSrc.match(/url=([^&]+)/)
    if (urlMatch) {
      const originalUrl = decodeURIComponent(urlMatch[1])
      console.log('尝试直接访问MinIO:', originalUrl)
      img.src = originalUrl
      return
    }
  }

  // 最后的回退方案：不显示图片
  console.log('图片加载失败，不显示默认图片')
  img.style.display = 'none'
}

// 标注相关方法
const handleToolChange = (tool) => {
  currentTool.value = tool
}

const handleAnnotationSelect = (annotationId) => {
  console.log('选中标注:', annotationId)
}

const handleSaveAnnotation = async () => {
  try {
    saving.value = true
    emit('save-annotation', {
      imageId: props.selectedImage.imageId,
      annotations: currentAnnotations.value
    })
  } finally {
    saving.value = false
  }
}

const handleClearAnnotation = async () => {
  try {
    await ElMessageBox.confirm('确定要清除当前图片的所有标注吗？', '确认清除', {
      type: 'warning'
    })
    currentAnnotations.value = []
  } catch (error) {
    // 用户取消操作
  }
}

// 操作按钮事件
const handleSetStandard = () => {
  emit('set-standard', props.selectedFolder)
}

const handleRemoveStandard = () => {
  emit('remove-standard', props.selectedFolder)
}

const handleStartAnnotation = () => {
  emit('start-annotation', props.selectedFolder)
}

const handleViewDetails = () => {
  emit('view-details', props.selectedFolder)
}

// 工具方法
const getModeText = (mode) => {
  const modeMap = {
    'VIEW_ONLY': '查看模式',
    'ANNOTATION_OVERLAY': '叠加查看',
    'ANNOTATION_EDIT': '标注编辑'
  }
  return modeMap[mode] || '未知模式'
}

const getModeTagType = (mode) => {
  const typeMap = {
    'VIEW_ONLY': 'info',
    'ANNOTATION_OVERLAY': 'warning',
    'ANNOTATION_EDIT': 'success'
  }
  return typeMap[mode] || 'info'
}

const getImageTypeText = (imageType) => {
  const typeMap = {
    'VISIBLE': '可见光',
    'INFRARED': '红外',
    'ULTRAVIOLET': '紫外',
    'OTHER': '其他'
  }
  return typeMap[imageType] || '未知'
}

const getImageTypeTagType = (imageType) => {
  const typeMap = {
    'VISIBLE': 'primary',
    'INFRARED': 'danger',
    'ULTRAVIOLET': 'warning',
    'OTHER': 'info'
  }
  return typeMap[imageType] || 'info'
}

// 获取标注样式
const getAnnotationStyle = (annotation) => {
  return {
    position: 'absolute',
    left: annotation.x + 'px',
    top: annotation.y + 'px',
    width: annotation.width + 'px',
    height: annotation.height + 'px',
    opacity: 0.7,
    pointerEvents: 'none'
  }
}
</script>

<style scoped>
.image-preview-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.folder-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-folder {
  font-weight: 500;
  color: #303133;
}

.no-selection {
  color: #909399;
  font-style: italic;
}

.folder-status {
  display: flex;
  gap: 4px;
}

.preview-mode {
  display: flex;
  align-items: center;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.image-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.image-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 12px;
}

.image-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.image-name {
  font-weight: 500;
  color: #303133;
}

.annotatable-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

.zoom-controls {
  display: flex;
  align-items: center;
}

.image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  cursor: grab;
  user-select: none;
}

.preview-image:active {
  cursor: grabbing;
}

.annotation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.overlay-annotation {
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.annotation-shape.rectangle {
  width: 100%;
  height: 100%;
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.annotation-shape.circle {
  width: 100%;
  height: 100%;
  border: 2px solid #67c23a;
  border-radius: 50%;
  background-color: rgba(103, 194, 58, 0.1);
}

.annotation-label {
  position: absolute;
  top: -20px;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.empty-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border-radius: 8px;
  min-height: 400px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid #ebeef5;
  margin-top: 16px;
  flex-wrap: wrap;
}

.annotation-tools {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .image-container {
    min-height: 300px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .annotation-tools {
    margin-left: 0;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    align-items: stretch;
  }

  .image-info-bar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .image-details {
    justify-content: center;
  }

  .zoom-controls {
    justify-content: center;
  }

  .image-container {
    min-height: 250px;
  }

  .action-buttons {
    gap: 6px;
  }

  .action-buttons .el-button {
    flex: 1;
  }
}
</style>
