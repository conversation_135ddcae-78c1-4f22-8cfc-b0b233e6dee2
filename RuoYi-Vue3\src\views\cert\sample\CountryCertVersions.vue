<template>
  <div class="country-cert-versions">
    <!-- greenfox - 新增：页面说明 - 2025-01-15 -->
    <el-alert
      title="国家版本查看说明"
      type="success"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    >
      <template #default>
        <p><strong>功能说明：</strong>这是特定国家的版本查看页面，专注于展示 {{ countryName }} 的所有证件版本和样本信息。</p>
        <p><strong>与全局版本管理的区别：</strong>全局版本管理支持跨国家的版本管理操作，这里专注于单个国家的详细信息展示。</p>
      </template>
    </el-alert>

    <!-- 国家信息头部 -->
    <div class="country-header">
      <div class="country-info">
        <img v-if="countryFlag" :src="countryFlag" :alt="countryName" class="country-flag" />
        <div class="country-details">
          <h2>{{ countryName }} ({{ countryCode }})</h2>
          <p>{{ countryNameEn }}</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="addVersion">
          <el-icon><Plus /></el-icon>
          添加版本
        </el-button>
      </div>
    </div>

    <!-- 版本信息表格 -->
    <div class="versions-table">
      <el-table
        :data="versions"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column prop="versionId" label="版本ID" width="120" fixed="left" />
        <el-table-column prop="versionName" label="版本名称" width="200" fixed="left" />
        <el-table-column prop="certInfo.type" label="证件类型" width="100" fixed="left" />
        <el-table-column prop="certInfo.type" label="证件类型" width="100">
          <template #default="scope">
            {{ getCertTypeName(scope.row.certInfo?.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="certInfo.startNo" label="起始编号" width="120" />
        <el-table-column prop="issueYear" label="发行年份" width="100" />
        <el-table-column prop="validityPeriod" label="有效期" width="80">
          <template #default="scope">
            {{ scope.row.validityPeriod }}年
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="训练图片" width="100">
          <template #default="scope">
            <el-button
              v-if="scope.row.imageInfo?.url"
              type="text"
              size="small"
              @click="previewImage(scope.row.imageInfo.url)"
            >
              查看
            </el-button>
            <span v-else class="text-gray-400">无</span>
          </template>
        </el-table-column>
        <el-table-column label="样本数量" width="100">
          <template #default="scope">
            {{ getSampleCount(scope.row.sampleInfo) }}
          </template>
        </el-table-column>
        <el-table-column prop="uploaderInfo.nickName" label="上传人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewVersionDetail(scope.row)">
              版本详情
            </el-button>
            <el-button type="success" size="small" @click="viewSamples(scope.row)">
              查看样本
            </el-button>
            <el-button type="info" size="small" @click="manageVersion(scope.row)">
              版本管理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imagePreviewVisible" title="图片预览" width="60%">
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" style="max-width: 100%; height: auto;" />
      </div>
    </el-dialog>

    <!-- 添加/编辑版本对话框 -->
    <cert-version-form
      v-if="showVersionForm"
      :visible="showVersionForm"
      :country-id="countryId"
      :country-code="countryCode"
      :country-name="countryName"
      :version-data="currentVersion"
      @close="closeVersionForm"
      @success="handleVersionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import CertVersionForm from './components/CertVersionForm.vue';
import { getVersionsWithSamples, delCertVersion } from '@/api/cert/version';
import { getCertTypes } from '@/api/cert/type';

// 路由参数
const route = useRoute();
const countryId = ref(parseInt(route.params.countryId));
const countryCode = ref(route.query.countryCode || '');
const countryName = ref(route.query.countryName || '');
const countryNameEn = ref(route.query.countryNameEn || '');
const countryFlag = ref(route.query.countryFlag || '');

// 数据
const versions = ref([]);
const certTypes = ref([]);
const loading = ref(false);
const showVersionForm = ref(false);
const currentVersion = ref(null);
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

// 获取版本信息
const fetchVersions = async () => {
  try {
    loading.value = true;
    const response = await getVersionsWithSamples(countryId.value);
    versions.value = response.data || [];
    console.log(versions.value);
  } catch (error) {
    console.error('获取版本信息失败:', error);
    ElMessage.error('获取版本信息失败');
  } finally {
    loading.value = false;
  }
};

// 获取证件类型信息
const fetchCertTypes = async () => {
  try {
    const response = await getCertTypes();
    certTypes.value = response.data || [];
  } catch (error) {
    console.error('获取证件类型失败:', error);
  }
};

// 获取证件类型名称
const getCertTypeName = (typeCode) => {
  const certType = certTypes.value.find(type => type.zjlbdm === typeCode);
  return certType ? certType.zjlbmc : typeCode;
};

// 获取样本数量
const getSampleCount = (sampleInfo) => {
  if (!sampleInfo || !sampleInfo.images) return 0;
  return sampleInfo.images.length;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString('zh-CN');
};

// 预览图片
const previewImage = (url) => {
  previewImageUrl.value = url;
  imagePreviewVisible.value = true;
};

// 添加版本
const addVersion = () => {
  currentVersion.value = null;
  showVersionForm.value = true;
};

// 查看版本详情
const viewVersionDetail = (version) => {
  router.push({
    path: '/cert/version',
    query: { versionId: version.versionId }
  });
};

// 查看样本
const viewSamples = (version) => {
  router.push({
    path: '/samples',
    query: { versionId: version.versionId }
  });
};

// 版本管理
const manageVersion = (version) => {
  router.push({
    path: '/cert/version',
    query: { versionId: version.versionId }
  });
};

// 删除版本
const deleteVersion = async (version) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除版本 "${version.versionName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await delCertVersion(version.id);
    ElMessage.success('删除成功');
    fetchVersions();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除版本失败:', error);
      ElMessage.error('删除版本失败');
    }
  }
};

// 关闭版本表单
const closeVersionForm = () => {
  showVersionForm.value = false;
  currentVersion.value = null;
};

// 版本操作成功回调
const handleVersionSuccess = () => {
  closeVersionForm();
  fetchVersions();
};

// 初始化
onMounted(() => {
  fetchVersions();
  fetchCertTypes();
});
</script>

<style scoped>
.country-cert-versions {
  padding: 20px;
}

.country-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.country-info {
  display: flex;
  align-items: center;
}

.country-flag {
  width: 48px;
  height: 32px;
  margin-right: 16px;
  border-radius: 4px;
  object-fit: cover;
}

.country-details h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  color: #333;
}

.country-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.versions-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview {
  text-align: center;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
