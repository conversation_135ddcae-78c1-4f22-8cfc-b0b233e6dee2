package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CertTypeMapper;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.service.ICertTypeService;

/**
 * 证件类别表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class CertTypeServiceImpl implements ICertTypeService 
{
    @Autowired
    private CertTypeMapper certTypeMapper;

    /**
     * 查询证件类别表
     * 
     * @param id 证件类别表主键
     * @return 证件类别表
     */
    @Override
    public CertType selectCertTypeById(Long id)
    {
        return certTypeMapper.selectCertTypeById(id);
    }

    /**
     * 根据证件代码查询证件类别
     * 
     * @param zjlbdm 证件代码
     * @return 证件类别
     */
    @Override
    public CertType selectCertTypeByCode(String zjlbdm)
    {
        return certTypeMapper.selectCertTypeByCode(zjlbdm);
    }

    /**
     * 查询证件类别表列表
     * 
     * @param certType 证件类别表
     * @return 证件类别表
     */
    @Override
    public List<CertType> selectCertTypeList(CertType certType)
    {
        return certTypeMapper.selectCertTypeList(certType);
    }

    /**
     * 新增证件类别表
     * 
     * @param certType 证件类别表
     * @return 结果
     */
    @Override
    public int insertCertType(CertType certType)
    {
        certType.setCreateTime(DateUtils.getNowDate());
        return certTypeMapper.insertCertType(certType);
    }

    /**
     * 修改证件类别表
     * 
     * @param certType 证件类别表
     * @return 结果
     */
    @Override
    public int updateCertType(CertType certType)
    {
        certType.setUpdateTime(DateUtils.getNowDate());
        return certTypeMapper.updateCertType(certType);
    }

    /**
     * 批量删除证件类别表
     * 
     * @param ids 需要删除的证件类别表主键
     * @return 结果
     */
    @Override
    public int deleteCertTypeByIds(Long[] ids)
    {
        return certTypeMapper.deleteCertTypeByIds(ids);
    }

    /**
     * 删除证件类别表信息
     * 
     * @param id 证件类别表主键
     * @return 结果
     */
    @Override
    public int deleteCertTypeById(Long id)
    {
        return certTypeMapper.deleteCertTypeById(id);
    }
}
