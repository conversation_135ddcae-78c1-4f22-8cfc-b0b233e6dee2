<template>
  <el-dialog
    v-model="dialogVisible"
    title="文件夹扫描中"
    width="600px"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <!-- 扫描进度 -->
    <div class="scan-progress-container">
      <div class="scan-header">
        <el-icon class="scan-icon" :class="{ 'rotating': isScanning }">
          <FolderOpened />
        </el-icon>
        <h3>正在扫描文件夹结构...</h3>
      </div>

      <!-- 进度条 -->
      <el-progress
        :percentage="scanProgress"
        :status="scanStatus"
        :stroke-width="8"
        :show-text="true"
      >
        <template #default="{ percentage }">
          <span class="progress-text">{{ percentage }}%</span>
        </template>
      </el-progress>

      <!-- 扫描统计 -->
      <div class="scan-stats">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ stats.foldersFound }}</div>
              <div class="stat-label">发现文件夹</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ stats.filesFound }}</div>
              <div class="stat-label">发现文件</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ stats.currentFolder }}</div>
              <div class="stat-label">当前文件夹</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 当前处理的文件 -->
      <div v-if="currentFile" class="current-file">
        <el-text type="info" size="small">
          正在处理: {{ currentFile }}
        </el-text>
      </div>

      <!-- 扫描日志 -->
      <div v-if="showLogs" class="scan-logs">
        <el-collapse v-model="activeLogPanel">
          <el-collapse-item title="扫描日志" name="logs">
            <div class="log-container">
              <div
                v-for="(log, index) in logs"
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessage" class="error-section">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="!isScanning"
          type="primary"
          @click="handleClose"
        >
          {{ scanCompleted ? '继续' : '关闭' }}
        </el-button>
        <el-button
          v-if="isScanning"
          type="warning"
          @click="handleCancel"
        >
          取消扫描
        </el-button>
        <el-button
          v-if="logs.length > 0"
          type="info"
          @click="toggleLogs"
        >
          {{ showLogs ? '隐藏日志' : '显示日志' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { FolderOpened } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  scanProgress: {
    type: Number,
    default: 0
  },
  isScanning: {
    type: Boolean,
    default: false
  },
  stats: {
    type: Object,
    default: () => ({
      foldersFound: 0,
      filesFound: 0,
      currentFolder: ''
    })
  },
  currentFile: {
    type: String,
    default: ''
  },
  errorMessage: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['close', 'cancel'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

const scanStatus = computed(() => {
  if (props.errorMessage) return 'exception'
  if (props.scanProgress === 100) return 'success'
  return undefined
})

const scanCompleted = computed(() => {
  return props.scanProgress === 100 && !props.isScanning
})

// 日志相关
const logs = ref([])
const showLogs = ref(false)
const activeLogPanel = ref([])

/**
 * 添加日志
 */
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  
  logs.value.push({
    time,
    message,
    type,
    timestamp: now.getTime()
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-100)
  }
}

/**
 * 切换日志显示
 */
const toggleLogs = () => {
  showLogs.value = !showLogs.value
  if (showLogs.value) {
    activeLogPanel.value = ['logs']
  } else {
    activeLogPanel.value = []
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  emit('close')
}

/**
 * 处理取消
 */
const handleCancel = () => {
  emit('cancel')
}

// 监听扫描进度变化，添加日志
watch(() => props.stats.foldersFound, (newValue, oldValue) => {
  if (newValue > oldValue) {
    addLog(`发现新文件夹，总计: ${newValue}`, 'info')
  }
})

watch(() => props.stats.filesFound, (newValue, oldValue) => {
  if (newValue > oldValue) {
    addLog(`发现新文件，总计: ${newValue}`, 'info')
  }
})

watch(() => props.currentFile, (newFile) => {
  if (newFile) {
    addLog(`正在处理: ${newFile}`, 'debug')
  }
})

watch(() => props.errorMessage, (error) => {
  if (error) {
    addLog(`扫描错误: ${error}`, 'error')
  }
})

// 暴露方法给父组件
defineExpose({
  addLog
})
</script>

<style scoped>
.scan-progress-container {
  padding: 20px 0;
}

.scan-header {
  text-align: center;
  margin-bottom: 30px;
}

.scan-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
  display: block;
}

.scan-icon.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.scan-header h3 {
  margin: 0;
  color: #303133;
  font-weight: 500;
}

.scan-stats {
  margin: 30px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.current-file {
  margin: 15px 0;
  padding: 10px;
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
}

.progress-text {
  font-weight: bold;
}

.scan-logs {
  margin-top: 20px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #606266;
}

.log-item.debug .log-message {
  color: #909399;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.error-section {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
