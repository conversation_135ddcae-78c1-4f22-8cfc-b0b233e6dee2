package com.ruoyi.system.service.news;

import java.util.List;
import com.ruoyi.system.domain.news.Categroy;

/**
 * 栏目信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface ICategroyService 
{
    /**
     * 查询栏目信息
     * 
     * @param categroyID 栏目信息主键
     * @return 栏目信息
     */
    public Categroy selectCategroyByCategroyID(Long categroyID);

    /**
     * 查询栏目信息列表
     * 
     * @param categroy 栏目信息
     * @return 栏目信息集合
     */
    public List<Categroy> selectCategroyList(Categroy categroy);

    /**
     * 新增栏目信息
     * 
     * @param categroy 栏目信息
     * @return 结果
     */
    public int insertCategroy(Categroy categroy);

    /**
     * 修改栏目信息
     * 
     * @param categroy 栏目信息
     * @return 结果
     */
    public int updateCategroy(Categroy categroy);

    /**
     * 批量删除栏目信息
     * 
     * @param categroyIDs 需要删除的栏目信息主键集合
     * @return 结果
     */
    public int deleteCategroyByCategroyIDs(Long[] categroyIDs);

    /**
     * 删除栏目信息信息
     * 
     * @param categroyID 栏目信息主键
     * @return 结果
     */
    public int deleteCategroyByCategroyID(Long categroyID);
}
