# 硬编码解耦改造总结

## 🎯 问题背景

原批量证件版本创建脚本存在严重的硬编码问题：

### ❌ 原有硬编码问题

1. **固定文件路径**
   ```python
   FOLDER_ROOT = r'C:\Users\<USER>\Documents\工作文档\2025研发项目\证研平台\configs_upd\configs\personal'
   ```

2. **固定MinIO配置**
   ```python
   MINIO_ENDPOINT = 'localhost:9000'
   MINIO_ACCESS_KEY = 'minioadmin'
   MINIO_SECRET_KEY = 'minioadmin'
   ```

3. **固定证件规格**
   ```python
   "endNo": "D9999",
   "numberFormat": "D{4}",
   "securityFeatures": ["水印", "防伪线", "全息图"],
   "width": 85.6,
   "height": 53.98,
   ```

4. **固定训练图片配置**
   ```python
   possible_paths = [
       os.path.join(root_dir, country_code, folder_name, "PER.JPG"),
       os.path.join(root_dir, country_code, folder_name, "per.jpg"),
       # ...
   ]
   ```

### 🔧 解决方案

## 1. 创建配置系统

### 配置文件结构 (`config.py`)

```python
# API配置
API_CONFIG = {
    'base_url': 'http://localhost:8000',
    'endpoint': '/api/v1/cert/version/unified',
    'timeout': 15
}

# 文件夹配置 - 支持环境变量
FOLDER_CONFIG = {
    'root_path': os.getenv('CERT_FOLDER_ROOT', r'C:\cert_configs\personal'),
    'country_subfolder': True,
    'folder_name_pattern': r'^[A-Z]{2,3}_[A-Z]{1,3}_\d+_\d{4}$'
}

# MinIO配置 - 支持环境变量
MINIO_CONFIG = {
    'endpoint': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
    'access_key': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
    'secret_key': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
    'bucket_name': os.getenv('MINIO_BUCKET_NAME', 'xjlfiles'),
    'secure': os.getenv('MINIO_SECURE', 'false').lower() == 'true'
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_concurrent_uploads': int(os.getenv('MAX_CONCURRENT_UPLOADS', '5')),
    'max_concurrent_requests': int(os.getenv('MAX_CONCURRENT_REQUESTS', '3')),
    'retry_attempts': int(os.getenv('RETRY_ATTEMPTS', '3')),
    'retry_delay': float(os.getenv('RETRY_DELAY', '1.0'))
}

# 默认证件规格
DEFAULT_CERT_SPECS = {
    'width': 85.6,
    'height': 53.98,
    'thickness': 0.76,
    'material': 'PVC',
    'security_features': ['水印', '防伪线', '全息图'],
    'number_format': 'D{4}',
    'end_no': 'D9999',
    'light_types': ['visible']
}

# 训练图片配置
TRAINING_IMAGE_CONFIG = {
    'file_names': ['PER.JPG', 'per.jpg', 'PER.jpg', 'per.JPG'],
    'upload_path_template': '{folder_name}/training/PER{extension}',
    'light_type': 'visible',
    'description_template': '标准样本训练图片 - {version_code}'
}
```

## 2. 主脚本改造

### 配置导入和兼容性处理

```python
# 导入配置
try:
    from config import (
        API_CONFIG, MINIO_CONFIG, PERFORMANCE_CONFIG, FOLDER_CONFIG,
        DEFAULT_CERT_SPECS, TRAINING_IMAGE_CONFIG, LOGGING_CONFIG,
        get_api_url, get_folder_root, validate_config, print_config_summary,
        load_config_from_file
    )
    CONFIG_AVAILABLE = True
except ImportError:
    print("⚠️  未找到config.py配置文件，使用默认配置")
    CONFIG_AVAILABLE = False
```

### 向后兼容处理

```python
if CONFIG_AVAILABLE:
    # 使用配置文件
    API_URL = get_api_url()
    FOLDER_ROOT = get_folder_root()
    MINIO_ENDPOINT = MINIO_CONFIG['endpoint']
    # ...
else:
    # 回退到硬编码配置（向后兼容）
    API_URL = 'http://localhost:8000/api/v1/cert/version/unified'
    FOLDER_ROOT = r'C:\Users\<USER>\Documents\...'
    # ...
```

### 动态证件规格配置

```python
# 获取默认证件规格
if CONFIG_AVAILABLE:
    cert_specs = DEFAULT_CERT_SPECS
    training_config = TRAINING_IMAGE_CONFIG
else:
    # 回退到硬编码配置
    cert_specs = {
        'width': 85.6, 'height': 53.98, 'thickness': 0.76,
        'material': 'PVC', 'security_features': ['水印', '防伪线', '全息图'],
        'number_format': 'D{4}', 'end_no': 'D9999', 'light_types': ['visible']
    }
```

## 3. 配置方式

### 方式1: JSON配置文件 (推荐)

```json
{
  "api": {
    "base_url": "http://localhost:8000",
    "endpoint": "/api/v1/cert/version/unified"
  },
  "folder": {
    "root_path": "D:\\your\\cert\\path"
  },
  "minio": {
    "endpoint": "your-server:9000",
    "access_key": "your-access-key",
    "secret_key": "your-secret-key"
  },
  "performance": {
    "max_concurrent_uploads": 10,
    "max_concurrent_requests": 8
  }
}
```

### 方式2: 环境变量

```bash
set CERT_FOLDER_ROOT=D:\your\cert\path
set MINIO_ENDPOINT=your-server:9000
set MINIO_ACCESS_KEY=your-access-key
set MINIO_SECRET_KEY=your-secret-key
set MAX_CONCURRENT_UPLOADS=10
set MAX_CONCURRENT_REQUESTS=8
```

### 方式3: 直接修改config.py

```python
FOLDER_CONFIG = {
    'root_path': r'D:\your\cert\path',  # 直接修改
    # ...
}
```

## 4. 配置验证和帮助

### 自动配置验证

```python
def validate_config() -> Dict[str, Any]:
    """验证配置有效性"""
    issues = []
    
    # 检查文件夹路径
    folder_root = get_folder_root()
    if not os.path.exists(folder_root):
        issues.append(f"文件夹路径不存在: {folder_root}")
    
    # 检查并发配置
    if PERFORMANCE_CONFIG['max_concurrent_uploads'] <= 0:
        issues.append("并发上传数必须大于0")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'folder_root': folder_root,
        'api_url': get_api_url()
    }
```

### 配置信息显示

```python
def print_config_summary():
    """打印配置摘要"""
    print("📋 当前配置:")
    print(f"  🌐 API地址: {get_api_url()}")
    print(f"  📁 文件夹路径: {get_folder_root()}")
    print(f"  🗄️  MinIO服务: {MINIO_CONFIG['endpoint']}")
    print(f"  📦 MinIO存储桶: {MINIO_CONFIG['bucket_name']}")
    print(f"  ⚙️  并发配置: 上传{PERFORMANCE_CONFIG['max_concurrent_uploads']}线程, API{PERFORMANCE_CONFIG['max_concurrent_requests']}线程")
```

## 5. 测试和使用

### 测试脚本

创建了 `test_config.py` 和 `test_config.bat` 用于快速验证配置：

```python
# test_config.py
from config import validate_config, print_config_summary, create_sample_config_file

validation = validate_config()
if validation['valid']:
    print("✅ 配置验证通过")
    print_config_summary()
else:
    print("⚠️  配置验证有问题:")
    for issue in validation['issues']:
        print(f"  - {issue}")
```

### 使用流程

1. **首次设置**
   ```bash
   python config.py  # 生成示例配置文件
   # 编辑 batch_config.json
   ```

2. **验证配置**
   ```bash
   python test_config.py
   ```

3. **运行批量处理**
   ```bash
   python batch_add_cert_versions.py
   ```

## ✅ 改造成果

### 解决的问题

1. **✅ 消除硬编码**: 所有参数都可配置
2. **✅ 环境适配**: 支持开发/测试/生产环境
3. **✅ 安全性**: 敏感信息通过环境变量配置
4. **✅ 向后兼容**: 没有配置文件时使用默认值
5. **✅ 易于维护**: 无需修改代码，只需修改配置
6. **✅ 灵活性**: 支持多种配置方式

### 配置灵活性对比

| 配置项 | 原来 | 现在 |
|--------|------|------|
| 文件路径 | 硬编码固定路径 | 环境变量/配置文件 |
| MinIO配置 | 硬编码localhost | 可配置任意服务器 |
| 证件规格 | 硬编码固定值 | 可配置各种规格 |
| 并发数 | 硬编码5/3 | 可根据硬件调整 |
| 训练图片 | 硬编码文件名 | 可配置多种格式 |

### 使用便利性提升

| 场景 | 原来 | 现在 |
|------|------|------|
| 更换服务器 | 修改代码重新部署 | 修改配置文件 |
| 调整性能 | 修改代码重新部署 | 设置环境变量 |
| 不同环境 | 维护多个代码版本 | 使用不同配置文件 |
| 团队协作 | 每人修改自己的代码 | 共享代码，各自配置 |

## 📚 相关文档

- `README_CONFIG.md` - 详细配置使用说明
- `config.py` - 配置文件和工具函数
- `test_config.py` - 配置测试脚本
- `test_config.bat` - Windows测试批处理

## 🎉 总结

通过这次硬编码解耦改造，实现了：

1. **代码与配置分离**: 代码专注逻辑，配置专注参数
2. **多环境支持**: 一套代码适用于开发/测试/生产环境
3. **团队协作友好**: 每个开发者可以有自己的配置
4. **运维友好**: 部署时只需调整配置，无需修改代码
5. **安全性提升**: 敏感信息不再出现在代码中
6. **向后兼容**: 保证现有使用方式仍然有效

现在这个工具真正成为了一个可配置、可扩展、易维护的专业工具！ 