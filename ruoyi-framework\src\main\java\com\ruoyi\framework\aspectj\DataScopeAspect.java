package com.ruoyi.framework.aspectj;

import java.util.ArrayList;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect
{
    private static final Logger log = LoggerFactory.getLogger(DataScopeAspect.class);
    
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";
    
    /**
     * 上级部门及以下数据权限
     */
    public static final String DATA_SCOPE_PARENT_AND_CHILD = "6";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(),
                        controllerDataScope.userAlias());
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user 用户
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias)
    {
        log.info("开始构建数据权限SQL, 用户ID: {}, 部门ID: {}, 部门别名: {}, 用户别名: {}", 
                user.getUserId(), user.getDeptId(), deptAlias, userAlias);
                
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<String>();

        for (SysRole role : user.getRoles())
        {
            String dataScope = role.getDataScope();
            log.info("处理角色: {}, 数据权限类型: {}", role.getRoleName(), dataScope);
            
            if (!DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope))
            {
                log.info("跳过重复的数据权限类型: {}", dataScope);
                continue;
            }
            if (DATA_SCOPE_ALL.equals(dataScope))
            {
                log.info("用户拥有全部数据权限，清空SQL条件");
                sqlString = new StringBuilder();
                break;
            }
            else if (DATA_SCOPE_CUSTOM.equals(dataScope))
            {
                String sql = StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", 
                        deptAlias, role.getRoleId());
                log.info("添加自定义数据权限SQL: {}", sql);
                sqlString.append(sql);
            }
            else if (DATA_SCOPE_DEPT.equals(dataScope))
            {
                String sql = StringUtils.format(" OR {}.dept_id = {} ", deptAlias, user.getDeptId());
                log.info("添加部门数据权限SQL: {}", sql);
                sqlString.append(sql);
            }
            else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
            {
                String sql = StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                        deptAlias, user.getDeptId(), user.getDeptId());
                log.info("添加部门及以下数据权限SQL: {}", sql);
                sqlString.append(sql);
            }
            else if (DATA_SCOPE_SELF.equals(dataScope))
            {
                if (StringUtils.isNotBlank(userAlias))
                {
                    String sql = StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId());
                    log.info("添加仅本人数据权限SQL: {}", sql);
                    sqlString.append(sql);
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    String sql = StringUtils.format(" OR {}.dept_id = 0 ", deptAlias);
                    log.info("添加仅本人数据权限SQL(无用户别名): {}", sql);
                    sqlString.append(sql);
                }
            }
            else if (DATA_SCOPE_PARENT_AND_CHILD.equals(dataScope))
            {
                // // 查询用户所在部门的ancestors
                // String sql = StringUtils.format(
                //         " OR {}.dept_id IN ( " +
                //         "   SELECT d.dept_id " +
                //         "   FROM sys_dept d " +
                //         "   JOIN sys_dept ud ON ud.dept_id = {} " +
                //         "   WHERE d.dept_id = {} " +  // 当前部门
                //         "   OR find_in_set(d.dept_id, ud.ancestors) " +  // 上级部门
                //         "   OR find_in_set({}, d.ancestors) " +  // 下级部门
                //         ")",
                //         deptAlias, user.getDeptId(), user.getDeptId(), user.getDeptId());
                // log.info("添加上级部门及以下数据权限SQL: {}", sql);
                // 简化查询：只查询当前部门和其父部门
                  String sql = StringUtils.format(
            " OR {}.dept_id IN ( " +
            "   SELECT dept_id " +
            "   FROM sys_dept " +
            "   WHERE dept_id = {} " +  // 当前部门
            "   OR dept_id = (SELECT parent_id FROM sys_dept WHERE dept_id = {}) " +  // 父部门
            ")",
            deptAlias, user.getDeptId(), user.getDeptId());
                log.info("添加当前部门及父部门数据权限SQL: {}", sql);
                sqlString.append(sql);                
                // sqlString.append(sql);
            }
            conditions.add(dataScope);
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BaseEntity)
            {
                BaseEntity baseEntity = (BaseEntity) params;
                String finalSql = " AND (" + sqlString.substring(4) + ")";
                baseEntity.getParams().put(DATA_SCOPE, finalSql);
                log.info("最终数据权限SQL条件: {}", finalSql);
            }
        }
        else
        {
            log.info("未添加数据权限SQL条件，用户可查看所有数据");
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity)
        {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
            log.debug("清空数据权限SQL条件");
        }
    }
}
