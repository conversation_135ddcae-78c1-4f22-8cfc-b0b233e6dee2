<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    clearable
    filterable
    :disabled="disabled"
    @change="handleChange"
  >
    <el-option
      v-for="item in certTypeOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { getAllCertTypes } from '@/api/cert/certType'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  countryCode: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择证件类型'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const certTypeOptions = ref([])
const selectedValue = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedValue.value = newVal
})



// 监听内部值变化
watch(() => selectedValue.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理选择变化
const handleChange = (val) => {
  emit('change', val)
}

// 获取证件类型列表
const getCertTypeList = async () => {
  try {
    console.log('开始获取证件类型列表...')
    const res = await getAllCertTypes({})


    if (res.code === 200) {
      const dataList = res.data || res.rows || []
      // console.log('证件类型原始数据:', dataList)

      if (Array.isArray(dataList) && dataList.length > 0) {
        certTypeOptions.value = dataList.map(item => {
          // console.log('处理证件类型项:', item)
          return {
            value: item.zjlbdm,
            label: `${item.zjlbmc} (${item.zjlbdm})`
          }
        })
        // console.log('处理后的证件类型选项:', certTypeOptions.value)
      } else {
        console.warn('证件类型数据为空或不是数组')
        certTypeOptions.value = []
      }
    } else {
      console.error('证件类型API返回错误:', res.msg || res.message)
      certTypeOptions.value = []
    }
  } catch (error) {
    console.error('获取证件类型列表失败', error)
    if (error.response) {
      console.error('HTTP错误状态:', error.response.status)
      console.error('HTTP错误数据:', error.response.data)
    }
    certTypeOptions.value = []
  }
}

onMounted(() => {
  console.log('CertTypeSelect组件挂载, countryCode:', props.countryCode)
  // console.log('开始加载证件类型数据...')

  // 添加延时，确保页面渲染完成
  setTimeout(() => {
    console.log('延时后开始调用证件类型API...')
    getCertTypeList()
  }, 100)
})
</script>
