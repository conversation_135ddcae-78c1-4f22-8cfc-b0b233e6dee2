<template>
  <el-dialog
    v-model="visible"
    title="文件夹管理"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="folder-management">
      <!-- 版本信息 -->
      <el-card class="version-info-card" shadow="never">
        <template #header>
          <span>版本信息</span>
        </template>
        <el-descriptions :column="4" border>
          <el-descriptions-item label="版本代码">
            {{ versionInfo.versionCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="国家">
            {{ versionInfo.countryInfo?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="证件类型">
            {{ versionInfo.certInfo?.zjlbmc || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="发行年份">
            {{ versionInfo.issueYear || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 标准样本设置 -->
      <el-card class="standard-setting-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>标准样本设置</span>
            <div class="header-actions">
              <el-button
                v-if="!versionInfo.standardFolderId"
                type="success"
                size="small"
                :disabled="selectedFolders.length !== 1"
                @click="handleSetStandardFolder"
              >
                设为标准样本
              </el-button>
              <el-button
                v-else
                type="warning"
                size="small"
                @click="handleRemoveStandardFolder"
              >
                取消标准样本
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="versionInfo.standardFolderId" class="current-standard">
          <el-alert
            title="当前标准样本文件夹"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="standard-folder-info">
                <p><strong>文件夹:</strong> {{ getStandardFolderName() }}</p>
                <p><strong>文件夹ID:</strong> {{ versionInfo.standardFolderId }}</p>
              </div>
            </template>
          </el-alert>
        </div>
        <div v-else class="no-standard">
          <el-alert
            title="尚未设置标准样本文件夹"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>请从下方文件夹列表中选择一个文件夹作为标准样本</p>
            </template>
          </el-alert>
        </div>
      </el-card>

      <!-- 文件夹列表 -->
      <el-card class="folder-list-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>关联文件夹列表 ({{ folderList.length }})</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="handleRefresh">
                刷新列表
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="folderList"
          @selection-change="handleSelectionChange"
          stripe
          border
          max-height="400"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            :selectable="(row) => row.status === 'associated'"
          />

          <el-table-column label="文件夹名称" prop="folderName" show-overflow-tooltip>
            <template #default="scope">
              <div class="folder-name-cell">
                <el-link type="primary" @click="handleViewFolder(scope.row)">
                  {{ scope.row.folderName || scope.row.folderId }}
                </el-link>
                <el-tag
                  v-if="scope.row.folderId === versionInfo.standardFolderId"
                  type="success"
                  size="small"
                  style="margin-left: 8px;"
                >
                  标准样本
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" prop="status" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getFolderStatusType(scope.row.status)">
                {{ getFolderStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="审核状态" prop="reviewStatus" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getReviewStatusType(scope.row.reviewStatus)">
                {{ getReviewStatusText(scope.row.reviewStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="图片数量" width="100" align="center">
            <template #default="scope">
              {{ scope.row.imageCount || 0 }} 张
            </template>
          </el-table-column>

          <el-table-column label="文件夹路径" prop="filename" show-overflow-tooltip />

          <el-table-column label="创建时间" prop="createTime" width="160" align="center">
            <template #default="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleViewFolder(scope.row)"
              >
                查看详情
              </el-button>

              <el-button
                v-if="scope.row.status === 'associated' && scope.row.folderId !== versionInfo.standardFolderId"
                type="success"
                size="small"
                @click="handleSetAsStandard(scope.row)"
              >
                设为标准
              </el-button>

              <el-button
                v-if="scope.row.folderId === versionInfo.standardFolderId"
                type="warning"
                size="small"
                @click="handleRemoveStandardFolder"
              >
                取消标准
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="folderList.length === 0" class="empty-folders">
          <el-empty description="暂无关联文件夹">
            <el-button type="primary" @click="handleRefresh">
              刷新数据
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 操作说明 -->
      <el-card class="help-card" shadow="never">
        <template #header>
          <span>操作说明</span>
        </template>
        <div class="help-content">
          <el-alert
            title="标准样本文件夹说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul>
                <li>每个版本只能设置一个标准样本文件夹</li>
                <li>只有状态为"已关联"且"审核通过"的文件夹才能设为标准样本</li>
                <li>标准样本文件夹中的图片可以进行标注操作</li>
                <li>普通样本文件夹中的图片只能查看，不能标注</li>
                <li>更换标准样本时，原标准样本文件夹会自动变为普通样本</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

// 导入API
import { getFoldersByVersionId, setStandardFolder, removeStandardFolder } from '@/api/cert/version'

const router = useRouter()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  versionInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const folderList = ref([])
const selectedFolders = ref([])

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.versionInfo?.versionId) {
    loadFolderList()
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const loadFolderList = async () => {
  if (!props.versionInfo?.versionId) return

  loading.value = true
  try {
    const response = await getFoldersByVersionId(props.versionInfo.versionId)
    if (response.code === 200) {
      folderList.value = response.data || []
    } else {
      ElMessage.error(response.msg || '加载文件夹列表失败')
    }
  } catch (error) {
    console.error('加载文件夹列表失败:', error)
    ElMessage.error('加载文件夹列表失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  selectedFolders.value = []
}

const handleRefresh = () => {
  loadFolderList()
  emit('refresh')
}

const handleSelectionChange = (selection) => {
  selectedFolders.value = selection
}

const handleSetStandardFolder = async () => {
  if (selectedFolders.value.length !== 1) {
    ElMessage.warning('请选择一个文件夹作为标准样本')
    return
  }

  const folder = selectedFolders.value[0]
  await handleSetAsStandard(folder)
}

const handleSetAsStandard = async (folder) => {
  if (folder.status !== 'associated') {
    ElMessage.warning('只有已关联的文件夹才能设为标准样本')
    return
  }

  if (folder.reviewStatus !== 'approved') {
    ElMessage.warning('只有审核通过的文件夹才能设为标准样本')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将文件夹"${folder.folderName || folder.folderId}"设为标准样本吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await setStandardFolder(props.versionInfo.versionId, {
      folderId: folder.folderId
    })

    if (response.code === 200) {
      ElMessage.success('设置标准样本成功')
      props.versionInfo.standardFolderId = folder.folderId
      loadFolderList()
      emit('refresh')
    } else {
      ElMessage.error(response.msg || '设置标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置标准样本失败:', error)
      ElMessage.error('设置标准样本失败')
    }
  }
}

const handleRemoveStandardFolder = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前标准样本设置吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await removeStandardFolder(props.versionInfo.versionId)

    if (response.code === 200) {
      ElMessage.success('取消标准样本成功')
      props.versionInfo.standardFolderId = null
      loadFolderList()
      emit('refresh')
    } else {
      ElMessage.error(response.msg || '取消标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消标准样本失败:', error)
      ElMessage.error('取消标准样本失败')
    }
  }
}

const handleViewFolder = (folder) => {
  router.push({
    path: `/cert/folder/${folder.folderId}`
  })
}

const getStandardFolderName = () => {
  const standardFolder = folderList.value.find(
    folder => folder.folderId === props.versionInfo.standardFolderId
  )
  return standardFolder?.folderName || standardFolder?.folderId || '未知'
}

// 工具方法
const getFolderStatusType = (status) => {
  const statusMap = {
    'associated': 'success',
    'unassociated': 'warning',
    'processing': 'info'
  }
  return statusMap[status] || 'info'
}

const getFolderStatusText = (status) => {
  const statusMap = {
    'associated': '已关联',
    'unassociated': '未关联',
    'processing': '处理中'
  }
  return statusMap[status] || '未知'
}

const getReviewStatusType = (status) => {
  const statusMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return statusMap[status] || 'info'
}

const getReviewStatusText = (status) => {
  const statusMap = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未审核'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.folder-management {
  max-height: 70vh;
  overflow-y: auto;
}

.version-info-card,
.standard-setting-card,
.folder-list-card,
.help-card {
  margin-bottom: 20px;
}

.help-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.current-standard,
.no-standard {
  margin-bottom: 16px;
}

.standard-folder-info p {
  margin: 4px 0;
}

.folder-name-cell {
  display: flex;
  align-items: center;
}

.empty-folders {
  text-align: center;
  padding: 20px;
}

.help-content ul {
  margin: 0;
  padding-left: 20px;
}

.help-content li {
  margin: 4px 0;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
