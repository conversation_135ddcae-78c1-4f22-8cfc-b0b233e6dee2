package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.service.ICertTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 证件类别表Controller
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/cert/type")
public class CertTypeController extends BaseController
{
    @Autowired
    private ICertTypeService certTypeService;

    /**
     * 查询证件类别表列表
     */
    @PreAuthorize("@ss.hasPermi('cert:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertType certType)
    {
        startPage();
        List<CertType> list = certTypeService.selectCertTypeList(certType);
        return getDataTable(list);
    }


    /**
     * 查询证件类别表列表（不分页）
     */
    @GetMapping("/listAll")
    public AjaxResult listAll(CertType certType)
    {
        List<CertType> list = certTypeService.selectCertTypeList(certType);
        logger.info("查询证件类型列表，共 {} 条数据", list.size());
        return AjaxResult.success(list);
    }

    /**
     * 导出证件类别表列表
     */
    @PreAuthorize("@ss.hasPermi('cert:type:export')")
    @Log(title = "证件类别表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertType certType)
    {
        List<CertType> list = certTypeService.selectCertTypeList(certType);
        ExcelUtil<CertType> util = new ExcelUtil<CertType>(CertType.class);
        util.exportExcel(response, list, "证件类别表数据");
    }

    /**
     * 获取证件类别表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cert:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(certTypeService.selectCertTypeById(id));
    }

    /**
     * 新增证件类别表
     */
    @PreAuthorize("@ss.hasPermi('cert:type:add')")
    @Log(title = "证件类别表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CertType certType)
    {
        return toAjax(certTypeService.insertCertType(certType));
    }

    /**
     * 修改证件类别表
     */
    @PreAuthorize("@ss.hasPermi('cert:type:edit')")
    @Log(title = "证件类别表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CertType certType)
    {
        return toAjax(certTypeService.updateCertType(certType));
    }

    /**
     * 删除证件类别表
     */
    @PreAuthorize("@ss.hasPermi('cert:type:remove')")
    @Log(title = "证件类别表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(certTypeService.deleteCertTypeByIds(ids));
    }
}
