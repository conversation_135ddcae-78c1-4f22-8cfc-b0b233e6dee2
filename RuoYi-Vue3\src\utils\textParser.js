/**
 * 智能文本解析工具
 * greenfox - 新增：解析描述文本中的版本关键信息 - 2025-01-15
 * 
 * 用于从文本描述中自动提取：
 * - 国家名称
 * - 证件类型
 * - 签发地
 * - 签发时间/年份
 * 
 * 示例：
 * "2021年签发的俄罗斯签证" -> { country: "俄罗斯", certType: "签证", issueYear: "2021" }
 * "2023年莫斯科签发的俄罗斯护照" -> { country: "俄罗斯", certType: "护照", issueYear: "2023", issuePlace: "莫斯科" }
 */

// greenfox - 修改：增强国家映射，包含ID和代码 - 2025-01-15
const COUNTRY_MAPPING = {
  // 主要国家
  '俄罗斯': { code: 'RUS', id: 1, nameEn: 'Russia' },
  '中国': { code: 'CHN', id: 2, nameEn: 'China' },
  '美国': { code: 'USA', id: 3, nameEn: 'United States' },
  '英国': { code: 'GBR', id: 4, nameEn: 'United Kingdom' },
  '德国': { code: 'DEU', id: 5, nameEn: 'Germany' },
  '法国': { code: 'FRA', id: 6, nameEn: 'France' },
  '日本': { code: 'JPN', id: 7, nameEn: 'Japan' },
  '韩国': { code: 'KOR', id: 8, nameEn: 'South Korea' },
  '意大利': { code: 'ITA', id: 9, nameEn: 'Italy' },
  '西班牙': { code: 'ESP', id: 10, nameEn: 'Spain' },
  '加拿大': { code: 'CAN', id: 11, nameEn: 'Canada' },
  '澳大利亚': { code: 'AUS', id: 12, nameEn: 'Australia' },
  '新西兰': { code: 'NZL', id: 13, nameEn: 'New Zealand' },
  '荷兰': { code: 'NLD', id: 14, nameEn: 'Netherlands' },
  '比利时': { code: 'BEL', id: 15, nameEn: 'Belgium' },
  '瑞士': { code: 'CHE', id: 16, nameEn: 'Switzerland' },
  '瑞典': { code: 'SWE', id: 17, nameEn: 'Sweden' },
  '挪威': { code: 'NOR', id: 18, nameEn: 'Norway' },
  '丹麦': { code: 'DNK', id: 19, nameEn: 'Denmark' },
  '芬兰': { code: 'FIN', id: 20, nameEn: 'Finland' },
  
  // 亚洲国家
  '印度': { code: 'IND', id: 21, nameEn: 'India' },
  '泰国': { code: 'THA', id: 22, nameEn: 'Thailand' },
  '越南': { code: 'VNM', id: 23, nameEn: 'Vietnam' },
  '新加坡': { code: 'SGP', id: 24, nameEn: 'Singapore' },
  '马来西亚': { code: 'MYS', id: 25, nameEn: 'Malaysia' },
  '印度尼西亚': { code: 'IDN', id: 26, nameEn: 'Indonesia' },
  '菲律宾': { code: 'PHL', id: 27, nameEn: 'Philippines' },
  '巴基斯坦': { code: 'PAK', id: 28, nameEn: 'Pakistan' },
  '孟加拉': { code: 'BGD', id: 29, nameEn: 'Bangladesh' },
  '缅甸': { code: 'MMR', id: 30, nameEn: 'Myanmar' },
  '柬埔寨': { code: 'KHM', id: 31, nameEn: 'Cambodia' },
  '老挝': { code: 'LAO', id: 32, nameEn: 'Laos' },
  
  // 中东国家
  '沙特阿拉伯': { code: 'SAU', id: 33, nameEn: 'Saudi Arabia' },
  '阿联酋': { code: 'ARE', id: 34, nameEn: 'United Arab Emirates' },
  '以色列': { code: 'ISR', id: 35, nameEn: 'Israel' },
  '土耳其': { code: 'TUR', id: 36, nameEn: 'Turkey' },
  '伊朗': { code: 'IRN', id: 37, nameEn: 'Iran' },
  '伊拉克': { code: 'IRQ', id: 38, nameEn: 'Iraq' },
  '科威特': { code: 'KWT', id: 39, nameEn: 'Kuwait' },
  '卡塔尔': { code: 'QAT', id: 40, nameEn: 'Qatar' },
  
  // 欧洲其他国家
  '奥地利': { code: 'AUT', id: 41, nameEn: 'Austria' },
  '波兰': { code: 'POL', id: 42, nameEn: 'Poland' },
  '捷克': { code: 'CZE', id: 43, nameEn: 'Czech Republic' },
  '匈牙利': { code: 'HUN', id: 44, nameEn: 'Hungary' },
  '希腊': { code: 'GRC', id: 45, nameEn: 'Greece' },
  '葡萄牙': { code: 'PRT', id: 46, nameEn: 'Portugal' },
  '爱尔兰': { code: 'IRL', id: 47, nameEn: 'Ireland' },
  '冰岛': { code: 'ISL', id: 48, nameEn: 'Iceland' },
  
  // 美洲国家
  '巴西': { code: 'BRA', id: 49, nameEn: 'Brazil' },
  '墨西哥': { code: 'MEX', id: 50, nameEn: 'Mexico' },
  '阿根廷': { code: 'ARG', id: 51, nameEn: 'Argentina' },
  '智利': { code: 'CHL', id: 52, nameEn: 'Chile' },
  '哥伦比亚': { code: 'COL', id: 53, nameEn: 'Colombia' },
  '秘鲁': { code: 'PER', id: 54, nameEn: 'Peru' },
  
  // 非洲国家
  '南非': { code: 'ZAF', id: 55, nameEn: 'South Africa' },
  '埃及': { code: 'EGY', id: 56, nameEn: 'Egypt' },
  '尼日利亚': { code: 'NGA', id: 57, nameEn: 'Nigeria' },
  '肯尼亚': { code: 'KEN', id: 58, nameEn: 'Kenya' },
  '摩洛哥': { code: 'MAR', id: 59, nameEn: 'Morocco' },
  
  // 其他国家
  '乌克兰': { code: 'UKR', id: 60, nameEn: 'Ukraine' },
  '白俄罗斯': { code: 'BLR', id: 61, nameEn: 'Belarus' },
  '哈萨克斯坦': { code: 'KAZ', id: 62, nameEn: 'Kazakhstan' },
  '乌兹别克斯坦': { code: 'UZB', id: 63, nameEn: 'Uzbekistan' }
};

// 证件类型映射表
const CERT_TYPE_MAPPING = {
  '护照': 'PASSPORT',
  '签证': 'VISA', 
  '身份证': 'ID_CARD',
  '驾照': 'DRIVING_LICENSE',
  '驾驶证': 'DRIVING_LICENSE',
  '居住证': 'RESIDENCE_PERMIT',
  '工作许可': 'WORK_PERMIT'
};

// 城市映射表
const CITY_MAPPING = {
  '莫斯科': { country: 'RUS', region: '莫斯科州' },
  '圣彼得堡': { country: 'RUS', region: '列宁格勒州' },
  '北京': { country: 'CHN', province: '北京市' },
  '上海': { country: 'CHN', province: '上海市' },
  '纽约': { country: 'USA', state: '纽约州' },
  '洛杉矶': { country: 'USA', state: '加利福尼亚州' }
};

/**
 * 解析版本信息
 */
export function parseVersionInfo(text) {
  if (!text || typeof text !== 'string') {
    return {
      country: null,
      countryCode: null,
      countryId: null,
      countryNameEn: null,
      certType: null,
      certTypeCode: null,
      issueYear: null,
      issuePlace: null,
      originalText: text,
      confidence: 0
    };
  }

  const result = {
    country: null,
    countryCode: null,
    countryId: null,
    countryNameEn: null,
    certType: null,
    certTypeCode: null,
    issueYear: null,
    issuePlace: null,
    originalText: text.trim(),
    confidence: 0
  };

  // 解析模式 - greenfox 修改：增加文件夹名称格式支持 - 2025-01-15
  const patterns = [
    // "2021年签发的俄罗斯签证"
    {
      regex: /(\d{4})年\s*签发的\s*(.+?)\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)/,
      extract: (match) => ({
        issueYear: match[1],
        country: match[2].trim(),
        certType: match[3]
      }),
      confidence: 0.9
    },
    
    // "2021年莫斯科签发的俄罗斯签证"
    {
      regex: /(\d{4})年\s*(.+?)\s*签发的\s*(.+?)\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)/,
      extract: (match) => ({
        issueYear: match[1],
        issuePlace: match[2].trim(),
        country: match[3].trim(),
        certType: match[4]
      }),
      confidence: 0.95
    },
    
    // "俄罗斯签证2021年"
    {
      regex: /(.+?)\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)\s*(\d{4})年?/,
      extract: (match) => ({
        country: match[1].trim(),
        certType: match[2],
        issueYear: match[3]
      }),
      confidence: 0.85
    },
    
    // "俄罗斯签证2021年莫斯科"
    {
      regex: /(.+?)\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)\s*(\d{4})年?\s*(.+)/,
      extract: (match) => ({
        country: match[1].trim(),
        certType: match[2],
        issueYear: match[3],
        issuePlace: match[4].trim()
      }),
      confidence: 0.9
    },
    
    // "俄罗斯_签证_2021" (下划线分隔)
    {
      regex: /(.+?)_\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)_\s*(\d{4})/,
      extract: (match) => ({
        country: match[1].trim(),
        certType: match[2],
        issueYear: match[3]
      }),
      confidence: 0.8
    },
    
    // "RUS_VISA_2021" (英文+下划线)
    {
      regex: /([A-Z]{3})_([A-Z_]+)_(\d{4})/,
      extract: (match) => ({
        countryCode: match[1],
        certTypeCode: match[2],
        issueYear: match[3]
      }),
      confidence: 0.85
    },
    
    // "俄罗斯签证" (简单格式)
    {
      regex: /(.+?)\s*(护照|签证|身份证|驾照|驾驶证|居住证|工作许可)$/,
      extract: (match) => ({
        country: match[1].trim(),
        certType: match[2]
      }),
      confidence: 0.7
    }
  ];

  // 尝试匹配
  for (const pattern of patterns) {
    const match = text.match(pattern.regex);
    if (match) {
      const extracted = pattern.extract(match);
      Object.assign(result, extracted);
      result.confidence = pattern.confidence;
      break;
    }
  }

  // 补充信息映射
  if (result.confidence > 0) {
    // 如果有国家名称，映射到代码和ID
    if (result.country && COUNTRY_MAPPING[result.country]) {
      const countryInfo = COUNTRY_MAPPING[result.country];
      result.countryCode = countryInfo.code;
      result.countryId = countryInfo.id;
      result.countryNameEn = countryInfo.nameEn;
    }
    
    // 如果有国家代码但没有国家名称，反向映射
    if (result.countryCode && !result.country) {
      for (const [countryName, countryInfo] of Object.entries(COUNTRY_MAPPING)) {
        if (countryInfo.code === result.countryCode) {
          result.country = countryName;
          result.countryId = countryInfo.id;
          result.countryNameEn = countryInfo.nameEn;
          break;
        }
      }
    }
    
    // 映射证件类型代码
    if (result.certType && CERT_TYPE_MAPPING[result.certType]) {
      result.certTypeCode = CERT_TYPE_MAPPING[result.certType];
    }
    
    // 如果有证件类型代码但没有中文名称，反向映射
    if (result.certTypeCode && !result.certType) {
      for (const [certName, certCode] of Object.entries(CERT_TYPE_MAPPING)) {
        if (certCode === result.certTypeCode) {
          result.certType = certName;
          break;
        }
      }
    }
  }

  // 如果没有匹配到，分别提取
  if (result.confidence < 0.5) {
    const yearMatch = text.match(/(\d{4})年?/);
    if (yearMatch) {
      result.issueYear = yearMatch[1];
      result.confidence += 0.2;
    }
    
    for (const [countryName, countryInfo] of Object.entries(COUNTRY_MAPPING)) {
      if (text.includes(countryName)) {
        result.country = countryName;
        result.countryCode = countryInfo.code;
        result.countryId = countryInfo.id;
        result.countryNameEn = countryInfo.nameEn;
        result.confidence += 0.3;
        break;
      }
    }
    
    for (const [certName, certCode] of Object.entries(CERT_TYPE_MAPPING)) {
      if (text.includes(certName)) {
        result.certType = certName;
        result.certTypeCode = certCode;
        result.confidence += 0.3;
        break;
      }
    }
    
    for (const [cityName, cityInfo] of Object.entries(CITY_MAPPING)) {
      if (text.includes(cityName)) {
        result.issuePlace = cityName;
        result.confidence += 0.2;
        break;
      }
    }
  }

  // 完善映射
  if (result.country && !result.countryCode) {
    const countryInfo = COUNTRY_MAPPING[result.country];
    if (countryInfo) {
      result.countryCode = countryInfo.code;
      result.countryId = countryInfo.id;
      result.countryNameEn = countryInfo.nameEn;
    }
  }
  
  if (result.certType && !result.certTypeCode) {
    result.certTypeCode = CERT_TYPE_MAPPING[result.certType];
  }

  return result;
}

/**
 * 检查版本重复
 */
export function checkVersionDuplicate(parsedInfo, existingVersions = []) {
  const duplicates = [];
  
  for (const version of existingVersions) {
    let score = 0;
    let matchedFields = [];
    
    if (parsedInfo.countryCode && version.countryCode === parsedInfo.countryCode) {
      score += 0.4;
      matchedFields.push('country');
    }
    
    if (parsedInfo.certTypeCode && version.certType === parsedInfo.certTypeCode) {
      score += 0.3;
      matchedFields.push('certType');
    }
    
    if (parsedInfo.issueYear && version.issueYear === parsedInfo.issueYear) {
      score += 0.2;
      matchedFields.push('issueYear');
    }
    
    if (parsedInfo.issuePlace && version.description && 
        version.description.includes(parsedInfo.issuePlace)) {
      score += 0.1;
      matchedFields.push('issuePlace');
    }
    
    if (score >= 0.9) {
      duplicates.push({
        version,
        score,
        matchedFields,
        type: 'duplicate'
      });
    }
  }
  
  return {
    hasDuplicates: duplicates.length > 0,
    duplicates,
    isNewVersion: duplicates.length === 0
  };
}

/**
 * 生成版本名称
 */
export function generateVersionName(parsedInfo) {
  const parts = [];
  
  if (parsedInfo.country) {
    parts.push(parsedInfo.country);
  }
  
  if (parsedInfo.certType) {
    parts.push(parsedInfo.certType);
  }
  
  if (parsedInfo.issueYear) {
    parts.push(`${parsedInfo.issueYear}年版`);
  }
  
  if (parsedInfo.issuePlace) {
    parts.push(`(${parsedInfo.issuePlace})`);
  }
  
  return parts.join(' ') || '未知版本';
}

// 导出映射表
export {
  COUNTRY_MAPPING,
  CERT_TYPE_MAPPING,
  CITY_MAPPING
}; 