<template>
  <div 
    class="folder-list-item"
    :class="{ 
      'is-checked': isChecked,
      'is-disabled': disabled,
      'is-selectable': showCheckbox
    }"
    @click="handleItemClick"
  >
    <!-- 复选框 -->
    <div v-if="showCheckbox" class="checkbox-wrapper" @click.stop="handleCheckboxClick">
      <el-checkbox 
        :model-value="isChecked"
        :disabled="disabled"
        @change="handleCheckboxChange"
      />
    </div>

    <!-- 缩略图 -->
    <div class="thumbnail-wrapper">
      <el-image
        v-if="thumbnailUrl"
        :src="thumbnailUrl"
        :preview-src-list="[thumbnailUrl]"
        fit="cover"
        class="folder-thumbnail"
      >
        <template #error>
          <div class="thumbnail-error">
            <el-icon><Folder /></el-icon>
          </div>
        </template>
      </el-image>
      <div v-else class="thumbnail-placeholder">
        <el-icon><Folder /></el-icon>
      </div>
    </div>

    <!-- 文件夹信息 -->
    <div class="folder-info">
      <div class="folder-name" :title="folderName">
        {{ folderName }}
      </div>
      
      <!-- 标准样本徽章 -->
      <div v-if="showStandardBadge" class="standard-badge">
        <el-tag type="success" size="small">
          <el-icon><Star /></el-icon>
          标准样本
        </el-tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <!-- 比对按钮 -->
      <el-button
        v-if="showCompareButton"
        type="primary"
        size="small"
        :disabled="disabled"
        @click.stop="handleCompareClick"
      >
        <el-icon><Compare /></el-icon>
        比对
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Folder, Star, Compare } from '@element-plus/icons-vue'
import type { FolderListItemProps, FolderListItemEmits } from './types'

// 定义Props
const props = withDefaults(defineProps<FolderListItemProps>(), {
  showCheckbox: false,
  isChecked: false,
  showCompareButton: false,
  showStandardBadge: false,
  disabled: false
})

// 定义Emits
const emit = defineEmits<FolderListItemEmits>()

// 处理列表项点击
const handleItemClick = () => {
  if (!props.disabled) {
    emit('item-clicked')
  }
}

// 处理复选框点击
const handleCheckboxClick = (event: Event) => {
  event.stopPropagation()
}

// 处理复选框变化
const handleCheckboxChange = (checked: boolean) => {
  emit('check-change', checked)
}

// 处理比对按钮点击
const handleCompareClick = () => {
  emit('compare-clicked')
}
</script>

<style scoped lang="scss">
.folder-list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  cursor: pointer;
  gap: 12px;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }

  &.is-checked {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  &.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      border-color: #e4e7ed;
      box-shadow: none;
    }
  }

  &.is-selectable {
    cursor: pointer;
  }
}

.checkbox-wrapper {
  flex-shrink: 0;
}

.thumbnail-wrapper {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.folder-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-error,
.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

.folder-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.standard-badge {
  align-self: flex-start;
}

.actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .folder-list-item {
    padding: 8px 12px;
    gap: 8px;
  }

  .thumbnail-wrapper {
    width: 50px;
    height: 50px;
  }

  .folder-name {
    font-size: 13px;
  }
}
</style> 