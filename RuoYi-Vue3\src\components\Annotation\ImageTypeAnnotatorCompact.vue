<template>
  <div class="image-type-annotator-compact">
    <el-card shadow="never" class="compact-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">图片类型</span>
          <el-button 
            v-if="canEdit && hasChanges"
            type="primary" 
            size="small"
            @click="handleSave"
            :loading="saving"
          >
            保存
          </el-button>
        </div>
      </template>

      <!-- 当前类型显示 -->
      <div class="current-type" v-if="imageInfo">
        <div class="type-display">
          <el-tag 
            :type="getImageTypeColor(imageInfo.imageType)"
            size="small"
          >
            {{ getImageTypeLabel(imageInfo.imageType) }}
          </el-tag>
          <el-tag 
            :type="isAnnotatableType(imageInfo.imageType) ? 'success' : 'danger'"
            size="small"
            effect="plain"
          >
            {{ isAnnotatableType(imageInfo.imageType) ? '可标注' : '仅查看' }}
          </el-tag>
        </div>
      </div>

      <!-- 类型选择 -->
      <div class="type-selection" v-if="canEdit">
        <el-select 
          v-model="selectedImageType" 
          placeholder="选择图片类型"
          size="small"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in imageTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <div class="option-content">
              <span class="option-label">{{ type.label }}</span>
              <el-tag 
                :type="type.annotatable ? 'success' : 'info'"
                size="small"
                effect="plain"
              >
                {{ type.annotatable ? '可标注' : '仅查看' }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 只读模式 -->
      <div class="readonly-mode" v-else>
        <el-text type="info" size="small">
          {{ readonlyReason }}
        </el-text>
      </div>

      <!-- 类型说明（可折叠） -->
      <div class="type-help">
        <el-collapse size="small">
          <el-collapse-item title="类型说明" name="help">
            <div class="help-content">
              <div class="help-item" v-for="type in imageTypes" :key="type.value">
                <div class="help-title">
                  <el-tag size="small" :type="type.annotatable ? 'success' : 'info'">
                    {{ type.label }}
                  </el-tag>
                </div>
                <div class="help-desc">{{ type.description }}</div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface ImageInfo {
  imageId: string
  imageName?: string
  imageType: string
  sampleType?: string
  isAnnotatable?: boolean
}

interface Props {
  /** 图片信息 */
  imageInfo?: ImageInfo
  /** 是否可编辑 */
  canEdit?: boolean
  /** 只读原因 */
  readonlyReason?: string
  /** 保存中状态 */
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: false,
  readonlyReason: '当前用户无编辑权限',
  saving: false
})

// Emits
const emit = defineEmits<{
  save: [imageType: string]
  typeChange: [imageType: string]
}>()

// 响应式数据
const selectedImageType = ref('')

// 图片类型选项
const imageTypes = [
  {
    value: 'VISIBLE_DATA_PAGE',
    label: '可见光资料页',
    description: '正常光线下的证件资料页',
    annotatable: true
  },
  {
    value: 'INFRARED_DATA_PAGE', 
    label: '红外资料页',
    description: '红外光下的证件资料页',
    annotatable: true
  },
  {
    value: 'ULTRAVIOLET_DATA_PAGE',
    label: '紫外资料页', 
    description: '紫外光下的证件资料页',
    annotatable: true
  },
  {
    value: 'OTHER',
    label: '其他类型',
    description: '证件封面、背面等其他类型',
    annotatable: false
  }
]

// 计算属性
const hasChanges = computed(() => {
  return selectedImageType.value !== props.imageInfo?.imageType
})

// 方法
const isAnnotatableType = (type: string) => {
  return ['VISIBLE_DATA_PAGE', 'INFRARED_DATA_PAGE', 'ULTRAVIOLET_DATA_PAGE'].includes(type)
}

const getImageTypeLabel = (type: string) => {
  const typeOption = imageTypes.find(option => option.value === type)
  return typeOption?.label || type
}

const getImageTypeColor = (type: string) => {
  if (isAnnotatableType(type)) {
    return 'success'
  }
  return 'info'
}

const handleTypeChange = (value: string) => {
  emit('typeChange', value)
}

const handleSave = () => {
  if (!hasChanges.value) {
    ElMessage.info('图片类型未发生变化')
    return
  }
  
  emit('save', selectedImageType.value)
}

// 监听图片信息变化
watch(() => props.imageInfo, (newImageInfo) => {
  if (newImageInfo) {
    selectedImageType.value = newImageInfo.imageType || 'OTHER'
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.image-type-annotator-compact {
  .compact-card {
    margin-bottom: 16px;
    
    :deep(.el-card__header) {
      padding: 12px 16px;
    }
    
    :deep(.el-card__body) {
      padding: 12px 16px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-weight: 500;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }

  .current-type {
    margin-bottom: 12px;
    
    .type-display {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .type-selection {
    margin-bottom: 12px;
    
    .option-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      
      .option-label {
        flex: 1;
      }
    }
  }

  .readonly-mode {
    margin-bottom: 12px;
  }

  .type-help {
    :deep(.el-collapse) {
      border: none;
      
      .el-collapse-item__header {
        padding-left: 0;
        font-size: 12px;
        height: 32px;
        line-height: 32px;
        border-bottom: 1px solid var(--el-border-color-lighter);
      }
      
      .el-collapse-item__content {
        padding-bottom: 8px;
      }
    }
    
    .help-content {
      .help-item {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .help-title {
          margin-bottom: 4px;
        }
        
        .help-desc {
          font-size: 11px;
          color: var(--el-text-color-regular);
          line-height: 1.3;
          padding-left: 8px;
        }
      }
    }
  }
}
</style>
