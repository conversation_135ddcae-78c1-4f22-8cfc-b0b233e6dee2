package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 证件类别表对象 cert_type
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public class CertType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 证件种类代码 */
    @Excel(name = "证件种类代码")
    private String zjlbdm;

    /** 证件种类名称 */
    @Excel(name = "证件种类名称")
    private String zjlbmc;

    /** 前台使用标记 */
    @Excel(name = "前台使用标记", readConverterExp = "1=可用,0=禁用")
    private String sybj;

    /** 证件简称 */
    @Excel(name = "证件简称")
    private String zjjc;

    /** 国籍属于 */
    @Excel(name = "国籍属于")
    private String gjsy;

    /** 国籍不属于 */
    @Excel(name = "国籍不属于")
    private String gjsy0;



    /** 因公因私标志 */
    @Excel(name = "因公因私标志", readConverterExp = "1=因公,2=因私")
    private String bz;

    private String zyType;

    public void setZyType(String zyType) {
		this.zyType = zyType;
	}

    public String getZyType(){
        return zyType;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setZjlbdm(String zjlbdm) 
    {
        this.zjlbdm = zjlbdm;
    }

    public String getZjlbdm() 
    {
        return zjlbdm;
    }

    public void setZjlbmc(String zjlbmc) 
    {
        this.zjlbmc = zjlbmc;
    }

    public String getZjlbmc() 
    {
        return zjlbmc;
    }

    public void setSybj(String sybj) 
    {
        this.sybj = sybj;
    }

    public String getSybj() 
    {
        return sybj;
    }

    public void setZjjc(String zjjc) 
    {
        this.zjjc = zjjc;
    }

    public String getZjjc() 
    {
        return zjjc;
    }

    public void setGjsy(String gjsy) 
    {
        this.gjsy = gjsy;
    }

    public String getGjsy() 
    {
        return gjsy;
    }

    public void setGjsy0(String gjsy0) 
    {
        this.gjsy0 = gjsy0;
    }

    public String getGjsy0() 
    {
        return gjsy0;
    }

    public void setBz(String bz) 
    {
        this.bz = bz;
    }

    public String getBz() 
    {
        return bz;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("zjlbdm", getZjlbdm())
            .append("zjlbmc", getZjlbmc())
            .append("sybj", getSybj())
            .append("zjjc", getZjjc())
            .append("gjsy", getGjsy())
            .append("gjsy0", getGjsy0())
            .append("bz", getBz())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
