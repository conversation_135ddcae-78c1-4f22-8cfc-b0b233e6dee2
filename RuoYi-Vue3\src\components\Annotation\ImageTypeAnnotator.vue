<template>
  <div class="image-type-annotator">
    <el-card shadow="never" class="type-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">图片类型标注</span>
          <el-button 
            v-if="canEdit && hasChanges"
            type="primary" 
            size="small"
            @click="handleSave"
            :loading="saving"
          >
            <el-icon><DocumentAdd /></el-icon>
            保存类型
          </el-button>
        </div>
      </template>

      <!-- 当前图片信息 -->
      <div class="image-info" v-if="imageInfo">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="图片名称">
            {{ imageInfo.imageName || imageInfo.imageId }}
          </el-descriptions-item>
          <el-descriptions-item label="当前类型">
            <el-tag 
              :type="getImageTypeColor(imageInfo.imageType)"
              size="small"
            >
              {{ getImageTypeLabel(imageInfo.imageType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否可标注">
            <el-tag 
              :type="isAnnotatableType(imageInfo.imageType) ? 'success' : 'danger'"
              size="small"
            >
              {{ isAnnotatableType(imageInfo.imageType) ? '可标注' : '仅查看' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="样本类型">
            <el-tag 
              :type="imageInfo.sampleType === 'standard' ? 'warning' : 'info'"
              size="small"
            >
              {{ imageInfo.sampleType === 'standard' ? '标准样本' : '普通样本' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 类型选择 -->
      <div class="type-selection" v-if="canEdit">
        <div class="selection-header">
          <h4>选择图片类型</h4>
          <el-text type="info" size="small">
            只有可标注类型的图片才能进行详细标注
          </el-text>
        </div>

        <el-radio-group 
          v-model="selectedImageType" 
          class="type-radio-group"
          @change="handleTypeChange"
        >
          <el-radio 
            v-for="type in imageTypes"
            :key="type.value"
            :label="type.value"
            :class="{ 'annotatable-type': type.annotatable }"
          >
            <div class="radio-content">
              <div class="type-info">
                <span class="type-label">{{ type.label }}</span>
                <el-tag 
                  :type="type.annotatable ? 'success' : 'info'"
                  size="small"
                  effect="plain"
                >
                  {{ type.annotatable ? '可标注' : '仅查看' }}
                </el-tag>
              </div>
              <div class="type-description">
                {{ type.description }}
              </div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 只读模式 -->
      <div class="readonly-mode" v-else>
        <el-alert
          title="只读模式"
          :description="readonlyReason"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 类型说明 -->
      <div class="type-explanation">
        <el-collapse>
          <el-collapse-item title="图片类型说明" name="explanation">
            <div class="explanation-content">
              <div class="type-item">
                <h5>可见光资料页 (VISIBLE_DATA_PAGE)</h5>
                <p>在正常光线下拍摄的证件资料页，包含基本的个人信息和证件信息。</p>
              </div>
              <div class="type-item">
                <h5>红外资料页 (INFRARED_DATA_PAGE)</h5>
                <p>使用红外光拍摄的证件资料页，用于检测隐藏的安全特征。</p>
              </div>
              <div class="type-item">
                <h5>紫外资料页 (ULTRAVIOLET_DATA_PAGE)</h5>
                <p>使用紫外光拍摄的证件资料页，用于显示紫外荧光安全特征。</p>
              </div>
              <div class="type-item">
                <h5>其他类型 (OTHER)</h5>
                <p>不属于上述三种类型的图片，如证件封面、背面等，仅供查看。</p>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentAdd } from '@element-plus/icons-vue'

// Props
interface ImageInfo {
  imageId: string
  imageName?: string
  imageType: string
  sampleType?: string
  isAnnotatable?: boolean
}

interface Props {
  /** 图片信息 */
  imageInfo?: ImageInfo
  /** 是否可编辑 */
  canEdit?: boolean
  /** 只读原因 */
  readonlyReason?: string
  /** 保存中状态 */
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: false,
  readonlyReason: '当前用户无编辑权限',
  saving: false
})

// Emits
const emit = defineEmits<{
  save: [imageType: string]
  typeChange: [imageType: string]
}>()

// 响应式数据
const selectedImageType = ref('')

// 图片类型选项
const imageTypes = [
  {
    value: 'VISIBLE_DATA_PAGE',
    label: '可见光资料页',
    description: '正常光线下的证件资料页，包含基本信息',
    annotatable: true
  },
  {
    value: 'INFRARED_DATA_PAGE', 
    label: '红外资料页',
    description: '红外光下的证件资料页，显示隐藏安全特征',
    annotatable: true
  },
  {
    value: 'ULTRAVIOLET_DATA_PAGE',
    label: '紫外资料页', 
    description: '紫外光下的证件资料页，显示荧光安全特征',
    annotatable: true
  },
  {
    value: 'OTHER',
    label: '其他类型',
    description: '证件封面、背面等其他类型图片',
    annotatable: false
  }
]

// 计算属性
const hasChanges = computed(() => {
  return selectedImageType.value !== props.imageInfo?.imageType
})

// 方法
const isAnnotatableType = (type: string) => {
  return ['VISIBLE_DATA_PAGE', 'INFRARED_DATA_PAGE', 'ULTRAVIOLET_DATA_PAGE'].includes(type)
}

const getImageTypeLabel = (type: string) => {
  const typeOption = imageTypes.find(option => option.value === type)
  return typeOption?.label || type
}

const getImageTypeColor = (type: string) => {
  if (isAnnotatableType(type)) {
    return 'success'
  }
  return 'info'
}

const handleTypeChange = (value: string) => {
  emit('typeChange', value)
}

const handleSave = () => {
  if (!hasChanges.value) {
    ElMessage.info('图片类型未发生变化')
    return
  }
  
  emit('save', selectedImageType.value)
}

// 监听图片信息变化
watch(() => props.imageInfo, (newImageInfo) => {
  if (newImageInfo) {
    selectedImageType.value = newImageInfo.imageType || 'OTHER'
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.image-type-annotator {
  .type-card {
    margin-bottom: 16px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .image-info {
    margin-bottom: 20px;
  }

  .type-selection {
    margin-bottom: 20px;

    .selection-header {
      margin-bottom: 16px;

      h4 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
    }

    .type-radio-group {
      display: flex;
      flex-direction: column;
      gap: 12px;

      :deep(.el-radio) {
        margin-right: 0;
        margin-bottom: 0;
        height: auto;
        align-items: flex-start;

        .el-radio__input {
          margin-top: 2px;
        }

        .el-radio__label {
          padding-left: 8px;
          width: 100%;
        }
      }

      .annotatable-type {
        :deep(.el-radio__label) {
          .radio-content {
            border-left: 3px solid var(--el-color-success);
            padding-left: 12px;
          }
        }
      }
    }

    .radio-content {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        background-color: var(--el-fill-color-extra-light);
      }

      .type-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        .type-label {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .type-description {
        font-size: 12px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }
  }

  .readonly-mode {
    margin-bottom: 20px;
  }

  .type-explanation {
    .explanation-content {
      .type-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        h5 {
          margin: 0 0 4px 0;
          color: var(--el-text-color-primary);
          font-size: 14px;
        }

        p {
          margin: 0;
          font-size: 12px;
          color: var(--el-text-color-regular);
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
