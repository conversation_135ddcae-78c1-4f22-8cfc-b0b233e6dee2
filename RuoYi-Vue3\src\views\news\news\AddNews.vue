<template>
  <div>
    <el-dialog
      :title="title"
      :model-value="dialogVisible"
      @update:model-value="val => dialogVisible = val"
      width="1000px"
      append-to-body
      @close="handleClose"
      @closed="handleClosed"
    >
      <el-form ref="newsRef" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="文章标题" prop="newsTitle">
          <el-input v-model="formData.newsTitle" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="文章栏目" prop="categroyID">
          <el-select v-model="formData.categroyID" placeholder="请选择文章栏目">
            <el-option
              v-for="categroy in categroyList"
              :key="categroy.categroyID"
              :label="categroy.categroyName"
              :value="categroy.categroyID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文章内容">
          <editor v-model="formData.newsContent" :min-height="192"/>
        </el-form-item>
        <el-form-item v-if="showUpload" label="封面图片">
          <!-- 替换为 image-upload 组件 -->
          <image-upload v-model="formData.headlinePic" :limit="1" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">提 交</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, inject } from 'vue';
import { addNews } from '@/api/news/news';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  formData: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(['update:visible', 'close', 'success']);

// 使用本地变量控制对话框显示状态
const dialogVisible = ref(props.visible);

// 表单验证规则
const rules = {
  newsTitle: [
    { required: true, message: "文章标题不能为空", trigger: "blur" }
  ],
  categroyID: [
    { required: true, message: "文章栏目不能为空", trigger: "change" }
  ]
};

// 定义 newsRef 并绑定到 el-form
const newsRef = ref(null);

// 注入栏目列表
const categroyList = inject('categroyList', []); // 默认值为空数组

// 监听父组件传递的 visible 属性变化
watch(() => props.visible, (newValue) => {
  console.log('props.visible changed to:', newValue);
  dialogVisible.value = newValue;
});

// 监听本地变量变化，同步更新父组件
watch(dialogVisible, (newValue) => {
  console.log('dialogVisible changed to:', newValue);
  emits('update:visible', newValue);
});

// 处理关闭事件
const handleClose = () => {
  console.log('handleClose called');
  dialogVisible.value = false;
};

// 对话框完全关闭后触发
const handleClosed = () => {
  console.log('Dialog fully closed');
  emits('close');
};

// 控制上传组件显示
const showUpload = ref(false);

// 监听栏目选择变化
watch(() => props.formData.categroyID, (newValue) => {

  const selectedCategroy = categroyList.value.find(item => item.categroyID === newValue);

  if (selectedCategroy) {

    if (Number(selectedCategroy.pictureStatus) === 1) {
      showUpload.value = true;
    } else {
      showUpload.value = false;
      props.formData.headline_pic = '';
      console.log('pictureStatus 不为 1，隐藏上传组件');
    }
  } else {
    console.log('未找到对应的栏目信息，隐藏上传组件');
    showUpload.value = false;
    props.formData.headline_pic = '';
  }
});


// 提交表单
const submitForm = () => {
  newsRef.value?.validate((valid) => {
    if (valid) {
      // 将 categroyID 的值赋给 newsType，并添加 uploadTime
      const formDataWithNewsType = {
        ...props.formData,
        newsType: props.formData.categroyID,
      };
      addNews(formDataWithNewsType).then(() => {
        emits('success');
        handleClose();
      }).catch((error) => {
        console.error('新增文章失败:', error);
      });
    } else {
      console.log('表单验证失败');
      return false;
    }
  });
};

</script>
