<template>
  <el-dialog
    :model-value="visible"
    :title="formTitle"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
    @update:model-value="handleDialogUpdate"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属国家">
            <el-input :value="countryDisplayText" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certType">
            <el-select
              v-model="form.certType"
              placeholder="请选择证件类型"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in certTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="版本名称" prop="versionName">
            <el-input v-model="form.versionName" placeholder="请输入证件版本名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发行年份" prop="issueYear">
            <el-date-picker
              v-model="form.issueYear"
              type="year"
              placeholder="选择发行年份"
              value-format="YYYY"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="证号起始值" prop="startNo">
            <el-input v-model="form.startNo" placeholder="请输入证号起始值" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio value="1">启用</el-radio>
              <el-radio value="0">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="需要人工确认" prop="needManualCheck">
            <el-switch
              v-model="form.needManualCheck"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本状态" prop="versionStatus">
            <el-select v-model="form.versionStatus" placeholder="请选择版本状态" style="width: 100%">
              <el-option label="待确认" value="pending" />
              <el-option label="已确认" value="confirmed" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="默认样本类型" prop="defaultSampleType">
            <el-select v-model="form.defaultSampleType" placeholder="请选择默认样本类型" style="width: 100%">
              <el-option label="标准样本" value="standard" />
              <el-option label="普通样本" value="regular" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标准样本数量">
            <el-input v-model="form.standardSampleCount" disabled placeholder="系统自动统计" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="普通样本数量">
            <el-input v-model="form.regularSampleCount" disabled placeholder="系统自动统计" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本确认说明">
            <el-input
              v-model="form.versionComments"
              type="textarea"
              :rows="3"
              placeholder="请输入版本确认说明（可选）"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="版本描述">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入版本描述"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="特征说明">
            <el-input
              v-model="form.features"
              type="textarea"
              :rows="3"
              placeholder="请输入特征说明"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 训练图片信息 -->
      <el-divider content-position="left">训练图片信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片URL">
            <el-input v-model="form.trainingImageUrl" placeholder="训练图片的URL地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图片描述">
            <el-input v-model="form.trainingImageDescription" placeholder="图片描述信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 上传人信息 -->
      <el-divider content-position="left">上传人信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上传人姓名">
            <el-input v-model="form.uploaderName" placeholder="上传人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门">
            <el-input v-model="form.uploaderDept" placeholder="上传人所属部门" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="备注信息"
          :rows="3"
        />
      </el-form-item>

      <!-- 扩展属性 -->
      <el-divider content-position="left">扩展属性</el-divider>

      <div class="extra-attributes">
        <div v-for="(attr, index) in extraAttributesList" :key="index" class="extra-attribute-item">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="属性名称" label-width="80px">
                <el-input v-model="attr.key" placeholder="请输入属性名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="属性值" label-width="80px">
                <el-input v-model="attr.value" placeholder="请输入属性值" />
              </el-form-item>
            </el-col>
            <el-col :span="4" class="flex-center">
              <el-button type="danger" icon="Delete" circle @click="removeExtraAttribute(index)" />
            </el-col>
          </el-row>
        </div>

        <el-button type="primary" @click="addExtraAttribute" plain>
          <el-icon><Plus /></el-icon> 添加扩展属性
        </el-button>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          {{ form.id ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Delete } from '@element-plus/icons-vue';
import { getCertVersion, addCertVersion, updateCertVersion } from '@/api/cert/version';
import { getCertTypes } from '@/api/cert/type';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  countryId: {
    type: Number,
    required: true
  },
  countryCode: {
    type: String,
    required: true
  },
  countryName: {
    type: String,
    required: true
  },
  countryNameEn: {
    type: String,
    default: ''
  },
  versionData: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['close', 'success', 'update:visible']);

const formRef = ref(null);
const loading = ref(false);
const certTypeOptions = ref([]);

// 计算属性
const formTitle = computed(() => {
  return props.versionData ? '编辑证件版本' : '添加证件版本';
});

const countryDisplayText = computed(() => {
  return `${props.countryName} (${props.countryCode})`;
});

// 表单数据
const form = reactive({
  id: undefined,
  versionId: undefined,
  versionName: '',
  issueYear: '',
  status: '1',
  certType: '',
  startNo: '',
  description: '',
  features: '',
  trainingImageUrl: '',
  trainingImageDescription: '训练用证件图片',
  uploaderName: '',
  uploaderDept: '',
  remark: '',
  extraAttributes: {},
  needManualCheck: false,
  versionStatus: 'pending',
  defaultSampleType: 'standard',
  standardSampleCount: 0,
  regularSampleCount: 0,
  versionComments: ''
});

// 扩展属性列表
const extraAttributesList = ref([]);

// 表单验证规则
const rules = {
  certType: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  versionName: [
    { required: true, message: '请输入版本名称', trigger: 'blur' },
    { min: 2, max: 100, message: '版本名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  issueYear: [
    { required: true, message: '请选择发行年份', trigger: 'change' }
  ],
  startNo: [
    { required: true, message: '请输入证号起始值', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 处理弹窗关闭
const handleDialogUpdate = (value) => {
  if (!value) {
    handleClose();
  }
};

// 获取证件类型列表
const getCertTypeList = async () => {
  try {
    const response = await getCertTypes();
    certTypeOptions.value = response.data?.map(item => ({
      value: item.zjlbdm,
      label: `${item.zjlbmc} (${item.zjlbdm})`
    })) || [];
  } catch (error) {
    console.error('获取证件类型列表失败:', error);
  }
};

// 初始化表单数据
const initFormData = () => {
  console.log('initFormData 被调用, versionData:', props.versionData);
  
  if (props.versionData) {
    // 编辑模式 - 从传入的数据填充表单
    const data = props.versionData;
    console.log('正在填充表单数据:', data);
    
    // 重置表单数据
    Object.assign(form, {
      id: data.id || data._id, // MongoDB 可能使用 _id
      versionId: data.versionId,
      versionName: data.versionName || '',
      issueYear: data.issueYear || '',
      status: data.status || '1',
      // 处理不同的数据结构
      certType: data.certType || data.certInfo?.type || '',
      startNo: data.startNo || data.certInfo?.startNo || '',
      description: data.description || '',
      features: data.features || '',
      // 处理训练图片信息
      trainingImageUrl: data.trainingImageUrl || data.trainingImage?.url || data.imageInfo?.url || '',
      trainingImageDescription: data.trainingImageDescription || data.trainingImage?.description || data.imageInfo?.description || '训练用证件图片',
      // 处理上传人信息
      uploaderName: data.uploaderName || data.uploaderInfo?.nickName || data.uploaderInfo?.userName || '',
      uploaderDept: data.uploaderDept || data.uploaderInfo?.deptName || data.uploaderInfo?.deptId || '',
      remark: data.remark || data.uploaderInfo?.remark || '',
      needManualCheck: data.needManualCheck || false,
      versionStatus: data.versionStatus || 'pending',
      defaultSampleType: data.defaultSampleType || 'standard',
      standardSampleCount: data.standardSampleCount || 0,
      regularSampleCount: data.regularSampleCount || 0,
      versionComments: data.versionComments || ''
    });

    console.log('表单数据填充后:', form);

    // 处理扩展属性
    extraAttributesList.value = [];
    const extraAttrs = data.extraAttributes || data.extra_attributes || {};
    if (extraAttrs && typeof extraAttrs === 'object') {
      for (const [key, value] of Object.entries(extraAttrs)) {
        extraAttributesList.value.push({ key, value });
      }
    }
    
    // 如果没有扩展属性，至少添加一个空的
    if (extraAttributesList.value.length === 0) {
      addExtraAttribute();
    }
  } else {
    // 新增模式 - 重置表单
    resetFormData();
  }
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(form, {
    id: undefined,
    versionId: undefined,
    versionName: '',
    issueYear: '',
    status: '1',
    certType: '',
    startNo: '',
    description: '',
    features: '',
    trainingImageUrl: '',
    trainingImageDescription: '训练用证件图片',
    uploaderName: '',
    uploaderDept: '',
    remark: '',
    needManualCheck: false,
    versionStatus: 'pending',
    defaultSampleType: 'standard',
    standardSampleCount: 0,
    regularSampleCount: 0,
    versionComments: ''
  });
  extraAttributesList.value = [];
  addExtraAttribute(); // 默认添加一个扩展属性
};

// 添加扩展属性
const addExtraAttribute = () => {
  extraAttributesList.value.push({ key: '', value: '' });
};

// 移除扩展属性
const removeExtraAttribute = (index) => {
  extraAttributesList.value.splice(index, 1);
};

// 将扩展属性列表转换为对象
const convertExtraAttributesToObject = () => {
  const extraAttributes = {};
  extraAttributesList.value.forEach(attr => {
    if (attr.key && attr.key.trim() !== '') {
      extraAttributes[attr.key] = attr.value;
    }
  });
  return extraAttributes;
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return;

    loading.value = true;
    try {
      // 构建提交数据，适配后端期望的结构
      const submitData = {
        id: form.id,
        versionId: form.versionId,
        versionName: form.versionName,
        issueYear: form.issueYear,
        status: form.status,
        countryInfo: {
          id: props.countryId,
          code: props.countryCode,
          name: props.countryName
        },
        certType: form.certType,
        startNo: form.startNo,
        trainingImage: {
          url: form.trainingImageUrl,
          description: form.trainingImageDescription
        },
        uploaderInfo: {
          nickName: form.uploaderName,
          deptName: form.uploaderDept,
          remark: form.remark
        },
        extraAttributes: convertExtraAttributesToObject(),
        description: form.description,
        features: form.features,
        needManualCheck: form.needManualCheck,
        versionStatus: form.versionStatus,
        defaultSampleType: form.defaultSampleType,
        standardSampleCount: form.standardSampleCount,
        regularSampleCount: form.regularSampleCount,
        versionComments: form.versionComments
      };

      console.log('提交数据:', submitData);

      if (form.id) {
        await updateCertVersion(submitData);
        ElMessage.success('修改成功');
      } else {
        await addCertVersion(submitData);
        ElMessage.success('新增成功');
      }
      
      emit('success');
    } catch (error) {
      console.error('提交证件版本失败:', error);
      ElMessage.error('提交失败: ' + (error.message || error));
    } finally {
      loading.value = false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  initFormData();
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  console.log('弹窗显示状态变化:', newVal);
  if (newVal) {
    nextTick(() => {
      getCertTypeList();
      initFormData();
    });
  }
}, { immediate: true });

// 监听版本数据变化
watch(() => props.versionData, (newVal) => {
  console.log('版本数据变化:', newVal);
  if (props.visible && newVal) {
    nextTick(() => {
      initFormData();
    });
  }
}, { deep: true, immediate: true });
</script>

<style scoped>
.extra-attributes {
  margin-bottom: 20px;
}

.extra-attribute-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
}
</style>
