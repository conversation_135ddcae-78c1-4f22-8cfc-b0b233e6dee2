<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    clearable
    filterable
    :filter-method="filterCountry"
    :disabled="disabled"
    :loading="loading"
    @change="handleChange"
    @visible-change="onSelectOpen"
    :style="{ width: width }"
  >
    <el-option
      v-for="item in filteredCountryOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>{{ item.name }}</span>
        <div style="color: #8492a6; font-size: 12px;">
          <span>{{ item.code }}</span>
          <span v-if="item.nameEn" style="margin-left: 4px;">
            {{ item.nameEn.slice(0, 15) }}{{ item.nameEn.length > 15 ? '...' : '' }}
          </span>
        </div>
      </div>
    </el-option>

    <!-- 没有搜索结果时的提示 -->
    <el-option
      v-if="filteredCountryOptions.length === 0 && currentSearch"
      disabled
      value=""
      label=""
    >
      <div style="text-align: center; color: #909399; padding: 10px;">
        <p>没有找到 "{{ currentSearch }}" 相关的国家</p>
        <p style="font-size: 12px;">支持搜索：中文名、英文名、国家代码</p>
      </div>
    </el-option>
  </el-select>

  <!-- 加载提示 -->
  <div v-if="showLoadingTip" style="margin-top: 2px;">
    <el-text type="info" size="small">
      已加载 {{ countryOptions.length }} 个国家，支持多语言搜索
    </el-text>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import {getAllCountries} from "@/api/cert/country.ts";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择国家（支持中文/英文/代码搜索）'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '250px'
  },
  showLoadingTip: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const countryOptions = ref([])
const filteredCountryOptions = ref([])
const selectedValue = ref(props.modelValue)
const loading = ref(false)
const currentSearch = ref('')

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedValue.value = newVal
})

// 监听内部值变化
watch(() => selectedValue.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理选择变化
const handleChange = (val) => {
  emit('change', val)
}

// 过滤国家选项
const filterCountry = (query) => {
  currentSearch.value = query

  if (!query || query.trim() === '') {
    filteredCountryOptions.value = [...countryOptions.value]
    return
  }

  const searchQuery = query.toLowerCase().trim()
  filteredCountryOptions.value = countryOptions.value.filter(country => {
    // 基于数据库字段进行搜索：中文名、英文名、国家代码
    const searchText = [
      country.name,           // 中文名
      country.nameEn,         // 英文名
      country.code            // 国家代码
    ].filter(Boolean).join(' ').toLowerCase()

    return searchText.includes(searchQuery)
  })

  console.log(`搜索"${query}"，找到 ${filteredCountryOptions.value.length} 个匹配的国家`)
}

// 处理下拉框可见性变化
const onSelectOpen = (visible) => {
  if (visible) {
    if (countryOptions.value.length === 0) {
      getCountryList()
    } else {
      // 重置过滤状态，显示所有选项
      filteredCountryOptions.value = [...countryOptions.value]
      currentSearch.value = ''
    }
  }
}

// 获取国家列表 - 保持原有逻辑不变
const getCountryList = async () => {
  try {
    loading.value = true
    const res = await getAllCountries()
    // console.log('国家列表API响应:', res)
    if (res.code === 200) {
      countryOptions.value = res.rows.map(item => ({
        value: item.code,
        label: `${item.name} (${item.code})`,
        name: item.name,           // 保存中文名用于搜索
        nameEn: item.nameEn || '', // 保存英文名用于搜索
        code: item.code            // 保存代码用于搜索
      }))

      // 初始化过滤选项
      filteredCountryOptions.value = [...countryOptions.value]

      console.log(`处理后的国家选项：${countryOptions.value.length} 个国家`)
    }
  } catch (error) {
    console.error('获取国家列表失败', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  console.log('CountrySelect 组件已挂载')
  getCountryList()
})
</script>
