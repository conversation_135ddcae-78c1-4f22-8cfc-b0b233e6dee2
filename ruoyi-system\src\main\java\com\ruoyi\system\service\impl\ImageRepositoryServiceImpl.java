package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.domain.dto.response.ImageRepositoryVO;
import com.ruoyi.system.domain.dto.AnnotationDTO;
import com.ruoyi.system.domain.dto.AnnotationBodyDTO;
import com.ruoyi.system.domain.dto.AnnotationTargetDTO;
import com.ruoyi.system.domain.dto.AnnotationSelectorDTO;
import com.ruoyi.system.domain.dto.request.AnnotationTemplateDTO;
import com.ruoyi.system.repository.ImageRepositoryRepo;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.service.IImageRepositoryService;
import com.ruoyi.system.service.IVersionAnnotationTemplateService;
import com.ruoyi.common.service.MinioService;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 图片仓库Service实现类
 * 职责: 作为最底层的服务，负责所有与图片相关的原子操作，如按条件查找、统计和更新标注
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class ImageRepositoryServiceImpl implements IImageRepositoryService {
    
    private static final Logger log = LoggerFactory.getLogger(ImageRepositoryServiceImpl.class);
    
    @Autowired
    private ImageRepositoryRepo imageRepositoryRepo;

    @Autowired
    private FolderInfoRepository folderInfoRepository;

    @Autowired
    private IVersionAnnotationTemplateService versionAnnotationTemplateService;
    
    @Override
    public ImageRepository createImage(ImageRepository imageRepository) {
        try {
            imageRepository.setCreateTime(new Date());
            imageRepository.setUpdateTime(new Date());
            return imageRepositoryRepo.save(imageRepository);
        } catch (Exception e) {
            log.error("创建图片记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建图片失败", e);
        }
    }
    
    @Override
    public ImageRepositoryVO getImageById(String imageId) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            return convertToVO(imageRepository);
        } catch (Exception e) {
            log.error("根据图片ID获取图片失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public ImageRepository getImageByMongoId(String id) {
        try {
            return imageRepositoryRepo.findById(id).orElse(null);
        } catch (Exception e) {
            log.error("根据MongoDB主键获取图片失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByFolderId(String folderId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据文件夹ID获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByVersionId(String versionId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByVersionId(versionId);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据版本ID获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public long countImagesByFolderId(String folderId) {
        try {
            return imageRepositoryRepo.countByFolderId(folderId);
        } catch (Exception e) {
            log.error("根据文件夹ID统计图片数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByTaskId(String taskId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByTaskId(taskId);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据任务ID获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByLightType(String lightType) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByLightType(lightType);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据光照类型获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByMainImageFlag(boolean isMainImage) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByIsMainImage(isMainImage);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据是否为主图获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByProcessStatus(String processStatus) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByProcessStatus(processStatus);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据处理状态获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByDeptId(Long deptId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByDeptId(deptId);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据部门ID获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByFolderIdAndLightType(String folderId, String lightType) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderIdAndLightType(folderId, lightType);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据文件夹ID和光照类型获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByFolderIdAndMainImageFlag(String folderId, boolean isMainImage) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderIdAndIsMainImage(folderId, isMainImage);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据文件夹ID和是否为主图获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByVersionIdAndLightType(String versionId, String lightType) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByVersionIdAndLightType(versionId, lightType);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据版本ID和光照类型获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getImagesByCreateTimeRange(Date startTime, Date endTime) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByCreateTimeBetween(startTime, endTime);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据创建时间范围获取图片列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<ImageRepositoryVO> getMainImagesByFolderId(String folderId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findMainImagesByFolderId(folderId);
            return convertToVOList(images);
        } catch (Exception e) {
            log.error("根据文件夹ID获取主图列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public long countProcessedImagesByFolderId(String folderId) {
        try {
            return imageRepositoryRepo.countProcessedImagesByFolderId(folderId);
        } catch (Exception e) {
            log.error("根据文件夹ID统计已处理图片数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public long countImagesByVersionId(String versionId) {
        try {
            return imageRepositoryRepo.countByVersionId(versionId);
        } catch (Exception e) {
            log.error("根据版本ID统计图片数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public ImageRepository updateImage(ImageRepository imageRepository) {
        try {
            imageRepository.setUpdateTime(new Date());
            return imageRepositoryRepo.save(imageRepository);
        } catch (Exception e) {
            log.error("更新图片信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新图片失败", e);
        }
    }
    
    @Override
    public boolean updateImageProcessStatus(String imageId, String processStatus) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                imageRepository.setProcessStatus(processStatus);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新图片处理状态失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean updateImageAnnotations(String imageId, Map<String, Object> annotations) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                // 将Map转换为List<Object>存储
                List<Object> annotationList = new ArrayList<>();
                annotationList.add(annotations);
                imageRepository.setAnnotations(annotationList);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新图片标注信息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateImageAnnotations(String imageId, List<AnnotationDTO> annotations) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                // 将AnnotationDTO列表转换为List<Object>存储
                List<Object> annotationList = new ArrayList<>();
                for (AnnotationDTO annotation : annotations) {
                    annotationList.add(annotation);
                }
                imageRepository.setAnnotations(annotationList);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);

                log.info("成功更新图片[{}]的标注信息，共{}个标注", imageId, annotations.size());
                return true;
            }
            log.warn("图片[{}]不存在，无法更新标注", imageId);
            return false;
        } catch (Exception e) {
            log.error("更新图片标注信息失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteImage(String imageId) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                imageRepositoryRepo.delete(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除图片记录失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public int deleteImagesByFolderId(String folderId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            int deletedCount = images.size();
            imageRepositoryRepo.deleteByFolderId(folderId);
            return deletedCount;
        } catch (Exception e) {
            log.error("根据文件夹ID删除图片失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取文件夹下所有图片的MinIO路径
     *
     * @param folderId 文件夹ID
     * @return MinIO路径列表
     */
    @Override
    public List<String> getMinioPathsByFolderId(String folderId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            List<String> minioPaths = new ArrayList<>();

            for (ImageRepository image : images) {
                // 使用filePath字段，因为实际存储的MinIO路径在filePath中
                String path = image.getFilePath();
                if (path != null && !path.trim().isEmpty()) {
                    minioPaths.add(path);
                }
            }

            log.info("获取文件夹{}下的MinIO路径，共{}个文件", folderId, minioPaths.size());
            if (!minioPaths.isEmpty()) {
                log.debug("MinIO路径列表: {}", minioPaths);
            }
            return minioPaths;
        } catch (Exception e) {
            log.error("获取文件夹MinIO路径失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取任务下所有图片的MinIO路径
     *
     * @param taskId 任务ID
     * @return MinIO路径列表
     */
    @Override
    public List<String> getMinioPathsByTaskId(String taskId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByTaskId(taskId);
            List<String> minioPaths = new ArrayList<>();

            for (ImageRepository image : images) {
                // 使用filePath字段，因为实际存储的MinIO路径在filePath中
                String path = image.getFilePath();
                if (path != null && !path.trim().isEmpty()) {
                    minioPaths.add(path);
                }
            }

            log.info("获取任务{}下的MinIO路径，共{}个文件", taskId, minioPaths.size());
            if (!minioPaths.isEmpty()) {
                log.debug("MinIO路径列表: {}", minioPaths);
            }
            return minioPaths;
        } catch (Exception e) {
            log.error("获取任务MinIO路径失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public int deleteImagesByTaskId(String taskId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByTaskId(taskId);
            int deletedCount = images.size();
            imageRepositoryRepo.deleteByTaskId(taskId);
            return deletedCount;
        } catch (Exception e) {
            log.error("根据任务ID删除图片失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public List<ImageRepository> batchCreateImages(List<ImageRepository> imageRepositories) {
        try {
            Date now = new Date();
            for (ImageRepository imageRepository : imageRepositories) {
                imageRepository.setCreateTime(now);
                imageRepository.setUpdateTime(now);
            }
            return imageRepositoryRepo.saveAll(imageRepositories);
        } catch (Exception e) {
            log.error("批量创建图片记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量创建图片失败", e);
        }
    }
    
    @Override
    public int batchUpdateImageProcessStatus(List<String> imageIds, String processStatus) {
        try {
            int updatedCount = 0;
            for (String imageId : imageIds) {
                if (updateImageProcessStatus(imageId, processStatus)) {
                    updatedCount++;
                }
            }
            return updatedCount;
        } catch (Exception e) {
            log.error("批量更新图片处理状态失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public Map<String, Object> getImageStatistics(String folderId) {
        Map<String, Object> statistics = new HashMap<>();
        try {
            long totalImages = countImagesByFolderId(folderId);
            long processedImages = countProcessedImagesByFolderId(folderId);
            
            statistics.put("totalImages", totalImages);
            statistics.put("processedImages", processedImages);
            statistics.put("pendingImages", totalImages - processedImages);
            
            // 统计各光照类型的图片数量
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            Map<String, Long> lightTypeCount = images.stream()
                    .collect(Collectors.groupingBy(ImageRepository::getLightType, Collectors.counting()));
            statistics.put("lightTypeCount", lightTypeCount);
            
            // 统计主图数量
            long mainImageCount = images.stream()
                    .mapToLong(img -> img.getIsMainImage() ? 1 : 0)
                    .sum();
            statistics.put("mainImageCount", mainImageCount);
            
        } catch (Exception e) {
            log.error("获取图片统计信息失败: {}", e.getMessage(), e);
        }
        return statistics;
    }
    
    @Override
    public Map<String, Object> getTaskImageStatistics(String taskId) {
        Map<String, Object> statistics = new HashMap<>();
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByTaskId(taskId);
            
            statistics.put("totalImages", images.size());
            
            // 统计各处理状态的图片数量
            Map<String, Long> statusCount = images.stream()
                    .collect(Collectors.groupingBy(ImageRepository::getProcessStatus, Collectors.counting()));
            statistics.put("processStatusCount", statusCount);
            
            // 统计各光照类型的图片数量
            Map<String, Long> lightTypeCount = images.stream()
                    .collect(Collectors.groupingBy(ImageRepository::getLightType, Collectors.counting()));
            statistics.put("lightTypeCount", lightTypeCount);
            
        } catch (Exception e) {
            log.error("获取任务图片统计失败: {}", e.getMessage(), e);
        }
        return statistics;
    }
    
    @Override
    public boolean existsById(String imageId) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            return imageRepository != null;
        } catch (Exception e) {
            log.error("检查图片是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean setAsMainImage(String imageId) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                imageRepository.setIsMainImage(true);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("设置主图失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean unsetMainImage(String imageId) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                imageRepository.setIsMainImage(false);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("取消主图失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteImages(List<String> imageIds) {
        try {
            for (String imageId : imageIds) {
                deleteImage(imageId);
            }
            log.info("批量删除图片成功，共删除{}张图片", imageIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除图片失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateImageStatus(String imageId, String status) {
        try {
            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository != null) {
                imageRepository.setProcessStatus(status);
                imageRepository.setUpdateTime(new Date());
                imageRepositoryRepo.save(imageRepository);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新图片状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateImageInfo(String imageId, Map<String, Object> updateData) {
        try {
            log.info("更新图片信息: imageId={}, updateData={}", imageId, updateData);

            ImageRepository imageRepository = imageRepositoryRepo.findByImageId(imageId);
            if (imageRepository == null) {
                log.warn("图片不存在: imageId={}", imageId);
                return false;
            }

            // 更新图片类型
            if (updateData.containsKey("imageType")) {
                String imageType = (String) updateData.get("imageType");
                imageRepository.setImageType(imageType);
                // 同时更新是否可标注类型
                boolean isAnnotatable = com.ruoyi.system.utils.ImageTypeDetector.isAnnotatableType(imageType);
                imageRepository.setIsAnnotatableType(isAnnotatable);
                log.info("更新图片类型: imageId={}, imageType={}, isAnnotatable={}", imageId, imageType, isAnnotatable);
            }

            // 更新光照类型
            if (updateData.containsKey("lightType")) {
                String lightType = (String) updateData.get("lightType");
                imageRepository.setLightType(lightType);
                log.info("更新光照类型: imageId={}, lightType={}", imageId, lightType);
            }

            // 更新是否主图
            if (updateData.containsKey("isMainImage")) {
                Boolean isMainImage = (Boolean) updateData.get("isMainImage");
                imageRepository.setIsMainImage(isMainImage);
                log.info("更新是否主图: imageId={}, isMainImage={}", imageId, isMainImage);
            }

            // 更新标签
            if (updateData.containsKey("tags")) {
                @SuppressWarnings("unchecked")
                List<String> tags = (List<String>) updateData.get("tags");
                imageRepository.setTags(tags);
                log.info("更新标签: imageId={}, tags={}", imageId, tags);
            }

            imageRepository.setUpdateTime(new Date());
            imageRepositoryRepo.save(imageRepository);

            log.info("更新图片信息成功: imageId={}", imageId);
            return true;

        } catch (Exception e) {
            log.error("更新图片信息失败: imageId={}, updateData={}, error={}", imageId, updateData, e.getMessage(), e);
            throw new RuntimeException("更新图片信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getFolderImageStatistics(String folderId) {
        return getImageStatistics(folderId);
    }
    
    /**
     * 转换为VO对象
     */
    private ImageRepositoryVO convertToVO(ImageRepository imageRepository) {
        if (imageRepository == null) {
            return null;
        }

        ImageRepositoryVO vo = new ImageRepositoryVO();

        // 复制基本属性
        vo.setId(imageRepository.getId());
        vo.setImageId(imageRepository.getImageId());
        vo.setTaskId(imageRepository.getTaskId());
        vo.setFolderId(imageRepository.getFolderId());
        vo.setVersionId(imageRepository.getVersionId());

        // 文件相关属性
        vo.setOriginalFileName(imageRepository.getOriginalFileName());
        vo.setMinioPath(imageRepository.getMinioPath());
        vo.setFileSize(imageRepository.getFileSize());
        vo.setContentType(imageRepository.getContentType());

        // 图片属性
        vo.setImageWidth(imageRepository.getImageWidth());
        vo.setImageHeight(imageRepository.getImageHeight());
        vo.setLightType(imageRepository.getLightType());
        vo.setIsMainImage(imageRepository.getIsMainImage());

        // 处理状态
        vo.setProcessStatus(imageRepository.getProcessStatus());
        vo.setProcessMessage(imageRepository.getProcessMessage());

        // 其他属性
        vo.setImageMd5(imageRepository.getImageMd5());
        vo.setTags(imageRepository.getTags());
        vo.setDeptInfo(imageRepository.getDeptInfo());

        // 时间属性
        vo.setCreateTime(imageRepository.getCreateTime());
        vo.setUpdateTime(imageRepository.getUpdateTime());

        // 标注信息转换
        if (imageRepository.getAnnotations() != null) {
            List<AnnotationDTO> annotations = new ArrayList<>();
            for (Object annotationObj : imageRepository.getAnnotations()) {
                try {
                    // 如果已经是AnnotationDTO类型，直接添加
                    if (annotationObj instanceof AnnotationDTO) {
                        annotations.add((AnnotationDTO) annotationObj);
                    } else if (annotationObj instanceof Map) {
                        // 如果是Map类型（如LinkedHashMap），转换为AnnotationDTO
                        log.debug("标注对象类型: {}", annotationObj.getClass().getSimpleName());
                        Map<String, Object> annotationMap = (Map<String, Object>) annotationObj;
                        log.debug("标注Map内容: {}", annotationMap);

                        AnnotationDTO annotationDTO = new AnnotationDTO();
                        // MongoDB中使用_id字段
                        String id = (String) annotationMap.get("_id");
                        if (id == null) {
                            id = (String) annotationMap.get("id");
                        }
                        annotationDTO.setId(id);
                        annotationDTO.setType((String) annotationMap.get("type"));

                        // 转换body
                        Object bodyObj = annotationMap.get("body");
                        if (bodyObj instanceof Map) {
                            Map<String, Object> bodyMap = (Map<String, Object>) bodyObj;
                            AnnotationBodyDTO bodyDTO = new AnnotationBodyDTO();
                            bodyDTO.setValue((String) bodyMap.get("value"));
                            bodyDTO.setPurpose((String) bodyMap.get("purpose"));
                            annotationDTO.setBody(bodyDTO);
                        }

                        // 转换target
                        Object targetObj = annotationMap.get("target");
                        if (targetObj instanceof Map) {
                            Map<String, Object> targetMap = (Map<String, Object>) targetObj;
                            AnnotationTargetDTO targetDTO = new AnnotationTargetDTO();

                            // 转换selector
                            Object selectorObj = targetMap.get("selector");
                            if (selectorObj instanceof Map) {
                                Map<String, Object> selectorMap = (Map<String, Object>) selectorObj;
                                AnnotationSelectorDTO selectorDTO = new AnnotationSelectorDTO();
                                selectorDTO.setValue((String) selectorMap.get("value"));
                                targetDTO.setSelector(selectorDTO);
                            }
                            annotationDTO.setTarget(targetDTO);
                        }

                        annotations.add(annotationDTO);
                        log.debug("成功转换LinkedHashMap到AnnotationDTO: {}", annotationDTO.getId());
                    } else {
                        log.debug("未知的标注对象类型: {}", annotationObj.getClass().getSimpleName());
                    }
                } catch (Exception e) {
                    log.warn("转换标注对象失败: {}", e.getMessage());
                }
            }
            vo.setAnnotations(annotations);
            log.debug("转换完成，共{}个标注", annotations.size());
        }

        return vo;
    }
    
    /**
     * 转换为VO列表
     */
    private List<ImageRepositoryVO> convertToVOList(List<ImageRepository> imageRepositories) {
        return imageRepositories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    // ==================== 标注相关方法实现 ====================

    @Override
    public String detectImageType(String fileName) {
        try {
            return com.ruoyi.system.utils.ImageTypeDetector.detectImageType(fileName);
        } catch (Exception e) {
            log.error("检测图片类型失败: fileName={}, error={}", fileName, e.getMessage(), e);
            return "OTHER";
        }
    }

    @Override
    @Transactional
    public void updateImageTypes(String folderId) {
        try {
            log.info("批量更新文件夹图片类型: folderId={}", folderId);

            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            if (images.isEmpty()) {
                log.warn("文件夹下没有图片: folderId={}", folderId);
                return;
            }

            int updatedCount = 0;
            for (ImageRepository image : images) {
                String fileName = image.getOriginalFileName() != null ?
                        image.getOriginalFileName() : image.getFileName();

                String detectedType = detectImageType(fileName);
                boolean isAnnotatable = com.ruoyi.system.utils.ImageTypeDetector.isAnnotatableType(detectedType);

                // 只有类型发生变化时才更新
                if (!detectedType.equals(image.getImageType()) ||
                    !Boolean.valueOf(isAnnotatable).equals(image.getIsAnnotatableType())) {

                    image.setImageType(detectedType);
                    image.setIsAnnotatableType(isAnnotatable);
                    image.setUpdateTime(new Date());

                    imageRepositoryRepo.save(image);
                    updatedCount++;
                }
            }

            log.info("批量更新图片类型完成: folderId={}, 总图片数={}, 更新数={}",
                    folderId, images.size(), updatedCount);

        } catch (Exception e) {
            log.error("批量更新图片类型失败: folderId={}, error={}", folderId, e.getMessage(), e);
            throw new RuntimeException("批量更新图片类型失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ImageAnnotationInfo getImageAnnotations(String imageId) {
        try {
            ImageRepository image = imageRepositoryRepo.findByImageId(imageId);
            if (image == null) {
                ImageAnnotationInfo info = new ImageAnnotationInfo();
                info.setImageId(imageId);
                info.setCanEdit(false);
                info.setCanView(false);
                info.setReason("图片不存在");
                info.setAnnotations(new ArrayList<>());
                return info;
            }

            ImageAnnotationInfo info = new ImageAnnotationInfo();
            info.setImageId(imageId);
            info.setImageType(image.getImageType());

            // 获取标注权限信息
            IVersionAnnotationTemplateService.AnnotationPermissionInfo permission =
                versionAnnotationTemplateService.getAnnotationPermission(imageId);

            info.setCanEdit(permission.isCanEdit());
            info.setCanView(permission.isCanView());
            info.setReason(permission.getReason());

            // 从版本模板加载标注数据
            if (permission.isCanView()) {
                try {
                    String versionId = getVersionId(image);
                    Optional<VersionAnnotationTemplate> template = versionAnnotationTemplateService.getTemplate(versionId, image.getImageType());

                    if (template.isPresent()) {
                        // 转换模板标注数据为返回格式
                        List<AnnotationItem> annotations = template.get().getAnnotations().stream()
                            .map(this::convertFromTemplateAnnotation)
                            .collect(Collectors.toList());
                        info.setAnnotations(annotations);
                        log.info("从版本模板加载标注数据: imageId={}, versionId={}, imageType={}, 标注数量={}",
                                imageId, versionId, image.getImageType(), annotations.size());
                    } else {
                        info.setAnnotations(new ArrayList<>());
                        log.info("未找到版本模板: imageId={}, versionId={}, imageType={}",
                                imageId, versionId, image.getImageType());
                    }
                } catch (Exception e) {
                    log.warn("加载版本模板标注数据失败: imageId={}, error={}", imageId, e.getMessage());
                    info.setAnnotations(new ArrayList<>());
                }
            } else {
                info.setAnnotations(new ArrayList<>());
            }

            return info;

        } catch (Exception e) {
            log.error("获取图片标注信息失败: imageId={}, error={}", imageId, e.getMessage(), e);
            ImageAnnotationInfo info = new ImageAnnotationInfo();
            info.setImageId(imageId);
            info.setCanEdit(false);
            info.setCanView(false);
            info.setReason("获取标注信息失败: " + e.getMessage());
            info.setAnnotations(new ArrayList<>());
            return info;
        }
    }

    @Override
    @Transactional
    public boolean saveImageAnnotations(String imageId, List<AnnotationItem> annotations) {
        try {
            log.info("保存图片标注: imageId={}, 标注数量={}", imageId, annotations != null ? annotations.size() : 0);

            // 1. 获取图片信息
            ImageRepository image = imageRepositoryRepo.findByImageId(imageId);
            if (image == null) {
                log.error("图片不存在: imageId={}", imageId);
                throw new RuntimeException("图片不存在: " + imageId);
            }

            // 2. 权限检查
            if (!canAnnotateImage(image)) {
                log.warn("无权限标注此图片: imageId={}, folderId={}, imageType={}",
                        imageId, image.getFolderId(), image.getImageType());
                throw new RuntimeException("无权限标注此图片：只有标准样本文件夹中的可标注类型图片才能编辑标注");
            }

            // 3. 获取版本信息
            String versionId = getVersionId(image);
            String imageType = image.getImageType();
            log.info("获取版本信息: versionId={}, imageType={}", versionId, imageType);

            // 4. 构建模板DTO
            AnnotationTemplateDTO templateDTO = buildTemplateDTO(annotations);

            // 5. 保存到版本模板
            versionAnnotationTemplateService.saveTemplate(versionId, imageType, templateDTO, image.getFolderId());
            log.info("标注数据已保存到版本模板: versionId={}, imageType={}", versionId, imageType);

            // 6. 更新图片状态
            updateImageAnnotationStatus(imageId, annotations != null && annotations.size() > 0);

            return true;

        } catch (Exception e) {
            log.error("保存图片标注失败: imageId={}, error={}", imageId, e.getMessage(), e);
            throw new RuntimeException("保存图片标注失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateFolderImagesAnnotatableType(String folderId) {
        try {
            log.info("更新文件夹图片可标注类型: folderId={}", folderId);
            updateImageTypes(folderId);
        } catch (Exception e) {
            log.error("更新文件夹图片可标注类型失败: folderId={}, error={}", folderId, e.getMessage(), e);
            throw new RuntimeException("更新图片可标注类型失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ImageRepositoryVO> getAnnotatableImages(String folderId) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            return images.stream()
                    .filter(image -> Boolean.TRUE.equals(image.getIsAnnotatableType()))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取可标注图片列表失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ImageRepositoryVO> getImagesByType(String folderId, String imageType) {
        try {
            List<ImageRepository> images = imageRepositoryRepo.findByFolderId(folderId);
            return images.stream()
                    .filter(image -> imageType.equals(image.getImageType()))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据类型获取图片列表失败: folderId={}, imageType={}, error={}",
                    folderId, imageType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // ==================== 标注保存相关的辅助方法 ====================

    /**
     * 检查图片是否可以进行标注
     */
    private boolean canAnnotateImage(ImageRepository image) {
        // 1. 检查图片是否存在
        if (image == null) {
            log.warn("图片不存在，无法标注");
            return false;
        }

        // 2. 检查文件夹类型是否为标准样本
        FolderInfo folder = folderInfoRepository.findByFolderId(image.getFolderId());
        if (folder == null || !"standard".equals(folder.getFolderType())) {
            log.warn("图片不属于标准样本文件夹，无法标注: folderId={}, folderType={}",
                    image.getFolderId(), folder != null ? folder.getFolderType() : "null");
            return false;
        }

        // 3. 检查图片类型是否可标注
        List<String> annotatableTypes = Arrays.asList(
            "VISIBLE_DATA_PAGE", "INFRARED_DATA_PAGE", "ULTRAVIOLET_DATA_PAGE"
        );
        if (!annotatableTypes.contains(image.getImageType())) {
            log.warn("图片类型不支持标注: imageType={}", image.getImageType());
            return false;
        }

        return true;
    }

    /**
     * 获取图片的版本ID
     */
    private String getVersionId(ImageRepository image) {
        // 优先从图片记录获取
        if (StringUtils.isNotEmpty(image.getVersionId())) {
            return image.getVersionId();
        }

        // 从文件夹信息获取
        FolderInfo folder = folderInfoRepository.findByFolderId(image.getFolderId());
        if (folder != null && StringUtils.isNotEmpty(folder.getAssociationInfo().getVersionId())) {
            return folder.getAssociationInfo().getVersionId();
        }

        throw new RuntimeException("无法获取图片的版本ID: imageId=" + image.getImageId());
    }

    /**
     * 构建标注模板DTO
     */
    private AnnotationTemplateDTO buildTemplateDTO(List<AnnotationItem> annotations) {
        AnnotationTemplateDTO templateDTO = new AnnotationTemplateDTO();

        if (annotations != null && !annotations.isEmpty()) {
            // 转换标注项格式
            List<AnnotationTemplateDTO.AnnotationItemDTO> templateAnnotations = annotations.stream()
                .map(this::convertToTemplateAnnotation)
                .collect(Collectors.toList());
            templateDTO.setAnnotations(templateAnnotations);
        } else {
            templateDTO.setAnnotations(new ArrayList<>());
        }

        return templateDTO;
    }

    /**
     * 转换标注项到模板格式
     */
    private AnnotationTemplateDTO.AnnotationItemDTO convertToTemplateAnnotation(AnnotationItem annotation) {
        AnnotationTemplateDTO.AnnotationItemDTO templateAnnotation = new AnnotationTemplateDTO.AnnotationItemDTO();

        templateAnnotation.setAnnotationId(annotation.getAnnotationId());
        templateAnnotation.setAnnotationType(annotation.getAnnotationType());
        templateAnnotation.setAnnotationName(annotation.getAnnotationName());
        templateAnnotation.setAnnotationValue(annotation.getAnnotationValue());
        templateAnnotation.setRequired(annotation.getRequired());
        templateAnnotation.setDisplayOrder(annotation.getDisplayOrder());

        // 转换坐标信息
        if (annotation.getCoordinate() != null) {
            AnnotationTemplateDTO.AnnotationCoordinateDTO coordinate = new AnnotationTemplateDTO.AnnotationCoordinateDTO();
            coordinate.setX(annotation.getCoordinate().getX());
            coordinate.setY(annotation.getCoordinate().getY());
            coordinate.setWidth(annotation.getCoordinate().getWidth());
            coordinate.setHeight(annotation.getCoordinate().getHeight());
            templateAnnotation.setCoordinate(coordinate);
        }

        return templateAnnotation;
    }

    /**
     * 从模板标注转换为返回格式
     */
    private AnnotationItem convertFromTemplateAnnotation(com.ruoyi.system.domain.mongo.VersionAnnotationTemplate.AnnotationItem templateAnnotation) {
        AnnotationItem annotation = new AnnotationItem();

        annotation.setAnnotationId(templateAnnotation.getAnnotationId());
        annotation.setAnnotationType(templateAnnotation.getAnnotationType());
        annotation.setAnnotationName(templateAnnotation.getAnnotationName());
        annotation.setAnnotationValue(templateAnnotation.getAnnotationValue());
        annotation.setRequired(templateAnnotation.getRequired());
        annotation.setDisplayOrder(templateAnnotation.getDisplayOrder());

        // 转换坐标信息
        if (templateAnnotation.getCoordinate() != null) {
            AnnotationCoordinate coordinate = new AnnotationCoordinate();
            coordinate.setX(templateAnnotation.getCoordinate().getX());
            coordinate.setY(templateAnnotation.getCoordinate().getY());
            coordinate.setWidth(templateAnnotation.getCoordinate().getWidth());
            coordinate.setHeight(templateAnnotation.getCoordinate().getHeight());
            annotation.setCoordinate(coordinate);
        }

        return annotation;
    }

    /**
     * 更新图片标注状态
     */
    private void updateImageAnnotationStatus(String imageId, boolean hasAnnotations) {
        try {
            ImageRepository image = imageRepositoryRepo.findByImageId(imageId);
            if (image != null) {
                // 这里可以添加标注状态字段的更新
                // 例如：image.setIsAnnotated(hasAnnotations);
                image.setUpdateTime(new Date());
                imageRepositoryRepo.save(image);
                log.info("更新图片标注状态: imageId={}, hasAnnotations={}", imageId, hasAnnotations);
            }
        } catch (Exception e) {
            log.error("更新图片标注状态失败: imageId={}, error={}", imageId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}
