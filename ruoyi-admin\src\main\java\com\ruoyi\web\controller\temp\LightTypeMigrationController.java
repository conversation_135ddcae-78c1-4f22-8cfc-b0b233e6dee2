package com.ruoyi.web.controller.temp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.mongodb.client.result.UpdateResult;
import java.util.HashMap;
import java.util.Map;

/**
 * 光源类型数据迁移控制器（临时使用）
 * greenfox - 2025-01-15
 * 
 * 使用方法：
 * 1. 访问 http://your-domain/temp/lightType/migrate
 * 2. 执行完成后可以删除此文件
 */
@RestController
@RequestMapping("/temp/lightType")
public class LightTypeMigrationController {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 执行光源类型映射修复
     * 访问地址: POST /temp/lightType/migrate
     */
    @PostMapping("/migrate")
    public AjaxResult migrateLightType() {
        try {
            Map<String, Object> result = new HashMap<>();
            int totalUpdated = 0;
            
            // 1. 红外光类型统一 ir -> infrared
            UpdateResult result1 = mongoTemplate.updateMulti(
                Query.query(Criteria.where("lightType").is("ir")),
                Update.update("lightType", "infrared"),
                "image"
            );
            result.put("ir_to_infrared", result1.getModifiedCount());
            totalUpdated += result1.getModifiedCount();
            
            // 2. 紫外光类型统一 uv -> ultraviolet
            UpdateResult result2 = mongoTemplate.updateMulti(
                Query.query(Criteria.where("lightType").is("uv")),
                Update.update("lightType", "ultraviolet"),
                "image"
            );
            result.put("uv_to_ultraviolet", result2.getModifiedCount());
            totalUpdated += result2.getModifiedCount();
            
            // 3. 全息图类型统一 hologram -> laser_hologram
            UpdateResult result3 = mongoTemplate.updateMulti(
                Query.query(Criteria.where("lightType").is("hologram")),
                Update.update("lightType", "laser_hologram"),
                "image"
            );
            result.put("hologram_to_laser_hologram", result3.getModifiedCount());
            totalUpdated += result3.getModifiedCount();
            
            // 4. 红外右侧光 ir_right -> oblique_right
            UpdateResult result4 = mongoTemplate.updateMulti(
                Query.query(Criteria.where("lightType").is("ir_right")),
                Update.update("lightType", "oblique_right"),
                "image"
            );
            result.put("ir_right_to_oblique_right", result4.getModifiedCount());
            totalUpdated += result4.getModifiedCount();
            
            // 5. 红外左侧光 ir_left -> oblique_left
            UpdateResult result5 = mongoTemplate.updateMulti(
                Query.query(Criteria.where("lightType").is("ir_left")),
                Update.update("lightType", "oblique_left"),
                "image"
            );
            result.put("ir_left_to_oblique_left", result5.getModifiedCount());
            totalUpdated += result5.getModifiedCount();
            
            result.put("total_updated", totalUpdated);
            result.put("message", "光源类型映射修复完成！总计更新 " + totalUpdated + " 条记录");
            
            return AjaxResult.success("数据迁移成功", result);
            
        } catch (Exception e) {
            return AjaxResult.error("数据迁移失败: " + e.getMessage());
        }
    }
    
    /**
     * 查看当前光源类型分布
     * 访问地址: GET /temp/lightType/distribution
     */
    @PostMapping("/distribution")
    public AjaxResult getLightTypeDistribution() {
        try {
            // 查询所有不同的光源类型及其数量
            Map<String, Long> distribution = new HashMap<>();
            
            // 获取所有可能的光源类型
            String[] lightTypes = {
                "visible", "infrared", "ultraviolet", "strong_light", 
                "laser_hologram", "side_light", "oblique_right", "oblique_left",
                "top_light", "bottom_light", "natural", "flash", "led", "halogen",
                // 旧的类型（应该已经被更新）
                "ir", "uv", "hologram", "ir_right", "ir_left"
            };
            
            for (String lightType : lightTypes) {
                long count = mongoTemplate.count(
                    Query.query(Criteria.where("lightType").is(lightType)), 
                    "image"
                );
                if (count > 0) {
                    distribution.put(lightType, count);
                }
            }
            
            return AjaxResult.success("光源类型分布查询成功", distribution);
            
        } catch (Exception e) {
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否还有旧的光源类型值
     * 访问地址: GET /temp/lightType/checkOld
     */
    @PostMapping("/checkOld")
    public AjaxResult checkOldLightTypes() {
        try {
            Map<String, Long> oldTypes = new HashMap<>();
            
            // 检查旧的光源类型
            String[] oldLightTypes = {"ir", "uv", "hologram", "ir_right", "ir_left"};
            
            for (String oldType : oldLightTypes) {
                long count = mongoTemplate.count(
                    Query.query(Criteria.where("lightType").is(oldType)), 
                    "image"
                );
                if (count > 0) {
                    oldTypes.put(oldType, count);
                }
            }
            
            if (oldTypes.isEmpty()) {
                return AjaxResult.success("检查完成：没有发现旧的光源类型值", oldTypes);
            } else {
                return AjaxResult.warn("发现旧的光源类型值，需要执行迁移", oldTypes);
            }
            
        } catch (Exception e) {
            return AjaxResult.error("检查失败: " + e.getMessage());
        }
    }
} 