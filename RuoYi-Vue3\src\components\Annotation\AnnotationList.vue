<template>
  <div class="annotation-list">
    <el-card shadow="never">
      <template #header>
        <div class="list-header">
          <span class="list-title">标注列表 ({{ annotations.length }})</span>
          <div class="list-actions">
            <el-button
              size="small"
              @click="handleSelectAll"
              :disabled="!canEdit"
            >
              全选
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDeleteSelected"
              :disabled="!canEdit || selectedAnnotations.length === 0"
            >
              删除选中
            </el-button>
          </div>
        </div>
      </template>

      <!-- 空状态 -->
      <el-empty
        v-if="annotations.length === 0"
        description="暂无标注数据"
        :image-size="80"
      />

      <!-- 标注列表 -->
      <div v-else class="annotation-items">
        <div
          v-for="(annotation, index) in sortedAnnotations"
          :key="annotation.annotationId || index"
          class="annotation-item"
          :class="{
            'selected': isSelected(annotation),
            'readonly': !canEdit
          }"
          @click="handleItemClick(annotation)"
        >
          <!-- 选择框 -->
          <el-checkbox
            v-if="canEdit"
            :model-value="isSelected(annotation)"
            @change="handleSelectionChange(annotation, $event)"
            @click.stop
          />

          <!-- 标注信息 -->
          <div class="annotation-content">
            <div class="annotation-header">
              <div class="annotation-title">
                <el-tag
                  :type="getAnnotationTypeColor(annotation.annotationType)"
                  size="small"
                >
                  {{ getAnnotationTypeLabel(annotation.annotationType) }}
                </el-tag>
                <span class="annotation-name">{{ annotation.annotationName }}</span>
                <el-tag
                  v-if="annotation.required"
                  type="danger"
                  size="small"
                  effect="plain"
                >
                  必填
                </el-tag>
              </div>
              <div class="annotation-actions" v-if="canEdit">
                <el-button
                  type="primary"
                  size="small"
                  text
                  @click.stop="handleEdit(annotation)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click.stop="handleDelete(annotation)"
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="annotation-details">
              <div v-if="annotation.annotationValue" class="annotation-value">
                <span class="label">标注值：</span>
                <span class="value">{{ annotation.annotationValue }}</span>
              </div>

              <div v-if="annotation.coordinate" class="annotation-coordinate">
                <span class="label">坐标：</span>
                <span class="value">
                  X: {{ annotation.coordinate.x?.toFixed(1) }}%,
                  Y: {{ annotation.coordinate.y?.toFixed(1) }}%,
                  W: {{ annotation.coordinate.width?.toFixed(1) }}%,
                  H: {{ annotation.coordinate.height?.toFixed(1) }}%
                </span>
              </div>

              <div class="annotation-order">
                <span class="label">显示顺序：</span>
                <span class="value">{{ annotation.displayOrder || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 拖拽手柄 -->
          <div v-if="canEdit" class="drag-handle">
            <el-icon><Rank /></el-icon>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑标注"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="标注类型" prop="annotationType">
          <el-select v-model="editForm.annotationType" style="width: 100%">
            <el-option
              v-for="type in annotationTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标注名称" prop="annotationName">
          <el-input v-model="editForm.annotationName" />
        </el-form-item>

        <el-form-item label="标注值" prop="annotationValue">
          <el-input v-model="editForm.annotationValue" />
        </el-form-item>

        <el-form-item label="显示顺序" prop="displayOrder">
          <el-input-number
            v-model="editForm.displayOrder"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="editForm.required">必填项</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Rank } from '@element-plus/icons-vue'
import type { AnnotationItem } from '@/api/cert/annotation'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  /** 标注列表 */
  annotations: AnnotationItem[]
  /** 是否可编辑 */
  canEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  annotations: () => [],
  canEdit: false
})

// Emits
const emit = defineEmits<{
  update: [annotations: AnnotationItem[]]
  edit: [annotation: AnnotationItem]
  delete: [annotation: AnnotationItem]
  select: [annotation: AnnotationItem]
}>()

// 响应式数据
const selectedAnnotations = ref<AnnotationItem[]>([])
const editDialogVisible = ref(false)
const editFormRef = ref<FormInstance>()
const editingAnnotation = ref<AnnotationItem | null>(null)

// 编辑表单
const editForm = reactive({
  annotationType: '',
  annotationName: '',
  annotationValue: '',
  displayOrder: 1,
  required: false
})

// 表单验证规则
const editRules: FormRules = {
  annotationType: [
    { required: true, message: '请选择标注类型', trigger: 'change' }
  ],
  annotationName: [
    { required: true, message: '请输入标注名称', trigger: 'blur' }
  ]
}

// 标注类型选项
const annotationTypes = [
  { label: '文本', value: 'TEXT' },
  { label: '数字', value: 'NUMBER' },
  { label: '日期', value: 'DATE' },
  { label: '区域', value: 'REGION' },
  { label: '其他', value: 'OTHER' }
]

// 计算属性
const sortedAnnotations = computed(() => {
  return [...props.annotations].sort((a, b) => {
    return (a.displayOrder || 0) - (b.displayOrder || 0)
  })
})

// 方法
const isSelected = (annotation: AnnotationItem) => {
  return selectedAnnotations.value.some(item =>
    item.annotationId === annotation.annotationId ||
    (item.annotationName === annotation.annotationName && item.annotationType === annotation.annotationType)
  )
}

const handleSelectionChange = (annotation: AnnotationItem, checked: boolean) => {
  if (checked) {
    if (!isSelected(annotation)) {
      selectedAnnotations.value.push(annotation)
    }
  } else {
    selectedAnnotations.value = selectedAnnotations.value.filter(item =>
      item.annotationId !== annotation.annotationId &&
      !(item.annotationName === annotation.annotationName && item.annotationType === annotation.annotationType)
    )
  }
}

const handleItemClick = (annotation: AnnotationItem) => {
  emit('select', annotation)
}

const handleSelectAll = () => {
  if (selectedAnnotations.value.length === props.annotations.length) {
    selectedAnnotations.value = []
  } else {
    selectedAnnotations.value = [...props.annotations]
  }
}

const handleDeleteSelected = async () => {
  if (selectedAnnotations.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAnnotations.value.length} 个标注吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const updatedAnnotations = props.annotations.filter(annotation =>
      !selectedAnnotations.value.some(selected =>
        selected.annotationId === annotation.annotationId ||
        (selected.annotationName === annotation.annotationName && selected.annotationType === annotation.annotationType)
      )
    )

    emit('update', updatedAnnotations)
    selectedAnnotations.value = []
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleEdit = (annotation: AnnotationItem) => {
  editingAnnotation.value = annotation

  // 填充表单
  editForm.annotationType = annotation.annotationType
  editForm.annotationName = annotation.annotationName
  editForm.annotationValue = annotation.annotationValue || ''
  editForm.displayOrder = annotation.displayOrder || 1
  editForm.required = annotation.required || false

  editDialogVisible.value = true
}

const handleDelete = async (annotation: AnnotationItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标注"${annotation.annotationName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    emit('delete', annotation)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSaveEdit = async () => {
  if (!editFormRef.value || !editingAnnotation.value) return

  try {
    await editFormRef.value.validate()

    const updatedAnnotation: AnnotationItem = {
      ...editingAnnotation.value,
      annotationType: editForm.annotationType,
      annotationName: editForm.annotationName,
      annotationValue: editForm.annotationValue,
      displayOrder: editForm.displayOrder,
      required: editForm.required
    }

    emit('edit', updatedAnnotation)
    editDialogVisible.value = false
    ElMessage.success('保存成功')
  } catch {
    // 验证失败
  }
}

const getAnnotationTypeLabel = (type: string) => {
  const typeOption = annotationTypes.find(option => option.value === type)
  return typeOption?.label || type
}

const getAnnotationTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'TEXT': 'primary',
    'NUMBER': 'success',
    'DATE': 'warning',
    'REGION': 'danger',
    'OTHER': 'info'
  }
  return colorMap[type] || 'info'
}
</script>

<style scoped lang="scss">
.annotation-list {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .list-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .list-actions {
      display: flex;
      gap: 8px;
    }
  }

  .annotation-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .annotation-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-fill-color-extra-light);
    }

    &.selected {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    &.readonly {
      cursor: default;
    }

    .annotation-content {
      flex: 1;
      min-width: 0;

      .annotation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .annotation-title {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .annotation-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }

        .annotation-actions {
          display: flex;
          gap: 4px;
          flex-shrink: 0;
        }
      }

      .annotation-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-regular);

        .label {
          font-weight: 500;
        }

        .value {
          margin-left: 4px;
        }
      }
    }

    .drag-handle {
      display: flex;
      align-items: center;
      color: var(--el-text-color-placeholder);
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }
  }
}
</style>
