<template>
  <TopNavbar />
  <!-- 主要内容区 -->
  <div class="app-container home">
    <!-- 轮播图和新闻列表整体容器 -->
    <div class="carousel-news-container">
      <div class="news-header-container">
        <div class="title-container">
          <div class="title-icon"></div>
          <h2 class="section-title">证研新闻</h2>
        </div>
        <el-button
          :link="true"
          @click="handleMore"
          class="more-btn"
        >
          更多 <i class="el-icon-arrow-right"></i>
        </el-button>
      </div>
      <el-row :gutter="20" class="main-content-row">
        <el-col :span="12" class="carousel-col">
          <div class="carousel-container">
            <el-carousel
              :interval="4000"
              arrow="always"
              height="360px"
              indicator-position="outside"
              class="custom-carousel"
            >
              <el-carousel-item
                v-for="(news, index) in newsList"
                :key="index"
                @click="goToNewsDetail(news.newsId)"
                class="carousel-item"
              >
                <div class="carousel-image-container">
                  <image-preview
                    :src="news.headlinePic"
                    alt="轮播图"
                    class="carousel-image"
                  />
                  <div class="carousel-overlay"></div>
                  <div class="carousel-title-container">
                    <h3 class="carousel-title">{{ news.newsTitle }}</h3>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </el-col>

        <el-col :span="12" class="news-list-col">
          <div class="news-list-container">
            <el-scrollbar class="news-scrollbar">
              <el-menu class="custom-menu">
                <el-menu-item
                  v-for="news in newsList"
                  :key="news.id"
                  class="news-item"
                  @click="goToNewsDetail(news.newsId)"
                >
                  <div class="news-row">
                    <div class="news-content">
                      <span class="news-bullet">•</span>
                      <span class="news-title">{{ news.newsTitle }}</span>
                    </div>
                    <span class="news-date">{{ formatDate(news.uploadTime) }}</span>
                  </div>
                </el-menu-item>
              </el-menu>
            </el-scrollbar>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-divider />
    <el-row :gutter="20">
      <!-- 左侧栏目区域 - 占据2/3宽度 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16">
        <div v-for="(column, index) in activeColumns" :key="index" class="column-container">
          <Newslistshort :title="column.categroyName" />
        </div>
      </el-col>

      <!-- 右侧边栏区域 - 占据1/3宽度 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <SearchForm
          :queryParams="queryParams"
          v-model:dateRange="dateRange"
          :categroyList="categroyList"
          :deptOptions="deptOptions"
          @query="handleQuery"
          @reset="resetQuery"
          formClass="search-form"
        />

        <CountNews class="sidebar-component" />

        <el-card class="box-card sidebar-component">
          <template #header>
            <div class="card-header">光荣榜</div>
          </template>
          <div v-if="honorrollList.length > 0" style="text-align: center;">
            <image-preview :src="honorrollList[currentIndex].honorImaglUrl" :width="270" :height="360" style="margin-top: 20px;"/>
            <p style="margin-top: 20px; font-size: 20px;">{{ honorrollList[currentIndex].honorName }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <FooterComponent />
  </div>
</template>

<script setup name="Index">
import { ref, onMounted, reactive, toRefs, watch, computed } from 'vue';
import Newslistshort from '@/views/news/news/NewsList_short.vue';
import { PublicDeptList } from '@/api/system/dept';
import { listCategroy } from '@/api/news/categroy';
import CountNews from '@/views/news/news/CountNews.vue';
import { listNews } from '@/api/news/news';
import { useRouter } from 'vue-router';
import { PubliclistlistHonorroll } from "@/api/news/honorroll";
import SearchForm from '@/views/news/components/SearchForm.vue';
import FooterComponent from '@/views/news/components/FooterComponent.vue';
import TopNavbar from '@/views/news/components/TopNavbar.vue';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import { ElMessageBox } from 'element-plus';
import { getCurrentInstance } from 'vue';

const router = useRouter();
const userStore = useUserStore();

const deptOptions = ref([]); // 部门树数据
const categroyList = ref([]);
const dateRange = ref([]);
const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    newsTitle: null,
    newsContent: null,  // 添加这行
    author: null,
    newsType: null,
    deptId: null,
    startTime: null,
    endTime: null
  }
});

const { queryParams } = toRefs(data);
const newsList = ref([]);
const honorrollList = ref([]);
const currentIndex = ref(0);
const isLoggedIn = ref(false); // 登录状态
const username = ref(''); // 用户名

// 获取启用状态的栏目，并按照排序号排序
const activeColumns = computed(() => {
  return categroyList.value
    .filter(column => column.categroyStatus === '1') // 只显示启用的栏目
    .sort((a, b) => a.displayOrder - b.displayOrder) // 按照排序号排序
    .slice(0, 4); // 只取前4个栏目
});

function goTarget(url) {
  window.open(url, '__blank')
}

watch(dateRange, (newValue) => {
    if (newValue && newValue.length === 2) {
        queryParams.value.startTime = newValue[0];
        queryParams.value.endTime = newValue[1];
    } else {
        queryParams.value.startTime = null;
        queryParams.value.endTime = null;
    }
});

function getDeptList() {
  PublicDeptList().then(response => {
    deptOptions.value = response.data;
  });
}

const handleMore = () => {
  router.push({
    name: 'NewsList',
    query: {
      title: '证研新闻'
    }
  }).catch(() => {
    // 如果路由跳转失败（比如权限问题），直接使用路径跳转
    window.location.href = '/newslist?title=证研新闻'
  });
};

const getHonorrollList = async () => {
  try {
    const response = await PubliclistlistHonorroll({ pageNum: 1, pageSize: 100 });
    if (response && response.rows) {
      honorrollList.value = response.rows;
      if (honorrollList.value.length > 0) {
        setInterval(() => {
          currentIndex.value = (currentIndex.value + 1) % honorrollList.value.length;
        }, 3000);
      }
    } else {
      proxy.$modal.msgError('光荣榜列表数据格式错误，请联系管理员');
    }
  } catch (error) {
    proxy.$modal.msgError('获取光荣榜列表失败，请检查网络连接或联系管理员');
  }
};

const goToNewsDetail = (newsId) => {
  console.log('Navigating to news detail with ID:', newsId); // 添加调试输出
  if (newsId) {
    router.push({ name: 'NewsDetail', params: { newsId } });
  } else {
    if (newsList.value.length > 0) {
      router.push({ name: 'NewsDetail', params: { newsId: newsList.value[0].newsId } });
    }
  }
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const getNewsList = async () => {
  try {
    const response = await listNews({
      newsType: 1,
      pageNum: 1,
      pageSize: 5
    });
    if (response && response.rows) {
      newsList.value = response.rows;
    } else {
      proxy.$modal.msgError('新闻列表数据格式错误，请联系管理员');
    }
  } catch (error) {
    proxy.$modal.msgError('获取新闻列表失败，请检查网络连接或联系管理员');
  }
};

// 搜索按钮
function handleQuery() {
  const searchFields = [
    'newsTitle',
    'newsContent',
    'author',
    'newsType',
    'deptId',
    'startTime',
    'endTime'
  ];

  const hasSearchParams = searchFields.some((field) => {
    const value = queryParams.value[field];
    if (typeof value === 'string') {
      return value.trim() !== '';
    }
    return value !== null && value !== undefined;
  });
  if (hasSearchParams) {
    router.push({
    name: 'NewsList',
    query: {
      ...queryParams.value,
      startTime: queryParams.value.startTime,
      endTime: queryParams.value.endTime
    }
  })
  }
}

//重置按钮
function resetQuery() {
  queryParams.value.newsTitle = null;
  queryParams.value.author = null;
  queryParams.value.newsType = null;
  queryParams.value.deptId = null;
  queryParams.value.startTime = null;
  queryParams.value.endTime = null;
  dateRange.value = [];
}

const getCategroyList = async () => {
  try {
    const response = await listCategroy({ pageNum: 1, pageSize: 100 });
    if (response && response.rows) {
      categroyList.value = response.rows;
    } else {
      proxy.$modal.msgError('文章类型列表数据格式错误，请联系管理员');
    }
  } catch (error) {
    proxy.$modal.msgError('获取文章类型列表失败，请检查网络连接或联系管理员');
  }
};

const openLoginModal = () => {
  router.push({ name: 'login' }); // 跳转到登录页面
};



// 检查用户登录状态
const checkLoginStatus = () => {
  if (getToken()) {
    // 如果有 token，尝试获取用户信息
    const userInfo = userStore;
    if (userInfo && userInfo.name) {
      isLoggedIn.value = true;
      username.value = userInfo.name;
    } else {
      // 如果有 token 但没有用户信息，可能需要重新获取用户信息
      userStore.getInfo().then(res => {
        isLoggedIn.value = true;
        username.value = userStore.name;
      }).catch(() => {
        isLoggedIn.value = false;
        username.value = '';
      });
    }
  } else {
    isLoggedIn.value = false;
    username.value = '';
  }
};

onMounted(() => {
  checkLoginStatus();
  getCategroyList();
  getNewsList();
  getHonorrollList();

});
getDeptList(); // 调用获取部门列表数据的方法
</script>

<style scoped lang="scss">
.home {
  padding: 20px 10%;
  background-color: #f5f9fd;

  ul {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #333;
  overflow-x: hidden;

  h4 {
    margin-top: 0px;
  }

  h1 {
    font-size: 36px;
    color: #333;
    margin: 15px 0;
    font-weight: bold;
  }

  h2 {
    font-size: 28px;
    color: #333;
    margin: 10px 0;
    font-weight: bold;
  }

  p {
    margin-top: 10px;
    line-height: 1.6;

    b {
      font-weight: 700;
    }
  }
}

.column-container {
  margin-bottom: 25px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: auto;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
  }
}

.sidebar-component {
  margin-top: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 530px;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .card-header {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 15px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background-color: #409EFF;
      border-radius: 2px;
    }
  }

  .t {
    font-size: 18px;
  }
}

.carousel-news-container {
  border: 1px solid #DCDFE6;
  border-radius: 8px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}

.news-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 15px;

  h2 {
    margin: 0;
    font-size: 28px;
    margin-left: 20px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: -15px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background-color: #409EFF;
      border-radius: 2px;
    }
  }

  .more-btn {
    padding: 0;
    margin-right: 15px;
    color: #409EFF;
    font-size: 16px;
    font-weight: normal;
    transition: all 0.3s ease;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
      transform: translateX(3px);
    }
  }
}

.el-divider {
  margin: 15px 0;
}

.news-item {
  padding: 36px 15px;
  transition: all 0.3s ease;
  border-radius: 4px;

  &:hover {
    background-color: #f0f7ff;
  }

  .news-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-width: 0;
    border: none;
  }

  .news-title {
    position: relative;
    padding-left: 15px;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 120px);
    color: #333;
    transition: color 0.3s;

    &:hover {
      color: #409EFF;
    }

    &::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #409EFF;
      font-size: 18px;
    }
  }

  .news-date {
    font-size: 14px;
    white-space: nowrap;
    margin-left: 15px;
    color: #999;
  }
}

.platform-container {
  border: 1px solid #DCDFE6;
  border-radius: 8px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}


.carousel-col {
    .carousel-image-container {
    .carousel-title {
    position: absolute;
    bottom: 10px;
    left: 10px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 5px;
    font-size: 20px;
    }
  }
  /* 修改轮播图指示器样式 */
  .custom-carousel {
    // 指示器容器
    :deep(.el-carousel__indicators) {
      bottom: 10px; // 调整指示器位置

      // 单个指示器按钮
      .el-carousel__indicator {
        padding: 20px 10px; // 调整按钮间距

        // 指示器横线
        .el-carousel__button {
          width: 60px; // 宽度
          height: 6px; // 高度/粗细
          background-color: #409EFF; // 默认颜色
          border-radius: 2px; // 圆角
          opacity: 0.8; // 透明度
          transition: all 0.3s ease; // 过渡效果
        }

        // 激活状态的指示器
        &.is-active {
          .el-carousel__button {
            background-color: #de753b; // 激活颜色
            width: 80px; // 激活时宽度
          }
        }
      }
    }
  }
  :deep(.el-carousel__arrow) {
    width: 36px;       // 宽度
    height: 36px;      // 高度
    background-color: rgba(0, 0, 0, 0.3); // 背景色（半透明黑）
    color: white;      // 图标颜色
    border-radius: 50%; // 圆形按钮
    /* 图标大小 */
    i {
      font-size: 35px; // 调整图标大小
    }
  }

  /* 左侧箭头 */
  :deep(.el-carousel__arrow--left) {
    left: 20px; // 调整左箭头位置
  }

  /* 右侧箭头 */
  :deep(.el-carousel__arrow--right) {
    right: 20px; // 调整右箭头位置
  }
}


/* 响应式设计 */
@media (max-width: 992px) {
  .main-content-row {
    flex-direction: column;

    .carousel-col,
    .news-list-col {
      width: 100%;
      padding: 0;
    }

    .carousel-col {
      margin-bottom: 20px;
    }
  }

  .top-container {
    padding: 0 20px;

    h3 {
      font-size: 16px;
    }

    .button-group {
      .nav-button {
        margin-left: 8px;
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }
}
</style>
