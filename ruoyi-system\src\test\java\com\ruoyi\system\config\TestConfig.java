package com.ruoyi.system.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

import static org.mockito.Mockito.mock;

/**
 * 测试配置类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@TestConfiguration
public class TestConfig {
    
    /**
     * 模拟MongoTemplate用于单元测试
     */
    @Bean
    @Primary
    public MongoTemplate mongoTemplate() {
        return mock(MongoTemplate.class);
    }
}
