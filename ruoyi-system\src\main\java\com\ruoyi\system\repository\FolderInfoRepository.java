package com.ruoyi.system.repository;

import com.ruoyi.system.domain.mongo.FolderInfo;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 文件夹信息Repository
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface FolderInfoRepository extends MongoRepository<FolderInfo, String> {

    /**
     * 根据文件夹ID查找
     *
     * @param folderId 文件夹ID
     * @return 文件夹信息
     */
    FolderInfo findByFolderId(String folderId);

    /**
     * 根据任务ID查找文件夹信息
     *
     * @param taskId 任务ID
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByTaskId(String taskId);

    /**
     * 根据任务ID查找单个文件夹信息（用于批量上传场景）
     *
     * @param taskId 任务ID
     * @return 文件夹信息
     */
    Optional<FolderInfo> findFirstByTaskId(String taskId);

    /**
     * 根据版本ID查找文件夹信息
     *
     * @param versionId 版本ID
     * @return 文件夹信息列表
     */
    @Query("{'associationInfo.versionId': ?0}")
    List<FolderInfo> findByVersionId(String versionId);

    /**
     * 根据文件夹类型查找
     *
     * @param folderType 文件夹类型
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByFolderType(String folderType);

    /**
     * 根据国家信息查找
     *
     * @param countryId 国家ID
     * @return 文件夹信息列表
     */
    @Query("{'countryInfo.id': ?0}")
    List<FolderInfo> findByCountryId(Long countryId);

    /**
     * 根据证件类型查找
     *
     * @param certTypeId 证件类型ID
     * @return 文件夹信息列表
     */
    @Query("{'certInfo.id': ?0}")
    List<FolderInfo> findByCertTypeId(Long certTypeId);

    /**
     * 根据发行年份查找
     *
     * @param issueYear 发行年份
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByIssueYear(String issueYear);

    /**
     * 根据状态查找
     *
     * @param status 状态
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByStatus(String status);

    /**
     * 根据部门查找
     *
     * @param deptId 部门ID
     * @return 文件夹信息列表
     */
    @Query("{'deptInfo.deptId': ?0}")
    List<FolderInfo> findByDeptId(Long deptId);

    /**
     * 根据上传者查找
     *
     * @param userId 用户ID
     * @return 文件夹信息列表
     */
    @Query("{'uploaderInfo.userId': ?0}")
    List<FolderInfo> findByUploaderId(Long userId);

    /**
     * 根据任务ID和文件夹名称查找
     *
     * @param taskId 任务ID
     * @param folderName 文件夹名称
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByTaskIdAndFolderName(String taskId, String folderName);

    /**
     * 根据创建时间范围查询
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByCreateTimeBetween(Date startTime, Date endTime);

    /**
     * 根据国家、证件类型和发行年份查找
     *
     * @param countryId 国家ID
     * @param certTypeId 证件类型ID
     * @param issueYear 发行年份
     * @return 文件夹信息列表
     */
    @Query("{'countryInfo.id': ?0, 'certInfo.id': ?1, 'issueYear': ?2}")
    List<FolderInfo> findByCountryIdAndCertTypeIdAndIssueYear(Long countryId, Long certTypeId, String issueYear);

    /**
     * 删除指定任务的所有文件夹信息
     *
     * @param taskId 任务ID
     */
    void deleteByTaskId(String taskId);

    /**
     * 根据关键词搜索文件夹（模糊匹配文件夹名称）
     *
     * @param keyword 关键词
     * @return 文件夹信息列表
     */
    @Query("{'folderName': {$regex: ?0, $options: 'i'}}")
    List<FolderInfo> findByFolderNameContainingIgnoreCase(String keyword);

    /**
     * 根据国家代码查找
     *
     * @param countryCode 国家代码
     * @return 文件夹信息列表
     */
    @Query("{'countryInfo.code': ?0}")
    List<FolderInfo> findByCountryCode(String countryCode);

    /**
     * 根据证件类型代码查找
     *
     * @param certTypeCode 证件类型代码
     * @return 文件夹信息列表
     */
    @Query("{'certInfo.zjlbdm': ?0}")
    List<FolderInfo> findByCertTypeCode(String certTypeCode);

    // ==================== 多级文件夹支持的查询方法 ====================

    /**
     * 根据父任务ID查找所有文件夹版本
     *
     * @param parentTaskId 父任务ID
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByParentTaskId(String parentTaskId);

    /**
     * 根据文件夹路径查找
     *
     * @param folderPath 完整文件夹路径
     * @return 文件夹信息
     */
    FolderInfo findByFolderPath(String folderPath);

    /**
     * 根据相对路径查找
     *
     * @param relativePath 相对路径
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByRelativePath(String relativePath);

    /**
     * 根据父任务ID和相对路径查找
     *
     * @param parentTaskId 父任务ID
     * @param relativePath 相对路径
     * @return 文件夹信息
     */
    FolderInfo findByParentTaskIdAndRelativePath(String parentTaskId, String relativePath);

    /**
     * 查找多版本任务的文件夹
     *
     * @param isMultiVersion 是否为多版本
     * @return 文件夹信息列表
     */
    List<FolderInfo> findByIsMultiVersion(Boolean isMultiVersion);

    /**
     * 根据父任务ID统计版本数量
     *
     * @param parentTaskId 父任务ID
     * @return 版本数量
     */
    long countByParentTaskId(String parentTaskId);

    /**
     * 根据父任务ID查找已完成上传的版本
     *
     * @param parentTaskId 父任务ID
     * @return 已完成版本列表
     */
    @Query("{'parentTaskId': ?0, 'uploadProgress': 100.0}")
    List<FolderInfo> findCompletedVersionsByParentTaskId(String parentTaskId);

    /**
     * 根据父任务ID查找上传中的版本
     *
     * @param parentTaskId 父任务ID
     * @return 上传中版本列表
     */
    @Query("{'parentTaskId': ?0, 'uploadProgress': {$lt: 100.0}}")
    List<FolderInfo> findUploadingVersionsByParentTaskId(String parentTaskId);

    /**
     * 根据父任务ID和版本索引查找
     *
     * @param parentTaskId 父任务ID
     * @param versionIndex 版本索引
     * @return 文件夹信息
     */
    FolderInfo findByParentTaskIdAndVersionIndex(String parentTaskId, Integer versionIndex);

    /**
     * 批量删除指定父任务的所有版本
     *
     * @param parentTaskId 父任务ID
     */
    void deleteByParentTaskId(String parentTaskId);

    /**
     * 根据文件夹路径列表批量查找
     *
     * @param folderPaths 文件夹路径列表
     * @return 文件夹信息列表
     */
    @Query("{'folderPath': {$in: ?0}}")
    List<FolderInfo> findByFolderPathIn(List<String> folderPaths);

    /**
     * 根据父任务ID更新所有版本的状态
     *
     * @param parentTaskId 父任务ID
     * @param status 新状态
     * @return 更新数量
     */
    @Query("{'parentTaskId': ?0}")
    List<FolderInfo> findByParentTaskIdForUpdate(String parentTaskId);

    // ==================== 标准样本相关查询方法 ====================

    /**
     * 根据版本ID和文件夹类型查找文件夹
     */
    @Query("{'associationInfo.versionId': ?0, 'folderType': ?1}")
    Optional<FolderInfo> findByVersionIdAndFolderType(String versionId, String folderType);

    /**
     * 检查版本是否已有标准样本文件夹
     */
    @Query("{'associationInfo.versionId': ?0, 'folderType': ?1}")
    boolean existsByVersionIdAndFolderType(String versionId, String folderType);

    /**
     * 根据版本ID查找所有标准样本文件夹（返回列表）
     */
    @Query("{'associationInfo.versionId': ?0, 'folderType': ?1}")
    List<FolderInfo> findAllByVersionIdAndFolderType(String versionId, String folderType);

    /**
     * 统计版本的标准样本文件夹数量
     */
    @Query("{'associationInfo.versionId': ?0, 'folderType': ?1}")
    long countByVersionIdAndFolderType(String versionId, String folderType);
}