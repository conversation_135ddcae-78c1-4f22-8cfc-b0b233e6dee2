<template>
  <el-card class="folder-list-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>文件夹列表 ({{ folders.length }})</span>
        <div class="header-controls">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件夹..."
            size="small"
            style="width: 150px; margin-right: 8px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <!-- 状态筛选 -->
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            size="small"
            style="width: 100px;"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="已审核" value="approved" />
            <el-option label="未审核" value="pending" />
            <el-option label="已驳回" value="rejected" />
          </el-select>
        </div>
      </div>
    </template>

    <div v-loading="loading" class="folder-list">
      <!-- 标准样本文件夹 -->
      <div v-if="standardFolders.length > 0" class="folder-section">
        <div class="section-title">
          <el-icon class="section-icon"><Star /></el-icon>
          标准样本文件夹
        </div>
        <div
          v-for="folder in standardFolders"
          :key="folder.folderId"
          class="folder-item standard-folder"
          :class="{ active: selectedFolderId === folder.folderId }"
          @click="handleFolderSelect(folder)"
        >
          <!-- 缩略图 -->
          <div class="folder-thumbnail">
            <img
              v-if="getMainPicUrl(folder.mainPicPath)"
              :src="getMainPicUrl(folder.mainPicPath)"
              :alt="folder.folderName"
              @error="handleImageError"
            />
            <div v-else class="thumbnail-placeholder">
              <el-icon><Picture /></el-icon>
            </div>
          </div>

          <!-- 文件夹信息 -->
          <div class="folder-info">
            <div class="folder-name">
              <el-icon class="standard-icon"><Star /></el-icon>
              {{ folder.folderName }}
            </div>
            <div class="folder-meta">
              <span class="image-count">{{ folder.imageCount || 0 }} 张图片</span>
              <el-tag :type="getReviewStatusType(folder.reviewStatus)" size="small">
                {{ getReviewStatusText(folder.reviewStatus) }}
              </el-tag>
            </div>
            <div class="folder-tags">
              <el-tag type="success" size="small" effect="dark">
                标准样本
              </el-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="folder-actions">
            <el-button
              type="text"
              size="small"
              @click.stop="handleFolderDetail(folder)"
            >
              详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 普通文件夹 -->
      <div v-if="ordinaryFolders.length > 0" class="folder-section">
        <div class="section-title">
          <el-icon class="section-icon"><Folder /></el-icon>
          普通文件夹
        </div>
        <div
          v-for="folder in ordinaryFolders"
          :key="folder.folderId"
          class="folder-item ordinary-folder"
          :class="{ active: selectedFolderId === folder.folderId }"
          @click="handleFolderSelect(folder)"
        >
          <!-- 缩略图 -->
          <div class="folder-thumbnail">
            <img
              v-if="getMainPicUrl(folder.mainPicPath)"
              :src="getMainPicUrl(folder.mainPicPath)"
              :alt="folder.folderName"
              @error="handleImageError"
            />
            <div v-else class="thumbnail-placeholder">
              <el-icon><Picture /></el-icon>
            </div>
          </div>

          <!-- 文件夹信息 -->
          <div class="folder-info">
            <div class="folder-name">{{ folder.folderName }}</div>
            <div class="folder-meta">
              <span class="image-count">{{ folder.imageCount || 0 }} 张图片</span>
              <el-tag :type="getReviewStatusType(folder.reviewStatus)" size="small">
                {{ getReviewStatusText(folder.reviewStatus) }}
              </el-tag>
            </div>
            <div class="folder-tags">
              <el-tag type="info" size="small" effect="plain">
                普通样本
              </el-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="folder-actions">
            <el-button
              type="text"
              size="small"
              @click.stop="handleFolderDetail(folder)"
            >
              详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredFolders.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无文件夹">
          <template v-if="hasFilters">
            <el-button @click="clearFilters">清除筛选条件</el-button>
          </template>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Search, Star, Folder, Picture } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  folders: {
    type: Array,
    default: () => []
  },
  selectedFolderId: {
    type: String,
    default: ''
  },
  standardFolderId: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['folder-select', 'folder-detail'])

// 筛选状态
const searchKeyword = ref('')
const statusFilter = ref('')

// 筛选后的文件夹列表
const filteredFolders = computed(() => {
  let filtered = props.folders

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(folder =>
      folder.folderName.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(folder => folder.reviewStatus === statusFilter.value)
  }

  return filtered
})

// 标准样本文件夹
const standardFolders = computed(() => {
  return filteredFolders.value.filter(folder => folder.folderId === props.standardFolderId)
})

// 普通文件夹
const ordinaryFolders = computed(() => {
  return filteredFolders.value.filter(folder => folder.folderId !== props.standardFolderId)
})

// 是否有筛选条件
const hasFilters = computed(() => {
  return searchKeyword.value || statusFilter.value
})

// 清除筛选条件
const clearFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
}

// 事件处理
const handleFolderSelect = (folder) => {
  emit('folder-select', folder)
}

const handleFolderDetail = (folder) => {
  emit('folder-detail', folder)
}

// 工具方法
const getMainPicUrl = (mainPicPath) => {
  if (!mainPicPath) return ''
  
  const directMinioUrl = `http://localhost:9000/xjlfiles/${mainPicPath}`
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`
  
  return proxyUrl
}

const handleImageError = (event) => {
  const img = event.target
  img.style.display = 'none'
}

const getReviewStatusType = (status) => {
  const statusMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return statusMap[status] || 'info'
}

const getReviewStatusText = (status) => {
  const statusMap = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未审核'
}
</script>

<style scoped>
.folder-list-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 300px;
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.folder-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.folder-section {
  margin-bottom: 16px;
}

.folder-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  padding: 0 8px;
}

.section-icon {
  font-size: 16px;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.folder-item:hover {
  background-color: #f5f7fa;
}

.folder-item.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

/* 标准样本文件夹特殊样式 */
.folder-item.standard-folder {
  border-left: 3px solid #67c23a;
  background-color: rgba(103, 194, 58, 0.05);
}

.folder-item.standard-folder:hover {
  background-color: rgba(103, 194, 58, 0.1);
}

.folder-item.standard-folder.active {
  background-color: #e6f7ff;
  border-color: #67c23a;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
}

/* 普通文件夹样式 */
.folder-item.ordinary-folder {
  border-left: 2px solid #dcdfe6;
}

.folder-item.ordinary-folder:hover {
  border-left-color: #909399;
}

.folder-item.ordinary-folder.active {
  border-left-color: #1890ff;
}

.folder-thumbnail {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.folder-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 20px;
}

.folder-info {
  flex: 1;
  min-width: 0;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;
}

.standard-icon {
  color: #67c23a;
  font-size: 14px;
}

.folder-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.image-count {
  font-size: 12px;
  color: #909399;
}

.folder-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.folder-actions {
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.folder-item:hover .folder-actions {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .folder-list-card {
    width: 100%;
    height: 200px;
  }

  .folder-list {
    max-height: 150px;
  }

  .folder-item {
    padding: 8px;
  }

  .folder-thumbnail {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
}

@media (max-width: 768px) {
  .header-controls {
    width: 100%;
    justify-content: flex-start;
  }

  .folder-item {
    padding: 6px;
  }

  .folder-thumbnail {
    width: 35px;
    height: 35px;
    margin-right: 6px;
  }

  .folder-name {
    font-size: 12px;
  }

  .image-count {
    font-size: 10px;
  }
}
</style>
