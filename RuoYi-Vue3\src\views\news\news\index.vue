<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="文章标题" prop="newsTitle">
        <el-input
          v-model="queryParams.newsTitle"
          placeholder="请输入文章标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文章内容" prop="newsContent">
        <el-input
          v-model="queryParams.newsContent"
          placeholder="请输入文章内容关键词"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作者" prop="author" v-hasPermi="['news:news:audit']">
        <el-input
          v-model="queryParams.author"
          placeholder="请输入作者"
          clearable
          @keyup.enter="handleQuery"
          style="width: 120px;"

        />
      </el-form-item>
      <el-form-item label="上传时间" prop="uploadTime">
        <el-date-picker clearable
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="文章栏目" prop="newsType">
        <el-select v-model="queryParams.newsType" placeholder="请选择文章栏目" clearable style="width: 150px">
          <el-option
            v-for="option in categroyList"
            :key="option.categroyID"
            :label="option.categroyName"
            :value="option.categroyID">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName" v-hasPermi="['news:news:audit']">
        <el-select v-model="queryParams.deptId" placeholder="请选择部门名称" clearable style="width: 150px">
          <el-option
            v-for="option in deptOptions"
            :key="option.deptId"
            :label="option.deptName"
            :value="option.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable style="width: 100px">
          <el-option
            v-for="option in sys_news_status"
            :key="option.value"
            :label="option.label"
            :value="option.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['news:news:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleAudit"
          v-hasPermi="['news:news:audit']"
        >审核</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['news:news:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['news:news:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['news:news:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="newsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文章编号" align="center" prop="newsId" />
      <el-table-column label="文章标题" align="center" prop="newsTitle" width="300"/>
      <el-table-column label="文章栏目" align="center">
        <template #default="scope">
          <span>{{ getCategroyName(scope.row.newsType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="sys_news_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" prop="reviewer" width="100" />
      <el-table-column label="审核时间" align="center" prop="reviewTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reviewTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="作者" align="center" prop="author" width="100" />
      <el-table-column label="上传时间" align="center" prop="uploadTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门名称" align="center" prop="deptName" width="120">

    </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleAudit(scope.row)" v-hasPermi="['news:news:audit']">审核</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['news:news:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['news:news:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <AuditNews
      :visible="auditopen"
      @update:visible="auditopen = $event"
      :title="title"
      :formData="form"
      :isAudit="isAudit"
      @success="getList"
      @close="handleAuditClose"
      v-hasPermi="['news:news:edit']"
    />
    <AddNews
      :visible="open"
      @update:visible="open = $event"
      :title="title"
      :formData="form"
      @success="getList"
      @close="handleClose"
      v-hasPermi="['news:news:add']"
    />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import AuditNews from './AuditNews.vue';
import AddNews from './AddNews.vue';
import { adminListNews, getNews, delNews } from "@/api/news/news";
import { checkPermi } from "@/utils/permission";
import { PublicDeptList } from "@/api/system/dept";
import { listCategroy } from '@/api/news/categroy';
import { provide } from 'vue';
const { proxy } = getCurrentInstance();
const { sys_news_status } = proxy.useDict("sys_news_status");
const newsList = ref([]);
const auditopen = ref(false);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const deptOptions = ref([]); // 部门树数据
const categroyList = ref([]);
// 提供栏目列表
provide('categroyList', categroyList);
const isAudit = ref(false); // 定义响应式变量，判断是审核还是修改
const dateRange = ref([]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    newsTitle: null,
    newsType: null,
    newsContent: null,
    status: null,
    reviewer: null,
    reviewTime: null,
    author: null,
    uploadTime: null,
    deptName: null,
    headlinePic: null,
    deptId: null,
    startTime: null,
    endTime: null
  },
});
const { queryParams, form} = toRefs(data);
/** 查询文章管理列表 */
function getList() {
  loading.value = true;
  adminListNews(queryParams.value).then(response => {
    newsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 表单重置
function reset() {
  form.value = {
    newsId: null,
    newsTitle: null,
    newsType: null,
    newsContent: null,
    status: undefined,
    reviewer: null,
    reviewTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    author: null,
    uploadTime: null,
    deptName: undefined,
    headlinePic: null,
    deptId: undefined,
    startTime: null,
    endTime: null
  };
  proxy.resetForm("newsRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dateRange.value = [];
  queryParams.value.deptId = null;
  reset();
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.newsId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 监听日期范围变化并更新查询参数
watch(dateRange, (newValue) => {
    if (newValue && newValue.length === 2) {
        queryParams.value.startTime = newValue[0];
        queryParams.value.endTime = newValue[1];
    } else {
        queryParams.value.startTime = null;
        queryParams.value.endTime = null;
    }
});
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加文章";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _newsId = row.newsId || ids.value;
  getNews(_newsId).then(response => {
    const isAdmin = checkPermi(['news:news:audit']);
    form.value = response.data;
    if (isAdmin) {
      auditopen.value = true;
      title.value = "修改文章";
      isAudit.value = false; // 修改时设置为 false
    }
    else {
      if (form.value.status == 1) {
        ElMessage.warning('文章已发布，不能进行修改。');
        return;
      }
      else {
        form.value.status = 0;
        auditopen.value = true;
        title.value = "修改文章";
        isAudit.value = false; // 修改时设置为 false
      }
    }
  });
}

/** 审核按钮操作 */
function handleAudit(row) {
  reset();
  const _newsId = row.newsId || ids.value;
  getNews(_newsId).then(response => {
    form.value = response.data;
    auditopen.value = true;
    title.value = "审核文章";
    isAudit.value = true; // 审核时设置为 true
  })
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _newsIds = row.newsId || ids.value;
  proxy.$modal.confirm('是否确认删除文章管理编号为"' + _newsIds + '"的数据项？').then(function() {
    return delNews(_newsIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download('news/news/export', {
  ...queryParams.value
  }, `news_${new Date().getTime()}.xlsx`);
}
// 处理审核和修改页面关闭事件
function handleAuditClose() {
  auditopen.value = false;
  isAudit.value = false; // 关闭时重置为默认值
}
// 处理关闭事件
function handleClose() {
  open.value = false;
}
/** 查询部门列表 */
function getDeptList() {
  PublicDeptList().then(response => {
    deptOptions.value = response.data;
  });
}
// 获取文章类型列表
const getCategroyList = async () => {
  try {
    const response = await listCategroy({ pageNum: 1, pageSize: 100 });
    if (response && response.rows) {
      categroyList.value = response.rows;

    } else {
      proxy.$modal.msgError('文章类型列表数据格式错误，请联系管理员');
    }
  } catch (error) {
    proxy.$modal.msgError('获取文章类型列表失败，请检查网络连接或联系管理员');
  }
};

// 根据 newsType 获取 categroyName
const getCategroyName = (newsType) => {
  if (categroyList.value && categroyList.value.length > 0) {
    const categroy = categroyList.value.find(item => item.categroyID === newsType);
    return categroy ? categroy.categroyName : '';
  }
  return '';
};

// 在组件挂载时获取文章类型列表
onMounted(() => {
  getCategroyList();
});
getDeptList(); // 调用获取部门列表数据的方法
getList();
</script>
