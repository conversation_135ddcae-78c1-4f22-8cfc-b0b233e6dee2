package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.domain.dto.request.AnnotationTemplateDTO;

import java.util.List;
import java.util.Optional;

/**
 * 版本标注模板服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IVersionAnnotationTemplateService {
    
    /**
     * 获取版本指定类型的标注模板
     */
    Optional<VersionAnnotationTemplate> getTemplate(String versionId, String imageType);
    
    /**
     * 创建或更新标注模板
     */
    VersionAnnotationTemplate saveTemplate(String versionId, String imageType, 
                                         AnnotationTemplateDTO templateDTO, String standardFolderId);
    
    /**
     * 获取版本所有标注模板
     */
    List<VersionAnnotationTemplate> getVersionTemplates(String versionId);
    
    /**
     * 删除版本所有标注模板
     */
    void deleteVersionTemplates(String versionId);
    
    /**
     * 检查图片是否可标注
     */
    boolean canAnnotateImage(String imageId);
    
    /**
     * 获取图片标注权限信息
     */
    AnnotationPermissionInfo getAnnotationPermission(String imageId);
    
    /**
     * 根据模板ID获取模板
     */
    Optional<VersionAnnotationTemplate> getTemplateById(String templateId);
    
    /**
     * 删除指定模板
     */
    boolean deleteTemplate(String templateId);
    
    /**
     * 检查版本是否已有指定类型的模板
     */
    boolean hasTemplate(String versionId, String imageType);
    
    /**
     * 根据标准文件夹ID获取模板列表
     */
    List<VersionAnnotationTemplate> getTemplatesByStandardFolder(String standardFolderId);
    
    /**
     * 删除标准文件夹的所有模板
     */
    void deleteTemplatesByStandardFolder(String standardFolderId);
    
    /**
     * 复制模板到新版本
     */
    List<VersionAnnotationTemplate> copyTemplatesToVersion(String sourceVersionId, String targetVersionId, String newStandardFolderId);
    
    /**
     * 验证模板数据有效性
     */
    boolean validateTemplate(AnnotationTemplateDTO templateDTO);
    
    /**
     * 获取模板统计信息
     */
    TemplateStatistics getTemplateStatistics(String versionId);
    
    /**
     * 标注权限信息DTO
     */
    class AnnotationPermissionInfo {
        private boolean canEdit;
        private boolean canView;
        private String reason;
        private VersionAnnotationTemplate template;
        private String imageType;
        private boolean isStandardSample;
        private boolean isAnnotatableType;

        // 构造函数
        public AnnotationPermissionInfo() {}
        
        public AnnotationPermissionInfo(boolean canEdit, boolean canView, String reason) {
            this.canEdit = canEdit;
            this.canView = canView;
            this.reason = reason;
        }

        // getters and setters
        public boolean isCanEdit() { return canEdit; }
        public void setCanEdit(boolean canEdit) { this.canEdit = canEdit; }
        
        public boolean isCanView() { return canView; }
        public void setCanView(boolean canView) { this.canView = canView; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public VersionAnnotationTemplate getTemplate() { return template; }
        public void setTemplate(VersionAnnotationTemplate template) { this.template = template; }
        
        public String getImageType() { return imageType; }
        public void setImageType(String imageType) { this.imageType = imageType; }
        
        public boolean isStandardSample() { return isStandardSample; }
        public void setStandardSample(boolean standardSample) { isStandardSample = standardSample; }
        
        public boolean isAnnotatableType() { return isAnnotatableType; }
        public void setAnnotatableType(boolean annotatableType) { isAnnotatableType = annotatableType; }
    }
    
    /**
     * 模板统计信息DTO
     */
    class TemplateStatistics {
        private int totalTemplates;
        private int annotatedTypes;
        private List<String> availableTypes;
        private String standardFolderId;
        private boolean hasStandardFolder;

        // 构造函数
        public TemplateStatistics() {}

        // getters and setters
        public int getTotalTemplates() { return totalTemplates; }
        public void setTotalTemplates(int totalTemplates) { this.totalTemplates = totalTemplates; }
        
        public int getAnnotatedTypes() { return annotatedTypes; }
        public void setAnnotatedTypes(int annotatedTypes) { this.annotatedTypes = annotatedTypes; }
        
        public List<String> getAvailableTypes() { return availableTypes; }
        public void setAvailableTypes(List<String> availableTypes) { this.availableTypes = availableTypes; }
        
        public String getStandardFolderId() { return standardFolderId; }
        public void setStandardFolderId(String standardFolderId) { this.standardFolderId = standardFolderId; }
        
        public boolean isHasStandardFolder() { return hasStandardFolder; }
        public void setHasStandardFolder(boolean hasStandardFolder) { this.hasStandardFolder = hasStandardFolder; }
    }
}
