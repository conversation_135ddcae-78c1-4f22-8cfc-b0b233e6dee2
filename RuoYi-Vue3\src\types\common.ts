/**
 * 通用业务类型定义
 * 统一管理所有业务相关的接口类型
 */

/** 国家信息 */
export interface Country {
  /** 国家ID */
  id: number
  /** 国家名称(中文) */
  name: string
  /** 国家名称(英文) */
  nameEn: string
  /** 国家代码(三位字母) */
  code: string
  /** 国旗图标路径 */
  flagIcon?: string
}

/** 证件类型信息 */
export interface CertType {
  /** 证件类型ID */
  id: number
  /** 证件种类名称 */
  zjlbmc: string
  /** 证件种类代码 */
  zjlbdm: string
  /** 证件简称 */
  zjjc?: string
  /** 前台使用标记 */
  sybj?: string
  /** 专业类型 */
  zyType?: string
}

/** 部门信息 */
export interface SysDept {
  /** 部门ID */
  deptId: number
  /** 部门名称 */
  deptName: string
  /** 父部门ID */
  parentId?: number
  /** 部门层级 */
  ancestors?: string
  /** 显示顺序 */
  orderNum?: number
  /** 负责人 */
  leader?: string
  /** 联系电话 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 部门状态 */
  status?: string
}

/** 用户信息 */
export interface SysUser {
  /** 用户ID */
  userId: number
  /** 用户名 */
  userName: string
  /** 昵称 */
  nickName: string
  /** 邮箱 */
  email?: string
  /** 手机号码 */
  phoneNumber?: string
  /** 性别 */
  sex?: string
  /** 头像 */
  avatar?: string
  /** 部门ID */
  deptId?: number
  /** 部门信息 */
  deptInfo?: SysDept
  /** 状态 */
  status?: string
}

/** 统一返回结果 */
export interface AjaxResult<T = any> {
  /** 状态码 */
  code: number
  /** 返回消息 */
  msg: string
  /** 返回数据 */
  data: T
}

/** 分页数据返回结果 */
export interface TableDataInfo<T = any> {
  /** 状态码 */
  code: number
  /** 返回消息 */
  msg: string
  /** 数据列表 */
  rows: T
  /** 总记录数 */
  total: number
}

/** 训练图片信息 */
export interface TrainingImageDTO {
  /** 图片URL */
  url?: string
  /** 图片描述 */
  description?: string
}

/** 标注选择器DTO */
export interface AnnotationSelectorDTO {
  /** 
   * 百分比坐标值 
   * 格式: "xywh=percent:10,20,30,10"
   * 表示: x=10%, y=20%, width=30%, height=10%
   */
  value: string
}

/** 标注目标DTO */
export interface AnnotationTargetDTO {
  /** 标注选择器 */
  selector: AnnotationSelectorDTO
}

/** 标注内容DTO */
export interface AnnotationBodyDTO {
  /** 标注值 */
  value?: string
  /** 标注目的 */
  purpose?: string
}

/** 标注信息DTO */
export interface AnnotationDTO {
  /** 标注ID */
  id?: string
  /** 标注类型 */
  type: string
  /** 标注内容 */
  body?: AnnotationBodyDTO
  /** 标注目标 */
  target?: AnnotationTargetDTO
} 