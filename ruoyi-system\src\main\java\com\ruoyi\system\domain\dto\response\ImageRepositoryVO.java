package com.ruoyi.system.domain.dto.response;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.domain.dto.AnnotationDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 图片仓库响应VO
 * 用于展示单张图片的完整信息
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ImageRepositoryVO {
    
    /** MongoDB主键 */
    private String id;
    
    /** 业务图片ID */
    private String imageId;

    private String imageName;
    
    /** 关联任务ID */
    private String taskId;
    
    /** 关联文件夹ID */
    private String folderId;
    
    /** 关联版本ID */
    private String versionId;
    
    /** 原始文件名 */
    private String originalFileName;
    
    /** MinIO存储路径 */
    private String minioPath;
    
    /** 文件大小 */
    private Long fileSize;
    
    /** 内容类型 */
    private String contentType;
    
    /** 图片宽度 */
    private Integer imageWidth;
    
    /** 图片高度 */
    private Integer imageHeight;
    
    /** 光照类型 */
    private String lightType;
    
    /** 是否为主图 */
    private Boolean isMainImage;
    
    /** 处理状态 */
    private String processStatus;
    
    /** 处理消息 */
    private String processMessage;
    
    /** 图片MD5值 */
    private String imageMd5;
    
    /** 标签列表 */
    private List<String> tags;
    
    /** 部门信息 */
    private SysDept deptInfo;
    
    /** 完整的标注信息列表 */
    private List<AnnotationDTO> annotations;
    
    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;


}
