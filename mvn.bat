@echo off
rem Maven Wrapper Script - Using IntelliJ IDEA Built-in Maven
rem Author: AI Assistant
rem Date: 2025-01-15

set MAVEN_PATH="C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\plugins\maven\lib\maven3\bin\mvn.cmd"

rem Check if <PERSON><PERSON> exists
if not exist %MAVEN_PATH% (
    echo Error: <PERSON><PERSON> not found, please check IntelliJ IDEA installation path
    echo Current path: %MAVEN_PATH%
    pause
    exit /b 1
)

rem Execute Maven command with all parameters
%MAVEN_PATH% %*
