package com.ruoyi.system.domain.dto.request;

import lombok.Data;

/**
 * 文件夹查询请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class FolderQueryDTO {
    
    /** 页码 */
    private Integer pageNum = 1;
    
    /** 页大小 */
    private Integer pageSize = 10;
    
    /** 文件夹状态 (unassociated/associated) */
    private String status;
    
    /** 版本ID */
    private String versionId;
    
    /** 任务ID */
    private String taskId;
    
    /** 文件夹类型 (standard/regular) */
    private String folderType;
    
    /** 国家ID */
    private Long countryId;

    /** 国家代码 */
    private String countryCode;

    /** 证件类型ID */
    private Long certTypeId;

    /** 证件类型代码 */
    private String certTypeCode;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 部门ID */
    private Long deptId;
    
    /** 上传者ID */
    private Long uploaderId;
    
    /** 关键词搜索 */
    private String keyword;

    // 手动添加getter/setter方法以确保编译通过
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFolderType() {
        return folderType;
    }

    public void setFolderType(String folderType) {
        this.folderType = folderType;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Long getCertTypeId() {
        return certTypeId;
    }

    public void setCertTypeId(Long certTypeId) {
        this.certTypeId = certTypeId;
    }

    public String getCertTypeCode() {
        return certTypeCode;
    }

    public void setCertTypeCode(String certTypeCode) {
        this.certTypeCode = certTypeCode;
    }

    public String getIssueYear() {
        return issueYear;
    }

    public void setIssueYear(String issueYear) {
        this.issueYear = issueYear;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(Long uploaderId) {
        this.uploaderId = uploaderId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
