/**
 * 上传测试工具
 * 用于测试TUS上传功能是否正常工作
 */

import { getToken } from '@/utils/auth'

/**
 * 测试TUS上传功能
 * @returns {Promise<Object>}
 */
export async function testTusUpload() {
  console.log('🧪 开始测试TUS上传功能...')
  
  try {
    const baseURL = import.meta.env.VITE_APP_BASE_API
    const tusEndpoint = `${baseURL}/tus/upload/`
    const token = getToken()

    if (!token) {
      return {
        success: false,
        error: '未获取到认证Token，请先登录'
      }
    }

    // 创建测试文件
    const testData = 'Test file content for TUS upload testing'
    const testFile = new Blob([testData], { type: 'text/plain' })
    const fileSize = testFile.size

    console.log('📄 创建测试文件:', {
      size: fileSize,
      type: testFile.type
    })

    // Step 1: OPTIONS 预检请求
    console.log('📡 发送OPTIONS预检请求...')
    const optionsResponse = await fetch(tusEndpoint, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0'
      }
    })

    if (!optionsResponse.ok) {
      return {
        success: false,
        error: `OPTIONS请求失败: ${optionsResponse.status} ${optionsResponse.statusText}`,
        step: 'options'
      }
    }

    const tusResumable = optionsResponse.headers.get('Tus-Resumable')
    const tusVersion = optionsResponse.headers.get('Tus-Version')
    const tusExtension = optionsResponse.headers.get('Tus-Extension')

    console.log('✅ OPTIONS预检成功:', {
      'Tus-Resumable': tusResumable,
      'Tus-Version': tusVersion,
      'Tus-Extension': tusExtension
    })

    // Step 2: POST 创建上传
    console.log('📡 创建上传会话...')
    const createResponse = await fetch(tusEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0',
        'Upload-Length': fileSize.toString(),
        'Upload-Metadata': btoa('filename') + ' ' + btoa('test-upload.txt') + ',' +
                          btoa('filetype') + ' ' + btoa('text/plain')
      }
    })

    if (createResponse.status !== 201) {
      return {
        success: false,
        error: `创建上传失败: ${createResponse.status} ${createResponse.statusText}`,
        step: 'create'
      }
    }

    const uploadLocation = createResponse.headers.get('Location')
    if (!uploadLocation) {
      return {
        success: false,
        error: '未获取到上传位置URL',
        step: 'create'
      }
    }

    console.log('✅ 上传会话创建成功:', { uploadLocation })

    // Step 3: HEAD 检查上传状态
    console.log('📡 检查上传状态...')
    const headResponse = await fetch(uploadLocation, {
      method: 'HEAD',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0'
      }
    })

    if (!headResponse.ok) {
      return {
        success: false,
        error: `检查上传状态失败: ${headResponse.status} ${headResponse.statusText}`,
        step: 'head'
      }
    }

    const uploadOffset = parseInt(headResponse.headers.get('Upload-Offset') || '0')
    const uploadLength = parseInt(headResponse.headers.get('Upload-Length') || '0')

    console.log('✅ 上传状态检查成功:', { uploadOffset, uploadLength })

    // Step 4: PATCH 上传数据
    console.log('📡 上传文件数据...')
    const patchResponse = await fetch(uploadLocation, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0',
        'Upload-Offset': uploadOffset.toString(),
        'Content-Type': 'application/offset+octet-stream'
      },
      body: testFile
    })

    if (patchResponse.status !== 204) {
      return {
        success: false,
        error: `上传数据失败: ${patchResponse.status} ${patchResponse.statusText}`,
        step: 'patch',
        uploadLocation
      }
    }

    const newUploadOffset = parseInt(patchResponse.headers.get('Upload-Offset') || '0')
    console.log('✅ 文件数据上传成功:', { newUploadOffset })

    // Step 5: 最终状态检查
    console.log('📡 最终状态检查...')
    const finalHeadResponse = await fetch(uploadLocation, {
      method: 'HEAD',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0'
      }
    })

    const finalUploadOffset = parseInt(finalHeadResponse.headers.get('Upload-Offset') || '0')
    const isCompleted = finalUploadOffset >= fileSize

    console.log('✅ TUS上传测试完成:', {
      uploadLocation,
      fileSize,
      uploadOffset: finalUploadOffset,
      uploadLength,
      completed: isCompleted
    })

    return {
      success: true,
      uploadLocation,
      fileSize,
      uploadOffset: finalUploadOffset,
      uploadLength,
      completed: isCompleted,
      steps: {
        options: true,
        create: true,
        head: true,
        patch: true,
        finalCheck: true
      }
    }

  } catch (error) {
    console.error('💥 TUS上传测试异常:', error)
    
    return {
      success: false,
      error: error.message,
      step: 'exception'
    }
  }
}

/**
 * 简单的上传连通性测试
 * @returns {Promise<boolean>}
 */
export async function quickUploadTest() {
  try {
    const baseURL = import.meta.env.VITE_APP_BASE_API
    const tusEndpoint = `${baseURL}/tus/upload/`
    const token = getToken()

    if (!token) {
      console.warn('未获取到认证Token')
      return false
    }

    // 只测试OPTIONS请求
    const response = await fetch(tusEndpoint, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0'
      }
    })

    return response.ok
  } catch (error) {
    console.error('快速上传测试失败:', error)
    return false
  }
}

/**
 * 测试文件上传权限
 * @returns {Promise<Object>}
 */
export async function testUploadPermission() {
  try {
    const baseURL = import.meta.env.VITE_APP_BASE_API
    const tusEndpoint = `${baseURL}/tus/upload/`
    const token = getToken()

    if (!token) {
      return {
        success: false,
        error: '未获取到认证Token',
        hasPermission: false
      }
    }

    // 测试权限
    const response = await fetch(tusEndpoint, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Tus-Resumable': '1.0.0'
      }
    })

    if (response.status === 403) {
      return {
        success: false,
        error: '没有上传权限',
        hasPermission: false
      }
    }

    if (response.status === 401) {
      return {
        success: false,
        error: '认证失败',
        hasPermission: false
      }
    }

    return {
      success: response.ok,
      hasPermission: response.ok,
      status: response.status,
      statusText: response.statusText
    }

  } catch (error) {
    return {
      success: false,
      error: error.message,
      hasPermission: false
    }
  }
}

/**
 * 批量上传测试
 * @param {number} fileCount - 测试文件数量
 * @returns {Promise<Object>}
 */
export async function batchUploadTest(fileCount = 3) {
  console.log(`🧪 开始批量上传测试 (${fileCount}个文件)...`)
  
  const results = []
  const startTime = Date.now()

  for (let i = 1; i <= fileCount; i++) {
    console.log(`📤 测试文件 ${i}/${fileCount}...`)
    
    try {
      const result = await testTusUpload()
      results.push({
        fileIndex: i,
        success: result.success,
        uploadLocation: result.uploadLocation,
        error: result.error
      })
    } catch (error) {
      results.push({
        fileIndex: i,
        success: false,
        error: error.message
      })
    }
  }

  const endTime = Date.now()
  const duration = endTime - startTime
  const successCount = results.filter(r => r.success).length
  const failureCount = results.filter(r => !r.success).length

  console.log('✅ 批量上传测试完成:', {
    totalFiles: fileCount,
    successCount,
    failureCount,
    duration: `${duration}ms`,
    successRate: `${((successCount / fileCount) * 100).toFixed(1)}%`
  })

  return {
    success: failureCount === 0,
    totalFiles: fileCount,
    successCount,
    failureCount,
    duration,
    successRate: (successCount / fileCount) * 100,
    results
  }
}

export default {
  testTusUpload,
  quickUploadTest,
  testUploadPermission,
  batchUploadTest
} 