package com.ruoyi.system.domain.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 标注信息DTO
 * 用于请求和响应中的标注信息
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AnnotationDTO {
    
    /** 标注ID */
    private String id;
    
    /** 标注类型 */
    @NotBlank(message = "标注类型不能为空")
    private String type;
    
    /** 标注内容 */
    @Valid
    private AnnotationBodyDTO body;
    
    /** 标注目标 */
    @Valid
    private AnnotationTargetDTO target;
}
