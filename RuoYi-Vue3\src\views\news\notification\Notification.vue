<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息通知</span>
          <div class="header-actions">
            <el-button type="primary" plain size="small" @click="markAllAsReadHandler">全部标为已读</el-button>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全部消息" name="all"></el-tab-pane>
        <el-tab-pane label="未读消息" name="unread"></el-tab-pane>
        <el-tab-pane label="已读消息" name="read"></el-tab-pane>
      </el-tabs>
      
      <el-table v-loading="loading" :data="notificationList">
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'danger' : 'info'" size="small">
              {{ scope.row.status === '0' ? '未读' : '已读' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标题" prop="title" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="内容" prop="content" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="时间" align="center" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              icon="View" 
              @click="viewNotification(scope.row)"
            >查看</el-button>
            <el-button 
              link 
              type="primary" 
              icon="Check" 
              @click="markAsReadHandler(scope.row)" 
              v-if="scope.row.status === '0'"
            >标为已读</el-button>
            <el-button 
              link 
              type="danger" 
              icon="Delete" 
              @click="deleteNotification(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    
    <!-- 消息详情对话框 -->
    <el-dialog v-model="dialogVisible" title="消息详情" width="500px">
      <div class="notification-detail">
        <h3>{{ currentNotification.title }}</h3>
        <div class="notification-time">{{ parseTime(currentNotification.createTime) }}</div>
        <div class="notification-content">{{ currentNotification.content }}</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleAction" v-if="currentNotification.sourceId && !currentNotification.title.includes('未通过')"
        >
          查看相关内容
        </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { listNotification, getNotification, markAsRead, markAllAsRead, delNotification } from '@/api/news/notification'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const userStore = useUserStore()
const { proxy } = getCurrentInstance()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: undefined
})

// 消息列表数据
const notificationList = ref([])
const total = ref(0)
const loading = ref(false)
const activeTab = ref('all')
const dialogVisible = ref(false)
const currentNotification = ref({})

// 获取消息列表
const getList = () => {
  loading.value = true
  listNotification(queryParams).then(response => {
    notificationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 标签页切换
const handleTabClick = (tab) => {
  activeTab.value = tab.props.name;

  if (activeTab.value === 'all') {
    queryParams.status = undefined;
  } else if (activeTab.value === 'unread') {
    queryParams.status = '0';
  } else if (activeTab.value === 'read') {
    queryParams.status = '1';
  }
  queryParams.pageNum = 1; 
  getList();
}

// 查看消息详情
const viewNotification = async (row) => {
  try {
    const res = await getNotification(row.notificationId)
    if (res.code === 200) {
      currentNotification.value = res.data
      dialogVisible.value = true
      
      // 如果是未读消息，标记为已读
      if (row.status === '0') {
        markAsReadHandler(row, false)
      }
    }
  } catch (error) {
    console.error('获取消息详情失败:', error)
  }
}

// 标记为已读
const markAsReadHandler = async (row, showMessage = true) => {
  try {
    const res = await markAsRead(row.notificationId)
    if (res.code === 200) {
      if (showMessage) {
        ElMessage.success('已标记为已读')
      }
      row.status = '1'
      // 更新未读消息数量
      userStore.getUnreadMessageCount()
    }
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

// 全部标记为已读
const markAllAsReadHandler = async () => {
  try {
    const res = await markAllAsRead()
    if (res.code === 200) {
      ElMessage.success('已全部标记为已读')
      getList()
      // 更新未读消息数量
      userStore.getUnreadMessageCount()
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
  }
}

// 删除消息
const deleteNotification = (row) => {
  ElMessageBox.confirm('确定要删除该消息吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await delNotification(row.notificationId)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        getList()
        // 如果删除的是未读消息，更新未读消息数量
        if (row.status === '0') {
          userStore.getUnreadMessageCount()
        }
      }
    } catch (error) {
      console.error('删除消息失败:', error)
    }
  }).catch(() => {})
}

// 处理消息相关操作
const handleAction = () => {
  router.push(`/news/detail/${currentNotification.value.sourceId}`)
  dialogVisible.value = false
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-detail {
  padding: 10px;
}

.notification-time {
  font-size: 12px;
  color: #999;
  margin: 10px 0;
}

.notification-content {
  line-height: 1.6;
  margin-top: 20px;
  white-space: pre-wrap;
}
</style>
