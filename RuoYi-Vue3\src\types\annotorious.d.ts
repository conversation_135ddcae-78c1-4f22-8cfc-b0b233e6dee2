declare module '@annotorious/annotorious' {
  export interface ImageAnnotation {
    id: string
    type: string
    body?: Array<{
      type: string
      value: string
      purpose?: string
    }>
    target: {
      selector: {
        type: string
        conformsTo?: string
        value: string
      }
    }
  }

  export interface AnnotoriousOpts {
    drawingEnabled?: boolean
    userSelectAction?: 'EDIT' | 'SELECT' | 'NONE'
    style?: any
  }

  export interface ImageAnnotator {
    // 事件监听
    on(event: 'createAnnotation', handler: (annotation: ImageAnnotation) => void): void
    on(event: 'updateAnnotation', handler: (annotation: ImageAnnotation, previous: ImageAnnotation) => void): void
    on(event: 'deleteAnnotation', handler: (annotation: ImageAnnotation) => void): void
    on(event: 'selectAnnotation', handler: (annotation: ImageAnnotation) => void): void
    on(event: 'cancelSelected', handler: () => void): void

    // 标注管理
    addAnnotation(annotation: ImageAnnotation): void
    clearAnnotations(): void
    getAnnotations(): ImageAnnotation[]
    setSelected(annotationId?: string, editable?: boolean): void
    cancelSelected(): void

    // 工具设置
    setDrawingTool(tool: string): void
    setDrawingEnabled(enabled: boolean): void
    
    // 图片设置
    setImage(image: HTMLImageElement): void
    
    // 视图控制
    fitBounds(): void

    // 销毁
    destroy(): void
  }

  export function createImageAnnotator(element: HTMLImageElement | HTMLCanvasElement | string, options?: AnnotoriousOpts): ImageAnnotator
}
