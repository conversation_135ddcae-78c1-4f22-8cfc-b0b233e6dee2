<template>
  <div class="version-browser">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="manualSearchQuery"
          type="search"
          placeholder="搜索版本..."
          :prefix-icon="Search"
          clearable
          class="search-input"
        />
        
        <el-select
          v-model="selectedTags"
          multiple
          placeholder="选择标签筛选"
          clearable
          class="tag-select"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </div>

      <div class="toolbar-actions">
        <div class="selection-info">
          <span v-if="selectedVersionIds.length > 0" class="selected-count">
            已选择 {{ selectedVersionIds.length }} 个版本
          </span>
          <el-button
            v-if="versions.length > 0"
            size="small"
            @click="handleSelectAll"
          >
            {{ selectedVersionIds.length === versions.length ? '取消全选' : '全选' }}
          </el-button>
        </div>

        <el-button
          type="primary"
          :icon="Plus"
          @click="handleCreateVersion"
          :disabled="!props.canCreateVersion"
        >
          新建版本
        </el-button>
      </div>
    </div>

    <!-- 上下文关键词显示 -->
    <div v-if="contextKeywords.length > 0" class="context-keywords">
      <span class="label">上下文关键词：</span>
      <el-tag
        v-for="keyword in contextKeywords"
        :key="keyword"
        type="info"
        size="small"
        class="keyword-tag"
      >
        {{ keyword }}
      </el-tag>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 版本列表 -->
    <div v-else class="version-list">
      <div v-if="versions.length === 0" class="empty-state">
        <el-empty description="暂无版本数据" />
      </div>
      
      <div v-else class="version-items">
        <div
          v-for="version in versions"
          :key="version.versionId"
          class="version-item"
          :class="{ 'is-selected': selectedVersionIds.includes(version.versionId) }"
          @click="handleVersionSelect(version)"
        >
          <div class="version-header">
            <div class="version-checkbox">
              <el-checkbox
                :model-value="selectedVersionIds.includes(version.versionId)"
                @change="(checked) => handleVersionCheckChange(version, checked)"
                @click.stop
              />
            </div>

            <div class="version-info">
              <h4 class="version-name">{{ version.versionCode }}</h4>
              <p class="version-description">{{ version.description || '暂无描述' }}</p>
            </div>

            <div class="version-meta">
              <el-tag
                :type="getStatusTagType(version.status)"
                size="small"
              >
                {{ getStatusText(version.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="version-details">
            <div class="detail-item">
              <span class="label">国家：</span>
              <span class="value">{{ version.countryInfo?.name || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">证件类型：</span>
              <span class="value">{{ version.certInfo?.zjlbmc || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">发行年份：</span>
              <span class="value">{{ version.issueYear || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(version.createTime) }}</span>
            </div>
          </div>
          
          <div class="version-actions">
            <el-button
              type="primary"
              size="small"
              @click="handleAssociate(version)"
            >
              关联
            </el-button>
            <el-button
              size="small"
              @click="handleViewDetails(version)"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { getVersionList, type CertVersionVO } from '@/api/cert/version'

// Props
interface Props {
  contextKeywords: string[]
  canCreateVersion?: boolean  // 新增这个 prop
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create-version-clicked': []
  'versions-selected': [versions: CertVersionVO[]]
}>()

// 响应式数据
const manualSearchQuery = ref('')
const selectedTags = ref<string[]>([])
const versions = ref<CertVersionVO[]>([])
const isLoading = ref(false)
const selectedVersionIds = ref<string[]>([]) // 多选模式：选中的版本ID数组

// 可用标签（示例数据，实际应从API获取）
const availableTags = ref(['护照', '身份证', '驾驶证', '签证'])

// 数据获取逻辑
const fetchVersions = async () => {
  isLoading.value = true
  try {
    let params: any = { 
      pageNum: 1, 
      pageSize: 20 
    }

    // 优先级1：手动搜索查询
    if (manualSearchQuery.value.trim()) {
      params.keyword = manualSearchQuery.value.trim()
    }
    // 优先级2：标签筛选
    else if (selectedTags.value.length > 0) {
      params.keyword = selectedTags.value.join(' ')
    }
    // 优先级3：上下文关键词（只有当关键词不为空时才使用）
    else if (props.contextKeywords.length > 0) {
      params.keyword = props.contextKeywords.join(' ')
    }
    // 默认：获取所有版本（不传 keyword 参数）
    // 打印参数
    console.log('=== fetchVersions 传递给后端的参数 ===')
    console.log('完整参数对象:', params)
    console.log('pageNum:', params.pageNum)
    console.log('pageSize:', params.pageSize)
    console.log('keyword:', params.keyword)
    console.log('manualSearchQuery:', manualSearchQuery.value)
    console.log('selectedTags:', selectedTags.value)
    console.log('contextKeywords:', props.contextKeywords)
    console.log('=====================================')
    const response = await getVersionList(params)
    
    if (response.code === 200) {
      versions.value = response.data?.rows || response.rows || []
    } else {
      ElMessage.error(response.msg || '获取版本列表失败')
    }
  } catch (error) {
    console.error('获取版本列表失败:', error)
    ElMessage.error('获取版本列表失败')
  } finally {
    isLoading.value = false
  }
}

// 监听依赖变化
watch(
  [manualSearchQuery, selectedTags],
  () => {
    fetchVersions()
  },
  { immediate: true, deep: true }
)

// 单独监听 contextKeywords 变化，但不自动触发搜索
watch(
  () => props.contextKeywords,
  (newKeywords) => {
    console.log('上下文关键词变化:', newKeywords)
    // 只有当用户没有手动搜索时，才使用上下文关键词
    if (!manualSearchQuery.value.trim() && selectedTags.value.length === 0) {
      fetchVersions()
    }
  },
  { deep: true }
)

// 工具函数
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'pending': return 'warning'
    case 'inactive': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'pending': return '待审核'
    case 'inactive': return '非活跃'
    default: return status || '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 事件处理
const handleCreateVersion = () => {
  emit('create-version-clicked')
}

// 版本选择处理
const handleVersionSelect = (version: CertVersionVO) => {
  const index = selectedVersionIds.value.indexOf(version.versionId)
  if (index > -1) {
    selectedVersionIds.value.splice(index, 1)
  } else {
    selectedVersionIds.value.push(version.versionId)
  }
  emitSelectedVersions()
}

const handleVersionCheckChange = (version: CertVersionVO, checked: boolean) => {
  if (checked) {
    if (!selectedVersionIds.value.includes(version.versionId)) {
      selectedVersionIds.value.push(version.versionId)
    }
  } else {
    const index = selectedVersionIds.value.indexOf(version.versionId)
    if (index > -1) {
      selectedVersionIds.value.splice(index, 1)
    }
  }
  emitSelectedVersions()
}

// 发送选中版本变化事件
const emitSelectedVersions = () => {
  const selectedVersions = versions.value.filter(version =>
    selectedVersionIds.value.includes(version.versionId)
  )
  emit('versions-selected', selectedVersions)
}

// 全选/取消全选
const handleSelectAll = () => {
  if (selectedVersionIds.value.length === versions.value.length) {
    // 取消全选
    selectedVersionIds.value = []
  } else {
    // 全选
    selectedVersionIds.value = versions.value.map(version => version.versionId)
  }
  emitSelectedVersions()
}

const handleAssociate = (version: CertVersionVO) => {
  ElMessage.info(`关联版本: ${version.versionCode}`)
  // TODO: 实现关联逻辑
}

const handleViewDetails = (version: CertVersionVO) => {
  ElMessage.info(`查看版本详情: ${version.versionCode}`)
  // TODO: 实现查看详情逻辑
}

// 暴露给父组件的方法
const refreshData = async () => {
  await fetchVersions()
}

defineExpose({
  refreshData
})

// 生命周期
onMounted(() => {
  fetchVersions()
})
</script>

<style scoped>
.version-browser {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.search-section {
  display: flex;
  gap: 12px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.tag-select {
  width: 200px;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.context-keywords {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.context-keywords .label {
  font-weight: 500;
  color: #303133;
  margin-right: 8px;
}

.keyword-tag {
  margin-right: 8px;
}

.loading-container {
  flex: 1;
  padding: 20px;
}

.version-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.version-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.version-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.2s;
  cursor: pointer;
}

.version-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.version-item.is-selected {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.version-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.version-checkbox {
  flex-shrink: 0;
  margin-top: 2px;
}

.version-info {
  flex: 1;
}

.version-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.version-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.version-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  font-size: 14px;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
}

.version-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
