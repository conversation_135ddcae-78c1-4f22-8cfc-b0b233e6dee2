package com.ruoyi.system.domain.dto.request;

import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 标注模板请求DTO
 * 用于创建和更新版本标注模板
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AnnotationTemplateDTO {
    
    /** 模板ID */
    private String templateId;
    
    /** 版本ID */
    @NotBlank(message = "版本ID不能为空")
    private String versionId;
    
    /** 图片类型 */
    @NotBlank(message = "图片类型不能为空")
    private String imageType;
    
    /** 证件类型 */
    private String certType;
    
    /** 国家代码 */
    private String countryCode;
    
    /** 标注项列表 */
    @Valid
    @NotEmpty(message = "标注项列表不能为空")
    private List<AnnotationItemDTO> annotations;
    
    /** 创建此模板的标准文件夹ID */
    @NotBlank(message = "标准文件夹ID不能为空")
    private String standardFolderId;
    
    /**
     * 标注项DTO
     */
    @Data
    public static class AnnotationItemDTO {
        /** 标注项ID */
        private String annotationId;
        
        /** 标注类型 */
        @NotBlank(message = "标注类型不能为空")
        private String annotationType;
        
        /** 标注名称 */
        @NotBlank(message = "标注名称不能为空")
        private String annotationName;
        
        /** 坐标信息 */
        @Valid
        private AnnotationCoordinateDTO coordinate;
        
        /** 标注值 */
        private String annotationValue;
        
        /** 是否必填 */
        private Boolean required = false;
        
        /** 显示顺序 */
        private Integer displayOrder = 0;
    }
    
    /**
     * 坐标信息DTO
     */
    @Data
    public static class AnnotationCoordinateDTO {
        /** X坐标（百分比） */
        private Double x;
        
        /** Y坐标（百分比） */
        private Double y;
        
        /** 宽度（百分比） */
        private Double width;
        
        /** 高度（百分比） */
        private Double height;
    }
    
    /**
     * 转换为实体类
     */
    public VersionAnnotationTemplate toEntity() {
        VersionAnnotationTemplate template = new VersionAnnotationTemplate();
        template.setTemplateId(this.templateId);
        template.setVersionId(this.versionId);
        template.setImageType(this.imageType);
        template.setCertType(this.certType);
        template.setCountryCode(this.countryCode);
        template.setStandardFolderId(this.standardFolderId);
        
        // 转换标注项列表
        if (this.annotations != null) {
            List<VersionAnnotationTemplate.AnnotationItem> items = this.annotations.stream()
                .map(this::convertToAnnotationItem)
                .collect(java.util.stream.Collectors.toList());
            template.setAnnotations(items);
        }
        
        return template;
    }
    
    /**
     * 转换标注项DTO为实体
     */
    private VersionAnnotationTemplate.AnnotationItem convertToAnnotationItem(AnnotationItemDTO dto) {
        VersionAnnotationTemplate.AnnotationItem item = new VersionAnnotationTemplate.AnnotationItem();
        item.setAnnotationId(dto.getAnnotationId());
        item.setAnnotationType(dto.getAnnotationType());
        item.setAnnotationName(dto.getAnnotationName());
        item.setAnnotationValue(dto.getAnnotationValue());
        item.setRequired(dto.getRequired());
        item.setDisplayOrder(dto.getDisplayOrder());
        
        // 转换坐标信息
        if (dto.getCoordinate() != null) {
            VersionAnnotationTemplate.AnnotationCoordinate coordinate = new VersionAnnotationTemplate.AnnotationCoordinate();
            coordinate.setX(dto.getCoordinate().getX());
            coordinate.setY(dto.getCoordinate().getY());
            coordinate.setWidth(dto.getCoordinate().getWidth());
            coordinate.setHeight(dto.getCoordinate().getHeight());
            item.setCoordinate(coordinate);
        }
        
        return item;
    }
    
    /**
     * 从实体类创建DTO
     */
    public static AnnotationTemplateDTO fromEntity(VersionAnnotationTemplate template) {
        AnnotationTemplateDTO dto = new AnnotationTemplateDTO();
        dto.setTemplateId(template.getTemplateId());
        dto.setVersionId(template.getVersionId());
        dto.setImageType(template.getImageType());
        dto.setCertType(template.getCertType());
        dto.setCountryCode(template.getCountryCode());
        dto.setStandardFolderId(template.getStandardFolderId());
        
        // 转换标注项列表
        if (template.getAnnotations() != null) {
            List<AnnotationItemDTO> items = template.getAnnotations().stream()
                .map(AnnotationTemplateDTO::convertFromAnnotationItem)
                .collect(java.util.stream.Collectors.toList());
            dto.setAnnotations(items);
        }
        
        return dto;
    }
    
    /**
     * 从实体标注项转换为DTO
     */
    private static AnnotationItemDTO convertFromAnnotationItem(VersionAnnotationTemplate.AnnotationItem item) {
        AnnotationItemDTO dto = new AnnotationItemDTO();
        dto.setAnnotationId(item.getAnnotationId());
        dto.setAnnotationType(item.getAnnotationType());
        dto.setAnnotationName(item.getAnnotationName());
        dto.setAnnotationValue(item.getAnnotationValue());
        dto.setRequired(item.getRequired());
        dto.setDisplayOrder(item.getDisplayOrder());
        
        // 转换坐标信息
        if (item.getCoordinate() != null) {
            AnnotationCoordinateDTO coordinate = new AnnotationCoordinateDTO();
            coordinate.setX(item.getCoordinate().getX());
            coordinate.setY(item.getCoordinate().getY());
            coordinate.setWidth(item.getCoordinate().getWidth());
            coordinate.setHeight(item.getCoordinate().getHeight());
            dto.setCoordinate(coordinate);
        }
        
        return dto;
    }
}
