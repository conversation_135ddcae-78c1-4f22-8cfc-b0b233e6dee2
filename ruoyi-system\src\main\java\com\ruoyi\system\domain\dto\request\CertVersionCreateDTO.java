package com.ruoyi.system.domain.dto.request;

import com.ruoyi.system.domain.dto.TrainingImageDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建证件版本请求DTO
 * 用于 POST /api/versions
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class CertVersionCreateDTO {
    
    /** 版本代码 (必需, e.g., "RUS_VISA_2023_V1") */
    @NotBlank(message = "版本代码不能为空")
    private String versionCode;
    
    /** 描述 */
    private String description;
    
    /** 国家ID (必需, 用于从MySQL加载 Country 对象) */
    @NotNull(message = "国家ID不能为空")
    private Long countryId;
    
    /** 证件类型ID (必需, 用于从MySQL加载 CertType 对象) */
    @NotNull(message = "证件类型ID不能为空")
    private Long certTypeId;
    
    /** 发行年份 (必需) */
    @NotBlank(message = "发行年份不能为空")
    private String issueYear;
    
    /** 训练图片信息 (可选, 用于设置独立的训练图) */
    private TrainingImageDTO trainingImage;
}
