@echo off
echo ========================================
echo MongoDB 迁移脚本执行工具
echo ========================================

REM 设置变量
set MONGO_URI=******************************************************
set AUTH_SOURCE=admin
set SCRIPT_FILE=sql/mongodb-migration-2025-01-15.js

echo.
echo 1. 检查MongoDB连接...
mongo %MONGO_URI% --authSource %AUTH_SOURCE% --eval "print('MongoDB连接成功: ' + db.getName())"

if %errorlevel% neq 0 (
    echo 错误: 无法连接到MongoDB，请检查服务是否运行
    pause
    exit /b 1
)

echo.
echo 2. 创建备份目录...
set BACKUP_DIR=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir %BACKUP_DIR% 2>nul

echo.
echo 3. 备份数据库...
echo 正在备份到: %BACKUP_DIR%
mongodump --uri="%MONGO_URI%?authSource=%AUTH_SOURCE%" --out=%BACKUP_DIR%

if %errorlevel% neq 0 (
    echo 警告: 备份失败，是否继续执行迁移？
    set /p continue="输入 y 继续，其他键退出: "
    if /i not "%continue%"=="y" (
        echo 迁移已取消
        pause
        exit /b 1
    )
) else (
    echo 备份完成: %BACKUP_DIR%
)

echo.
echo 4. 执行迁移脚本...
echo 正在执行: %SCRIPT_FILE%
mongo %MONGO_URI% --authSource %AUTH_SOURCE% %SCRIPT_FILE%

if %errorlevel% neq 0 (
    echo 警告: 迁移脚本执行遇到问题，尝试执行修复脚本
    echo.
    echo 执行索引冲突修复脚本...
    mongo %MONGO_URI% --authSource %AUTH_SOURCE% sql/fix-index-conflict.js

    echo.
    echo 执行完整修复脚本...
    mongo %MONGO_URI% --authSource %AUTH_SOURCE% sql/mongodb-migration-fix-2025-01-15.js
)

echo.
echo 5. 验证迁移结果...
mongo %MONGO_URI% --authSource %AUTH_SOURCE% --eval "
print('=== 迁移结果验证 ===');
print('版本标注模板集合存在: ' + (db.getCollectionNames().indexOf('version_annotation_template') !== -1));
print('文件夹标准样本字段数量: ' + db.folder_info.countDocuments({'isStandardSample': {$exists: true}}));
print('图片可标注类型字段数量: ' + db.image_repository.countDocuments({'isAnnotatableType': {$exists: true}}));
print('可标注图片数量: ' + db.image_repository.countDocuments({'isAnnotatableType': true}));
print('=== 验证完成 ===');
"

echo.
echo ========================================
echo 迁移脚本执行完成！
echo 备份位置: %BACKUP_DIR%
echo ========================================
pause
