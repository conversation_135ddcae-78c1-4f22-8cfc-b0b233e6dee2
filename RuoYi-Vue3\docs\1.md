好的，我来为您提供详细的手工操作步骤。

## �� 第一步：删除旧的JavaScript文件

### 1.1 删除旧的API文件
```bash
# 删除旧的cert目录（如果存在）
rm -rf src/api/cert/

# 或者手动删除以下文件：
rm src/api/cert/annotation.js
rm src/api/cert/certType.js
rm src/api/cert/country.js
rm src/api/cert/type.js
rm src/api/cert/version.js
rm src/api/cert/warehouse.js
```

### 1.2 删除旧的Composables文件
```bash
# 删除旧的JavaScript composables文件
rm src/composables/useImageAnnotation.js
rm src/composables/useBatchTasks.js
rm src/composables/useFolderInfo.js

# 删除旧的TypeScript文件（功能已合并）
rm src/composables/useAnnotationPermission.ts
```

### 1.3 删除旧的类型文件（可选）
```bash
# 如果确定不再需要简化版本，可以删除
rm src/types/simpleBatchTask.ts
```

## �� 第二步：更新现有组件的导入

### 2.1 查找需要更新的文件

首先，搜索项目中使用了旧版导入的文件：

```bash
# 搜索使用了旧版API的文件
grep -r "from '@/api/cert" src/
grep -r "from '@/api/cert" src/

# 搜索使用了旧版composables的文件
grep -r "from '@/composables/useImageAnnotation" src/
grep -r "from '@/composables/useAnnotationPermission" src/
grep -r "from '@/composables/useBatchTasks" src/
grep -r "from '@/composables/useFolderInfo" src/

# 搜索使用了旧版类型的文件
grep -r "from '@/types/batchTask" src/
grep -r "from '@/types/simpleBatchTask" src/
```

### 2.2 更新API导入

将以下导入模式进行替换：

#### 旧版 → 新版
```typescript
// 版本管理API
import { listVersion, getVersion, addVersion } from '@/api/cert/version'
// ↓
import { getVersionList, getVersionDetails, createVersion } from '@/api/cert'

// 文件夹管理API
import { getFolderList } from '@/api/cert/folder'
// ↓
import { getFolderList, getFolderDetails } from '@/api/cert'

// 标注管理API
import { getImagesByFileInfo, updateImageAnnotation } from '@/api/cert/annotation'
// ↓
import { getImagesByFolder, updateImageAnnotations } from '@/api/cert'

// 国家管理API
import { listCountry, getCountry } from '@/api/cert/country'
// ↓
import { listCountry, getCountry } from '@/api/cert/legacy'

// 证件类型管理API
import { listCertType, getCertType } from '@/api/cert/certType'
// ↓
import { listCertType, getCertType } from '@/api/cert/legacy'
```

### 2.3 更新Composables导入

#### 旧版 → 新版
```typescript
// 标注相关
import { useImageAnnotation } from '@/composables/useImageAnnotation'
import { useAnnotationPermission } from '@/composables/useAnnotationPermission'
// ↓
import { useImageAnnotation, useAnnotationPermission } from '@/composables'

// 批量任务
import { useBatchTasks } from '@/composables/useBatchTasks'
// ↓
import { useBatchTasks } from '@/composables'

// 文件夹信息
import { useFolderInfo } from '@/composables/useFolderInfo'
// ↓
import { useFolderInfo, useFolderSelection, useFolderFilter } from '@/composables'
```

### 2.4 更新类型导入

#### 旧版 → 新版
```typescript
// 通用类型
import { Country, CertType } from '@/types/common'
// ↓
import { Country, CertType } from '@/types'

// 批量任务类型
import { BatchTaskCreateDTO } from '@/types/batchTask'
import { SimpleBatchTaskCreateDTO } from '@/types/simpleBatchTask'
// ↓
import { BatchTaskCreateDTO, SimpleBatchTaskCreateDTO } from '@/types'

// 新增类型
import { 
  VersionVO, 
  FolderInfoVO, 
  ImageInfoVO,
  AnnotationItem 
} from '@/types'
```

### 2.5 具体文件更新示例

#### 示例1：更新Vue组件
```vue
<script setup lang="ts">
// 旧版导入
import { useImageAnnotation } from '@/composables/useImageAnnotation'
import { listVersion } from '@/api/cert/version'
import { Country } from '@/types/common'

// 新版导入
import { useImageAnnotation } from '@/composables'
import { getVersionList } from '@/api/cert'
import { Country } from '@/types'
</script>
```

#### 示例2：更新JavaScript文件
```javascript
// 旧版导入
import { useBatchTasks } from '@/composables/useBatchTasks'
import { getFolderList } from '@/api/cert/folder'

// 新版导入
import { useBatchTasks } from '@/composables'
import { getFolderList } from '@/api/cert'
```

### 2.6 检查常见问题

#### 函数名变化
```typescript
// 旧版函数名
listVersion(query)
getVersion(id)
addVersion(data)

// 新版函数名
getVersionList(query)
getVersionDetails(id)
createVersion(data)
```

#### 参数类型变化
```typescript
// 旧版（JavaScript）
const response = await listVersion(query)

// 新版（TypeScript）
const response = await getVersionList(query as VersionQueryParams)
```

### 2.7 验证更新

更新完成后，进行以下验证：

1. **编译检查**
```bash
npm run build
# 或
yarn build
```

2. **类型检查**
```bash
npm run type-check
# 或
yarn type-check
```

3. **功能测试**
- 启动开发服务器
- 测试各个功能模块
- 检查控制台是否有错误

### 2.8 处理错误

如果遇到错误，可能的解决方案：

#### 类型错误
```typescript
// 如果遇到类型错误，可以添加类型断言
const response = await getVersionList(query as any)
```

#### 函数不存在错误
```typescript
// 如果新函数不存在，可以使用兼容性API
import { listVersion } from '@/api/cert/legacy'
```

#### 导入路径错误
```typescript
// 检查路径是否正确
import { useImageAnnotation } from '@/composables' // 确保index.ts存在
```

## �� 操作检查清单

### 删除文件检查
- [ ] 删除 `src/api/cert/` 目录
- [ ] 删除 `src/composables/useImageAnnotation.js`
- [ ] 删除 `src/composables/useBatchTasks.js`
- [ ] 删除 `src/composables/useFolderInfo.js`
- [ ] 删除 `src/composables/useAnnotationPermission.ts`

### 更新导入检查
- [ ] 更新所有 `@/api/cert/` 导入为 `@/api/cert`
- [ ] 更新所有 `@/composables/useXXX` 导入为 `@/composables`
- [ ] 更新所有 `@/types/xxx` 导入为 `@/types`
- [ ] 更新函数名（如 `listVersion` → `getVersionList`）
- [ ] 添加必要的类型注解

### 验证检查
- [ ] 项目能够正常编译
- [ ] 没有TypeScript类型错误
- [ ] 功能测试通过
- [ ] 控制台没有导入错误

## �� 遇到问题怎么办

1. **导入错误** - 检查文件路径是否正确
2. **函数不存在** - 使用兼容性API或检查新函数名
3. **类型错误** - 添加类型断言或检查类型定义
4. **编译失败** - 逐步回滚更改，找出问题所在

如果在操作过程中遇到任何问题，请告诉我具体的错误信息，我会帮您解决。
