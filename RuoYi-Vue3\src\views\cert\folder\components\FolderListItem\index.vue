<template>
  <div
    class="folder-list-item"
    :class="{
      'is-checked': isChecked,
      'is-selected': isSelected,
      'is-clickable': showRadio
    }"
    @click="handleItemClick"
  >
    <!-- 复选框 -->
    <div v-if="showCheckbox" class="checkbox-wrapper">
      <el-checkbox
        :model-value="isChecked"
        @change="handleCheckChange"
        :disabled="disabled"
      />
    </div>

    <!-- 单选按钮 -->
    <div v-if="showRadio" class="radio-wrapper">
      <el-radio
        :model-value="isSelected"
        :label="true"
        @change="handleRadioChange"
        :disabled="disabled"
        class="hidden-label"
      />
    </div>

    <!-- 缩略图 -->
    <div class="thumbnail-wrapper">
      <div class="thumbnail">
        <img
          v-if="thumbnailUrl"
          :src="thumbnailUrl"
          :alt="folderName"
          @error="handleImageError"
          class="thumbnail-image"
        />
        <div v-else class="thumbnail-placeholder">
          <el-icon><Folder /></el-icon>
        </div>
      </div>
    </div>

    <!-- 文件夹信息 -->
    <div class="folder-info">
      <div class="folder-name" :title="folderName">
        {{ folderName }}
      </div>

      <!-- 标准标签 -->
      <div v-if="showStandardBadge" class="standard-badge">
        <el-tag type="success" size="small" effect="dark">
          <el-icon><Star /></el-icon>
          标准样本
        </el-tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <!-- 比对按钮 -->
      <el-button
        v-if="showCompareButton"
        type="primary"
        size="small"
        @click="handleCompareClick"
        :disabled="disabled"
      >
        比对
      </el-button>

      <!-- 插槽：自定义操作 -->
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Folder, Star } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  /** 文件夹名称 */
  folderName: {
    type: String,
    required: true
  },
  /** 主图缩略图URL */
  thumbnailUrl: {
    type: String,
    default: ''
  },
  /** 是否显示复选框 */
  showCheckbox: {
    type: Boolean,
    default: false
  },
  /** 复选框是否被选中 */
  isChecked: {
    type: Boolean,
    default: false
  },
  /** 是否显示单选按钮 */
  showRadio: {
    type: Boolean,
    default: false
  },
  /** 单选按钮是否被选中 */
  isSelected: {
    type: Boolean,
    default: false
  },
  /** 是否显示[比对]按钮 */
  showCompareButton: {
    type: Boolean,
    default: false
  },
  /** 是否显示[标准]标签 */
  showStandardBadge: {
    type: Boolean,
    default: false
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false
  },
  /** 自定义样式类 */
  customClass: {
    type: String,
    default: ''
  }
})

// Emits 定义
const emit = defineEmits([
  'check-change',
  'radio-change',
  'folder-clicked',
  'compare-clicked',
  'image-error'
])

// 计算属性
const itemClass = computed(() => {
  return {
    'folder-list-item': true,
    'is-checked': props.isChecked,
    'is-disabled': props.disabled,
    [props.customClass]: !!props.customClass
  }
})

// 事件处理
const handleCheckChange = (checked) => {
  emit('check-change', checked)
}

const handleRadioChange = (checked) => {
  emit('radio-change', checked)
}

const handleItemClick = () => {
  if (props.showRadio && !props.disabled) {
    emit('folder-clicked')
  }
}

const handleCompareClick = () => {
  emit('compare-clicked')
}

const handleImageError = (event) => {
  emit('image-error', event)
  // 设置默认图片或隐藏图片
  event.target.style.display = 'none'
}
</script>

<style scoped>
.folder-list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  gap: 12px;
}

.folder-list-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.folder-list-item.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.folder-list-item.is-selected {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.folder-list-item.is-clickable {
  cursor: pointer;
}

.folder-list-item.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.folder-list-item.is-disabled:hover {
  border-color: #e4e7ed;
  box-shadow: none;
}

/* 复选框样式 */
.checkbox-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 单选按钮样式 */
.radio-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.hidden-label :deep(.el-radio__label) {
  display: none !important;
}

.custom-radio {
  width: 16px;
  height: 16px;
  border: 2px solid #dcdfe6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.custom-radio.is-selected {
  border-color: #409eff;
  background-color: #409eff;
}

.custom-radio .radio-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0;
  transition: opacity 0.3s;
}

.custom-radio.is-selected .radio-dot {
  opacity: 1;
}

.no-text :deep(.el-radio-button__inner) {
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #dcdfe6;
}

.no-text :deep(.el-radio-button__inner::before) {
  content: '';
  display: none;
}

/* 缩略图样式 */
.thumbnail-wrapper {
  flex-shrink: 0;
}

.thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 24px;
}

/* 文件夹信息样式 */
.folder-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.standard-badge {
  display: flex;
  align-items: center;
}

/* 操作按钮样式 */
.actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .folder-list-item {
    padding: 8px 12px;
    gap: 8px;
  }

  .thumbnail {
    width: 50px;
    height: 50px;
  }

  .folder-name {
    font-size: 13px;
  }

  .actions {
    gap: 4px;
  }
}

/* 紧凑模式 */
.folder-list-item.compact {
  padding: 8px 12px;
  gap: 8px;
}

.folder-list-item.compact .thumbnail {
  width: 40px;
  height: 40px;
}

.folder-list-item.compact .folder-name {
  font-size: 13px;
}

/* 大尺寸模式 */
.folder-list-item.large {
  padding: 16px 20px;
  gap: 16px;
}

.folder-list-item.large .thumbnail {
  width: 80px;
  height: 80px;
}

.folder-list-item.large .folder-name {
  font-size: 16px;
}
</style>
