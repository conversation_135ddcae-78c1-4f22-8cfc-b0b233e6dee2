package com.ruoyi.system.domain.mongo;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * 图像仓库
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Document(collection = "image_repository")
public class ImageRepository {

    @Id
    private String id;

    private String imageId; // 业务ID
    private String taskId;
    private String folderId;
    private String versionId;
    private Country countryInfo;
    private CertType certInfo;
    private String issueYear;
    private String fileName;
    private String filePath;
    private String originalFileName;
    private String minioPath;
    private Long fileSize;
    private String contentType;
    private Integer imageWidth;
    private Integer imageHeight;
    private String imageType;

    /** 是否为可标注的特定类型 */
    @Field("isAnnotatableType")
    private Boolean isAnnotatableType = false;

    private String lightType;
    private Boolean isMainImage;
    @Field("processingStatus")
    private String processStatus;
    private String processMessage;
    private String imageMd5;
    private List<String> tags;
    private SysDept deptInfo;
    private SysUser creatorInfo;
    private List<Object> annotations;
    private Date createTime;
    private Date updateTime;

    // 新增字段 - 样本分类相关



    @Field("standardSampleInfo")
    private StandardSampleInfo standardSampleInfo; // 标准样本信息

    /**
     * 标准样本信息
     */
    @Data
    public static class StandardSampleInfo {
        @Field("designation")
        private String designation; // 指定原因

        @Field("designatedAt")
        private Date designatedAt; // 指定时间

        @Field("designatedBy")
        private String designatedBy; // 指定人ID

        @Field("description")
        private String description; // 描述信息
    }



    // 便捷方法：判断是否可以标注（基于图片类型）
    public boolean canAnnotate() {
        return Boolean.TRUE.equals(this.isAnnotatableType);
    }
}