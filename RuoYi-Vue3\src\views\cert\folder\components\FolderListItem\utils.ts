import type { FolderData, SimpleFolderInfo, FolderFilterFunction, FolderSortFunction } from './types'

/**
 * 文件夹工具函数
 */

/**
 * 将MongoDB数据转换为简化的文件夹信息
 */
export function transformFolderData(folder: FolderData): SimpleFolderInfo {
  return {
    folderId: folder.folderId,
    folderName: folder.folderName,
    thumbnailUrl: folder.mainPicPath,
    folderType: folder.folderType,
    status: folder.status,
    countryName: folder.countryInfo?.name,
    certTypeName: folder.certInfo?.zjlbmc,
    issueYear: folder.issueYear,
    fileCount: folder.fileCount,
    isSelected: false
  }
}

/**
 * 批量转换文件夹数据
 */
export function transformFolderList(folders: FolderData[]): SimpleFolderInfo[] {
  return folders.map(transformFolderData)
}

/**
 * 根据文件夹类型过滤
 */
export function filterByFolderType(folderType: 'standard' | 'regular'): FolderFilterFunction {
  return (folder: FolderData) => folder.folderType === folderType
}

/**
 * 根据状态过滤
 */
export function filterByStatus(status: string): FolderFilterFunction {
  return (folder: FolderData) => folder.status === status
}

/**
 * 根据国家过滤
 */
export function filterByCountry(countryCode: string): FolderFilterFunction {
  return (folder: FolderData) => folder.countryInfo?.code === countryCode
}

/**
 * 根据证件类型过滤
 */
export function filterByCertType(certTypeCode: string): FolderFilterFunction {
  return (folder: FolderData) => folder.certInfo?.zjlbdm === certTypeCode
}

/**
 * 根据关键词搜索文件夹名称
 */
export function filterByKeyword(keyword: string): FolderFilterFunction {
  const lowerKeyword = keyword.toLowerCase()
  return (folder: FolderData) => 
    folder.folderName.toLowerCase().includes(lowerKeyword) ||
    folder.countryInfo?.name.toLowerCase().includes(lowerKeyword) ||
    folder.certInfo?.zjlbmc.toLowerCase().includes(lowerKeyword)
}

/**
 * 按创建时间排序
 */
export function sortByCreateTime(ascending: boolean = true): FolderSortFunction {
  return (a: FolderData, b: FolderData) => {
    const timeA = new Date(a.createTime.$date).getTime()
    const timeB = new Date(b.createTime.$date).getTime()
    return ascending ? timeA - timeB : timeB - timeA
  }
}

/**
 * 按文件夹名称排序
 */
export function sortByFolderName(ascending: boolean = true): FolderSortFunction {
  return (a: FolderData, b: FolderData) => {
    const nameA = a.folderName.toLowerCase()
    const nameB = b.folderName.toLowerCase()
    return ascending ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA)
  }
}

/**
 * 按文件数量排序
 */
export function sortByFileCount(ascending: boolean = true): FolderSortFunction {
  return (a: FolderData, b: FolderData) => {
    const countA = a.fileCount || 0
    const countB = b.fileCount || 0
    return ascending ? countA - countB : countB - countA
  }
}

/**
 * 获取文件夹状态显示文本
 */
export function getFolderStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'associated': '已关联',
    'unassociated': '未关联',
    'processing': '处理中'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 获取文件夹状态标签类型
 */
export function getFolderStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'associated': 'success',
    'unassociated': 'warning',
    'processing': 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取审核状态显示文本
 */
export function getReviewStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未审核'
}

/**
 * 获取审核状态标签类型
 */
export function getReviewStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 格式化时间
 */
export function formatTime(time: string | { $date: string }): string {
  if (!time) return '-'
  
  let dateString: string
  if (typeof time === 'string') {
    dateString = time
  } else {
    dateString = time.$date
  }
  
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

/**
 * 获取文件夹显示名称
 */
export function getFolderDisplayName(folder: FolderData): string {
  // 优先使用解析后的信息
  if (folder.preParseVersionInfo?.parsedVersionCode) {
    return `${folder.countryInfo?.name}_${folder.certInfo?.zjlbmc}_${folder.issueYear}_${folder.preParseVersionInfo.certNumberPrefix}_${folder.preParseVersionInfo.issuePlace}`
  }
  
  // 回退到原始文件夹名称
  return folder.folderName
}

/**
 * 检查文件夹是否可以设为标准样本
 */
export function canSetAsStandard(folder: FolderData): boolean {
  return folder.status === 'associated' && folder.reviewStatus === 'approved'
}

/**
 * 检查文件夹是否可以比对
 */
export function canCompare(folder: FolderData): boolean {
  return folder.status === 'associated' && folder.fileCount > 0
}

/**
 * 获取文件夹缩略图URL
 */
export function getFolderThumbnailUrl(folder: FolderData): string {
  // 如果有主图路径，直接返回
  if (folder.mainPicPath) {
    return folder.mainPicPath
  }
  
  // 可以根据需要添加默认图片逻辑
  return ''
}

/**
 * 计算文件夹处理进度
 */
export function calculateFolderProgress(folder: FolderData): number {
  if (folder.fileCount === 0) return 0
  return Math.round((folder.processedFileCount / folder.fileCount) * 100)
}

/**
 * 获取文件夹进度状态
 */
export function getFolderProgressStatus(folder: FolderData): {
  percentage: number
  status: 'success' | 'warning' | 'exception'
} {
  const percentage = calculateFolderProgress(folder)
  
  if (percentage === 100) {
    return { percentage, status: 'success' }
  } else if (percentage > 0) {
    return { percentage, status: 'warning' }
  } else {
    return { percentage, status: 'exception' }
  }
}

/**
 * 验证文件夹数据完整性
 */
export function validateFolderData(folder: FolderData): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (!folder.folderId) {
    errors.push('文件夹ID不能为空')
  }
  
  if (!folder.folderName) {
    errors.push('文件夹名称不能为空')
  }
  
  if (!folder.countryInfo) {
    errors.push('国家信息不能为空')
  }
  
  if (!folder.certInfo) {
    errors.push('证件类型信息不能为空')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 生成文件夹唯一标识
 */
export function generateFolderKey(folder: FolderData): string {
  return `${folder.folderId}_${folder.createTime.$date}`
}

/**
 * 检查文件夹是否过期（基于创建时间）
 */
export function isFolderExpired(folder: FolderData, days: number = 30): boolean {
  const createTime = new Date(folder.createTime.$date)
  const now = new Date()
  const diffTime = now.getTime() - createTime.getTime()
  const diffDays = diffTime / (1000 * 60 * 60 * 24)
  
  return diffDays > days
}

/**
 * 获取文件夹年龄（天数）
 */
export function getFolderAge(folder: FolderData): number {
  const createTime = new Date(folder.createTime.$date)
  const now = new Date()
  const diffTime = now.getTime() - createTime.getTime()
  return Math.floor(diffTime / (1000 * 60 * 60 * 24))
} 