package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CertType;

/**
 * 证件类别Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ICertTypeService 
{
    /**
     * 查询证件类别
     * 
     * @param id 证件类别主键
     * @return 证件类别
     */
    public CertType selectCertTypeById(Long id);
    
    /**
     * 根据证件代码查询证件类别
     * 
     * @param zjlbdm 证件代码
     * @return 证件类别
     */
    public CertType selectCertTypeByCode(String zjlbdm);

    /**
     * 查询证件类别列表
     * 
     * @param certType 证件类别
     * @return 证件类别集合
     */
    public List<CertType> selectCertTypeList(CertType certType);

    /**
     * 新增证件类别
     * 
     * @param certType 证件类别
     * @return 结果
     */
    public int insertCertType(CertType certType);

    /**
     * 修改证件类别
     * 
     * @param certType 证件类别
     * @return 结果
     */
    public int updateCertType(CertType certType);

    /**
     * 批量删除证件类别
     * 
     * @param ids 需要删除的证件类别主键集合
     * @return 结果
     */
    public int deleteCertTypeByIds(Long[] ids);

    /**
     * 删除证件类别信息
     * 
     * @param id 证件类别主键
     * @return 结果
     */
    public int deleteCertTypeById(Long id);
}
