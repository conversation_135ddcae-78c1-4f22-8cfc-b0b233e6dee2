<template>
  <div class="annotation-editor-view">
    <!-- 顶部标签页导航 -->
    <el-tabs 
      v-model="activeTab" 
      @tab-change="handleTabChange"
      class="annotation-tabs"
    >
      <el-tab-pane label="可见光" name="visible">
        <div class="tab-content">
          <div class="main-workspace">
            <!-- 左侧主面板 -->
            <div class="left-panel">
              <AnnotationToolbar 
                :can-edit="canEdit"
                :can-view="canView"
                :reason="permissionReason"
                :image-type="'visible'"
                :image-type-display-name="'可见光'"
                :is-standard-sample="isStandardSample"
                :is-annotatable-type="true"
                :annotations="currentImageData.annotations"
                :saving="saving"
                @save="handleSaveAnnotations"
                @reset="handleResetAnnotations"
                @add-annotation="handleAddAnnotation"
              />
              <AnnotationCanvas 
                ref="annotationCanvasRef"
                :image-url="currentImageData.url"
                :tool="currentTool"
                v-model="currentImageData.annotations"
                @select-annotation="handleCanvasSelectAnnotation"
              />
            </div>
            
            <!-- 右侧边栏 -->
            <div class="right-sidebar">
              <AnnotationList 
                :annotations="currentImageData.annotations"
                :can-edit="canEdit"
                :selected-annotation-id="selectedAnnotationId"
                @select-annotation="handleListSelectAnnotation"
                @update:annotations="handleListUpdateAnnotations"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="红外" name="infrared">
        <div class="tab-content">
          <div class="main-workspace">
            <!-- 左侧主面板 -->
            <div class="left-panel">
              <AnnotationToolbar 
                :can-edit="canEdit"
                :can-view="canView"
                :reason="permissionReason"
                :image-type="'infrared'"
                :image-type-display-name="'红外'"
                :is-standard-sample="isStandardSample"
                :is-annotatable-type="true"
                :annotations="currentImageData.annotations"
                :saving="saving"
                @save="handleSaveAnnotations"
                @reset="handleResetAnnotations"
                @add-annotation="handleAddAnnotation"
              />
              <AnnotationCanvas 
                ref="annotationCanvasRef"
                :image-url="currentImageData.url"
                :tool="currentTool"
                v-model="currentImageData.annotations"
                @select-annotation="handleCanvasSelectAnnotation"
              />
            </div>
            
            <!-- 右侧边栏 -->
            <div class="right-sidebar">
              <AnnotationList 
                :annotations="currentImageData.annotations"
                :can-edit="canEdit"
                :selected-annotation-id="selectedAnnotationId"
                @select-annotation="handleListSelectAnnotation"
                @update:annotations="handleListUpdateAnnotations"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="紫外" name="ultraviolet">
        <div class="tab-content">
          <div class="main-workspace">
            <!-- 左侧主面板 -->
            <div class="left-panel">
              <AnnotationToolbar 
                :can-edit="canEdit"
                :can-view="canView"
                :reason="permissionReason"
                :image-type="'ultraviolet'"
                :image-type-display-name="'紫外'"
                :is-standard-sample="isStandardSample"
                :is-annotatable-type="true"
                :annotations="currentImageData.annotations"
                :saving="saving"
                @save="handleSaveAnnotations"
                @reset="handleResetAnnotations"
                @add-annotation="handleAddAnnotation"
              />
              <AnnotationCanvas 
                ref="annotationCanvasRef"
                :image-url="currentImageData.url"
                :tool="currentTool"
                v-model="currentImageData.annotations"
                @select-annotation="handleCanvasSelectAnnotation"
              />
            </div>
            
            <!-- 右侧边栏 -->
            <div class="right-sidebar">
              <AnnotationList 
                :annotations="currentImageData.annotations"
                :can-edit="canEdit"
                :selected-annotation-id="selectedAnnotationId"
                @select-annotation="handleListSelectAnnotation"
                @update:annotations="handleListUpdateAnnotations"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
      <div class="action-buttons">
        <el-button @click="handleCancel">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSaveAndReturn"
          :loading="saving"
          :disabled="!canEdit"
        >
          <el-icon><Check /></el-icon>
          保存并返回
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-spinner size="large" />
      <p>正在加载标注数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close, Check } from '@element-plus/icons-vue'
import AnnotationToolbar from '@/components/Annotation/AnnotationToolbar.vue'
import AnnotationCanvas from './AnnotationCanvas.vue'
import AnnotationList from '@/components/Annotation/AnnotationList.vue'
import type { AnnotationItem } from '@/types/annotation'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const activeTab = ref('visible')
const currentTool = ref('select')
const selectedAnnotationId = ref<string | null>(null)
const annotationCanvasRef = ref()

// 权限相关
const canEdit = ref(true)
const canView = ref(true)
const permissionReason = ref('')
const isStandardSample = ref(true)

// 集中管理三组标注数据
const allAnnotationData = reactive({
  visible: { 
    url: '', 
    annotations: [] as AnnotationItem[] 
  },
  infrared: { 
    url: '', 
    annotations: [] as AnnotationItem[] 
  },
  ultraviolet: { 
    url: '', 
    annotations: [] as AnnotationItem[] 
  }
})

// 计算属性：根据当前激活的标签页返回对应的数据
const currentImageData = computed(() => {
  return allAnnotationData[activeTab.value as keyof typeof allAnnotationData]
})

// 生命周期
onMounted(async () => {
  const versionId = route.params.versionId as string
  if (!versionId) {
    ElMessage.error('缺少版本ID参数')
    router.go(-1)
    return
  }
  
  await loadStandardSampleData(versionId)
})

// API 调用：获取标准样本数据
const loadStandardSampleData = async (versionId: string) => {
  try {
    loading.value = true
    
    // TODO: 替换为实际的API调用
    // const response = await getStandardSampleData(versionId)
    
    // 模拟API响应数据
    const mockData = {
      visible: {
        url: '/api/images/visible-light.jpg',
        annotations: []
      },
      infrared: {
        url: '/api/images/infrared.jpg', 
        annotations: []
      },
      ultraviolet: {
        url: '/api/images/ultraviolet.jpg',
        annotations: []
      }
    }
    
    // 填充数据
    Object.assign(allAnnotationData, mockData)
    
    // 检查权限
    checkAnnotationPermissions(versionId)
    
  } catch (error) {
    ElMessage.error('加载数据失败：' + (error as Error).message)
    router.go(-1)
  } finally {
    loading.value = false
  }
}

// 检查标注权限
const checkAnnotationPermissions = async (versionId: string) => {
  try {
    // TODO: 替换为实际的权限检查API
    // const permissionInfo = await getAnnotationPermissions(versionId)
    
    // 模拟权限数据
    const permissionInfo = {
      canEdit: true,
      canView: true,
      reason: '',
      isStandardSample: true
    }
    
    canEdit.value = permissionInfo.canEdit
    canView.value = permissionInfo.canView
    permissionReason.value = permissionInfo.reason
    isStandardSample.value = permissionInfo.isStandardSample
    
  } catch (error) {
    ElMessage.error('权限检查失败：' + (error as Error).message)
  }
}

// 标签页切换处理
const handleTabChange = (tabName: string) => {
  // 清空选中的标注
  selectedAnnotationId.value = null
  
  // 重置工具为选择模式
  currentTool.value = 'select'
  
  // 等待DOM更新后重新初始化画布
  nextTick(() => {
    if (annotationCanvasRef.value) {
      annotationCanvasRef.value.resetTool()
    }
  })
}

// 画布选择标注处理
const handleCanvasSelectAnnotation = (annotation: AnnotationItem) => {
  selectedAnnotationId.value = annotation.annotationId || null
}

// 列表选择标注处理
const handleListSelectAnnotation = (annotationId: string) => {
  selectedAnnotationId.value = annotationId
  
  // 调用画布的选中方法
  if (annotationCanvasRef.value) {
    annotationCanvasRef.value.selectAnnotation(annotationId)
  }
}

// 列表更新标注处理
const handleListUpdateAnnotations = (annotations: AnnotationItem[]) => {
  currentImageData.value.annotations = annotations
}

// 工具栏添加标注处理
const handleAddAnnotation = (annotation: AnnotationItem) => {
  currentImageData.value.annotations.push(annotation)
}

// 工具栏保存标注处理
const handleSaveAnnotations = (annotations: AnnotationItem[]) => {
  currentImageData.value.annotations = annotations
  ElMessage.success('标注已保存')
}

// 工具栏重置标注处理
const handleResetAnnotations = () => {
  ElMessageBox.confirm(
    '确定要重置当前图片的所有标注吗？此操作不可撤销。',
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    currentImageData.value.annotations = []
    ElMessage.success('标注已重置')
  }).catch(() => {
    // 用户取消
  })
}

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    '确定要取消编辑吗？未保存的更改将丢失。',
    '确认取消',
    {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }
  ).then(() => {
    router.go(-1)
  }).catch(() => {
    // 用户选择继续编辑
  })
}

// 保存并返回
const handleSaveAndReturn = async () => {
  try {
    saving.value = true
    
    const versionId = route.params.versionId as string
    
    // TODO: 替换为实际的保存API
    // await saveAllAnnotations(versionId, allAnnotationData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('标注保存成功')
    router.go(-1)
    
  } catch (error) {
    ElMessage.error('保存失败：' + (error as Error).message)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.annotation-editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.annotation-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
  padding: 16px;
}

.main-workspace {
  height: 100%;
  display: flex;
  gap: 16px;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
}

.right-sidebar {
  width: 320px;
  flex-shrink: 0;
}

.bottom-action-bar {
  height: 60px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-overlay p {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .main-workspace {
    flex-direction: column;
  }
  
  .right-sidebar {
    width: 100%;
    height: 300px;
  }
}
</style> 