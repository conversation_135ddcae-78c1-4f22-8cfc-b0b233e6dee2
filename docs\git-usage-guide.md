 # Git 使用指南

## �� 概述

本文档介绍如何在证件样本管理系统中使用Git进行版本控制，包括本地仓库配置、日常操作和文件恢复方法。

## �� 快速开始

### 1. 检查Git仓库状态

```bash
# 检查当前Git状态
git status

# 查看是否有.git目录
ls -la
# 或
dir
```

### 2. 配置Git用户信息

```bash
# 全局配置（推荐）
git config --global user.name "你的姓名"
git config --global user.email "你的邮箱@example.com"

# 仅当前仓库配置
git config user.name "你的姓名"
git config user.email "你的邮箱@example.com"

# 验证配置
git config user.name
git config user.email
```

## �� 日常Git操作

### 查看状态和差异

```bash
# 查看工作区状态
git status

# 查看文件修改差异
git diff

# 查看暂存区差异
git diff --cached

# 查看暂存区文件列表
git diff --cached --name-only
```

### 添加和提交文件

```bash
# 添加所有文件到暂存区
git add .

# 添加特定文件
git add 文件名

# 添加特定目录
git add 目录名/

# 排除特定目录（如临时文件）
git add . ':!uploads/'

# 提交暂存区的文件
git commit -m "提交信息"

# 查看提交历史
git log --oneline
git log --oneline --graph
```

### 分支操作

```bash
# 查看所有分支
git branch -a

# 创建并切换到新分支
git checkout -b 分支名

# 切换分支
git checkout 分支名

# 删除分支
git branch -d 分支名
```

## 🔍 查看文件历史

### 查看文件的提交历史

```bash
# 查看特定文件的提交历史
git log --oneline 文件名

# 例如查看Vue文件的历史
git log --oneline RuoYi-Vue3/src/composables/useBatchTasks.ts

# 查看文件的详细修改记录
git log -p 文件名

# 查看最近3次修改
git log -p -3 文件名
```

### 查看文件内容

```bash
# 查看当前工作区的文件
cat 文件名

# 查看暂存区的文件
git show :文件名

# 查看最近一次提交的文件内容
git show HEAD:文件名

# 查看指定提交的文件内容
git show 提交ID:文件名

# 例如
git show abc1234:RuoYi-Vue3/src/composables/useBatchTasks.ts
```

### 比较文件差异

```bash
# 比较工作区和最近一次提交
git diff 文件名

# 比较工作区和指定提交
git diff 提交ID 文件名

# 比较两个提交之间的差异
git diff 提交ID1 提交ID2 文件名

# 比较暂存区和最近提交
git diff --cached 文件名
```

## 🔄 恢复文件到历史版本

### 恢复单个文件

```bash
# 恢复文件到最近一次提交的状态
git checkout -- 文件名
# 或使用新版本命令
git restore 文件名

# 恢复文件到指定提交的状态
git checkout 提交ID -- 文件名
# 或使用新版本命令
git restore --source=提交ID 文件名
```

### 查看提交详情

```bash
# 查看最近一次提交的详细信息
git show HEAD

# 查看指定提交的详细信息
git show 提交ID

# 查看提交的统计信息
git show --stat 提交ID

# 查看提交的文件列表
git show --name-only 提交ID
```

## 🛠️ 实际使用示例

### 示例1：查看和恢复Vue组件文件

```bash
# 1. 查看文件历史
git log --oneline RuoYi-Vue3/src/composables/useBatchTasks.ts

# 2. 查看最近提交的文件内容
git show HEAD:RuoYi-Vue3/src/composables/useBatchTasks.ts

# 3. 比较当前版本和最近提交的差异
git diff RuoYi-Vue3/src/composables/useBatchTasks.ts

# 4. 恢复文件到最近一次提交的状态
git restore RuoYi-Vue3/src/composables/useBatchTasks.ts

# 5. 恢复到一个特定的提交
git restore --source=abc1234 RuoYi-Vue3/src/composables/useBatchTasks.ts
```

### 示例2：处理临时文件

```bash
# 1. 查看暂存区内容
git diff --cached --name-only

# 2. 如果看到uploads目录，先移除
git reset HEAD uploads/

# 3. 更新.gitignore文件，添加临时文件规则
# 在.gitignore中添加：
# uploads/tus-temp/
# uploads/*/

# 4. 重新添加需要的文件
git add . ':!uploads/'

# 5. 检查并提交
git status
git commit -m "添加新功能和重构：证书管理、新闻系统、批量上传等"
```

## �� .gitignore 配置建议

在项目根目录的 `.gitignore` 文件中添加以下规则：

```gitignore
# 临时上传文件
uploads/tus-temp/
uploads/tus-temp/uploads/
uploads/*/

# Vue3 相关
node_modules/
dist/
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 编辑器
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统
.DS_Store
Thumbs.db

# 构建输出
target/
build/
```

## 🔧 常用命令速查

| 命令 | 说明 |
|------|------|
| `git status` | 查看工作区状态 |
| `git add .` | 添加所有文件到暂存区 |
| `git commit -m "信息"` | 提交暂存区文件 |
| `git log --oneline` | 查看提交历史 |
| `git diff 文件名` | 查看文件修改差异 |
| `git restore 文件名` | 恢复文件到最近提交 |
| `git show HEAD:文件名` | 查看最近提交的文件内容 |
| `git log --oneline 文件名` | 查看文件历史 |

## 🎯 最佳实践

### 1. 提交信息规范

```bash
# 使用清晰的提交信息
git commit -m "feat: 添加证书管理模块"
git commit -m "fix: 修复登录验证bug"
git commit -m "docs: 更新API文档"
git commit -m "refactor: 重构批量上传组件"
```

### 2. 定期提交

```bash
# 经常提交小的变更
git add .
git commit -m "feat: 添加新功能"

# 避免一次性提交大量文件
```

### 3. 使用分支

```bash
# 为功能开发创建分支
git checkout -b feature/证书管理

# 开发完成后合并到主分支
git checkout master
git merge feature/证书管理
```

### 4. 备份重要提交

```bash
# 创建备份分支
git branch backup/$(date +%Y%m%d)

# 查看所有分支
git branch -a
```

## 🚨 注意事项

1. **不要提交临时文件** - 确保 `.gitignore` 正确配置
2. **提交前检查** - 使用 `git status` 和 `git diff` 检查变更
3. **备份重要数据** - 定期创建备份分支
4. **使用描述性提交信息** - 便于后续维护和协作

## 📞 技术支持

如遇到Git相关问题，可以：
1. 查看Git官方文档
2. 使用 `git help 命令名` 查看帮助
3. 联系开发团队

---

**文档维护**: 开发团队  
**最后更新**: 2025-07-12 
**适用版本**: Git 2.x+