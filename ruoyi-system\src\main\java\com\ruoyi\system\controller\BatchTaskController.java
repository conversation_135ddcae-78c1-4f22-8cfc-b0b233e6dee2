package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.dto.request.BatchTaskCreateDTO;
import com.ruoyi.system.domain.dto.response.BatchUploadTaskVO;
import com.ruoyi.system.domain.mongo.BatchUploadTask;
import com.ruoyi.system.repository.BatchUploadTaskRepository;
import com.ruoyi.system.service.IBatchTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 批量上传任务控制器 (统一版)
 *
 * 提供批量断点续传任务的REST API接口，包括：
 * 1. 创建批量上传任务
 * 2. 查询任务列表和详情
 * 3. 管理任务状态
 * 4. 兼容性API路径支持
 *
 * API路径说明：
 * - 新统一路径: /batch/tasks/*
 * - 兼容性路径: /cert/batch/* (标记为 @Deprecated)
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/batch/tasks")
public class BatchTaskController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(BatchTaskController.class);

    @Autowired
    private IBatchTaskService batchTaskService;

    @Autowired
    private BatchUploadTaskRepository batchUploadTaskRepository;

    /**
     * 创建批量上传任务（统一接口）
     *
     * 一次性创建批量上传任务及其所有文件夹信息。
     * 支持单文件夹和多文件夹场景的统一处理。
     *
     * @param dto 任务创建DTO（包含文件夹列表）
     * @return 创建的任务信息，包含taskId和文件夹信息
     */
    @PreAuthorize("@ss.hasPermi('cert:batch:create')")
    @Log(title = "批量上传任务", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createBatchTask(@Validated @RequestBody BatchTaskCreateDTO dto) {
        try {
            log.info("接收到批量任务创建请求: 文件夹数={}, 总文件数={}",
                    dto.getFolderCount(), dto.getTotalFileCount());

            // 调用服务层创建任务
            BatchUploadTask task = batchTaskService.createBatchTask(dto);

            log.info("批量任务创建成功，taskId: {}, 任务名称: {}",
                    task.getTaskId(), task.getTaskName());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getTaskId());
            result.put("taskName", task.getTaskName());
            result.put("status", task.getStatus());
            result.put("totalFiles", task.getTotalFiles());
            result.put("totalFolders", task.getTotalFolders());
            result.put("folderCount", dto.getFolderCount());
            result.put("isMultiFolder", dto.isMultiFolder());

            return AjaxResult.success("批量任务创建成功", result);

        } catch (ServiceException e) {
            log.error("创建批量任务失败: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("创建批量任务时发生未知异常", e);
            return AjaxResult.error("创建任务失败: " + e.getMessage());
        }
    }





    /**
     * 获取批量上传任务列表
     * 
     * 支持分页查询和状态过滤，返回当前用户可见的任务列表。
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param status 任务状态过滤（可选）
     * @return 分页的任务列表
     */
    @PreAuthorize("@ss.hasPermi('cert:batch:list')")
    @GetMapping("/list")
    public TableDataInfo getBatchTaskList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String status) {
        
        try {
            log.debug("查询批量任务列表，页码: {}, 每页大小: {}, 状态过滤: {}", 
                    pageNum, pageSize, status);
            
            // 构建分页参数，按创建时间倒序
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize, 
                    Sort.by(Sort.Direction.DESC, "createTime"));
            
            Page<BatchUploadTask> page;
            
            // 根据状态过滤
            if (status != null && !status.trim().isEmpty()) {
                page = batchUploadTaskRepository.findByStatus(status, pageable);
            } else {
                page = batchUploadTaskRepository.findAll(pageable);
            }
            
            log.debug("查询到 {} 条任务记录", page.getTotalElements());
            
            return getDataTable(page.getContent());

        } catch (Exception e) {
            log.error("查询批量任务列表失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取批量上传任务详情
     * 
     * 根据taskId查询任务的详细信息，包括进度、状态等。
     * 
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('cert:batch:query')")
    @GetMapping("/{taskId}")
    public AjaxResult getBatchTaskDetail(@PathVariable String taskId) {
        try {
            log.debug("查询批量任务详情，taskId: {}", taskId);
            
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            
            if (!taskOpt.isPresent()) {
                log.warn("任务不存在，taskId: {}", taskId);
                return AjaxResult.error("任务不存在");
            }
            
            BatchUploadTask task = taskOpt.get();
            log.debug("查询到任务详情: {}", task.getTaskName());
            
            return AjaxResult.success("查询成功", task);
            
        } catch (Exception e) {
            log.error("查询批量任务详情失败，taskId: {}", taskId, e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除批量上传任务
     *
     * 删除指定的批量上传任务及其关联数据。
     * 注意：此操作会级联删除相关的FolderInfo、ImageRepository记录以及MinIO中的文件。
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @PreAuthorize("@ss.hasPermi('cert:batch:remove')")
    @Log(title = "批量上传任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskId}")
    public AjaxResult deleteBatchTask(@PathVariable String taskId) {
        try {
            log.info("开始删除批量任务，taskId: {}", taskId);

            // 检查任务是否存在
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("要删除的任务不存在，taskId: {}", taskId);
                return AjaxResult.error("任务不存在");
            }

            BatchUploadTask task = taskOpt.get();
            log.info("找到任务: {}, 开始级联删除相关数据", task.getTaskName());

            // 使用 BatchTaskService 进行级联删除
            boolean deleteSuccess = batchTaskService.deleteBatchTaskWithCascade(taskId);

            if (deleteSuccess) {
                log.info("批量任务删除成功，taskId: {}", taskId);
                return AjaxResult.success("删除成功");
            } else {
                log.error("批量任务删除失败，taskId: {}", taskId);
                return AjaxResult.error("删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除批量任务失败，taskId: {}", taskId, e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }



    /**
     * 重新启动失败的任务
     * 
     * 将失败的任务状态重置为UPLOADING，允许重新上传。
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('cert:batch:edit')")
    @Log(title = "批量上传任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/restart")
    public AjaxResult restartTask(@PathVariable String taskId) {
        try {
            log.info("重新启动任务，taskId: {}", taskId);
            
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                return AjaxResult.error("任务不存在");
            }
            
            BatchUploadTask task = taskOpt.get();
            
            // 只允许重启失败的任务
            if (!"FAILED".equals(task.getStatus())) {
                return AjaxResult.error("只能重启失败的任务");
            }
            
            // 重置任务状态
            task.setStatus("UPLOADING");
            task.setProcessedFiles(0);
            task.setEndTime(null);
            batchUploadTaskRepository.save(task);
            
            log.info("任务重启成功，taskId: {}", taskId);
            return AjaxResult.success("任务重启成功");
            
        } catch (Exception e) {
            log.error("重启任务失败，taskId: {}", taskId, e);
            return AjaxResult.error("重启失败: " + e.getMessage());
        }
    }

    // ==================== 以下API来自原 IBatchUploadTaskService (保持兼容性) ====================

    /**
     * 获取用户的任务列表
     * GET /batch/tasks/user/{userId}
     */
    @PreAuthorize("@ss.hasPermi('batch:task:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getTasksByUserId(@PathVariable Long userId) {
        try {
            log.info("获取用户任务列表，userId: {}", userId);
            List<BatchUploadTaskVO> tasks = batchTaskService.getTasksByUserId(userId);
            return AjaxResult.success("获取用户任务列表成功", tasks);
        } catch (Exception e) {
            log.error("获取用户任务列表失败，userId: {}", userId, e);
            return AjaxResult.error("获取用户任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门的任务列表
     * GET /batch/tasks/dept/{deptId}
     */
    @PreAuthorize("@ss.hasPermi('batch:task:list')")
    @GetMapping("/dept/{deptId}")
    public AjaxResult getTasksByDeptId(@PathVariable Long deptId) {
        try {
            log.info("获取部门任务列表，deptId: {}", deptId);
            List<BatchUploadTaskVO> tasks = batchTaskService.getTasksByDeptId(deptId);
            return AjaxResult.success("获取部门任务列表成功", tasks);
        } catch (Exception e) {
            log.error("获取部门任务列表失败，deptId: {}", deptId, e);
            return AjaxResult.error("获取部门任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定状态的任务列表
     * GET /batch/tasks/status/{status}
     */
    @PreAuthorize("@ss.hasPermi('batch:task:list')")
    @GetMapping("/status/{status}")
    public AjaxResult getTasksByStatus(@PathVariable String status) {
        try {
            log.info("获取指定状态任务列表，status: {}", status);
            List<BatchUploadTaskVO> tasks = batchTaskService.getTasksByStatus(status);
            return AjaxResult.success("获取指定状态任务列表成功", tasks);
        } catch (Exception e) {
            log.error("获取指定状态任务列表失败，status: {}", status, e);
            return AjaxResult.error("获取指定状态任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务状态
     * PUT /batch/tasks/{taskId}/status
     */
    @PreAuthorize("@ss.hasPermi('batch:task:edit')")
    @Log(title = "批量任务", businessType = BusinessType.UPDATE)
    @PutMapping("/{taskId}/status")
    public AjaxResult updateTaskStatus(@PathVariable String taskId, @RequestParam String status) {
        try {
            log.info("更新任务状态，taskId: {}, status: {}", taskId, status);
            boolean success = batchTaskService.updateTaskStatus(taskId, status);
            if (success) {
                return AjaxResult.success("更新任务状态成功");
            } else {
                return AjaxResult.error("更新任务状态失败");
            }
        } catch (Exception e) {
            log.error("更新任务状态失败，taskId: {}, status: {}", taskId, status, e);
            return AjaxResult.error("更新任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务进度
     * PUT /batch/tasks/{taskId}/progress
     */
    @PreAuthorize("@ss.hasPermi('batch:task:edit')")
    @Log(title = "批量任务", businessType = BusinessType.UPDATE)
    @PutMapping("/{taskId}/progress")
    public AjaxResult updateTaskProgress(@PathVariable String taskId,
                                       @RequestParam Integer processedFolders,
                                       @RequestParam Integer processedFiles) {
        try {
            log.info("更新任务进度，taskId: {}, processedFolders: {}, processedFiles: {}",
                    taskId, processedFolders, processedFiles);
            boolean success = batchTaskService.updateTaskProgress(taskId, processedFolders, processedFiles);
            if (success) {
                return AjaxResult.success("更新任务进度成功");
            } else {
                return AjaxResult.error("更新任务进度失败");
            }
        } catch (Exception e) {
            log.error("更新任务进度失败，taskId: {}", taskId, e);
            return AjaxResult.error("更新任务进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     * GET /batch/tasks/{taskId}/stats
     */
    @PreAuthorize("@ss.hasPermi('batch:task:query')")
    @GetMapping("/{taskId}/stats")
    public AjaxResult getTaskStatistics(@PathVariable String taskId) {
        try {
            log.info("获取任务统计信息，taskId: {}", taskId);
            Map<String, Object> stats = batchTaskService.getTaskStatistics(taskId);
            return AjaxResult.success("获取任务统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取任务统计信息失败，taskId: {}", taskId, e);
            return AjaxResult.error("获取任务统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 重试失败的任务
     * POST /batch/tasks/{taskId}/retry
     */
    @PreAuthorize("@ss.hasPermi('batch:task:edit')")
    @Log(title = "批量任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/retry")
    public AjaxResult retryTask(@PathVariable String taskId) {
        try {
            log.info("重试任务，taskId: {}", taskId);
            boolean success = batchTaskService.retryTask(taskId);
            if (success) {
                return AjaxResult.success("重试任务成功");
            } else {
                return AjaxResult.error("重试任务失败");
            }
        } catch (Exception e) {
            log.error("重试任务失败，taskId: {}", taskId, e);
            return AjaxResult.error("重试任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停任务
     * POST /batch/tasks/{taskId}/pause
     */
    @PreAuthorize("@ss.hasPermi('batch:task:edit')")
    @Log(title = "批量任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/pause")
    public AjaxResult pauseTask(@PathVariable String taskId) {
        try {
            log.info("暂停任务，taskId: {}", taskId);
            boolean success = batchTaskService.pauseTask(taskId);
            if (success) {
                return AjaxResult.success("暂停任务成功");
            } else {
                return AjaxResult.error("暂停任务失败");
            }
        } catch (Exception e) {
            log.error("暂停任务失败，taskId: {}", taskId, e);
            return AjaxResult.error("暂停任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复任务
     * POST /batch/tasks/{taskId}/resume
     */
    @PreAuthorize("@ss.hasPermi('batch:task:edit')")
    @Log(title = "批量任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/resume")
    public AjaxResult resumeTask(@PathVariable String taskId) {
        try {
            log.info("恢复任务，taskId: {}", taskId);
            boolean success = batchTaskService.resumeTask(taskId);
            if (success) {
                return AjaxResult.success("恢复任务成功");
            } else {
                return AjaxResult.error("恢复任务失败");
            }
        } catch (Exception e) {
            log.error("恢复任务失败，taskId: {}", taskId, e);
            return AjaxResult.error("恢复任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取批量上传统计信息
     * GET /batch/tasks/stats
     */
    @PreAuthorize("@ss.hasPermi('batch:task:query')")
    @GetMapping("/stats")
    public AjaxResult getBatchTaskStats() {
        try {
            log.info("获取批量上传统计信息");

            // 获取基本统计信息
            long totalTasks = batchUploadTaskRepository.count();
            long completedTasks = batchUploadTaskRepository.countByStatus("COMPLETED");
            long failedTasks = batchUploadTaskRepository.countByStatus("FAILED");
            long uploadingTasks = batchUploadTaskRepository.countByStatus("UPLOADING");
            long pausedTasks = batchUploadTaskRepository.countByStatus("PAUSED");

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalTasks", totalTasks);
            stats.put("completedTasks", completedTasks);
            stats.put("failedTasks", failedTasks);
            stats.put("uploadingTasks", uploadingTasks);
            stats.put("pausedTasks", pausedTasks);
            stats.put("successRate", totalTasks > 0 ? Math.round((double) completedTasks / totalTasks * 10000) / 100.0 : 0.0);

            return AjaxResult.success("获取统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return AjaxResult.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
