package com.ruoyi.common.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * MinIO 服务接口
 * 
 * <AUTHOR>
 */
public interface MinioService {
    
    /**
     * 上传文件
     * 
     * @param file 文件
     * @param directory 目录
     * @return 文件URL
     */
    String uploadFile(MultipartFile file, String directory) throws Exception;
    
    /**
     * 上传文件
     * 
     * @param inputStream 输入流
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @return 文件URL
     */
    String uploadFile(InputStream inputStream, String objectName, String contentType) throws Exception;
    
    /**
     * 获取文件URL
     * 
     * @param objectName 对象名称
     * @return 文件URL
     */
    String getFileUrl(String objectName) throws Exception;
    
    /**
     * 获取临时访问URL
     * 
     * @param objectName 对象名称
     * @param expirySeconds 过期时间（秒）
     * @return 临时访问URL
     */
    String getPresignedUrl(String objectName, int expirySeconds) throws Exception;
    
    /**
     * 删除文件
     * 
     * @param objectName 对象名称
     */
    void deleteFile(String objectName) throws Exception;
    
    /**
     * 批量删除文件
     * 
     * @param objectNames 对象名称列表
     */
    void deleteFiles(List<String> objectNames) throws Exception;
    
    /**
     * 检查文件是否存在
     * 
     * @param objectName 对象名称
     * @return 是否存在
     */
    boolean isFileExist(String objectName) throws Exception;
    
    /**
     * 创建存储桶
     * 
     * @param bucketName 存储桶名称
     */
    void createBucket(String bucketName) throws Exception;
    
    /**
     * 检查存储桶是否存在
     * 
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    boolean isBucketExist(String bucketName) throws Exception;
    
    /**
     * greenfox - 新增：获取文件对象流 - 2025-01-15
     *
     * @param objectName 对象名称
     * @return 文件输入流
     */
    InputStream getObject(String objectName) throws Exception;

    /**
     * greenfox - 新增：列出文件 - 2025-01-15
     *
     * @param prefix 文件前缀
     * @param limit 限制数量
     * @return 文件列表
     */
    List<String> listObjects(String prefix, int limit) throws Exception;
} 