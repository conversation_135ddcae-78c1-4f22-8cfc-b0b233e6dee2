-- =====================================================
-- 修复菜单路径配置 SQL 脚本
-- 日期: 2025-07-08
-- 说明: 修复文件移动后的菜单组件路径不匹配问题
-- =====================================================

-- 1. 修复证件版本表菜单 - 更新组件路径
UPDATE sys_menu 
SET component = 'cert/version/VersionManagementView',
    update_time = NOW(),
    update_by = 'admin',
    remark = '证件版本管理页面 - 已更新组件路径'
WHERE menu_id = 2032;

-- 2. 检查并修复批量管理相关菜单的父级ID
-- 批量任务相关权限菜单的父级ID需要指向正确的菜单
UPDATE sys_menu 
SET parent_id = 2229
WHERE menu_id IN (2230, 2231, 2232, 2233)
AND parent_id IS NULL OR parent_id = '';

-- 3. 修复样本管理相关权限菜单的父级ID
UPDATE sys_menu 
SET parent_id = 2101
WHERE menu_id IN (2237, 2238, 2239, 2240, 2241, 2242)
AND parent_id IS NULL OR parent_id = '';

-- 4. 修复文件夹详情相关权限菜单的父级ID
UPDATE sys_menu 
SET parent_id = 2100
WHERE menu_id IN (2243, 2244, 2245, 2246)
AND parent_id IS NULL OR parent_id = '';

-- 5. 修复国家管理相关权限菜单的父级ID
UPDATE sys_menu 
SET parent_id = 2044
WHERE menu_id IN (2247, 2248, 2249, 2250)
AND parent_id IS NULL OR parent_id = '';

-- 6. 修复证件类型相关权限菜单的父级ID
UPDATE sys_menu 
SET parent_id = 2026
WHERE menu_id IN (2251, 2252, 2253, 2254)
AND parent_id IS NULL OR parent_id = '';

-- 7. 添加版本管理菜单（如果不存在）
INSERT INTO sys_menu (
    menu_name, parent_id, order_num, path, component, 
    is_frame, is_cache, menu_type, visible, status, 
    perms, icon, create_by, create_time, update_by, update_time, remark
)
SELECT 
    '版本管理', 2000, 5, 'version-management', 'cert/version/VersionManagementView',
    1, 0, 'C', '0', '0',
    'cert:version:manage', 'tree-table', 'admin', NOW(), 'admin', NOW(), 
    '证件版本管理页面 - 统一版本管理入口'
WHERE NOT EXISTS (
    SELECT 1 FROM sys_menu 
    WHERE path = 'version-management' AND parent_id = 2000
);

-- 8. 更新菜单排序，确保逻辑顺序
UPDATE sys_menu SET order_num = 1 WHERE menu_id = 2044; -- 国家表
UPDATE sys_menu SET order_num = 2 WHERE menu_id = 2026; -- 证件类别表
UPDATE sys_menu SET order_num = 3 WHERE menu_id = 2032; -- 证件版本表
UPDATE sys_menu SET order_num = 4 WHERE menu_id = 2229; -- 批量上传任务
UPDATE sys_menu SET order_num = 5 WHERE menu_id = 2101; -- 样本管理
UPDATE sys_menu SET order_num = 6 WHERE menu_id = 2050; -- 证件样本库

-- 9. 确保隐藏菜单设置正确
UPDATE sys_menu 
SET visible = '1' 
WHERE menu_id IN (2051, 2052, 2053, 2054, 2100)
AND visible = '0';

-- 10. 清理无效的菜单项（可选 - 请谨慎执行）
-- 删除一些重复或无用的菜单项
DELETE FROM sys_menu WHERE menu_id IN (2052, 2053, 2054) AND visible = '1';

-- =====================================================
-- 验证修改结果
-- =====================================================

-- 查看证件管理下的所有菜单
SELECT 
    m.menu_id,
    m.menu_name,
    m.path,
    m.component,
    m.visible,
    m.order_num,
    p.menu_name as parent_name
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.parent_id = 2000 OR m.menu_id = 2000
ORDER BY m.parent_id, m.order_num;

-- 查看权限菜单的父级关系
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    p.menu_name as parent_name,
    m.perms
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.perms LIKE 'cert:%' OR m.perms LIKE 'samples:%'
ORDER BY m.parent_id, m.menu_id;

-- 执行完成提示
SELECT '菜单路径修复完成！' as message;
SELECT '请重新登录系统以刷新菜单权限' as notice;
