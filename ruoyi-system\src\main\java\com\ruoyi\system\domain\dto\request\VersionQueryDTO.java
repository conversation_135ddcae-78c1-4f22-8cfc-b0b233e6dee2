package com.ruoyi.system.domain.dto.request;

import lombok.Data;

/**
 * 版本查询请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class VersionQueryDTO {
    
    /** 页码 */
    private Integer pageNum = 1;
    
    /** 页大小 */
    private Integer pageSize = 10;
    
    /** 国家ID */
    private Long countryId;
    
    /** 证件类型 */
    private String certType;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 版本状态 */
    private String status;
    
    /** 是否需要人工确认 */
    private Boolean needManualCheck;
    
    /** 关键词搜索 */
    private String keyword;
    
    /** 版本代码 */
    private String versionCode;
    
    /** 创建者部门ID */
    private Long deptId;
    
    /** 任务ID */
    private String taskId;
}
