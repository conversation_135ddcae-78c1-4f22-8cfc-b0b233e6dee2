package com.ruoyi.system.controller;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.service.ICountryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import java.util.HashMap;

/**
 * 国家表Controller
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@RestController
@RequestMapping("/cert/country")
public class CountryController extends BaseController
{
    @Autowired
    private ICountryService countryService;

    /**
     * 查询国家表列表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:list')")
    @GetMapping("/list")
    public TableDataInfo list(Country country)
    {
        startPage();
        List<Country> list = countryService.selectCountryList(country);
        
        // 为每个国家设置国旗图标路径（如果尚未设置）
        list.forEach(c -> {
            if (c.getFlagIcon() == null || c.getFlagIcon().isEmpty()) {
                // 使用静态资源路径 - 使用大写的三位国家代码
                String countryCode = c.getCode() != null ? c.getCode().toUpperCase() : "UNKNOWN";
                String iconPath = "/static/flags/" + countryCode + ".png";
                c.setFlagIcon(iconPath);
//                logger.info("国家[{}]({})设置图标路径: {}", c.getName(), countryCode, iconPath);
            } else {
//                logger.info("国家[{}]({})已有图标路径: {}", c.getName(), c.getCode(), c.getFlagIcon());
            }
        });
        
        return getDataTable(list);
    }

    /**
     * 查询国家表列表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:list')")
    @GetMapping("/listAll")
    public TableDataInfo listAll(Country country, @RequestParam(required = false) String nameCn)
    {
        // 处理前端传递的nameCn参数
        if (nameCn != null && !nameCn.trim().isEmpty()) {
            country.setName(nameCn.trim());
        }

        List<Country> list = countryService.selectCountryList(country);

        // 为每个国家设置国旗图标路径（如果尚未设置）
        list.forEach(c -> {
            if (c.getFlagIcon() == null || c.getFlagIcon().isEmpty()) {
                // 使用静态资源路径 - 使用大写的三位国家代码
                String countryCode = c.getCode() != null ? c.getCode().toUpperCase() : "UNKNOWN";
                String iconPath = "/static/flags/" + countryCode + ".png";
                c.setFlagIcon(iconPath);
//                logger.info("国家[{}]({})设置图标路径: {}", c.getName(), countryCode, iconPath);
            } else {
//                logger.info("国家[{}]({})已有图标路径: {}", c.getName(), c.getCode(), c.getFlagIcon());
            }
        });

        return getDataTable(list);
    }

    /**
     * 搜索国家（支持中英文名称模糊查询）
     */
    @PreAuthorize("@ss.hasPermi('cert:country:list')")
    @GetMapping("/search")
    public TableDataInfo searchCountries(@RequestParam String keyword)
    {
        logger.info("搜索国家，关键词: {}", keyword);

        List<Country> list = countryService.searchCountries(keyword);

        // 为每个国家设置国旗图标路径（如果尚未设置）
        list.forEach(c -> {
            if (c.getFlagIcon() == null || c.getFlagIcon().isEmpty()) {
                // 使用静态资源路径 - 使用大写的三位国家代码
                String countryCode = c.getCode() != null ? c.getCode().toUpperCase() : "UNKNOWN";
                String iconPath = "/static/flags/" + countryCode + ".png";
                c.setFlagIcon(iconPath);
            }
        });

        logger.info("搜索到{}个国家", list.size());
        return getDataTable(list);
    }
    /**
     * 导出国家表列表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:export')")
    @Log(title = "国家表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Country country)
    {
        List<Country> list = countryService.selectCountryList(country);
        ExcelUtil<Country> util = new ExcelUtil<Country>(Country.class);
        util.exportExcel(response, list, "国家表数据");
    }

    /**
     * 获取国家表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cert:country:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(countryService.selectCountryById(id));
    }

    /**
     * 新增国家表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:add')")
    @Log(title = "国家表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Country country)
    {
        return toAjax(countryService.insertCountry(country));
    }

    /**
     * 修改国家表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:edit')")
    @Log(title = "国家表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Country country)
    {
        return toAjax(countryService.updateCountry(country));
    }

    /**
     * 删除国家表
     */
    @PreAuthorize("@ss.hasPermi('cert:country:remove')")
    @Log(title = "国家表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(countryService.deleteCountryByIds(ids));
    }

    /**
     * 根据首字母查询国家列表
     */
    @GetMapping("/listByLetter/{letter}")
    public AjaxResult listByLetter(@PathVariable("letter") String letter)
    {
        if (letter == null || letter.isEmpty() || letter.length() > 1) {
            return AjaxResult.error("首字母参数无效");
        }
        
        Country country = new Country();
        List<Country> list = countryService.selectCountryList(country);
        logger.info("根据首字母[{}]查询国家列表，共查询到{}个国家", letter, list.size());
        
        // 过滤出英文名称以指定字母开头的国家
        List<Country> filteredList = list.stream()
            .filter(c -> c.getNameEn() != null && !c.getNameEn().isEmpty() && 
                    c.getNameEn().toUpperCase().startsWith(letter.toUpperCase()))
            .collect(Collectors.toList());
        
        logger.info("过滤后符合首字母[{}]的国家数量: {}", letter, filteredList.size());
        
        // 为每个国家设置国旗图标路径（如果尚未设置）
        filteredList.forEach(c -> {
            if (c.getFlagIcon() == null || c.getFlagIcon().isEmpty()) {
                // 使用静态资源路径 - 使用大写的三位国家代码
                String countryCode = c.getCode() != null ? c.getCode().toUpperCase() : "UNKNOWN";
                String iconPath = "/static/flags/" + countryCode + ".png";
                c.setFlagIcon(iconPath);
                logger.info("首字母查询：国家[{}]({})设置图标路径: {}", c.getName(), countryCode, iconPath);
            } else {
                logger.info("首字母查询：国家[{}]({})已有图标路径: {}", c.getName(), c.getCode(), c.getFlagIcon());
            }
        });
        
        return AjaxResult.success(filteredList);
    }

    /**
     * 获取所有首字母及对应的国家数量
     */
    @GetMapping("/letters")
    public AjaxResult getLetters()
    {
        try {
            Map<String, Long> letterCounts = countryService.selectCountryLetterCounts();
            return AjaxResult.success(letterCounts);
        } catch (Exception e) {
            logger.error("获取字母统计失败", e);
            return AjaxResult.error("获取字母统计失败: " + e.getMessage());
        }
    }

    /**
     * 添加测试接口，用于检查静态资源路径
     */
    @GetMapping("/checkFlags")
    public AjaxResult checkFlags()
    {
        try {
            // 获取一个国家记录用于测试
            Country country = new Country();
            List<Country> list = countryService.selectCountryList(country);
            
            if (list.isEmpty()) {
                return AjaxResult.error("没有找到任何国家记录");
            }
            
            Country testCountry = list.get(0);
            String countryCode = testCountry.getCode() != null ? testCountry.getCode().toUpperCase() : "CHN";
            
            // 记录测试信息
            logger.info("测试国家图标路径 - 国家: {}, 代码: {}", testCountry.getName(), countryCode);
            
            // 构建返回信息
            Map<String, Object> result = new HashMap<>();
            result.put("country", testCountry);
            result.put("suggestedPath", "/static/flags/" + countryCode + ".png");
            result.put("frontendPath", "C:\\javaProject\\RuoYi-Vue\\RuoYi-Vue3\\public\\static\\flags\\" + countryCode + ".png");
            result.put("staticResourcesInfo", "请确认图片位于正确的静态资源目录中");
            
            return AjaxResult.success("请检查日志获取更多信息", result);
        } catch (Exception e) {
            logger.error("检查国家图标时发生错误", e);
            return AjaxResult.error("检查失败: " + e.getMessage());
        }
    }
}
