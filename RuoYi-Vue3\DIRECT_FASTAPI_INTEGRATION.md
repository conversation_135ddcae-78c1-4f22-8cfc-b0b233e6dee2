# 直接FastAPI集成说明

## 修改概述

本次修改将前端批量上传功能从"前端 → Spring Boot → FastAPI"的架构改为"前端 → FastAPI"的直接集成架构。

## 主要变更

### 1. 移除Spring Boot依赖
- 删除了对`@/api/cert/batchUpload.js`的依赖
- 移除了Spring Boot相关的API调用
- 不再需要先创建MySQL任务记录

### 2. 直接调用FastAPI接口
- 使用FastAPI的`/api/v1/batch/process-folders`异步处理接口
- 直接传递文件和文件夹结构给FastAPI
- 使用FastAPI自己的任务ID管理系统

### 3. 简化任务状态管理
- 使用FastAPI的任务状态查询接口：`/api/v1/batch/task-status/{task_id}`
- 实现了任务状态轮询机制
- 移除了复杂的手工处理对话框

### 4. 优化用户体验
- 按钮文本改为"上传到FastAPI"
- 提示信息明确说明使用FastAPI服务
- 简化了状态检查流程

## 技术实现

### 上传流程
1. 用户选择文件夹
2. 前端生成任务ID：`TASK_{timestamp}_{random}`
3. 构建FormData，包含：
   - `taskId`: 任务ID
   - `processMode`: 处理模式（async）
   - `createdBy`: 创建者（system）
   - `folderStructure`: 文件夹结构JSON
   - `files`: 所有文件
4. 直接POST到FastAPI的文件夹处理接口
5. 开始轮询任务状态

### 状态轮询
- 每5秒查询一次任务状态
- 10分钟后自动停止轮询
- 根据状态更新前端显示

### 任务状态映射
- `processing`: 处理中
- `completed`: 处理完成
- `failed`: 处理失败

## 优势

1. **架构简化**：减少了一层中间层，降低了系统复杂度
2. **性能提升**：直接调用FastAPI，减少了网络延迟
3. **维护简化**：不需要维护Spring Boot和FastAPI之间的集成逻辑
4. **功能完整**：FastAPI本身就具备完整的批量处理能力

## 注意事项

1. 确保FastAPI服务运行在`http://localhost:8000`
2. 需要激活conda环境：`conda activate fastapi-env`
3. 任务ID格式：`TASK_{timestamp}_{random}`
4. 轮询机制会在10分钟后自动停止

## 相关文件

- `src/views/cert/batch/BatchImagesUpload.vue` - 主要修改文件
- FastAPI接口：
  - `POST /api/v1/batch/process-folders` - 异步文件夹处理
  - `GET /api/v1/batch/task-status/{task_id}` - 状态查询 