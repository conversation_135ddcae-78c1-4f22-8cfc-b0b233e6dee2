<template>
  <el-dialog
    :model-value="visible"
    title="编辑图片信息"
    width="500px"
    :modal="true"
    :close-on-click-modal="false"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div v-loading="loading" class="image-edit-container">
      <!-- 图片预览 -->
      <el-collapse v-model="activeCollapse" class="image-preview-section">
        <el-collapse-item title="图片预览" name="preview">
          <div class="preview-container">
            <img
              v-if="imageInfo?.minioPath"
              :src="getImageUrl(imageInfo.minioPath)"
              :alt="imageInfo.originalFileName"
              class="preview-image"
              @error="handleImageError"
            />
            <div v-else class="no-image">
              <el-icon size="32"><Picture /></el-icon>
              <p>暂无图片</p>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 基本信息 -->
      <div class="basic-info-section">
        <div class="section-header">
          <h4>基本信息</h4>
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="文件名">
            {{ imageInfo?.originalFileName || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(imageInfo?.fileSize) }}
          </el-descriptions-item>
          <el-descriptions-item label="图片尺寸">
            {{ imageInfo?.imageWidth }}×{{ imageInfo?.imageHeight }}
          </el-descriptions-item>
          <el-descriptions-item label="内容类型">
            {{ imageInfo?.contentType || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="上传时间">
            {{ formatDate(imageInfo?.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(imageInfo?.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 图片特征编辑 -->
      <div class="features-edit-section">
        <div class="section-header">
          <h4>图片特征</h4>
          <el-text type="info" size="small">
            修改图片类型将影响是否可以进行标注
          </el-text>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="图片类型" prop="imageType">
            <el-select
              v-model="formData.imageType"
              placeholder="请选择图片类型"
              style="width: 100%"
              @change="handleImageTypeChange"
            >
              <el-option
                v-for="type in imageTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
                <div class="type-option">
                  <span class="type-label">{{ type.label }}</span>
                  <el-tag
                    :type="type.annotatable ? 'success' : 'info'"
                    size="small"
                    effect="plain"
                  >
                    {{ type.annotatable ? '可标注' : '仅查看' }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="光照类型" prop="lightType">
            <el-select
              v-model="formData.lightType"
              placeholder="请选择光照类型"
              style="width: 100%"
              clearable
            >
              <el-option label="常光" value="visible" />
              <el-option label="红外" value="infrared" />
              <el-option label="紫外" value="ultraviolet" />
            </el-select>
          </el-form-item>

          <el-form-item label="是否主图" prop="isMainImage">
            <el-switch
              v-model="formData.isMainImage"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>

          <el-form-item label="标签" prop="tags">
            <el-select
              v-model="formData.tags"
              multiple
              filterable
              allow-create
              placeholder="请输入或选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in commonTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 可标注性提示 -->
      <div class="annotation-info">
        <el-alert
          :type="isAnnotatableType ? 'success' : 'info'"
          :title="annotationMessage"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          :disabled="!hasChanges"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { updateImageInfo } from '@/api/cert/image'

// Props
interface ImageInfo {
  imageId: string
  originalFileName?: string
  minioPath?: string
  fileSize?: number
  imageWidth?: number
  imageHeight?: number
  contentType?: string
  imageType?: string
  lightType?: string
  isMainImage?: boolean
  tags?: string[]
  createTime?: string
  updateTime?: string
}

interface Props {
  /** 对话框显示状态 */
  visible: boolean
  /** 图片信息 */
  imageInfo?: ImageInfo
  /** 是否可编辑 */
  canEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  canEdit: true
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
  saved: [imageInfo: ImageInfo]
}>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const formRef = ref()
const activeCollapse = ref([])

// 表单数据
const formData = ref({
  imageType: '',
  lightType: '',
  isMainImage: false,
  tags: [] as string[]
})

// 原始数据（用于比较变化）
const originalData = ref({})

// 图片类型选项
const imageTypeOptions = [
  {
    value: 'VISIBLE_DATA_PAGE',
    label: '可见光资料页',
    annotatable: true
  },
  {
    value: 'INFRARED_DATA_PAGE',
    label: '红外资料页',
    annotatable: true
  },
  {
    value: 'ULTRAVIOLET_DATA_PAGE',
    label: '紫外资料页',
    annotatable: true
  },
  {
    value: 'OTHER',
    label: '其他类型',
    annotatable: false
  }
]

// 常用标签
const commonTags = [
  '数据页', '封面', '背面', '安全特征', '水印', '防伪元素'
]

// 表单验证规则
const formRules = {
  imageType: [
    { required: true, message: '请选择图片类型', trigger: 'change' }
  ]
}

// 计算属性
const isAnnotatableType = computed(() => {
  return ['VISIBLE_DATA_PAGE', 'INFRARED_DATA_PAGE', 'ULTRAVIOLET_DATA_PAGE']
    .includes(formData.value.imageType)
})

const annotationMessage = computed(() => {
  return isAnnotatableType.value
    ? '此类型图片支持详细标注功能'
    : '此类型图片仅支持查看，不可标注'
})

const hasChanges = computed(() => {
  return JSON.stringify(formData.value) !== JSON.stringify(originalData.value)
})

// 方法
const getImageUrl = (minioPath: string) => {
  // 根据实际MinIO配置构建图片URL
  return `/api/minio/preview/${minioPath}`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/no-image.png' // 默认占位图
}

const formatFileSize = (size?: number) => {
  if (!size) return '未知'
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString()
}

const handleImageTypeChange = (value: string) => {
  // 图片类型变化时的处理逻辑
  console.log('图片类型变更为:', value)
}

const initFormData = () => {
  if (props.imageInfo) {
    formData.value = {
      imageType: props.imageInfo.imageType || 'OTHER',
      lightType: props.imageInfo.lightType || '',
      isMainImage: props.imageInfo.isMainImage || false,
      tags: props.imageInfo.tags || []
    }
    originalData.value = { ...formData.value }
  }
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (!hasChanges.value) {
      ElMessage.info('没有修改内容')
      return
    }

    await ElMessageBox.confirm(
      '确定要保存对图片信息的修改吗？',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    saving.value = true

    // 调用API保存修改
    await updateImageInfo(props.imageInfo!.imageId, formData.value)

    ElMessage.success('保存成功')
    emit('saved', { ...props.imageInfo!, ...formData.value })
    handleClose()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initFormData()
    })
  }
})

watch(() => props.imageInfo, () => {
  if (props.visible) {
    initFormData()
  }
}, { deep: true })
</script>

<style scoped lang="scss">
.image-edit-container {
  .image-preview-section {
    margin-bottom: 16px;

    :deep(.el-collapse-item__header) {
      font-size: 14px;
      font-weight: 500;
      padding-left: 0;
    }

    :deep(.el-collapse-item__content) {
      padding-bottom: 10px;
    }

    .preview-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      background-color: #fafafa;

      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      .no-image {
        text-align: center;
        color: #909399;

        p {
          margin: 8px 0 0 0;
        }
      }
    }
  }

  .basic-info-section,
  .features-edit-section {
    margin-bottom: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h4 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .type-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .type-label {
      flex: 1;
    }
  }

  .annotation-info {
    margin-top: 15px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
