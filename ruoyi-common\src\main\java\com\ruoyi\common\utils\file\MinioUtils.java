package com.ruoyi.common.utils.file;

import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.utils.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MinIO 工具类
 * 
 * <AUTHOR>
 */
public class MinioUtils {
    
    private static final Logger log = LoggerFactory.getLogger(MinioUtils.class);
    
    private static MinioClient minioClient;
    
    /**
     * 默认存储桶名称
     */
    private static final String DEFAULT_BUCKET_NAME = "xjlfiles";
    
    /**
     * 初始化 MinIO 客户端
     *
     * @param endpoint   端点
     * @param accessKey  访问密钥
     * @param secretKey  秘密密钥
     */
    public static void init(String endpoint, String accessKey, String secretKey) {
        try {
            minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
            log.info("MinIO client initialized successfully");
        } catch (Exception e) {
            log.error("MinIO client initialization failed: {}", e.getMessage());
            throw new RuntimeException("MinIO client initialization failed", e);
        }
    }
    
    /**
     * 上传文件
     *
     * @param bucketName     存储桶名称
     * @param fileName       文件名
     * @param file           文件
     * @return               文件访问路径
     * @throws Exception     上传异常
     */
    public static String uploadFile(String bucketName, String fileName, MultipartFile file) throws Exception {
        // 检查存储桶是否存在
        createBucketIfNotExists(bucketName);
        
        // 生成唯一文件名
        String objectName = generateObjectName(fileName);
        
        // 获取文件输入流
        InputStream inputStream = file.getInputStream();
        
        // 获取文件类型
        String contentType = file.getContentType();
        if (contentType == null) {
            contentType = getContentType(fileName);
        }
        
        // 上传文件
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .contentType(contentType)
                        .stream(inputStream, file.getSize(), -1)
                        .build()
        );
        
        // 关闭输入流
        inputStream.close();
        
        // 返回文件访问路径
        return getFileUrl(bucketName, objectName);
    }
    
    /**
     * 上传文件到指定目录
     *
     * @param bucketName     存储桶名称
     * @param directory      目录
     * @param file           文件
     * @return               文件访问路径
     * @throws Exception     上传异常
     */
    public static String uploadFileToDirectory(String bucketName, String directory, MultipartFile file) throws Exception {
        // 生成文件名
        String fileName = generateUniqueFileName(file.getOriginalFilename());
        
        // 构建对象名称（包含目录）
        String objectName = directory.endsWith("/") ? 
                directory + fileName : 
                directory + "/" + fileName;
        
        // 上传文件
        return uploadFile(bucketName, objectName, file);
    }
    
    /**
     * 获取文件访问URL
     *
     * @param bucketName     存储桶名称
     * @param objectName     对象名称
     * @return               文件访问URL
     * @throws Exception     获取异常
     */
    public static String getFileUrl(String bucketName, String objectName) throws Exception {
        return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .method(Method.GET)
                        .expiry(7, TimeUnit.DAYS)
                        .build()
        );
    }
    
    /**
     * 删除文件
     *
     * @param bucketName     存储桶名称
     * @param objectName     对象名称
     * @throws Exception     删除异常
     */
    public static void deleteFile(String bucketName, String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build()
        );
    }
    
    /**
     * 批量删除文件
     *
     * @param bucketName     存储桶名称
     * @param objectNames    对象名称列表
     * @return               删除错误列表
     * @throws Exception     删除异常
     */
    public static List<DeleteError> deleteFiles(String bucketName, List<String> objectNames) throws Exception {
        List<DeleteObject> objects = new ArrayList<>(objectNames.size());
        for (String objectName : objectNames) {
            objects.add(new DeleteObject(objectName));
        }
        
        Iterable<Result<DeleteError>> results = minioClient.removeObjects(
                RemoveObjectsArgs.builder()
                        .bucket(bucketName)
                        .objects(objects)
                        .build()
        );
        
        List<DeleteError> errors = new ArrayList<>();
        for (Result<DeleteError> result : results) {
            errors.add(result.get());
        }
        
        return errors;
    }
    
    /**
     * 检查存储桶是否存在，不存在则创建
     *
     * @param bucketName     存储桶名称
     * @throws Exception     创建异常
     */
    public static void createBucketIfNotExists(String bucketName) throws Exception {
        boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder()
                        .bucket(bucketName)
                        .build()
        );
        
        if (!exists) {
            minioClient.makeBucket(
                    MakeBucketArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
        }
    }
    
    /**
     * 生成对象名称
     *
     * @param fileName       文件名
     * @return               对象名称
     */
    public static String generateObjectName(String fileName) {
        // 如果文件名已经包含路径，则直接返回
        if (fileName.contains("/")) {
            return fileName;
        }
        
        // 生成唯一文件名
        return generateUniqueFileName(fileName);
    }
    
    /**
     * 从 MinIO URL 中提取对象名称
     *
     * @param url MinIO URL
     * @return 对象名称
     */
    public static String extractObjectName(String url) {
        try {
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();
            // 移除开头的斜杠和桶名
            String[] parts = path.split("/", 3);
            if (parts.length >= 3) {
                return parts[2];
            }
            return path.startsWith("/") ? path.substring(1) : path;
        } catch (Exception e) {
            return url;
        }
    }
    
    /**
     * 构建 MinIO URL
     *
     * @param endpoint 端点
     * @param bucketName 桶名
     * @param objectName 对象名
     * @return MinIO URL
     */
    public static String buildMinioUrl(String endpoint, String bucketName, String objectName) {
        if (endpoint.endsWith("/")) {
            endpoint = endpoint.substring(0, endpoint.length() - 1);
        }
        return endpoint + "/" + bucketName + "/" + objectName;
    }
    
    /**
     * 生成唯一文件名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    public static String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }
    
    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }

        String extension = filename.substring(filename.lastIndexOf("."));

        // 防止重复扩展名：检查是否有类似 .jpg.JPG 的情况
        String lowerExtension = extension.toLowerCase();
        String lowerFilename = filename.toLowerCase();

        // 如果文件名包含重复的扩展名（如 .jpg.jpg 或 .jpg.JPG），只保留最后一个
        if (lowerFilename.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp|tiff)\\.(jpg|jpeg|png|gif|bmp|webp|tiff)$")) {
            // 获取真正的扩展名（最后一个）
            return extension;
        }

        return extension;
    }
    
    /**
     * 获取文件内容类型
     *
     * @param filename 文件名
     * @return 内容类型
     */
    public static String getContentType(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        switch (extension) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".pdf":
                return "application/pdf";
            case ".doc":
                return "application/msword";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".xls":
                return "application/vnd.ms-excel";
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * MultipartFile 转 File
     *
     * @param file MultipartFile
     * @return File
     */
    public static File multipartFileToFile(MultipartFile file) throws IOException {
        File convertedFile = new File(System.getProperty("java.io.tmpdir") + "/" + file.getOriginalFilename());
        try (FileOutputStream fos = new FileOutputStream(convertedFile)) {
            fos.write(file.getBytes());
        }
        return convertedFile;
    }
    
    /**
     * 删除临时文件
     *
     * @param file 文件
     */
    public static void deleteTempFile(File file) {
        if (file != null && file.exists()) {
            file.delete();
        }
    }
    
    /**
     * 获取默认存储桶名称
     *
     * @return 默认存储桶名称
     */
    public static String getBucketName() {
        // 从配置中获取默认存储桶名称
        String configBucketName = com.ruoyi.common.config.MinioConfig.getStaticBucketName();
        return configBucketName != null ? configBucketName : DEFAULT_BUCKET_NAME;
    }
    
    /**
     * 上传大文件
     * 
     * @param file 文件
     * @param versionId 版本ID
     * @param fileType 文件类型
     * @return 文件URL
     * @throws Exception 上传异常
     */
    public static String uploadLargeFile(MultipartFile file, String versionId, String fileType) throws Exception {
        log.info("开始上传大文件到MinIO: {}, 版本ID: {}, 文件类型: {}", file.getOriginalFilename(), versionId, fileType);
        
        try {
            // 检查MinIO客户端是否已初始化
            if (minioClient == null) {
                log.info("MinIO客户端未初始化，尝试初始化...");
                // 从配置中获取MinIO配置
                String endpoint = com.ruoyi.common.config.MinioConfig.getStaticUrl();
                String accessKey = com.ruoyi.common.config.MinioConfig.getStaticAccessKey();
                String secretKey = com.ruoyi.common.config.MinioConfig.getStaticSecretKey();
                
                // 初始化MinIO客户端
                init(endpoint, accessKey, secretKey);
                log.info("MinIO客户端初始化成功");
            }
            
            // 构建MinIO存储路径
            String bucketName = getBucketName();
            String directory = "cert/sample";
            
            if (StringUtils.isNotEmpty(versionId)) {
                directory += "/" + versionId;
            }
            
            if (StringUtils.isNotEmpty(fileType)) {
                directory += "/" + fileType;
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID().toString() + extension;
            
            // 完整的存储路径
            String objectName = directory + "/" + fileName;
            
            log.info("MinIO存储路径: {}, 文件名: {}", objectName, fileName);
            
            // 上传到MinIO
            String url = uploadFile(bucketName, objectName, file);
            log.info("文件上传成功，URL: {}", url);
            
            return url;
        } catch (Exception e) {
            log.error("上传文件到MinIO失败: {}", e.getMessage(), e);
            throw e;
        }
    }
} 