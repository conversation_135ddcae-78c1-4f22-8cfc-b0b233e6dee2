package com.ruoyi.framework.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * HTTP客户端配置
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Configuration
public class HttpClientConfig {
    
    @Value("${fastapi.service.timeout:5000}")
    private int fastApiTimeout;
    
    /**
     * 创建RestTemplate Bean
     * 用于调用外部HTTP服务，如FastAPI
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 配置请求工厂
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(fastApiTimeout); // 连接超时
        factory.setReadTimeout(fastApiTimeout);    // 读取超时
        
        restTemplate.setRequestFactory(factory);
        
        return restTemplate;
    }
} 