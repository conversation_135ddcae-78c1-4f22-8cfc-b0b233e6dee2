# Spring Data MongoDB Repository 接口说明

本目录包含了证件样本库后端服务的MongoDB数据访问层接口。

## Repository 接口列表

### 1. CertVersionRepository.java
**继承**: `MongoRepository<CertVersion, String>`  
**用途**: 证件版本数据访问层

**主要方法**:
- `findByVersionId(String versionId)` - 根据版本ID查询
- `findByVersionCode(String versionCode)` - 根据版本代码查询
- `findByCountryId(Long countryId)` - 根据国家ID查询
- `findByCertTypeId(Long certTypeId)` - 根据证件类型ID查询
- `findByIssueYear(String issueYear)` - 根据发行年份查询
- `findByStatus(String status)` - 根据状态查询
- `findByDeptId(Long deptId)` - 根据部门ID查询
- `findByCreatorId(Long userId)` - 根据创建者ID查询
- `findByCountryIdAndCertTypeId(Long countryId, Long certTypeId)` - 组合查询
- `findByCountryIdAndCertTypeIdAndIssueYear(Long countryId, Long certTypeId, String issueYear)` - 复合查询

### 2. FolderInfoRepository.java
**继承**: `MongoRepository<FolderInfo, String>`  
**用途**: 文件夹信息数据访问层

**主要方法**:
- `findByFolderId(String folderId)` - 根据文件夹ID查询
- `findByTaskId(String taskId)` - 根据任务ID查询
- `findByVersionId(String versionId)` - 根据版本ID查询
- `findByFolderType(String folderType)` - 根据文件夹类型查询
- `findByCountryId(Long countryId)` - 根据国家ID查询
- `findByCertTypeId(Long certTypeId)` - 根据证件类型ID查询
- `findByIssueYear(String issueYear)` - 根据发行年份查询
- `findByStatus(String status)` - 根据状态查询
- `findByDeptId(Long deptId)` - 根据部门ID查询
- `findByUploaderId(Long userId)` - 根据上传者ID查询
- `findByTaskIdAndFolderName(String taskId, String folderName)` - 组合查询
- `findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime)` - 时间范围查询
- `findByCountryIdAndCertTypeIdAndIssueYear(Long countryId, Long certTypeId, String issueYear)` - 复合查询
- `deleteByTaskId(String taskId)` - 删除指定任务的所有文件夹

### 3. ImageRepositoryRepo.java
**继承**: `MongoRepository<ImageRepository, String>`  
**用途**: 图片仓库数据访问层

**核心方法** (按您的要求):
- `List<ImageRepository> findByFolderId(String folderId)` - 根据文件夹ID查找图片列表
- `List<ImageRepository> findByVersionId(String versionId)` - 根据版本ID查找图片列表
- `long countByFolderId(String folderId)` - 根据文件夹ID统计图片数量

**其他方法**:
- `findByImageId(String imageId)` - 根据图片ID查询
- `findByTaskId(String taskId)` - 根据任务ID查询
- `findByLightType(String lightType)` - 根据光照类型查询
- `findByIsMainImage(boolean isMainImage)` - 根据是否为主图查询
- `findByProcessStatus(String processStatus)` - 根据处理状态查询
- `findByDeptId(Long deptId)` - 根据部门ID查询
- `findByFolderIdAndLightType(String folderId, String lightType)` - 组合查询
- `findByFolderIdAndIsMainImage(String folderId, boolean isMainImage)` - 组合查询
- `findByVersionIdAndLightType(String versionId, String lightType)` - 组合查询
- `findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime)` - 时间范围查询
- `findMainImagesByFolderId(String folderId)` - 查找文件夹的主图
- `countProcessedImagesByFolderId(String folderId)` - 统计已处理图片数量
- `countByVersionId(String versionId)` - 统计版本的图片数量
- `deleteByFolderId(String folderId)` - 删除文件夹的所有图片
- `deleteByTaskId(String taskId)` - 删除任务的所有图片

### 4. BatchUploadTaskRepository.java
**继承**: `MongoRepository<BatchUploadTask, String>`  
**用途**: 批量上传任务数据访问层  
**说明**: 此接口保持现有实现，未做修改

## 设计特点

### 1. 统一的命名规范
- 所有Repository接口都继承自`MongoRepository<Entity, String>`
- 使用Spring Data MongoDB的方法命名约定
- 支持自动查询方法生成

### 2. 查询方法分类
- **单字段查询**: `findByFieldName`
- **组合查询**: `findByField1AndField2`
- **范围查询**: `findByFieldBetween`
- **统计查询**: `countByField`
- **删除操作**: `deleteByField`

### 3. 复杂查询支持
- 使用`@Query`注解支持MongoDB原生查询语法
- 支持嵌套对象字段查询，如`{'countryInfo.id': ?0}`
- 支持正则表达式查询和模糊匹配

### 4. 数据类型适配
- 主键统一使用`String`类型（MongoDB的ObjectId）
- 时间字段使用`LocalDateTime`类型
- 外键关联使用`Long`类型（对应MySQL的ID）

## 使用示例

```java
@Autowired
private CertVersionRepository certVersionRepository;

@Autowired
private FolderInfoRepository folderInfoRepository;

@Autowired
private ImageRepositoryRepo imageRepositoryRepo;

// 查询证件版本
CertVersion version = certVersionRepository.findByVersionCode("RUS_VISA_2023_V1");

// 查询文件夹下的图片
List<ImageRepository> images = imageRepositoryRepo.findByFolderId("folder123");

// 统计图片数量
long imageCount = imageRepositoryRepo.countByFolderId("folder123");

// 组合查询
List<CertVersion> versions = certVersionRepository.findByCountryIdAndCertTypeId(1L, 2L);
```

## 注意事项

1. **实体类名称冲突**: `ImageRepositoryRepo`接口名称避免与`ImageRepository`实体类冲突
2. **字段映射**: 查询嵌套MySQL实体时使用点号语法，如`countryInfo.id`
3. **类型转换**: MongoDB的String主键与MySQL的Long主键需要注意类型匹配
4. **性能优化**: 复杂查询建议使用`@Query`注解优化查询性能
5. **事务支持**: MongoDB 4.0+支持事务，可在Service层使用`@Transactional`
