package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.Country;

/**
 * 国家表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ICountryService 
{
    /**
     * 查询国家表
     * 
     * @param id 国家表主键
     * @return 国家表
     */
    public Country selectCountryById(Long id);

    /**
     * 查询国家表列表
     * 
     * @param country 国家表
     * @return 国家表集合
     */
    public List<Country> selectCountryList(Country country);

    /**
     * 新增国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    public int insertCountry(Country country);

    /**
     * 修改国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    public int updateCountry(Country country);

    /**
     * 批量删除国家表
     * 
     * @param ids 需要删除的国家表主键集合
     * @return 结果
     */
    public int deleteCountryByIds(Long[] ids);

    /**
     * 删除国家表信息
     * 
     * @param id 国家表主键
     * @return 结果
     */
    public int deleteCountryById(Long id);
    
    /**
     * 根据首字母查询国家列表
     * 
     * @param letter 首字母
     * @return 国家列表
     */
    public List<Country> selectCountryByFirstLetter(String letter);
    
    /**
     * 获取所有首字母及对应的国家数量
     * 
     * @return 首字母及数量映射
     */
    public Map<String, Long> selectCountryLetterCounts();

    /**
     * 根据国家代码查询国家信息
     * 
     * @param code 国家代码
     * @return 国家信息
     */
    public Country selectCountryByCode(String code);

    /**
     * 批量查询国家信息
     * 
     * @param codes 国家代码列表
     * @return 国家列表
     */
    public List<Country> selectCountryByCodes(List<String> codes);

    /**
     * 搜索国家（支持中英文名称模糊查询）
     * 
     * @param keyword 搜索关键词
     * @return 国家列表
     */
    public List<Country> searchCountries(String keyword);

    /**
     * 批量同步国旗图标路径
     * 
     * @return 更新数量
     */
    public int syncAllFlagIcons();

    /**
     * 验证国家代码是否唯一
     * 
     * @param code 国家代码
     * @param excludeId 排除的ID（用于编辑时验证）
     * @return 是否唯一
     */
    public boolean checkCodeUnique(String code, Long excludeId);
}
