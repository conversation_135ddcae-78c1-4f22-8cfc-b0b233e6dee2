package com.ruoyi.system.domain.dto.response;

import lombok.Data;

/**
 * 标准样本信息响应VO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class StandardSampleInfoVO {
    
    /** 文件夹ID */
    private String folderId;
    
    /** 文件夹名称 */
    private String folderName;
    
    /** 版本ID */
    private String versionId;
    
    /** 是否为标准样本 */
    private Boolean isStandardSample;
    
    /** 是否可设置为标准样本 */
    private Boolean canSetAsStandard;
    
    /** 是否可取消标准样本 */
    private Boolean canRemoveStandard;
    
    /** 状态说明 */
    private String reason;
    
    /** 模板数量 */
    private Integer templateCount;
    
    /** 可标注图片数量 */
    private Integer annotatableImageCount;
    
    /** 总图片数量 */
    private Integer totalImageCount;
}
