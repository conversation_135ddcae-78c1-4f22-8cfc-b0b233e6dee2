FROM mongo:latest

# 创建必要的目录
RUN mkdir -p /docker-entrypoint-initdb.d

# 复制副本集初始化脚本
COPY mongo-init-replica.js /docker-entrypoint-initdb.d/
COPY mongo-keyfile /etc/mongo-keyfile

# 设置权限
RUN chmod 400 /etc/mongo-keyfile && chown mongodb:mongodb /etc/mongo-keyfile
RUN chmod +x /docker-entrypoint-initdb.d/mongo-init-replica.js

# 暴露端口
EXPOSE 27017

# 启动命令：以副本集模式启动，支持 retryable writes
CMD ["mongod", "--replSet", "rs0", "--auth", "--keyFile", "/etc/mongo-keyfile", "--bind_ip_all"]
