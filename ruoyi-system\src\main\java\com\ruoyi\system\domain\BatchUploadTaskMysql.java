package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 批量上传任务MySQL实体
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class BatchUploadTaskMysql extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 任务ID */
    @Excel(name = "任务ID")
    private String taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String description;

    /** 总文件夹数 */
    @Excel(name = "总文件夹数")
    private Integer totalFolders;

    /** 总文件数 */
    @Excel(name = "总文件数")
    private Integer totalFiles;

    /** 任务状态 */
    @Excel(name = "任务状态")
    private String status;

    /** FastAPI任务ID */
    @Excel(name = "FastAPI任务ID")
    private String fastApiTaskId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 提交时间 */
    private Date submitTime;

    /** 完成时间 */
    private Date completeTime;

    /** 错误信息 */
    private String errorMessage;

    public BatchUploadTaskMysql() {}

    public BatchUploadTaskMysql(String taskId, String taskName, String description, 
                               Integer totalFolders, Integer totalFiles, String createdBy) {
        this.taskId = taskId;
        this.taskName = taskName;
        this.description = description;
        this.totalFolders = totalFolders;
        this.totalFiles = totalFiles;
        this.createdBy = createdBy;
        this.status = "created";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getTotalFolders() {
        return totalFolders;
    }

    public void setTotalFolders(Integer totalFolders) {
        this.totalFolders = totalFolders;
    }

    public Integer getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(Integer totalFiles) {
        this.totalFiles = totalFiles;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFastApiTaskId() {
        return fastApiTaskId;
    }

    public void setFastApiTaskId(String fastApiTaskId) {
        this.fastApiTaskId = fastApiTaskId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "BatchUploadTaskMysql{" +
                "id=" + id +
                ", taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", description='" + description + '\'' +
                ", totalFolders=" + totalFolders +
                ", totalFiles=" + totalFiles +
                ", status='" + status + '\'' +
                ", fastApiTaskId='" + fastApiTaskId + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", submitTime=" + submitTime +
                ", completeTime=" + completeTime +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
} 