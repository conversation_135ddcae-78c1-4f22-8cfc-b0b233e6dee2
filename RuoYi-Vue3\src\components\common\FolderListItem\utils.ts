/**
 * 业务逻辑工具函数
 * 包含所有与FolderListItem相关的业务逻辑，但不属于组件本身
 * 这些函数将在后续移动到对应的业务视图层
 */

// ==================== 业务数据类型定义 ====================

/**
 * 完整的MongoDB文件夹数据结构
 */
export interface FolderData {
  /** MongoDB主键 */
  _id: {
    $oid: string
  }
  /** 业务文件夹ID */
  folderId: string
  /** 文件信息ID */
  fileInfoId: string
  /** 任务ID */
  taskId: string
  /** 版本ID */
  versionId?: string
  /** 文件夹类型 */
  folderType: 'standard' | 'regular'
  /** 文件夹名称 */
  folderName: string
  /** 预解析版本信息 */
  preParseVersionInfo?: {
    originalFolderName: string
    parsedVersionCode: string
    countryName: string
    countryCode: string
    certTypeName: string
    certTypeCode: string
    issueYear: string
    certNumberPrefix: string
    issuePlace: string
    parseStatus: 'SUCCESS' | 'FAILED' | 'PARSING'
    parseErrors: string[]
    parseTime: {
      $date: string
    }
  }
  /** 文件夹路径 */
  folderPath: string
  /** 已上传文件数量 */
  uploadedFileCount: number
  /** 总文件数量 */
  totalFileCount: number
  /** 上传进度 */
  uploadProgress: number
  /** 是否多版本 */
  isMultiVersion: boolean
  /** 国家信息 */
  countryInfo: {
    _id: {
      $numberLong: string
    }
    name: string
    nameEn: string
    code: string
    createTime: {
      $date: string
    }
    updateTime: {
      $date: string
    }
  }
  /** 证件类型信息 */
  certInfo: {
    _id: {
      $numberLong: string
    }
    zjlbdm: string
    zjlbmc: string
    sybj: string
    zjjc: string
    gjsy0: string
    bz: string
    createTime: {
      $date: string
    }
    updateTime: {
      $date: string
    }
  }
  /** 发行年份 */
  issueYear: string
  /** 文件数量 */
  fileCount: number
  /** 已处理文件数量 */
  processedFileCount: number
  /** 状态 */
  status: 'associated' | 'unassociated' | 'processing'
  /** 创建时间 */
  createTime: {
    $date: string
  }
  /** 更新时间 */
  updateTime: {
    $date: string
  }
  /** 审核状态 */
  reviewStatus: 'pending' | 'approved' | 'rejected'
  /** 版本关联方式 */
  versionAssociationMethod: 'manual_select' | 'auto_parse'
  /** 主图路径 */
  mainPicPath?: string
}

/**
 * 简化后的文件夹信息结构
 */
export interface SimpleFolderInfo {
  folderId: string
  folderName: string
  thumbnailUrl?: string
  status: string
  folderType: string
  countryInfo?: {
    name: string
    code: string
  }
  certInfo?: {
    zjlbmc: string
    zjlbdm: string
  }
  issueYear?: string
  fileCount: number
  processedFileCount: number
  createTime: string
  reviewStatus: string
  isStandardSample: boolean
}

/**
 * 文件夹列表配置
 */
export interface FolderListConfig {
  showCheckbox: boolean
  showCompareButton: boolean
  showStandardBadge: boolean
  allowSelection: boolean
  allowCompare: boolean
  maxSelectionCount?: number
}

/**
 * 文件夹选择事件
 */
export interface FolderSelectionEvent {
  folderId: string
  selected: boolean
  allSelected: string[]
}

/**
 * 文件夹操作事件
 */
export interface FolderActionEvent {
  action: 'compare' | 'view' | 'edit' | 'delete'
  folderId: string
  folderData: SimpleFolderInfo
}

// ==================== 数据转换函数 ====================

/**
 * 将MongoDB文件夹数据转换为简化的展示结构
 */
export function transformFolderData(folderData: FolderData): SimpleFolderInfo {
  return {
    folderId: folderData.folderId,
    folderName: folderData.folderName,
    thumbnailUrl: folderData.mainPicPath,
    status: folderData.status,
    folderType: folderData.folderType,
    countryInfo: folderData.countryInfo ? {
      name: folderData.countryInfo.name,
      code: folderData.countryInfo.code
    } : undefined,
    certInfo: folderData.certInfo ? {
      zjlbmc: folderData.certInfo.zjlbmc,
      zjlbdm: folderData.certInfo.zjlbdm
    } : undefined,
    issueYear: folderData.issueYear,
    fileCount: folderData.fileCount,
    processedFileCount: folderData.processedFileCount,
    createTime: folderData.createTime.$date,
    reviewStatus: folderData.reviewStatus,
    isStandardSample: folderData.folderType === 'standard'
  }
}

/**
 * 批量转换文件夹数据
 */
export function transformFolderDataList(folderDataList: FolderData[]): SimpleFolderInfo[] {
  return folderDataList.map(transformFolderData)
}

// ==================== 筛选函数 ====================

/**
 * 按状态筛选文件夹
 */
export function filterByStatus(folders: SimpleFolderInfo[], status: string): SimpleFolderInfo[] {
  if (!status) return folders
  return folders.filter(folder => folder.status === status)
}

/**
 * 按文件夹类型筛选
 */
export function filterByFolderType(folders: SimpleFolderInfo[], folderType: string): SimpleFolderInfo[] {
  if (!folderType) return folders
  return folders.filter(folder => folder.folderType === folderType)
}

/**
 * 按国家筛选文件夹
 */
export function filterByCountry(folders: SimpleFolderInfo[], countryCode: string): SimpleFolderInfo[] {
  if (!countryCode) return folders
  return folders.filter(folder => folder.countryInfo?.code === countryCode)
}

/**
 * 按证件类型筛选文件夹
 */
export function filterByCertType(folders: SimpleFolderInfo[], certType: string): SimpleFolderInfo[] {
  if (!certType) return folders
  return folders.filter(folder => folder.certInfo?.zjlbdm === certType)
}

/**
 * 按年份筛选文件夹
 */
export function filterByYear(folders: SimpleFolderInfo[], year: string): SimpleFolderInfo[] {
  if (!year) return folders
  return folders.filter(folder => folder.issueYear === year)
}

/**
 * 按关键词搜索文件夹
 */
export function filterByKeyword(folders: SimpleFolderInfo[], keyword: string): SimpleFolderInfo[] {
  if (!keyword) return folders
  const lowerKeyword = keyword.toLowerCase()
  return folders.filter(folder => 
    folder.folderName.toLowerCase().includes(lowerKeyword) ||
    folder.countryInfo?.name.toLowerCase().includes(lowerKeyword) ||
    folder.certInfo?.zjlbmc.toLowerCase().includes(lowerKeyword) ||
    folder.issueYear?.includes(lowerKeyword)
  )
}

/**
 * 筛选标准样本文件夹
 */
export function filterStandardSamples(folders: SimpleFolderInfo[]): SimpleFolderInfo[] {
  return folders.filter(folder => folder.isStandardSample)
}

/**
 * 筛选普通样本文件夹
 */
export function filterRegularSamples(folders: SimpleFolderInfo[]): SimpleFolderInfo[] {
  return folders.filter(folder => !folder.isStandardSample)
}

// ==================== 排序函数 ====================

/**
 * 按文件夹名称排序
 */
export function sortByName(folders: SimpleFolderInfo[], ascending: boolean = true): SimpleFolderInfo[] {
  return [...folders].sort((a, b) => {
    const comparison = a.folderName.localeCompare(b.folderName)
    return ascending ? comparison : -comparison
  })
}

/**
 * 按创建时间排序
 */
export function sortByCreateTime(folders: SimpleFolderInfo[], ascending: boolean = true): SimpleFolderInfo[] {
  return [...folders].sort((a, b) => {
    const timeA = new Date(a.createTime).getTime()
    const timeB = new Date(b.createTime).getTime()
    return ascending ? timeA - timeB : timeB - timeA
  })
}

/**
 * 按文件数量排序
 */
export function sortByFileCount(folders: SimpleFolderInfo[], ascending: boolean = true): SimpleFolderInfo[] {
  return [...folders].sort((a, b) => {
    return ascending ? a.fileCount - b.fileCount : b.fileCount - a.fileCount
  })
}

/**
 * 按处理进度排序
 */
export function sortByProgress(folders: SimpleFolderInfo[], ascending: boolean = true): SimpleFolderInfo[] {
  return [...folders].sort((a, b) => {
    const progressA = a.fileCount > 0 ? a.processedFileCount / a.fileCount : 0
    const progressB = b.fileCount > 0 ? b.processedFileCount / b.fileCount : 0
    return ascending ? progressA - progressB : progressB - progressA
  })
}

// ==================== 状态和类型获取函数 ====================

/**
 * 获取状态显示文本
 */
export function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'unassociated': '未关联',
    'associated': '已关联',
    'processing': '处理中',
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 获取状态标签类型
 */
export function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'unassociated': 'info',
    'associated': 'success',
    'processing': 'warning',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取文件夹类型显示文本
 */
export function getFolderTypeText(folderType: string): string {
  const typeMap: Record<string, string> = {
    'standard': '标准样本',
    'regular': '普通样本'
  }
  return typeMap[folderType] || '未知类型'
}

/**
 * 获取文件夹类型标签类型
 */
export function getFolderTypeTagType(folderType: string): string {
  const typeMap: Record<string, string> = {
    'standard': 'success',
    'regular': 'info'
  }
  return typeMap[folderType] || 'info'
}

// ==================== 验证函数 ====================

/**
 * 验证文件夹数据是否完整
 */
export function validateFolderData(folderData: FolderData): boolean {
  return !!(
    folderData.folderId &&
    folderData.folderName &&
    folderData.taskId
  )
}

/**
 * 验证文件夹是否可以关联版本
 */
export function canAssociateToVersion(folderData: FolderData): boolean {
  return folderData.status === 'unassociated' && 
         folderData.reviewStatus === 'approved'
}

/**
 * 验证文件夹是否可以设为标准样本
 */
export function canSetAsStandard(folderData: FolderData): boolean {
  return folderData.status === 'associated' && 
         folderData.reviewStatus === 'approved' &&
         folderData.folderType !== 'standard'
}

// ==================== 计算函数 ====================

/**
 * 计算文件夹处理进度
 */
export function calculateProgress(folderData: FolderData): number {
  if (folderData.fileCount === 0) return 0
  return Math.round((folderData.processedFileCount / folderData.fileCount) * 100)
}

/**
 * 计算文件夹处理进度（简化版本）
 */
export function calculateSimpleProgress(fileCount: number, processedCount: number): number {
  if (fileCount === 0) return 0
  return Math.round((processedCount / fileCount) * 100)
}

/**
 * 生成文件夹唯一键
 */
export function generateFolderKey(folderData: FolderData): string {
  return `${folderData.taskId}_${folderData.folderId}`
}

/**
 * 生成简化文件夹唯一键
 */
export function generateSimpleFolderKey(folderInfo: SimpleFolderInfo): string {
  return folderInfo.folderId
}

// ==================== 检查函数 ====================

/**
 * 检查文件夹是否过期
 */
export function isFolderExpired(folderData: FolderData, expirationDays: number = 30): boolean {
  const createTime = new Date(folderData.createTime.$date)
  const expirationTime = new Date(createTime.getTime() + expirationDays * 24 * 60 * 60 * 1000)
  return new Date() > expirationTime
}

/**
 * 检查文件夹是否为标准样本
 */
export function isStandardSample(folderData: FolderData): boolean {
  return folderData.folderType === 'standard'
}

/**
 * 检查文件夹是否可编辑
 */
export function isFolderEditable(folderData: FolderData): boolean {
  return folderData.status === 'unassociated' || 
         (folderData.status === 'associated' && folderData.reviewStatus === 'pending')
}

/**
 * 检查文件夹是否可删除
 */
export function isFolderDeletable(folderData: FolderData): boolean {
  return folderData.status === 'unassociated' && 
         folderData.reviewStatus === 'pending'
}

// ==================== 格式化函数 ====================

/**
 * 格式化文件数量显示
 */
export function formatFileCount(count: number): string {
  if (count === 0) return '0 个文件'
  if (count === 1) return '1 个文件'
  return `${count} 个文件`
}

/**
 * 格式化处理进度显示
 */
export function formatProgress(progress: number): string {
  return `${progress}%`
}

/**
 * 格式化创建时间显示
 */
export function formatCreateTime(createTime: string): string {
  const date = new Date(createTime)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 格式化文件夹名称显示
 */
export function formatFolderName(name: string, maxLength: number = 50): string {
  if (name.length <= maxLength) return name
  return name.substring(0, maxLength) + '...'
} 