package com.ruoyi.system.domain.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * 批量上传任务实体
 *
 * 简化版本，只负责跟踪文件上传进度，不包含业务元数据。
 * 业务元数据（国家、证件类型等）存储在FolderInfo中。
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Document(collection = "batchUploadTask")
public class BatchUploadTask {

    @Id
    private String id;

    /**
     * 任务ID（UUID）
     */
    @Field("taskId")
    private String taskId;

    /**
     * 任务名称 (格式: 时间_部门名称_批量上传任务)
     */
    @Field("taskName")
    private String taskName;

    /**
     * 任务状态：UPLOADING, COMPLETED, FAILED
     */
    @Field("status")
    private String status;

    /**
     * 总文件夹数
     */
    @Field("totalFolders")
    private Integer totalFolders;

    /**
     * 已处理文件夹数
     */
    @Field("processedFolders")
    private Integer processedFolders;

    /**
     * 总文件数
     */
    @Field("totalFiles")
    private Integer totalFiles;

    /**
     * 已处理文件数
     */
    @Field("processedFiles")
    private Integer processedFiles;

    /**
     * 创建者用户名
     */
    @Field("createdBy")
    private String createdBy;

    /**
     * 创建者部门ID
     */
    @Field("deptId")
    private Long deptId;

    /**
     * 创建时间
     */
    @Field("createTime")
    private Date createTime;

    /**
     * 开始时间
     */
    @Field("startTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @Field("endTime")
    private Date endTime;

    /**
     * 任务描述
     */
    @Field("description")
    private String description;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private Date updateTime;
}