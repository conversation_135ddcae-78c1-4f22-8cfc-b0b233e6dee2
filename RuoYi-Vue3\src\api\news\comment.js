import request from '@/utils/request'

// 查询新闻评论列表
export function listComment(query) {
  return request({
    url: '/system/comment/list',
    method: 'get',
    params: query
  })
}

// 查询新闻评论详细
export function getComment(commentId) {
  return request({
    url: '/system/comment/' + commentId,
    method: 'get'
  })
}

// 新增新闻评论
export function addComment(data) {
  return request({
    url: '/system/comment',
    method: 'post',
    data: data
  })
}

// 修改新闻评论
export function updateComment(data) {
  return request({
    url: '/system/comment',
    method: 'put',
    data: data
  })
}

// 删除新闻评论
export function delComment(commentId) {
  return request({
    url: '/system/comment/' + commentId,
    method: 'delete'
  })
}

// 根据新闻ID获取评论列表
export function getCommentsByNewsId(newsId) {
  return request({
    url: '/system/comment/news/' + newsId,
    method: 'get'
  })
}

// 添加评论（前台用户）
export function addUserComment(data) {
  return request({
    url: '/system/comment',
    method: 'post',
    data: data
  })
}
