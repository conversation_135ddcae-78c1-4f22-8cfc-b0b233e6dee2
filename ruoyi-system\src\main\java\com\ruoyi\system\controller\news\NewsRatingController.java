package com.ruoyi.system.controller.news;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.news.NewsRating;
import com.ruoyi.system.service.news.INewsRatingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.news.SysNews;
import com.ruoyi.system.service.news.ISysNewsService;
import com.ruoyi.system.service.news.ISysNotificationService;
import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.core.context.SecurityContextHolder;
import com.ruoyi.system.mapper.news.NewsRatingMapper;

/**
 * 文章评分Controller
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/news_rating/news_rating")
public class NewsRatingController extends BaseController
{
    @Autowired
    private INewsRatingService newsRatingService;

    @Autowired
    private ISysNewsService newsService;

    @Autowired
    private ISysNotificationService notificationService;
    
    @Autowired
    private NewsRatingMapper newsRatingMapper;

    /**
     * 查询文章评分列表
     */
    @PreAuthorize("@ss.hasPermi('news_rating:news_rating:list')")
    @GetMapping("/list")
    public TableDataInfo list(NewsRating newsRating)
    {
        startPage();
        List<NewsRating> list = newsRatingService.selectNewsRatingList(newsRating);
        return getDataTable(list);
    }

    /**
     * 导出文章评分列表
     */
    @PreAuthorize("@ss.hasPermi('news_rating:news_rating:export')")
    @Log(title = "文章评分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NewsRating newsRating)
    {
        List<NewsRating> list = newsRatingService.selectNewsRatingList(newsRating);
        ExcelUtil<NewsRating> util = new ExcelUtil<NewsRating>(NewsRating.class);
        util.exportExcel(response, list, "文章评分数据");
    }

    /**
     * 获取文章评分详细信息
     */
    //@PreAuthorize("@ss.hasPermi('news_rating:news_rating:query')")
    @GetMapping(value = "/{ratingId}")
    @Anonymous
    public AjaxResult getInfo(@PathVariable("ratingId") Long ratingId)
    {
        return success(newsRatingService.selectNewsRatingByRatingId(ratingId));
    }

    /**
     * 新增文章评分
     */
    //@PreAuthorize("@ss.hasPermi('news_rating:news_rating:add')")
    @Log(title = "文章评分", businessType = BusinessType.INSERT)
    @PostMapping
    @Anonymous
    public AjaxResult addRating(@RequestBody NewsRating newsRating)
    {
        newsRating.setUserId(getUserId());
        
        int rows = newsRatingService.insertOrUpdateRating(newsRating);
        
        // 获取文章信息，用于发送通知
        SysNews news = newsService.selectSysNewsByNewsId(newsRating.getNewsId());
        if (news != null && rows > 0) {
            // 增加判断：如果评分用户不是文章作者，才发送通知
            if (!newsRating.getUserId().equals(news.getUserId())) {
                // 发送评分通知给文章作者
                notificationService.sendRatingNotification(
                    newsRating.getNewsId(),
                    news.getNewsTitle(),
                    getUserId(),
                    getUsername(),
                    news.getUserId(),
                    newsRating.getScore().intValue()
                );
            }
        }
        
        return toAjax(rows);
    }

    /**
     * 修改文章评分
     */
    //@PreAuthorize("@ss.hasPermi('news_rating:news_rating:edit')")
    @Log(title = "文章评分", businessType = BusinessType.UPDATE)
    @PutMapping
    @Anonymous
    public AjaxResult edit(@RequestBody NewsRating newsRating)
    {
        return toAjax(newsRatingService.updateNewsRating(newsRating));
    }

    /**
     * 删除文章评分
     */
    @PreAuthorize("@ss.hasPermi('news_rating:news_rating:remove')")
    @Log(title = "文章评分", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ratingIds}")
    public AjaxResult remove(@PathVariable Long[] ratingIds)
    {
        return toAjax(newsRatingService.deleteNewsRatingByRatingIds(ratingIds));
    }
    
    /**
     * 获取文章平均评分（匿名可访问）
     */
    @GetMapping("/public/avg/{newsId}")
    @Anonymous
    public AjaxResult getPublicAvgRating(@PathVariable("newsId") Long newsId)
    {
        Map<String, Object> result = new HashMap<>();
        Double avgRating = newsRatingMapper.selectAvgRatingByNewsId(newsId);
        Integer ratingCount = newsRatingMapper.selectRatingCountByNewsId(newsId);
        
        result.put("avgRating", avgRating != null ? avgRating : 0.0);
        result.put("ratingCount", ratingCount != null ? ratingCount : 0);
        
        return success(result);
    }

    /**
     * 获取用户对文章的评分（需要登录）
     */
    @GetMapping("/user/rating/{newsId}")
    public AjaxResult getUserRating(@PathVariable("newsId") Long newsId)
    {
        Long userId = getUserId();
        NewsRating userRating = newsRatingMapper.selectRatingByNewsIdAndUserId(newsId, userId);
        return success(userRating != null ? userRating.getScore() : 0);
    }
    
    /**
     * 用户添加文章评分
     */
    //@PreAuthorize("@ss.hasPermi('news_rating:news_rating:add')")
    @PostMapping("/rate")
    @Anonymous
    public AjaxResult addUserRating(@RequestBody NewsRating newsRating)
    {
        // 如果是匿名用户，设置一个默认ID
        if (SecurityContextHolder.getContext().getAuthentication() == null || 
            SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof String) {
            newsRating.setUserId(-1L); // 使用一个特殊值表示匿名用户
        } else {
            newsRating.setUserId(getUserId());
        }
        return toAjax(newsRatingService.addUserRating(newsRating));
    }
}
