<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`为${props.targetFolder?.folderName || ''}关联版本`"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 目标文件夹信息 -->
    <div class="target-folder-info" v-if="props.targetFolder">
      <h4 class="info-title">要关联的文件夹信息：</h4>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">文件夹名称：</span>
          <span class="value">{{ props.targetFolder.folderName }}</span>
        </div>
        <div class="info-item">
          <span class="label">国家：</span>
          <span class="value">{{ props.targetFolder.countryInfo?.name }} ({{ props.targetFolder.countryInfo?.code }})</span>
        </div>
        <div class="info-item">
          <span class="label">证件类型：</span>
          <span class="value">{{ props.targetFolder.certInfo?.zjlbmc }} ({{ props.targetFolder.certInfo?.zjlbdm }})</span>
        </div>
        <div class="info-item">
          <span class="label">年份：</span>
          <span class="value">{{ props.targetFolder.issueYear }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件数量：</span>
          <span class="value">{{ props.targetFolder.fileCount }} 个</span>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchParams">
        <el-form-item label="关键词">
          <el-input
            v-model="searchParams.keyword"
            placeholder="搜索版本代码或描述"
            clearable
            @input="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="国家">
          <CountrySelect
            v-model="searchParams.countryId"
            placeholder="请选择国家"
            width="150px"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item label="证件类型">
          <CertTypeSelect
            v-model="searchParams.certType"
            placeholder="请选择证件类型"
            width="150px"
            @change="handleSearch"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 版本列表表格 -->
    <el-table
      :data="versionList"
      v-loading="loading"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      highlight-current-row
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="versionCode" label="版本代码" min-width="150" />
      <el-table-column label="国家" min-width="100">
        <template #default="{ row }">
          {{ row.countryInfo?.name || row.countryName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="证件类型" min-width="120">
        <template #default="{ row }">
          {{ row.certInfo?.zjlbmc || row.certTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="issueYear" label="年份" min-width="80" />
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" min-width="120" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="searchParams.pageNum"
        v-model:page-size="searchParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedVersion">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
// 导入选择器组件
import CountrySelect from '@/components/CertCommon/CountrySelect.vue'
import CertTypeSelect from '@/components/CertCommon/CertTypeSelect.vue'
// 导入API
import { getVersionList as getVersionListAPI } from '@/api/cert/version'
import { associateFolderToVersion } from '@/api/cert/folder'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  targetFolder: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'association-success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const versionList = ref([])
const selectedVersion = ref(null)
const total = ref(0)

// 搜索参数
const searchParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  countryId: null,
  certType: ''
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetSearchParams()
    fetchVersionList()
  }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
    resetState()
  }
})

// 获取版本列表
async function fetchVersionList() {
  loading.value = true
  try {
    const response = await getVersionListAPI(searchParams)
    if (response.code === 200) {
      versionList.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取版本列表失败')
    }
  } catch (error) {
    console.error('获取版本列表失败:', error)
    ElMessage.error('获取版本列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索参数
function resetSearchParams() {
  searchParams.pageNum = 1
  searchParams.pageSize = 10
  searchParams.keyword = ''
  searchParams.countryId = null
  searchParams.certType = ''
}

// 事件处理函数
function handleSearch() {
  // 搜索时重置分页
  searchParams.pageNum = 1
  fetchVersionList()
}

function handleRowClick(row) {
  selectedVersion.value = row
}

function handleSelectionChange(selection) {
  if (selection.length > 0) {
    selectedVersion.value = selection[0]
  } else {
    selectedVersion.value = null
  }
}

function handleSizeChange(val) {
  searchParams.pageSize = val
  searchParams.pageNum = 1
  fetchVersionList()
}

function handleCurrentChange(val) {
  searchParams.pageNum = val
  fetchVersionList()
}

function handleClose() {
  dialogVisible.value = false
}

async function handleConfirm() {
  if (!selectedVersion.value) {
    ElMessage.warning('请选择一个版本')
    return
  }

  try {
    loading.value = true
    const versionAssociationData = {
      versionId: selectedVersion.value.versionId
    }

    const result = await associateFolderToVersion(
      props.targetFolder.folderId,
      versionAssociationData
    )

    if (result.code === 200) {
      ElMessage.success('关联成功')
      emit('association-success')
      handleClose()
    } else {
      ElMessage.error(result.msg || '关联失败')
    }
  } catch (error) {
    console.error('关联操作失败:', error)
    ElMessage.error('关联操作失败')
  } finally {
    loading.value = false
  }
}

function resetState() {
  selectedVersion.value = null
  versionList.value = []
  total.value = 0
  resetSearchParams()
}
</script>

<style scoped lang="scss">
.target-folder-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  .info-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px 16px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
      min-width: 80px;
    }

    .value {
      color: #303133;
      font-weight: 600;
    }
  }
}

.search-area {
  margin-bottom: 16px;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  .el-table__row {
    cursor: pointer;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}
</style>
