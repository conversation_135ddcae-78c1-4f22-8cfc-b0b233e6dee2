package com.ruoyi.system.service.news;

import java.util.List;
import com.ruoyi.system.domain.news.SysHonorroll;

/**
 * 光荣榜信息表Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
public interface ISysHonorrollService 
{
    /**
     * 查询光荣榜信息表
     * 
     * @param honorId 光荣榜信息表主键
     * @return 光荣榜信息表
     */
    public SysHonorroll selectSysHonorrollByHonorId(Long honorId);

    /**
     * 查询光荣榜信息表列表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 光荣榜信息表集合
     */
    public List<SysHonorroll> selectSysHonorrollList(SysHonorroll sysHonorroll);

    /**
     * 新增光荣榜信息表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 结果
     */
    public int insertSysHonorroll(SysHonorroll sysHonorroll);

    /**
     * 修改光荣榜信息表
     * 
     * @param sysHonorroll 光荣榜信息表
     * @return 结果
     */
    public int updateSysHonorroll(SysHonorroll sysHonorroll);

    /**
     * 批量删除光荣榜信息表
     * 
     * @param honorIds 需要删除的光荣榜信息表主键集合
     * @return 结果
     */
    public int deleteSysHonorrollByHonorIds(Long[] honorIds);

    /**
     * 删除光荣榜信息表信息
     * 
     * @param honorId 光荣榜信息表主键
     * @return 结果
     */
    public int deleteSysHonorrollByHonorId(Long honorId);
}
