package com.ruoyi.system.domain.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 图片标注信息响应VO
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ImageAnnotationVO {
    
    /** 图片ID */
    private String imageId;
    
    /** 图片类型 */
    private String imageType;
    
    /** 图片类型显示名称 */
    private String imageTypeDisplayName;
    
    /** 是否可编辑标注 */
    private Boolean canEdit;
    
    /** 是否可查看标注 */
    private Boolean canView;
    
    /** 权限说明 */
    private String reason;
    
    /** 标注项列表 */
    private List<AnnotationItemVO> annotations;
    
    /** 模板来源（standard-标准样本, template-模板） */
    private String templateSource;
    
    /** 是否为标准样本 */
    private Boolean isStandardSample;
    
    /** 是否为可标注类型 */
    private Boolean isAnnotatableType;
    
    /**
     * 标注项VO
     */
    @Data
    public static class AnnotationItemVO {
        /** 标注项ID */
        private String annotationId;
        
        /** 标注类型 */
        private String annotationType;
        
        /** 标注名称 */
        private String annotationName;
        
        /** 坐标信息 */
        private AnnotationCoordinateVO coordinate;
        
        /** 标注值 */
        private String annotationValue;
        
        /** 是否必填 */
        private Boolean required;
        
        /** 显示顺序 */
        private Integer displayOrder;
    }
    
    /**
     * 坐标信息VO
     */
    @Data
    public static class AnnotationCoordinateVO {
        /** X坐标（百分比） */
        private Double x;
        
        /** Y坐标（百分比） */
        private Double y;
        
        /** 宽度（百分比） */
        private Double width;
        
        /** 高度（百分比） */
        private Double height;
    }
}
