#!/usr/bin/env python3
"""
高性能并发版本的批量证件版本创建工具
预期性能提升: 5-10倍
"""
import os
import requests
from minio import Minio
from minio.error import S3Error
import concurrent.futures
import time
from datetime import datetime

# 配置
API_URL = 'http://localhost:8000/api/v1/cert/version/unified'
FOLDER_ROOT = r'C:\Users\<USER>\Documents\工作文档\2025研发项目\证研平台\configs_upd\configs\personal'

# MinIO配置
MINIO_ENDPOINT = 'localhost:9000'
MINIO_ACCESS_KEY = 'minioadmin'
MINIO_SECRET_KEY = 'minioadmin'
MINIO_BUCKET_NAME = 'xjlfiles'

# 高性能配置
MAX_UPLOAD_WORKERS = 10    # 上传并发数
MAX_API_WORKERS = 8        # API并发数

minio_client = Minio(MINIO_ENDPOINT, access_key=MINIO_ACCESS_KEY, secret_key=MINIO_SECRET_KEY, secure=False)

def ensure_bucket_exists():
    try:
        if not minio_client.bucket_exists(MINIO_BUCKET_NAME):
            minio_client.make_bucket(MINIO_BUCKET_NAME)
        return True
    except:
        return False

def upload_image(local_path, folder_name):
    """快速上传图片"""
    try:
        if not os.path.exists(local_path):
            return None
        
        file_ext = os.path.splitext(local_path)[1].lower()
        minio_path = f"{folder_name}/training/PER{file_ext}"
        
        # 检查是否已存在
        try:
            minio_client.stat_object(MINIO_BUCKET_NAME, minio_path)
            return f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
        except S3Error:
            pass
        
        # 上传
        minio_client.fput_object(MINIO_BUCKET_NAME, minio_path, local_path)
        return f"http://{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/{minio_path}"
    except:
        return None

def find_training_image(root_dir, country_code, folder_name):
    """查找训练图片"""
    paths = [
        os.path.join(root_dir, country_code, folder_name, "PER.JPG"),
        os.path.join(root_dir, country_code, folder_name, "per.jpg"),
        os.path.join(root_dir, country_code, folder_name, "PER.jpg"),
        os.path.join(root_dir, country_code, folder_name, "per.JPG")
    ]
    for path in paths:
        if os.path.exists(path):
            return path
    return None

def find_cert_folders(root_dir):
    """查找证件文件夹"""
    result = []
    for country in os.listdir(root_dir):
        country_path = os.path.join(root_dir, country)
        if os.path.isdir(country_path):
            for folder in os.listdir(country_path):
                if '_' in folder and len(folder.split('_')) == 4:
                    result.append({
                        'folderName': folder,
                        'countryCode': country,
                        'folderPath': os.path.join(country_path, folder)
                    })
    return result

def parse_folder_name(folder_name):
    """解析文件夹名称"""
    parts = folder_name.split('_')
    return {
        'countryCode': parts[0],
        'certType': parts[1], 
        'startNo': parts[2],
        'issueYear': parts[3]
    } if len(parts) >= 4 else None

def process_folder(folder_info):
    """处理单个文件夹 - 包含图片上传"""
    try:
        folder_name = folder_info['folderName']
        country_code = folder_info['countryCode']
        
        parsed = parse_folder_name(folder_name)
        if not parsed:
            return {'status': 'error', 'folder': folder_name, 'error': '解析失败'}
        
        # 上传训练图片
        image_path = find_training_image(FOLDER_ROOT, country_code, folder_name)
        image_url = None
        if image_path:
            image_url = upload_image(image_path, folder_name)
        
        # 生成版本代码
        version_code = f"{country_code}_{parsed['certType']}_{parsed['issueYear']}_{datetime.now().strftime('%Y%m%d')}"
        
        # 构建请求数据
        request_data = {
            "creationMode": "STANDARD_SAMPLE",
            "folderName": folder_name,
            "certType": parsed['certType'],
            "issueYear": parsed['issueYear'],
            "countryCode": country_code,
            "startNo": parsed['startNo'],
            "endNo": "D9999",
            "numberFormat": "D{4}",
            "securityFeatures": ["水印", "防伪线", "全息图"],
            "width": 85.6,
            "height": 53.98,
            "thickness": 0.76,
            "material": "PVC",
            "versionCode": version_code,
            "trainingImageUrl": image_url,
            "trainingImageLightType": "visible",
            "trainingImageDescription": f"标准样本训练图片 - {version_code}",
            "imageCount": 1,
            "lightTypes": ["visible"]
        }
        
        return {
            'status': 'ready',
            'folder': folder_name,
            'request_data': request_data,
            'image_url': image_url
        }
        
    except Exception as e:
        return {'status': 'error', 'folder': folder_info['folderName'], 'error': str(e)}

def send_api_request(processed_data):
    """发送API请求"""
    try:
        if processed_data['status'] != 'ready':
            return {**processed_data, 'api_status': 'skipped'}
        
        response = requests.post(
            API_URL, 
            json=processed_data['request_data'],
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return {
                    **processed_data,
                    'api_status': 'success',
                    'version_id': result.get('data', {}).get('versionId')
                }
            else:
                return {**processed_data, 'api_status': 'failed', 'api_error': result.get('msg')}
        else:
            return {**processed_data, 'api_status': 'http_error', 'api_error': f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {**processed_data, 'api_status': 'exception', 'api_error': str(e)}

def main():
    print("🚀 高性能并发版本 - 证件版本批量创建工具")
    print("=" * 60)
    
    if not ensure_bucket_exists():
        print("❌ MinIO连接失败")
        return
    
    folders = find_cert_folders(FOLDER_ROOT)
    if not folders:
        print("未找到文件夹")
        return
    
    print(f"📁 找到 {len(folders)} 个文件夹")
    print(f"⚙️  并发配置: 上传{MAX_UPLOAD_WORKERS}线程, API{MAX_API_WORKERS}线程")
    
    confirm = input("继续处理? (y/n): ")
    if confirm.lower() != 'y':
        return
    
    start_time = time.time()
    
    # 阶段1: 并发处理文件夹和上传图片
    print(f"\n🔄 阶段1: 并发处理 ({MAX_UPLOAD_WORKERS}线程)")
    processed_data = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_UPLOAD_WORKERS) as executor:
        futures = [executor.submit(process_folder, folder) for folder in folders]
        
        for i, future in enumerate(concurrent.futures.as_completed(futures), 1):
            result = future.result()
            processed_data.append(result)
            
            if i % 100 == 0 or i == len(folders):
                print(f"  进度: {i}/{len(folders)}")
    
    stage1_time = time.time() - start_time
    ready_count = sum(1 for d in processed_data if d['status'] == 'ready')
    print(f"✅ 阶段1完成: {ready_count}/{len(folders)} 就绪, 用时: {stage1_time:.1f}秒")
    
    # 阶段2: 并发API请求
    print(f"\n🔄 阶段2: 并发API请求 ({MAX_API_WORKERS}线程)")
    final_results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_API_WORKERS) as executor:
        futures = [executor.submit(send_api_request, data) for data in processed_data]
        
        for i, future in enumerate(concurrent.futures.as_completed(futures), 1):
            result = future.result()
            final_results.append(result)
            
            if i % 100 == 0 or i == len(processed_data):
                print(f"  进度: {i}/{len(processed_data)}")
    
    # 统计结果
    total_time = time.time() - start_time
    success_count = sum(1 for r in final_results if r.get('api_status') == 'success')
    
    print("\n" + "="*60)
    print("🎉 处理完成!")
    print(f"📊 总数: {len(folders)}")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {len(folders) - success_count}")
    print(f"⏱️  总时间: {total_time:.1f} 秒")
    print(f"🚀 速度: {len(folders)/total_time:.2f} 个/秒")
    print(f"💡 相比0.37个/秒提升: {(len(folders)/total_time)/0.37:.1f} 倍")
    
    # 显示部分失败案例
    failed = [r for r in final_results if r.get('api_status') != 'success']
    if failed and len(failed) <= 10:
        print(f"\n❌ 失败案例 (显示前{len(failed)}个):")
        for f in failed:
            print(f"  - {f['folder']}: {f.get('api_error', f.get('error', 'Unknown'))}")

if __name__ == "__main__":
    main() 