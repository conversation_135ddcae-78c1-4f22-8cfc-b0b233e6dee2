@echo off
echo ========================================
echo 证件版本管理 API 启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查requirements.txt是否存在
if not exist requirements.txt (
    echo 错误: 未找到requirements.txt文件
    pause
    exit /b 1
)

echo 正在安装依赖...
pip install -r requirements.txt

echo.
echo 启动 FastAPI 服务...
echo 访问地址: http://localhost:8001
echo API文档: http://localhost:8001/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

python start.py

pause 