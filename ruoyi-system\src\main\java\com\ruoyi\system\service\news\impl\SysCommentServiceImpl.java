package com.ruoyi.system.service.news.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.news.SysCommentMapper;
import com.ruoyi.system.domain.news.SysComment;
import com.ruoyi.system.service.news.ISysCommentService;

/**
 * 新闻评论Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-15
 */
@Service
public class SysCommentServiceImpl implements ISysCommentService 
{
    @Autowired
    private SysCommentMapper sysCommentMapper;

    /**
     * 查询新闻评论
     * 
     * @param commentId 新闻评论主键
     * @return 新闻评论
     */
    @Override
    public SysComment selectSysCommentByCommentId(Long commentId)
    {
        return sysCommentMapper.selectSysCommentByCommentId(commentId);
    }

    /**
     * 查询新闻评论列表
     * 
     * @param sysComment 新闻评论
     * @return 新闻评论
     */
    @Override
    public List<SysComment> selectSysCommentList(SysComment sysComment)
    {
        return sysCommentMapper.selectSysCommentList(sysComment);
    }

    /**
     * 新增新闻评论
     * 
     * @param sysComment 新闻评论
     * @return 结果
     */
    @Override
    public int insertSysComment(SysComment sysComment)
    {
        sysComment.setCreateTime(DateUtils.getNowDate());
        return sysCommentMapper.insertSysComment(sysComment);
    }

    /**
     * 修改新闻评论
     * 
     * @param sysComment 新闻评论
     * @return 结果
     */
    @Override
    public int updateSysComment(SysComment sysComment)
    {
        sysComment.setUpdateTime(DateUtils.getNowDate());
        return sysCommentMapper.updateSysComment(sysComment);
    }

    /**
     * 批量删除新闻评论
     * 
     * @param commentIds 需要删除的新闻评论主键
     * @return 结果
     */
    @Override
    public int deleteSysCommentByCommentIds(Long[] commentIds)
    {
        return sysCommentMapper.deleteSysCommentByCommentIds(commentIds);
    }

    /**
     * 删除新闻评论信息
     * 
     * @param commentId 新闻评论主键
     * @return 结果
     */
    @Override
    public int deleteSysCommentByCommentId(Long commentId)
    {
        return sysCommentMapper.deleteSysCommentByCommentId(commentId);
    }
    
    /**
     * 根据新闻ID查询评论列表
     * 
     * @param newsId 新闻ID
     * @return 评论列表
     */
    @Override
    public List<SysComment> selectSysCommentByNewsId(Long newsId)
    {
        return sysCommentMapper.selectSysCommentByNewsId(newsId);
    }
}
