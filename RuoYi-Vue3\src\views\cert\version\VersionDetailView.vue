<template>
  <div class="version-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <router-link to="/cert/version">版本管理</router-link>
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="versionInfo">
          {{ versionInfo.versionCode }}
        </el-breadcrumb-item>
      </el-breadcrumb>

      <div class="header-actions">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <el-button @click="refreshData" :icon="Refresh" :loading="loading">刷新</el-button>
      </div>
    </div>

    <!-- 版本基本信息卡片 -->
    <VersionInfoCard
      :version-info="versionInfo"
      :folder-count="folderList.length"
      :standard-folder-name="getStandardFolderName()"
    />

    <!-- 三列协同工作区 -->
    <div class="main-workspace" v-loading="loading">
      <!-- 左侧：文件夹列表 -->
      <div class="left-panel">
        <VersionFolderList
          :folders="folderList"
          :selected-folder-id="selectedFolder?.folderId"
          :standard-folder-id="versionInfo?.standardFolderId"
          :loading="folderLoading"
          @folder-select="handleFolderSelect"
          @folder-detail="handleFolderDetail"
        />
      </div>

      <!-- 中间：图片预览与操作区 -->
      <div class="center-panel">
        <VersionImagePreview
          :selected-image="selectedImage"
          :selected-folder="selectedFolder"
          :version-info="versionInfo"
          :standard-annotations="standardAnnotations"
          @set-standard="handleSetAsStandard"
          @remove-standard="handleRemoveStandard"
          @start-annotation="handleStartAnnotation"
          @view-details="handleViewFolderDetails"
          @save-annotation="handleSaveAnnotation"
        />
      </div>

      <!-- 右侧：缩略图网格 -->
      <div class="right-panel">
        <VersionThumbnailGrid
          :images="currentFolderImages"
          :selected-image="selectedImage"
          :loading="imageLoading"
          @image-select="handleImageSelect"
          @image-edit="handleImageEdit"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'

// 导入组件
import VersionInfoCard from './components/VersionInfoCard.vue'
import VersionFolderList from './components/VersionFolderList.vue'
import VersionImagePreview from './components/VersionImagePreview.vue'
import VersionThumbnailGrid from './components/VersionThumbnailGrid.vue'

// 导入API
import { 
  getVersionDetails, 
  getFoldersByVersionId, 
  setStandardFolder, 
  removeStandardFolder 
} from '@/api/cert/version'
import { getImagesByFolder } from '@/api/cert/image'
import { getStandardAnnotations, saveImageAnnotation } from '@/api/cert/annotation'

const route = useRoute()
const router = useRouter()

// 响应式数据
const state = reactive({
  versionInfo: null,           // 版本基本信息
  folderList: [],             // 版本下的文件夹列表
  selectedFolder: null,        // 当前选中的文件夹
  selectedImage: null,         // 当前选中的图片
  currentFolderImages: [],     // 当前文件夹的图片列表
  standardAnnotations: new Map(), // 标准样本标注数据缓存
  loading: false,             // 页面加载状态
  folderLoading: false,       // 文件夹加载状态
  imageLoading: false         // 图片加载状态
})

// 解构响应式数据
const {
  versionInfo,
  folderList,
  selectedFolder,
  selectedImage,
  currentFolderImages,
  standardAnnotations,
  loading,
  folderLoading,
  imageLoading
} = toRefs(state)

// 权限控制逻辑
const isAnnotatableImage = (image) => {
  if (!image) return false
  const annotatableNames = ['可见光主图.jpg', '红外光主图.jpg', '紫外光主图.jpg']
  return annotatableNames.includes(image.imageName)
}

const isStandardFolder = computed(() => {
  return selectedFolder.value?.folderId === versionInfo.value?.standardFolderId
})

const canAnnotate = computed(() => {
  return isStandardFolder.value && isAnnotatableImage(selectedImage.value)
})

const canViewAnnotationOverlay = computed(() => {
  return !isStandardFolder.value && 
         isAnnotatableImage(selectedImage.value) && 
         versionInfo.value?.standardFolderId
})

const canSetAsStandard = computed(() => {
  return selectedFolder.value?.status === 'associated' && 
         selectedFolder.value?.reviewStatus === 'approved' &&
         !isStandardFolder.value
})

// 生命周期
onMounted(() => {
  loadVersionData()
})

// 监听路由参数变化
watch(() => route.params.versionId, (newVersionId) => {
  if (newVersionId) {
    loadVersionData()
  }
})

// 监听选中文件夹变化
watch(selectedFolder, async (newFolder, oldFolder) => {
  if (newFolder && newFolder.folderId !== oldFolder?.folderId) {
    await loadFolderImages(newFolder.folderId)
    
    // 默认选择主图或第一张图片
    if (currentFolderImages.value.length > 0) {
      const mainImage = findMainImage(currentFolderImages.value)
      selectedImage.value = mainImage || currentFolderImages.value[0]
    } else {
      selectedImage.value = null
    }
  }
})

// 监听版本信息变化，加载标准样本标注数据
watch(() => versionInfo.value?.standardFolderId, async (standardFolderId) => {
  if (standardFolderId) {
    await loadStandardAnnotations(standardFolderId)
  }
})

// 方法
const loadVersionData = async () => {
  const versionId = route.params.versionId
  if (!versionId) {
    ElMessage.error('版本ID不能为空')
    return
  }

  try {
    loading.value = true
    
    // 并行加载版本信息和文件夹列表
    const [versionResponse, foldersResponse] = await Promise.all([
      getVersionDetails(versionId),
      getFoldersByVersionId(versionId)
    ])

    if (versionResponse.code === 200) {
      versionInfo.value = versionResponse.data
    } else {
      throw new Error(versionResponse.msg || '获取版本信息失败')
    }

    if (foldersResponse.code === 200) {
      folderList.value = foldersResponse.data || []
      
      // 默认选择第一个文件夹
      if (folderList.value.length > 0) {
        selectedFolder.value = folderList.value[0]
      }
    } else {
      throw new Error(foldersResponse.msg || '获取文件夹列表失败')
    }

  } catch (error) {
    console.error('加载版本数据失败:', error)
    ElMessage.error(error.message || '加载版本数据失败')
  } finally {
    loading.value = false
  }
}

const loadFolderImages = async (folderId) => {
  if (!folderId) return

  try {
    imageLoading.value = true
    const response = await getImagesByFolder(folderId)
    
    if (response.code === 200) {
      currentFolderImages.value = response.data || []
    } else {
      throw new Error(response.msg || '获取图片列表失败')
    }
  } catch (error) {
    console.error('加载图片列表失败:', error)
    ElMessage.error(error.message || '加载图片列表失败')
    currentFolderImages.value = []
  } finally {
    imageLoading.value = false
  }
}

const loadStandardAnnotations = async (standardFolderId) => {
  try {
    const annotatableTypes = ['VISIBLE', 'INFRARED', 'ULTRAVIOLET']
    
    for (const type of annotatableTypes) {
      try {
        const response = await getStandardAnnotations(standardFolderId, type)
        if (response.code === 200) {
          standardAnnotations.value.set(type, response.data || [])
        }
      } catch (error) {
        console.warn(`加载${type}类型标注数据失败:`, error)
      }
    }
  } catch (error) {
    console.error('加载标准样本标注数据失败:', error)
  }
}

const findMainImage = (images) => {
  return images.find(image => 
    image.imageName.includes('主图') || 
    image.imageName.includes('main') ||
    image.isMain
  )
}

const getStandardFolderName = () => {
  if (!versionInfo.value?.standardFolderId) return ''
  
  const standardFolder = folderList.value.find(
    folder => folder.folderId === versionInfo.value.standardFolderId
  )
  
  return standardFolder?.folderName || ''
}

const refreshData = () => {
  loadVersionData()
}

const goBack = () => {
  router.push('/cert/version')
}

// 事件处理方法
const handleFolderSelect = (folder) => {
  selectedFolder.value = folder
}

const handleFolderDetail = (folder) => {
  router.push(`/cert/folder/${folder.folderId}`)
}

const handleImageSelect = (image, index) => {
  selectedImage.value = image
}

const handleImageEdit = (image) => {
  // 跳转到图片编辑页面或打开编辑弹窗
  console.log('编辑图片:', image)
}

const handleSetAsStandard = async (folder) => {
  try {
    const currentStandardName = getStandardFolderName()
    let confirmMessage = `确定要将文件夹"${folder.folderName}"设为标准样本吗？`

    if (versionInfo.value.standardFolderId) {
      confirmMessage += `\n\n当前标准样本"${currentStandardName}"将被自动取消。`
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '设置标准样本',
      {
        confirmButtonText: '确定设置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await setStandardFolder(versionInfo.value.versionId, {
      folderId: folder.folderId
    })

    if (response.code === 200) {
      ElMessage.success('设置标准样本成功')

      // 更新本地数据
      versionInfo.value.standardFolderId = folder.folderId

      // 重新加载标准样本标注数据
      await loadStandardAnnotations(folder.folderId)
    } else {
      ElMessage.error(response.msg || '设置标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置标准样本失败:', error)
      ElMessage.error('设置标准样本失败')
    }
  }
}

const handleRemoveStandard = async (folder) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消文件夹"${folder.folderName}"的标准样本设置吗？\n\n取消后，该版本下所有文件夹都无法进行标注操作。`,
      '取消标准样本',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '保持现状',
        type: 'warning'
      }
    )

    const response = await removeStandardFolder(versionInfo.value.versionId)

    if (response.code === 200) {
      ElMessage.success('取消标准样本成功')

      // 更新本地数据
      versionInfo.value.standardFolderId = null

      // 清空标注数据缓存
      standardAnnotations.value.clear()
    } else {
      ElMessage.error(response.msg || '取消标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消标准样本失败:', error)
      ElMessage.error('取消标准样本失败')
    }
  }
}

const handleStartAnnotation = (folder) => {
  // 跳转到标注页面
  router.push(`/cert/annotation/${folder.folderId}`)
}

const handleViewFolderDetails = (folder) => {
  router.push(`/cert/folder/${folder.folderId}`)
}

const handleSaveAnnotation = async (data) => {
  try {
    const response = await saveImageAnnotation({
      imageId: data.imageId,
      annotations: data.annotations
    })

    if (response.code === 200) {
      ElMessage.success('标注保存成功')

      // 更新图片的标注状态
      if (selectedImage.value && selectedImage.value.imageId === data.imageId) {
        selectedImage.value.isAnnotated = true
      }

      // 刷新当前文件夹的图片列表
      if (selectedFolder.value) {
        await loadFolderImages(selectedFolder.value.folderId)
      }
    } else {
      ElMessage.error(response.msg || '标注保存失败')
    }
  } catch (error) {
    console.error('保存标注失败:', error)
    ElMessage.error('保存标注失败')
  }
}
</script>

<style scoped>
.version-detail-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.main-workspace {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.center-panel {
  flex: 1;
  min-width: 0;
}

.right-panel {
  width: 320px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-workspace {
    flex-direction: column;
    gap: 16px;
  }

  .left-panel {
    width: 100%;
    height: 200px;
    order: 1;
  }

  .right-panel {
    width: 100%;
    height: 150px;
    order: 2;
  }

  .center-panel {
    order: 3;
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .version-detail-container {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .main-workspace {
    gap: 12px;
  }

  .left-panel {
    height: 150px;
  }

  .right-panel {
    height: 120px;
  }

  .center-panel {
    min-height: 300px;
  }
}
</style>
