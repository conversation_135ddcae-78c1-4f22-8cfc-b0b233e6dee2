package com.ruoyi.web.controller.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.HttpURLConnection;
import java.net.URL;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.IOException;

import com.ruoyi.common.service.MinioService;
import com.ruoyi.common.config.MinioConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;

/**
 * 通用请求处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private MinioService minioService;

    @Autowired
    private MinioConfig minioConfig;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     * 
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file, @RequestParam(value = "directory", required = false) String directory) {
        try {
            // 设置默认目录
            if (StringUtils.isEmpty(directory)) {
                directory = "common";
            }
            
            // 上传文件到 MinIO
            String url = minioService.uploadFile(file, directory);

            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", file.getOriginalFilename());
            ajax.put("newFileName", url.substring(url.lastIndexOf("/") + 1));
            
            return ajax;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public AjaxResult deleteFile(@RequestParam("objectName") String objectName) {
        try {
            minioService.deleteFile(objectName);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * greenfox - 新增：图片代理访问接口，解决MinIO 403权限问题 - 2025-01-15
     * 通过后端代理访问MinIO图片，使用MinIO的用户密码进行认证
     */
    @GetMapping("/image/proxy")
    public void proxyImage(@RequestParam("url") String imageUrl, HttpServletResponse response) {
        try {
            log.info("代理访问图片: {}", imageUrl);
            
            // 验证URL是否为MinIO URL
            if (!imageUrl.startsWith("http://localhost:9000/") && !imageUrl.contains("X-Amz-Algorithm")) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("Invalid image URL");
                return;
            }
            
            // greenfox - 修改：改进URL解析和对象名称提取 - 2025-01-15
            String objectName = extractObjectNameFromUrl(imageUrl);
            
            if (objectName == null) {
                log.warn("无法从URL中提取对象名称: {}", imageUrl);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("Cannot extract object name from URL");
                return;
            }
            
            log.info("提取的对象名称: {}", objectName);
            
            // 使用MinIO服务获取图片流
            try (InputStream inputStream = minioService.getObject(objectName)) {
                if (inputStream == null) {
                    log.warn("MinIO返回空输入流: {}", objectName);
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    response.getWriter().write("Image not found");
                    return;
                }

                // 根据文件扩展名设置内容类型
                String contentType = "image/jpeg"; // 默认
                if (objectName.toLowerCase().endsWith(".png")) {
                    contentType = "image/png";
                } else if (objectName.toLowerCase().endsWith(".gif")) {
                    contentType = "image/gif";
                } else if (objectName.toLowerCase().endsWith(".webp")) {
                    contentType = "image/webp";
                }

                response.setContentType(contentType);

                // 设置缓存头
                response.setHeader("Cache-Control", "public, max-age=3600");
                response.setDateHeader("Expires", System.currentTimeMillis() + 3600000L);

                // 流式传输图片数据
                try (OutputStream outputStream = response.getOutputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = 0;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        totalBytes += bytesRead;
                    }
                    outputStream.flush();
                    log.info("图片代理访问成功: {}, 传输字节数: {}", objectName, totalBytes);
                }

            } catch (Exception e) {
                log.error("使用MinIO服务访问失败: {}, 错误详情: {}", objectName, e.getMessage(), e);

                // 检查是否是权限问题
                if (e.getMessage() != null && e.getMessage().contains("Access Denied")) {
                    log.warn("MinIO访问被拒绝，可能是权限问题: {}", objectName);
                }

                // 回退到HTTP代理方式
                proxyImageViaHttp(imageUrl, response);
            }
            
        } catch (Exception e) {
            log.error("代理访问图片失败: {}", imageUrl, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("Failed to proxy image: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * greenfox - 新增：HTTP代理方式访问图片（回退方案） - 2025-01-15
     */
    private void proxyImageViaHttp(String imageUrl, HttpServletResponse response) throws Exception {
        // 创建HTTP客户端请求
        URL url = new URL(imageUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);
        
        // 检查响应状态
        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            log.warn("HTTP代理返回错误状态: {} for URL: {}", responseCode, imageUrl);
            response.setStatus(responseCode);
            return;
        }
        
        // 获取内容类型
        String contentType = connection.getContentType();
        if (contentType != null) {
            response.setContentType(contentType);
        } else {
            // 根据URL推断内容类型
            if (imageUrl.toLowerCase().contains(".jpg") || imageUrl.toLowerCase().contains(".jpeg")) {
                response.setContentType("image/jpeg");
            } else if (imageUrl.toLowerCase().contains(".png")) {
                response.setContentType("image/png");
            } else if (imageUrl.toLowerCase().contains(".gif")) {
                response.setContentType("image/gif");
            } else {
                response.setContentType("image/jpeg"); // 默认
            }
        }
        
        // 设置缓存头
        response.setHeader("Cache-Control", "public, max-age=3600");
        response.setDateHeader("Expires", System.currentTimeMillis() + 3600000L);
        
        // 流式传输图片数据
        try (InputStream inputStream = connection.getInputStream();
             OutputStream outputStream = response.getOutputStream()) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
        
        log.info("HTTP代理访问成功: {}", imageUrl);
    }
    
    /**
     * greenfox - 新增：从URL中提取MinIO对象名称的工具方法 - 2025-01-15
     */
    private String extractObjectNameFromUrl(String imageUrl) {
        try {
            log.info("开始解析URL: {}", imageUrl);
            
            // 先解码URL，处理可能的双重编码问题
            String decodedUrl = java.net.URLDecoder.decode(imageUrl, "UTF-8");
            log.info("第一次解码后的URL: {}", decodedUrl);
            
            // 如果解码后还包含编码字符，再次解码
            if (decodedUrl.contains("%")) {
                decodedUrl = java.net.URLDecoder.decode(decodedUrl, "UTF-8");
                log.info("二次解码后的URL: {}", decodedUrl);
            }
            
            String objectName = null;
            
            // 处理预签名URL
            if (decodedUrl.contains("X-Amz-Algorithm")) {
                // 移除查询参数
                String baseUrl = decodedUrl.split("\\?")[0];
                log.info("移除查询参数后: {}", baseUrl);
                
                // 提取对象名称：http://localhost:9000/bucket/path/to/file.jpg
                if (baseUrl.startsWith("http://localhost:9000/")) {
                    String pathPart = baseUrl.substring("http://localhost:9000/".length());
                    log.info("路径部分: {}", pathPart);
                    
                    // 跳过bucket名称（第一个路径段）
                    int firstSlash = pathPart.indexOf('/');
                    log.info("第一个斜杠位置: {}, 路径长度: {}", firstSlash, pathPart.length());
                    
                    if (firstSlash > 0 && firstSlash < pathPart.length() - 1) {
                        objectName = pathPart.substring(firstSlash + 1);
                        log.info("提取的对象名称: {}", objectName);
                    } else {
                        log.warn("无法从路径中提取对象名称，firstSlash: {}, pathLength: {}", firstSlash, pathPart.length());
                    }
                } else {
                    log.warn("URL不是预期的MinIO格式: {}", baseUrl);
                }
            } 
            // 处理直接MinIO URL
            else if (decodedUrl.startsWith("http://localhost:9000/")) {
                String pathPart = decodedUrl.substring("http://localhost:9000/".length());
                log.info("直接URL路径部分: {}", pathPart);
                
                // 跳过bucket名称（第一个路径段）  
                int firstSlash = pathPart.indexOf('/');
                if (firstSlash > 0 && firstSlash < pathPart.length() - 1) {
                    objectName = pathPart.substring(firstSlash + 1);
                    log.info("直接URL提取的对象名称: {}", objectName);
                }
            } else {
                log.warn("不支持的URL格式: {}", decodedUrl);
            }
            
            if (objectName != null) {
                log.info("最终提取的对象名称: {}", objectName);
            } else {
                log.error("无法提取对象名称从URL: {}", imageUrl);
            }
            
            return objectName;
            
        } catch (Exception e) {
            log.error("解析URL失败: {}", imageUrl, e);
            return null;
        }
    }






}
