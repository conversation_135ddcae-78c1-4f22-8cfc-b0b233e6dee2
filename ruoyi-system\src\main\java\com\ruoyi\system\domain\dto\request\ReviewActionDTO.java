package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 审核操作请求DTO
 * 用于文件夹版本关联的审核操作
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
public class ReviewActionDTO {

    /** 审核操作 (approve/reject) */
    @NotBlank(message = "审核操作不能为空")
    @Pattern(regexp = "^(approve|reject)$", message = "审核操作只能是 approve 或 reject")
    private String action;

    /** 审核人ID */
    @NotBlank(message = "审核人ID不能为空")
    private String reviewerId;

    /** 审核人姓名 */
    @NotBlank(message = "审核人姓名不能为空")
    private String reviewerName;

    /** 审核意见 */
    private String comments;

    // 手动添加getter/setter方法以确保编译通过
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(String reviewerId) {
        this.reviewerId = reviewerId;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
