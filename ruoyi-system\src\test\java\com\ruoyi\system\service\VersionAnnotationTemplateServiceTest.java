package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.domain.dto.request.AnnotationTemplateDTO;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.ImageRepositoryRepo;
import com.ruoyi.system.repository.VersionAnnotationTemplateRepository;
import com.ruoyi.system.service.IVersionAnnotationTemplateService.AnnotationPermissionInfo;
import com.ruoyi.system.service.IVersionAnnotationTemplateService.TemplateStatistics;
import com.ruoyi.system.service.impl.VersionAnnotationTemplateServiceImpl;
import com.ruoyi.system.utils.ImageTypeDetector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 版本标注模板服务测试类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@SpringBootTest
@ActiveProfiles("test")
public class VersionAnnotationTemplateServiceTest {
    
    /**
     * 测试图片类型检测
     */
    @Test
    public void testImageTypeDetection() {
        // 测试可见光数据页
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("passport_visible_data_page.jpg"));
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("证件_可见光_数据页.png"));
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("front_page.jpg"));
        
        // 测试红外数据页
        assertEquals(ImageTypeDetector.INFRARED_DATA_PAGE, 
                ImageTypeDetector.detectImageType("passport_infrared_data_page.jpg"));
        assertEquals(ImageTypeDetector.INFRARED_DATA_PAGE, 
                ImageTypeDetector.detectImageType("证件_红外_数据页.png"));
        assertEquals(ImageTypeDetector.INFRARED_DATA_PAGE, 
                ImageTypeDetector.detectImageType("ir_scan.jpg"));
        
        // 测试紫外数据页
        assertEquals(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE, 
                ImageTypeDetector.detectImageType("passport_ultraviolet_data_page.jpg"));
        assertEquals(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE, 
                ImageTypeDetector.detectImageType("证件_紫外_数据页.png"));
        assertEquals(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE, 
                ImageTypeDetector.detectImageType("uv_scan.jpg"));
        
        // 测试其他类型
        assertEquals(ImageTypeDetector.OTHER, 
                ImageTypeDetector.detectImageType("random_image.jpg"));
        assertEquals(ImageTypeDetector.OTHER, 
                ImageTypeDetector.detectImageType("background.png"));
    }
    
    /**
     * 测试可标注类型判断
     */
    @Test
    public void testAnnotatableType() {
        assertTrue(ImageTypeDetector.isAnnotatableType(ImageTypeDetector.VISIBLE_DATA_PAGE));
        assertTrue(ImageTypeDetector.isAnnotatableType(ImageTypeDetector.INFRARED_DATA_PAGE));
        assertTrue(ImageTypeDetector.isAnnotatableType(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE));
        assertFalse(ImageTypeDetector.isAnnotatableType(ImageTypeDetector.OTHER));
    }
    
    /**
     * 测试批量检测
     */
    @Test
    public void testBatchDetection() {
        List<String> fileNames = Arrays.asList(
                "passport_visible_data_page.jpg",
                "passport_infrared_data_page.jpg",
                "passport_ultraviolet_data_page.jpg",
                "random_image.jpg"
        );
        
        List<ImageTypeDetector.DetectionResult> results = ImageTypeDetector.batchDetect(fileNames);
        assertEquals(4, results.size());
        
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, results.get(0).getImageType());
        assertTrue(results.get(0).isAnnotatable());
        
        assertEquals(ImageTypeDetector.INFRARED_DATA_PAGE, results.get(1).getImageType());
        assertTrue(results.get(1).isAnnotatable());
        
        assertEquals(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE, results.get(2).getImageType());
        assertTrue(results.get(2).isAnnotatable());
        
        assertEquals(ImageTypeDetector.OTHER, results.get(3).getImageType());
        assertFalse(results.get(3).isAnnotatable());
    }
    
    /**
     * 测试支持的图片类型
     */
    @Test
    public void testSupportedImageTypes() {
        List<String> supportedTypes = ImageTypeDetector.getSupportedImageTypes();
        assertEquals(4, supportedTypes.size());
        assertTrue(supportedTypes.contains(ImageTypeDetector.VISIBLE_DATA_PAGE));
        assertTrue(supportedTypes.contains(ImageTypeDetector.INFRARED_DATA_PAGE));
        assertTrue(supportedTypes.contains(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE));
        assertTrue(supportedTypes.contains(ImageTypeDetector.OTHER));
        
        List<String> annotatableTypes = ImageTypeDetector.getAnnotatableImageTypes();
        assertEquals(3, annotatableTypes.size());
        assertFalse(annotatableTypes.contains(ImageTypeDetector.OTHER));
    }
    
    /**
     * 测试显示名称
     */
    @Test
    public void testDisplayNames() {
        assertEquals("可见光数据页", ImageTypeDetector.getImageTypeDisplayName(ImageTypeDetector.VISIBLE_DATA_PAGE));
        assertEquals("红外数据页", ImageTypeDetector.getImageTypeDisplayName(ImageTypeDetector.INFRARED_DATA_PAGE));
        assertEquals("紫外数据页", ImageTypeDetector.getImageTypeDisplayName(ImageTypeDetector.ULTRAVIOLET_DATA_PAGE));
        assertEquals("其他", ImageTypeDetector.getImageTypeDisplayName(ImageTypeDetector.OTHER));
        assertEquals("未知类型", ImageTypeDetector.getImageTypeDisplayName("UNKNOWN"));
    }
    
    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        // 测试空文件名
        assertEquals(ImageTypeDetector.OTHER, ImageTypeDetector.detectImageType(null));
        assertEquals(ImageTypeDetector.OTHER, ImageTypeDetector.detectImageType(""));
        assertEquals(ImageTypeDetector.OTHER, ImageTypeDetector.detectImageType("   "));
        
        // 测试不同大小写
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("PASSPORT_VISIBLE_DATA_PAGE.JPG"));
        assertEquals(ImageTypeDetector.INFRARED_DATA_PAGE, 
                ImageTypeDetector.detectImageType("Passport_Infrared_Data_Page.Jpg"));
        
        // 测试包含data/page关键词但不匹配其他类型的情况
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("some_data_page.jpg"));
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, 
                ImageTypeDetector.detectImageType("document_page.png"));
    }
    
    /**
     * 测试检测结果对象
     */
    @Test
    public void testDetectionResult() {
        ImageTypeDetector.DetectionResult result = new ImageTypeDetector.DetectionResult("passport_visible_data_page.jpg");
        
        assertEquals("passport_visible_data_page.jpg", result.getFileName());
        assertEquals(ImageTypeDetector.VISIBLE_DATA_PAGE, result.getImageType());
        assertTrue(result.isAnnotatable());
        assertEquals("可见光数据页", result.getDisplayName());
        
        String toString = result.toString();
        assertTrue(toString.contains("passport_visible_data_page.jpg"));
        assertTrue(toString.contains(ImageTypeDetector.VISIBLE_DATA_PAGE));
        assertTrue(toString.contains("true"));
        assertTrue(toString.contains("可见光数据页"));
    }
}
