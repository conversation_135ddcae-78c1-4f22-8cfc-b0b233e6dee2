/**
 * 图片相关的TypeScript类型定义
 */

/** 图片状态枚举 */
export enum ImageStatus {
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  PROCESSED = 'PROCESSED',
  FAILED = 'FAILED'
}

/** 图片类型枚举 */
export enum ImageType {
  MAIN = 'main',
  BACK = 'back',
  SIDE = 'side',
  DETAIL = 'detail',
  OTHER = 'other'
}

/** 图片查询参数 */
export interface ImageQueryParams {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 图片名称 */
  imageName?: string
  /** 图片类型 */
  imageType?: ImageType
  /** 文件夹ID */
  folderId?: string
  /** 是否已标注 */
  isAnnotated?: boolean
  /** 状态 */
  status?: ImageStatus
}

/** 图片信息VO */
export interface ImageInfoVO {
  /** 图片ID */
  imageId: string
  /** 图片名称 */
  imageName: string
  /** 图片类型 */
  imageType: ImageType
  /** 图片路径 */
  imagePath: string
  /** 缩略图路径 */
  thumbnailPath?: string
  /** 图片大小（字节） */
  fileSize: number
  /** 图片格式 */
  fileFormat: string
  /** 宽度（像素） */
  width?: number
  /** 高度（像素） */
  height?: number
  /** 状态 */
  status: ImageStatus
  /** 是否已标注 */
  isAnnotated: boolean
  /** 文件夹ID */
  folderId: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 创建者 */
  createdBy: string
  /** 更新者 */
  updatedBy?: string
}

/** 图片详情VO */
export interface ImageDetailVO extends ImageInfoVO {
  /** 标注信息 */
  annotationInfo?: ImageAnnotationInfo
  /** 元数据 */
  metadata?: ImageMetadata
  /** 处理信息 */
  processingInfo?: ImageProcessingInfo
}

/** 图片标注信息 */
export interface ImageAnnotationInfo {
  /** 是否可编辑 */
  canEdit: boolean
  /** 是否可查看 */
  canView: boolean
  /** 权限说明 */
  reason: string
  /** 标注项数量 */
  annotationCount: number
  /** 最后标注时间 */
  lastAnnotationTime?: string
  /** 标注者 */
  annotatedBy?: string
}

/** 图片元数据 */
export interface ImageMetadata {
  /** EXIF信息 */
  exif?: ExifInfo
  /** 文件信息 */
  fileInfo?: FileInfo
  /** 自定义属性 */
  customProperties?: Record<string, any>
}

/** EXIF信息 */
export interface ExifInfo {
  /** 拍摄时间 */
  dateTime?: string
  /** 相机型号 */
  model?: string
  /** 制造商 */
  make?: string
  /** ISO值 */
  iso?: number
  /** 光圈值 */
  fNumber?: number
  /** 快门速度 */
  exposureTime?: string
  /** 焦距 */
  focalLength?: number
  /** GPS信息 */
  gps?: GpsInfo
}

/** GPS信息 */
export interface GpsInfo {
  /** 纬度 */
  latitude?: number
  /** 经度 */
  longitude?: number
  /** 海拔 */
  altitude?: number
}

/** 文件信息 */
export interface FileInfo {
  /** 原始文件名 */
  originalName: string
  /** 文件扩展名 */
  extension: string
  /** MIME类型 */
  mimeType: string
  /** 文件哈希值 */
  hash?: string
  /** 文件创建时间 */
  fileCreatedTime?: string
  /** 文件修改时间 */
  fileModifiedTime?: string
}

/** 图片处理信息 */
export interface ImageProcessingInfo {
  /** 处理状态 */
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed'
  /** 处理进度 */
  progress: number
  /** 处理开始时间 */
  startTime?: string
  /** 处理完成时间 */
  endTime?: string
  /** 处理错误信息 */
  errorMessage?: string
  /** 处理结果 */
  result?: ProcessingResult
}

/** 处理结果 */
export interface ProcessingResult {
  /** 是否成功 */
  success: boolean
  /** 处理后的图片路径 */
  processedImagePath?: string
  /** 质量评分 */
  qualityScore?: number
  /** 检测到的文本 */
  detectedText?: string[]
  /** 人脸检测结果 */
  faceDetection?: FaceDetectionResult
  /** OCR结果 */
  ocrResult?: OcrResult
}

/** 人脸检测结果 */
export interface FaceDetectionResult {
  /** 检测到的人脸数量 */
  faceCount: number
  /** 人脸位置 */
  faces: FaceInfo[]
}

/** 人脸信息 */
export interface FaceInfo {
  /** 人脸ID */
  faceId: string
  /** 边界框 */
  boundingBox: BoundingBox
  /** 置信度 */
  confidence: number
  /** 年龄估计 */
  age?: number
  /** 性别 */
  gender?: 'male' | 'female' | 'unknown'
}

/** 边界框 */
export interface BoundingBox {
  /** X坐标 */
  x: number
  /** Y坐标 */
  y: number
  /** 宽度 */
  width: number
  /** 高度 */
  height: number
}

/** OCR结果 */
export interface OcrResult {
  /** 检测到的文本行 */
  textLines: TextLine[]
  /** 总文本 */
  fullText: string
  /** 置信度 */
  confidence: number
}

/** 文本行 */
export interface TextLine {
  /** 文本内容 */
  text: string
  /** 位置 */
  boundingBox: BoundingBox
  /** 置信度 */
  confidence: number
}

/** 图片上传请求 */
export interface ImageUploadRequest {
  /** 文件对象 */
  file: File
  /** 图片类型 */
  imageType: ImageType
  /** 文件夹ID */
  folderId: string
  /** 自定义属性 */
  customProperties?: Record<string, any>
}

/** 图片批量操作DTO */
export interface ImageBatchOperationDTO {
  /** 图片ID列表 */
  imageIds: string[]
  /** 操作类型 */
  operation: 'delete' | 'move' | 'update' | 'process'
  /** 目标文件夹ID（用于移动操作） */
  targetFolderId?: string
  /** 更新数据（用于更新操作） */
  updateData?: Partial<ImageUpdateDTO>
}

/** 图片更新DTO */
export interface ImageUpdateDTO {
  /** 图片名称 */
  imageName?: string
  /** 图片类型 */
  imageType?: ImageType
  /** 自定义属性 */
  customProperties?: Record<string, any>
} 