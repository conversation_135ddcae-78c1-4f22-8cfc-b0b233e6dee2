package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * 文件夹信息创建DTO
 * 
 * 用于创建文件夹信息记录，包含业务元数据
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class FolderInfoCreateDTO {

    /**
     * 文件夹名称
     */
    @NotBlank(message = "文件夹名称不能为空")
    private String folderName;

    /**
     * 文件夹路径
     */
    @NotBlank(message = "文件夹路径不能为空")
    private String folderPath;

    /**
     * 国家ID
     */
    @NotNull(message = "国家ID不能为空")
    private Long countryId;

    /**
     * 证件类型ID
     */
    @NotNull(message = "证件类型ID不能为空")
    private Long certTypeId;

    /**
     * 签发年份
     */
    @NotBlank(message = "签发年份不能为空")
    private String issueYear;

    /**
     * 签发地（可选）
     */
    private String issuePlace;

    /**
     * 文件数量
     */
    @NotNull(message = "文件数量不能为空")
    @Min(value = 1, message = "文件数量必须大于0")
    private Integer fileCount;

    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        return folderName != null && !folderName.trim().isEmpty() &&
               folderPath != null && !folderPath.trim().isEmpty() &&
               countryId != null && certTypeId != null &&
               issueYear != null && !issueYear.trim().isEmpty() &&
               fileCount != null && fileCount > 0;
    }
}
