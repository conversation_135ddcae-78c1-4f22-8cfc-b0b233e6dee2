package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CountryMapper;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.service.ICountryService;

/**
 * 国家表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@Service
public class CountryServiceImpl implements ICountryService 
{
    @Autowired
    private CountryMapper countryMapper;

    /**
     * 查询国家表
     * 
     * @param id 国家表主键
     * @return 国家表
     */
    @Override
    public Country selectCountryById(Long id)
    {
        return countryMapper.selectCountryById(id);
    }

    /**
     * 查询国家表列表
     * 
     * @param country 国家表
     * @return 国家表
     */
    @Override
    public List<Country> selectCountryList(Country country)
    {
        return countryMapper.selectCountryList(country);
    }

    /**
     * 新增国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    @Override
    public int insertCountry(Country country)
    {
        country.setCreateTime(DateUtils.getNowDate());
        return countryMapper.insertCountry(country);
    }

    /**
     * 修改国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    @Override
    public int updateCountry(Country country)
    {
        country.setUpdateTime(DateUtils.getNowDate());
        return countryMapper.updateCountry(country);
    }

    /**
     * 批量删除国家表
     * 
     * @param ids 需要删除的国家表主键
     * @return 结果
     */
    @Override
    public int deleteCountryByIds(Long[] ids)
    {
        return countryMapper.deleteCountryByIds(ids);
    }

    /**
     * 删除国家表信息
     * 
     * @param id 国家表主键
     * @return 结果
     */
    @Override
    public int deleteCountryById(Long id)
    {
        return countryMapper.deleteCountryById(id);
    }
    
    /**
     * 根据首字母查询国家列表
     * 
     * @param letter 首字母
     * @return 国家列表
     */
    @Override
    public List<Country> selectCountryByFirstLetter(String letter)
    {
        return countryMapper.selectCountryByFirstLetter(letter);
    }
    
    /**
     * 获取所有首字母及对应的国家数量
     * 
     * @return 首字母及数量映射
     */
    @Override
    public Map<String, Long> selectCountryLetterCounts()
    {
        List<Map<String, Object>> letterCounts = countryMapper.selectCountryLetterCounts();
        Map<String, Long> result = new TreeMap<>();
        
        for (Map<String, Object> item : letterCounts) {
            String letter = (String) item.get("letter");
            Long count = ((Number) item.get("count")).longValue();
            result.put(letter, count);
        }
        
        return result;
    }

    /**
     * 根据国家代码查询国家信息
     * 
     * @param code 国家代码
     * @return 国家信息
     */
    @Override
    public Country selectCountryByCode(String code)
    {
        return countryMapper.selectCountryByCode(code);
    }

    /**
     * 批量查询国家信息
     * 
     * @param codes 国家代码列表
     * @return 国家列表
     */
    @Override
    public List<Country> selectCountryByCodes(List<String> codes)
    {
        if (codes == null || codes.isEmpty()) {
            return null;
        }
        return countryMapper.selectCountryByCodes(codes);
    }

    /**
     * 搜索国家（支持中英文名称模糊查询）
     * 
     * @param keyword 搜索关键词
     * @return 国家列表
     */
    @Override
    public List<Country> searchCountries(String keyword)
    {
        return countryMapper.searchCountries(keyword);
    }

    /**
     * 批量同步国旗图标路径
     * 
     * @return 更新数量
     */
    @Override
    public int syncAllFlagIcons()
    {
        // 获取所有国家
        List<Country> countries = countryMapper.selectCountryList(new Country());
        int updateCount = 0;
        
        for (Country country : countries) {
            // 如果没有设置国旗路径，根据国家代码自动生成
            if (country.getFlagIcon() == null || country.getFlagIcon().trim().isEmpty()) {
                String flagIcon = "/static/flags/" + country.getCode() + ".png";
                country.setFlagIcon(flagIcon);
                country.setUpdateTime(DateUtils.getNowDate());
                
                int result = countryMapper.updateCountry(country);
                if (result > 0) {
                    updateCount++;
                }
            }
        }
        
        return updateCount;
    }

    /**
     * 验证国家代码是否唯一
     * 
     * @param code 国家代码
     * @param excludeId 排除的ID（用于编辑时验证）
     * @return 是否唯一
     */
    @Override
    public boolean checkCodeUnique(String code, Long excludeId)
    {
        Country existingCountry = countryMapper.selectCountryByCode(code);
        
        // 如果没有找到相同代码的国家，则唯一
        if (existingCountry == null) {
            return true;
        }
        
        // 如果找到的国家就是当前编辑的国家（通过excludeId排除），则唯一
        if (excludeId != null && existingCountry.getId().equals(excludeId)) {
            return true;
        }
        
        // 其他情况都是不唯一
        return false;
    }

}
