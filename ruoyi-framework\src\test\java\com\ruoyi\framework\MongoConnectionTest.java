package com.ruoyi.framework;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

public class MongoConnectionTest {
    public static void main(String[] args) {
        // 测试连接字符串，确保 retryWrites=false
        String connectionString = "*****************************************************************************************";
        
        System.out.println("测试 MongoDB 连接...");
        System.out.println("连接字符串: " + connectionString);
        
        try {
            MongoClientSettings settings = MongoClientSettings.builder()
                    .applyConnectionString(new ConnectionString(connectionString))
                    .retryWrites(false)  // 强制禁用 retryWrites
                    .build();
                    
            MongoClient mongoClient = MongoClients.create(settings);
            MongoDatabase database = mongoClient.getDatabase("docu_research");
            
            // 测试插入一个简单文档
            Document testDoc = new Document("test", "connection")
                    .append("timestamp", System.currentTimeMillis());
                    
            database.getCollection("connection_test").insertOne(testDoc);
            
            System.out.println("✅ MongoDB 连接成功！retryWrites 已禁用");
            System.out.println("✅ 测试文档插入成功");
            
            // 清理测试数据
            database.getCollection("connection_test").deleteOne(testDoc);
            System.out.println("✅ 测试数据清理完成");
            
            mongoClient.close();
            
        } catch (Exception e) {
            System.err.println("❌ MongoDB 连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
