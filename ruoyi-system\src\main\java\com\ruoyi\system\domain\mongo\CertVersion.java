package com.ruoyi.system.domain.mongo;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 证件版本对象 MongoDB
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Data
@Document(collection = "cert_version")
public class CertVersion {

    @Id
    private String id;

    private String versionId; // 业务ID
    private String versionCode;
    private String description;
    private Country countryInfo;
    private CertType certInfo;
    private String issueYear;
    private String status;
    private String standardFolderId; // 关联FolderInfo的ID
    private String mainPicPath; // 主图路径，从FolderInfo继承
    private TrainingImage trainingImage;
    private SysDept deptInfo;
    private SysUser creatorInfo;
    private Date createTime;
    private Date updateTime;
}