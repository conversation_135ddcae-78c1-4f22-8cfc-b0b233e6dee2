import request from '@/utils/request'
import type { 
  SysDept, 
  AnnotationSelectorDTO, 
  AnnotationTargetDTO, 
  AnnotationBodyDTO, 
  AnnotationDTO 
} from '@/types/common'

// ==================== TypeScript 接口定义 ====================

/** 图片标注更新请求DTO */
export interface ImageAnnotationUpdateDTO {
  /** 完整的标注列表，将覆盖旧数据 */
  annotations: AnnotationDTO[]
}



/** 图片仓库响应VO */
export interface ImageRepositoryVO {
  /** MongoDB主键 */
  id: string
  /** 业务图片ID */
  imageId: string
  /** 关联任务ID */
  taskId?: string
  /** 关联文件夹ID */
  folderId?: string
  /** 关联版本ID */
  versionId?: string
  /** 原始文件名 */
  originalFileName: string
  /** MinIO存储路径 */
  minioPath: string
  /** 文件大小 */
  fileSize: number
  /** 内容类型 */
  contentType: string
  /** 图片宽度 */
  imageWidth?: number
  /** 图片高度 */
  imageHeight?: number
  /** 光照类型 */
  lightType?: string
  /** 是否为主图 */
  isMainImage?: boolean
  /** 处理状态 */
  processStatus: string
  /** 处理消息 */
  processMessage?: string
  /** 图片MD5值 */
  imageMd5?: string
  /** 标签列表 */
  tags?: string[]
  /** 部门信息 */
  deptInfo?: SysDept
  /** 完整的标注信息列表 */
  annotations?: AnnotationDTO[]
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/** 统计信息 */
export interface StatisticsInfo {
  [key: string]: any
}

// ==================== API 函数 ====================

/**
 * 更新图片标注
 * PUT /api/images/{imageId}/annotations
 */
export function updateImageAnnotations(imageId: string, data: ImageAnnotationUpdateDTO) {
  return request({
    url: `/api/images/${imageId}/annotations`,
    method: 'put',
    data
  })
}

/**
 * 获取图片详情
 * GET /api/images/{imageId}
 */
export function getImageDetails(imageId: string) {
  return request({
    url: `/api/images/${imageId}`,
    method: 'get'
  })
}

/**
 * 根据文件夹ID查询图片列表
 * GET /api/images/folder/{folderId}
 */
export function getImagesByFolder(folderId: string) {
  return request({
    url: `/api/images/folder/${folderId}`,
    method: 'get'
  })
}

/**
 * 根据版本ID查询图片列表
 * GET /api/images/version/{versionId}
 */
export function getImagesByVersion(versionId: string) {
  return request({
    url: `/api/images/version/${versionId}`,
    method: 'get'
  })
}

/**
 * 获取图片统计信息
 * GET /api/images/{imageId}/statistics
 */
export function getImageStatistics(imageId: string) {
  return request({
    url: `/api/images/${imageId}/statistics`,
    method: 'get'
  })
}

/**
 * 获取文件夹图片统计
 * GET /api/images/folder/{folderId}/statistics
 */
export function getFolderImageStatistics(folderId: string) {
  return request({
    url: `/api/images/folder/${folderId}/statistics`,
    method: 'get'
  })
}

/**
 * 更新图片信息
 * PUT /api/images/{imageId}/info
 */
export function updateImageInfo(imageId: string, data: {
  imageType?: string
  lightType?: string
  isMainImage?: boolean
  tags?: string[]
}) {
  return request({
    url: `/api/images/${imageId}/info`,
    method: 'put',
    data
  })
}

/**
 * 删除图片
 * DELETE /api/images/{imageId}
 */
export function deleteImage(imageId: string) {
  return request({
    url: `/api/images/${imageId}`,
    method: 'delete'
  })
}

/**
 * 更新图片状态
 * PUT /api/images/{imageId}/status
 */
export function updateImageStatus(imageId: string, status: string) {
  return request({
    url: `/api/images/${imageId}/status`,
    method: 'put',
    params: { status }
  })
}
