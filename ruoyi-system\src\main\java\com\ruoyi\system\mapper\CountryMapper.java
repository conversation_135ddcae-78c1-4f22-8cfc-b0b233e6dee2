package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.Country;
import org.apache.ibatis.annotations.Param;

/**
 * 国家表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface CountryMapper 
{
    /**
     * 查询国家表
     * 
     * @param id 国家表主键
     * @return 国家表
     */
    public Country selectCountryById(Long id);

    /**
     * 查询国家表列表
     * 
     * @param country 国家表
     * @return 国家表集合
     */
    public List<Country> selectCountryList(Country country);

    /**
     * 新增国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    public int insertCountry(Country country);

    /**
     * 修改国家表
     * 
     * @param country 国家表
     * @return 结果
     */
    public int updateCountry(Country country);

    /**
     * 删除国家表
     * 
     * @param id 国家表主键
     * @return 结果
     */
    public int deleteCountryById(Long id);

    /**
     * 批量删除国家表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCountryByIds(Long[] ids);
    
    /**
     * 根据首字母查询国家列表
     * 
     * @param letter 首字母
     * @return 国家列表
     */
    public List<Country> selectCountryByFirstLetter(@Param("letter") String letter);
    
    /**
     * 获取所有首字母及对应的国家数量
     * 
     * @return 首字母及数量映射
     */
    public List<Map<String, Object>> selectCountryLetterCounts();

    /**
     * 根据国家代码查询国家信息
     * 
     * @param code 国家代码
     * @return 国家信息
     */
    public Country selectCountryByCode(@Param("code") String code);

    /**
     * 批量查询国家信息
     * 
     * @param codes 国家代码列表
     * @return 国家列表
     */
    public List<Country> selectCountryByCodes(@Param("list") List<String> codes);

    /**
     * 搜索国家（支持中英文名称模糊查询）
     * 
     * @param keyword 搜索关键词
     * @return 国家列表
     */
    public List<Country> searchCountries(@Param("keyword") String keyword);

    /**
     * 批量更新国旗图标路径
     * 
     * @param countries 国家列表
     * @return 更新数量
     */
    public int batchUpdateFlagIcon(@Param("list") List<Country> countries);

    /**
     * 按条件统计数量
     * 
     * @param country 查询条件
     * @return 统计数量
     */
    public int countByCondition(Country country);
}
