# DTO (Data Transfer Object) 类说明

本目录包含了证件样本库后端服务的API接口数据传输对象。

## 目录结构

```
dto/
├── request/           # 请求DTO (用于接收前端请求数据)
├── response/          # 响应VO (用于返回给前端的数据)
├── AnnotationDTO.java # 嵌套DTO (可复用组件)
├── AnnotationBodyDTO.java
├── AnnotationTargetDTO.java
├── AnnotationSelectorDTO.java
└── TrainingImageDTO.java
```

## Request DTOs (请求数据传输对象)

### 1. CertVersionCreateDTO.java
**用途**: POST /api/versions - 创建新的证件版本
**字段**:
- `versionCode` (String, 必需): 版本代码，如 "RUS_VISA_2023_V1"
- `description` (String): 描述信息
- `countryId` (Long, 必需): 国家ID，用于从MySQL加载Country对象
- `certTypeId` (Long, 必需): 证件类型ID，用于从MySQL加载CertType对象
- `issueYear` (String, 必需): 发行年份
- `trainingImage` (TrainingImageDTO): 训练图片信息（可选）

### 2. ImageAnnotationUpdateDTO.java
**用途**: PUT /api/images/{imageId}/annotations - 更新图片标注信息
**字段**:
- `annotations` (List<AnnotationDTO>): 完整的标注列表，将覆盖旧数据

### 3. VersionStandardSetDTO.java
**用途**: POST /api/versions/{versionId}/set-standard - 设置版本标准样本
**字段**:
- `folderId` (String, 必需): 要设为标准的文件夹ID

## Response VOs (响应视图对象)

### 1. BatchUploadTaskVO.java
**用途**: 展示批量上传任务详情
**特点**: 结构与BatchUploadTask.java实体类基本一致，包含完整的MySQL关联对象

### 2. CertVersionVO.java
**用途**: 展示证件版本详情
**特点**: 结构与CertVersion.java实体类基本一致，包含完整的Country、CertType等对象

### 3. FolderInfoVO.java
**用途**: 展示样本文件夹详情
**特点**: 结构与FolderInfo.java实体类基本一致，包含完整的关联对象

### 4. ImageRepositoryVO.java
**用途**: 展示单张图片完整信息
**特点**: 结构与ImageRepository.java实体类基本一致，包含完整的标注列表

## Nested DTOs (嵌套数据传输对象)

### 1. TrainingImageDTO.java
**用途**: 训练图片信息的可复用组件
**字段**:
- `url` (String): 图片URL
- `description` (String): 图片描述

### 2. AnnotationDTO.java
**用途**: 标注信息的主要DTO
**字段**:
- `id` (String): 标注ID
- `type` (String, 必需): 标注类型
- `body` (AnnotationBodyDTO): 标注内容
- `target` (AnnotationTargetDTO): 标注目标

### 3. AnnotationBodyDTO.java
**用途**: 标注内容信息
**字段**:
- `value` (String): 标注值
- `purpose` (String): 标注目的

### 4. AnnotationTargetDTO.java
**用途**: 标注目标信息
**字段**:
- `selector` (AnnotationSelectorDTO): 标注选择器

### 5. AnnotationSelectorDTO.java
**用途**: 标注选择器信息
**字段**:
- `value` (String, 必需): 百分比坐标，格式如 "xywh=percent:10,20,30,10"

## 使用说明

1. **所有DTO类都使用Lombok的@Data注解**，自动生成getter/setter方法
2. **请求DTO使用JSR-303验证注解**，如@NotNull、@NotBlank、@Valid等
3. **响应VO直接包含MySQL实体对象**，避免数据转换的复杂性
4. **嵌套DTO可在多个地方复用**，保持代码的一致性
5. **坐标格式统一使用百分比**，便于前端适配不同尺寸的图片

## API映射关系

| API端点 | 请求DTO | 响应VO |
|---------|---------|---------|
| POST /api/versions | CertVersionCreateDTO | CertVersionVO |
| PUT /api/images/{id}/annotations | ImageAnnotationUpdateDTO | ImageRepositoryVO |
| POST /api/versions/{id}/set-standard | VersionStandardSetDTO | CertVersionVO |
| GET /api/tasks/{id} | - | BatchUploadTaskVO |
| GET /api/folders/{id} | - | FolderInfoVO |
| GET /api/images/{id} | - | ImageRepositoryVO |

## 数据验证

- 使用`@NotNull`验证必需的对象字段
- 使用`@NotBlank`验证必需的字符串字段
- 使用`@Valid`验证嵌套对象
- 坐标值格式验证在业务层进行
