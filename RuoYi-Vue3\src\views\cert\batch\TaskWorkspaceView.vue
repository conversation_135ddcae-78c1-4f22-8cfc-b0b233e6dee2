<template>
  <div class="task-workspace">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2>任务工作台</h2>
        <p>管理任务 {{ taskId }} 的文件夹资源与版本成果</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 左侧栏：待处理文件夹 -->
      <el-aside width="400px" class="left-panel">
        <div class="panel-header">
          <h3>待处理文件夹</h3>
          <el-tag type="info">{{ selectedFolderIds.length }} 个已选择</el-tag>
        </div>

        <UnassignedFolderList
          :task-id="taskId"
          @selection-change="handleFolderSelectionChange"
          @folder-selected="handleFolderSelected"
          @compare-clicked="handleCompareClicked"
          @edit-version-clicked="handleEditVersionClicked"
          @reassociate-clicked="handleReassociateClicked"
          @cancel-association-clicked="handleCancelAssociation"
          ref="unassignedFolderListRef"
        />
      </el-aside>

      <!-- 右侧栏：版本列表 -->
      <el-main class="right-panel">
        <div class="panel-header">
          <h3>版本成果</h3>
        </div>

        <VersionBrowser
          :context-keywords="contextKeywords"
          :can-create-version="!!selectedFolder"
          @create-version-clicked="handleCreateVersionClicked"
          @versions-selected="handleVersionsSelected"
          ref="versionBrowserRef"
        />
      </el-main>
    </el-container>

    <!-- 比对弹窗 -->
    <el-dialog
      v-model="showCompareDialog"
      title="图片比对"
      width="1200px"
      :close-on-click-modal="false"
      @close="handleCloseCompare"
    >
      <CompareArea
        :selected-folder="compareFolder"
        :selected-versions="selectedVersions"
        :current-version-index="currentVersionIndex"
        @version-change="handleVersionChange"
        @associate-folder="handleAssociateFolder"
        @approve-folder="handleApproveFolder"
        @reject-folder="handleRejectFolder"
        @close-compare="handleCloseCompare"
      />
    </el-dialog>

    <!-- 简单版本创建表单 -->
    <SimpleVersionForm
      v-model:visible="showVersionForm"
      :folder-info="selectedFolderForVersion"
      @success="handleVersionCreated"
      @close="handleVersionFormClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import UnassignedFolderList from '../folder/components/UnassignedFolderList.vue'
import VersionBrowser from '../version/components/VersionBrowser.vue'
import CompareArea from '@/views/cert/components/common/CompareArea.vue'
import SimpleVersionForm from '@/views/cert/components/common/SimpleVersionForm.vue'
import { reassociateFolder, cancelFolderAssociation, reviewFolderVersion } from '@/api/cert/folder'
import type { FolderInfoVO, VersionReviewDTO } from '@/api/cert/folder'
import type { CertVersionVO } from '@/api/cert/version'

// 路由参数
const route = useRoute()
const taskId = computed(() => route.params.taskId as string)

// 子组件引用
const unassignedFolderListRef = ref()
const versionBrowserRef = ref()

// 响应式数据
const selectedFolders = ref<FolderInfoVO[]>([])
const selectedFolderIds = ref<string[]>([])
const selectedFolder = ref<FolderInfoVO | null>(null) // 单选的文件夹
const selectedVersions = ref<CertVersionVO[]>([]) // 多选的版本
const currentVersionIndex = ref(0) // 当前显示的版本索引


// 添加新的响应式数据
const showCompareDialog = ref(false)
const compareFolder = ref<FolderInfoVO | null>(null)

// 版本创建相关状态
const showVersionForm = ref(false)
const selectedFolderForVersion = ref<FolderInfoVO | null>(null)



// 计算属性：从选中的文件夹中提取关键词
const contextKeywords = computed(() => {
  const keywords: string[] = []

  // 如果有单选的文件夹，使用预解析的版本信息
  if (selectedFolder.value?.preParseVersionInfo) {
    const parseInfo = selectedFolder.value.preParseVersionInfo
    if (parseInfo.countryCode) {
      keywords.push(parseInfo.countryCode) // 使用国家代码，如 USA, GBR
    }
    if (parseInfo.certTypeCode) {
      keywords.push(parseInfo.certTypeCode) // 使用证件类型代码，如 13
    }
  }
  // 如果没有预解析信息，回退到文件夹名称解析
  else if (selectedFolder.value?.folderName) {
    const parts = selectedFolder.value.folderName.split('_')
    if (parts.length > 0) {
      keywords.push(parts[0]) // 提取国家名
    }
    if (parts.length > 1) {
      keywords.push(parts[1]) // 提取证件类型
    }
  }

  // 兼容多选模式
  selectedFolders.value.forEach((folder: any) => {
    if (folder.preParseVersionInfo) {
      const parseInfo = folder.preParseVersionInfo
      if (parseInfo.countryCode) {
        keywords.push(parseInfo.countryCode)
      }
      if (parseInfo.certTypeCode) {
        keywords.push(parseInfo.certTypeCode)
      }
    }
    // 回退到文件夹名称解析
    else if (folder.folderName) {
      const parts = folder.folderName.split('_')
      if (parts.length > 0) {
        keywords.push(parts[0])
      }
      if (parts.length > 1) {
        keywords.push(parts[1])
      }
    }
  })

  // 去重并返回
  // 注意：当没有选中文件夹时，返回空数组，避免触发自动搜索
  return [...new Set(keywords)]
})

// 计算属性：是否显示比对区域
const showCompareArea = computed(() => {
  console.log('显示比对区域:', selectedFolder.value, selectedVersions.value.length)
  return selectedFolder.value && selectedVersions.value.length > 0
})

// 文件夹名称缓存（用于显示）
const folderNameCache = ref<Map<string, string>>(new Map())

// 获取文件夹名称
const getFolderNameById = (folderId: string): string => {
  return folderNameCache.value.get(folderId) || folderId
}

// 处理文件夹选择变化
const handleFolderSelectionChange = (selection: { folderIds: string[], folders: FolderInfoVO[] }) => {
  selectedFolderIds.value = selection.folderIds
  selectedFolders.value = selection.folders

  // 更新文件夹名称缓存
  selection.folders.forEach(folder => {
    folderNameCache.value.set(folder.folderId, folder.folderName)
  })
}

// 处理文件夹单选
const handleFolderSelected = (folder: FolderInfoVO | null) => {
  console.log('文件夹选择变化:', folder)
  selectedFolder.value = folder
  // 重置版本索引
  currentVersionIndex.value = 0
}

// 处理版本多选
const handleVersionsSelected = (versions: CertVersionVO[]) => {
  console.log('版本选择变化:', versions)
  selectedVersions.value = versions
  // 重置版本索引
  currentVersionIndex.value = 0
}

// 处理版本切换
const handleVersionChange = (index: number) => {
  currentVersionIndex.value = index
}

// 处理关联文件夹
const handleAssociateFolder = async (folder: FolderInfoVO, version: CertVersionVO) => {
  try {
    console.log('关联文件夹到版本:', folder, version)
    ElMessage.success(`成功关联文件夹 "${folder.folderName}" 到版本 "${version.versionCode}"`)

    // 关联成功后关闭弹窗
    showCompareDialog.value = false
    compareFolder.value = null

    // 刷新数据
    await refreshComponents()
  } catch (error) {
    console.error('关联失败:', error)
    ElMessage.error('关联失败，请重试')
  }
}

// 处理关闭比对
const handleCloseCompare = () => {
  showCompareDialog.value = false
  compareFolder.value = null
  // 不清空版本选择，保持右侧的选择状态
}

// 处理审核通过
const handleApproveFolder = async (folder: FolderInfoVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要审核通过文件夹"${folder.folderName}"吗？`,
      '确认审核',
      {
        confirmButtonText: '审核通过',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const reviewData: VersionReviewDTO = {
      reviewResult: 'approved',
      reviewComment: '审核通过'
    }

    const response = await reviewFolderVersion(folder.folderId, reviewData)

    if (response.code === 200) {
      ElMessage.success('审核通过成功')
      handleCloseCompare()
      // 刷新数据
      await refreshComponents()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败')
    }
  }
}

// 处理审核驳回
const handleRejectFolder = async (folder: FolderInfoVO) => {
  try {
    const { value: comment } = await ElMessageBox.prompt(
      `请输入驳回原因：`,
      `驳回文件夹"${folder.folderName}"`,
      {
        confirmButtonText: '确认驳回',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入驳回原因...',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入驳回原因'
          }
          return true
        }
      }
    )

    const reviewData: VersionReviewDTO = {
      reviewResult: 'rejected',
      reviewComment: comment
    }

    const response = await reviewFolderVersion(folder.folderId, reviewData)

    if (response.code === 200) {
      ElMessage.success('审核驳回成功')
      handleCloseCompare()
      // 刷新数据
      await refreshComponents()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败')
    }
  }
}

// 处理创建版本按钮点击
const handleCreateVersionClicked = () => {
  // 检查是否有选中的文件夹（单选模式）
  if (!selectedFolder.value) {
    ElMessage.warning('请先选择一个待处理的文件夹')
    return
  }

  // 设置选中的文件夹并打开版本创建表单
  selectedFolderForVersion.value = selectedFolder.value
  showVersionForm.value = true
}

// 版本创建成功处理
const handleVersionCreated = async () => {
  ElMessage.success('版本创建成功')

  // 清空选择
  selectedFolder.value = null
  selectedFolderForVersion.value = null

  // 刷新数据
  await refreshComponents()
}

// 版本创建表单关闭处理
const handleVersionFormClose = () => {
  selectedFolderForVersion.value = null
}



// 处理比对按钮点击
const handleCompareClicked = (folder: FolderInfoVO) => {
  console.log('打开比对弹窗:', folder)
  compareFolder.value = folder
  showCompareDialog.value = true
}

// 处理编辑版本事件
const handleEditVersionClicked = (folder: FolderInfoVO) => {
  ElMessageBox.confirm(
    '版本信息修改需要在版本管理页面进行，是否跳转到版本管理？',
    '提示',
    {
      confirmButtonText: '跳转到版本管理',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 跳转到版本管理页面，并传递版本ID参数
    const versionId = folder.associationInfo?.versionId
    if (versionId) {
      // 这里可以根据实际的路由配置进行跳转
      console.log('跳转到版本管理页面，版本ID:', versionId)
      ElMessage.info('版本管理页面功能待实现')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 处理重新关联事件
const handleReassociateClicked = (folder: FolderInfoVO) => {
  console.log('重新关联文件夹:', folder)
  // 复用现有的比对功能，但修改关联逻辑
  compareFolder.value = folder
  showCompareDialog.value = true
}

// 处理取消关联事件
const handleCancelAssociation = async (folder: FolderInfoVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消文件夹 "${folder.folderName}" 与版本 "${folder.associationInfo?.versionCode}" 的关联吗？`,
      '确认取消关联',
      {
        type: 'warning',
        confirmButtonText: '确认取消',
        cancelButtonText: '取消'
      }
    )

    // 调用取消关联 API
    const result = await cancelFolderAssociation(folder.folderId)

    if (result.code === 200) {
      ElMessage.success('取消关联成功')
      await refreshComponents()
    } else {
      ElMessage.error('取消关联失败: ' + (result.msg || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消关联失败:', error)
      ElMessage.error('取消关联失败: ' + (error.message || '未知错误'))
    }
  }
}

// 刷新子组件数据
const refreshComponents = async () => {
  try {
    // 刷新未分配文件夹列表
    if (unassignedFolderListRef.value?.refreshData) {
      await unassignedFolderListRef.value.refreshData()
    }

    // 刷新版本浏览器
    if (versionBrowserRef.value?.refreshData) {
      await versionBrowserRef.value.refreshData()
    }
  } catch (error) {
    console.error('刷新组件数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  if (!taskId.value) {
    ElMessage.error('任务ID不能为空')
    return
  }
})
</script>

<style scoped>
.task-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  padding: 20px 24px 0;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.task-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  align-items: stretch; /* <--- 新增此行 */
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.create-version-content {
  padding: 20px 0;
}

.selected-folders-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.selected-folders-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.folder-list {
  width: 100%; /* 新增，确保内容区撑满 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.folder-tag {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-aside) {
  overflow: hidden;
  padding: 0 !important;
}

:deep(.el-main) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}


/* 调整弹窗内容的样式 */
:deep(.el-dialog__body) {
  padding: 0;
}

.selected-folder-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.selected-folder-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.folder-info {
  display: flex;
  justify-content: center;
}
</style>
