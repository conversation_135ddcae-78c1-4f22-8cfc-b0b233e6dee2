package com.ruoyi.system.repository;

import com.ruoyi.system.domain.mongo.BatchUploadTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 批量上传任务Repository
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface BatchUploadTaskRepository extends MongoRepository<BatchUploadTask, String> {
    
    /**
     * 根据任务ID查找
     */
    Optional<BatchUploadTask> findByTaskId(String taskId);
    
    /**
     * 根据状态查找
     */
    List<BatchUploadTask> findByStatus(String status);

    /**
     * 根据状态分页查找
     */
    Page<BatchUploadTask> findByStatus(String status, Pageable pageable);

    /**
     * 根据状态统计数量
     */
    long countByStatus(String status);

    /**
     * 根据任务ID删除
     */
    void deleteByTaskId(String taskId);
    
    /**
     * 根据创建人用户名查找
     */
    List<BatchUploadTask> findByCreatedBy(String createdBy);
    
    /**
     * 根据任务名称模糊查找
     */
    @Query("{'taskName': {$regex: ?0, $options: 'i'}}")
    List<BatchUploadTask> findByTaskNameContaining(String taskName);

    /**
     * 根据国家信息查找（已废弃 - 业务元数据现在存储在FolderInfo中）
     */
    @Deprecated
    default List<BatchUploadTask> findByCountryId(Long countryId) {
        // 业务元数据现在存储在FolderInfo中，不再从BatchUploadTask查询
        return new ArrayList<>();
    }

    /**
     * 根据证件类型查找（已废弃 - 业务元数据现在存储在FolderInfo中）
     */
    @Deprecated
    default List<BatchUploadTask> findByCertTypeId(Long certTypeId) {
        // 业务元数据现在存储在FolderInfo中，不再从BatchUploadTask查询
        return new ArrayList<>();
    }
    
    /**
     * 查找进行中的任务
     */
    @Query("{'status': {$in: ['uploading', 'processing']}}")
    List<BatchUploadTask> findProcessingTasks();
} 