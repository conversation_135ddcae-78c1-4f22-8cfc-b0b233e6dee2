package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 证件仓库对象 cert_warehouse
 * 
 * <AUTHOR>
 * @date 2023-06-30
 */
public class CertWarehouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 证件编号（如 CN123456789） */
    @Excel(name = "证件编号")
    private String witnessNumber;

    /** 证件类型 ID */
    @Excel(name = "证件类型ID")
    private Long certTypeId;

    /** 国籍（国家 ID） */
    @Excel(name = "国籍ID")
    private Long countryId;

    /** 采集来源（如"手动上传"） */
    @Excel(name = "采集来源")
    private String collectionSource;

    /** MinIO 图片路径 */
    @Excel(name = "图片路径")
    private String imagePath;

    /** 是否为样本（0: 否, 1: 是） */
    @Excel(name = "是否为样本", readConverterExp = "0=否,1=是")
    private Integer isSample;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setWitnessNumber(String witnessNumber) 
    {
        this.witnessNumber = witnessNumber;
    }

    public String getWitnessNumber() 
    {
        return witnessNumber;
    }

    public void setCertTypeId(Long certTypeId) 
    {
        this.certTypeId = certTypeId;
    }

    public Long getCertTypeId() 
    {
        return certTypeId;
    }

    public void setCountryId(Long countryId) 
    {
        this.countryId = countryId;
    }

    public Long getCountryId() 
    {
        return countryId;
    }

    public void setCollectionSource(String collectionSource) 
    {
        this.collectionSource = collectionSource;
    }

    public String getCollectionSource() 
    {
        return collectionSource;
    }

    public void setImagePath(String imagePath) 
    {
        this.imagePath = imagePath;
    }

    public String getImagePath() 
    {
        return imagePath;
    }

    public void setIsSample(Integer isSample) 
    {
        this.isSample = isSample;
    }

    public Integer getIsSample() 
    {
        return isSample;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("witnessNumber", getWitnessNumber())
            .append("certTypeId", getCertTypeId())
            .append("countryId", getCountryId())
            .append("collectionSource", getCollectionSource())
            .append("imagePath", getImagePath())
            .append("isSample", getIsSample())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
