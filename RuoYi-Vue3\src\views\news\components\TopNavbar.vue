<template>
  <div class="top-container">
    <div class="logo-container">
      <h3>深圳出入境边防检查总站智能证件研究平台</h3>
    </div>
    <div class="button-group">
      <el-button
        type="primary"
        icon="HomeFilled"
        plain
        @click="goTarget('https://gitee.com/y_project/RuoYi-Vue')"
        class="nav-button"
      >证件样本库</el-button>
      <el-button
        type="primary"
        icon="Cloudy"
        plain
        @click="goTarget('http://ruoyi.vip')"
        class="nav-button"
      >AI证研</el-button>
      <!-- 将消息中心移到此处 -->
      <el-badge :value="unreadCount" class="item">
        <el-button
          type="primary"
          plain
          @click="handleMessageCenterClick()"
          class="nav-button"
        >消息中心</el-button>
      </el-badge>
      <el-button 
        type="primary" 
        v-if="!isLoggedIn" 
        @click="openLoginModal"
        class="login-button"
      >登录</el-button>
      <div v-if="isLoggedIn" class="user-info">
        <span class="welcome-text">欢迎, {{ username }}</span>
        <el-dropdown class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/index">
                <el-dropdown-item>后台管理</el-dropdown-item>
              </router-link>
              <!-- 移除原有的消息中心链接 -->
              <el-dropdown-item @click="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import { ElMessage } from 'element-plus'; // 导入 ElMessage

const router = useRouter();
const userStore = useUserStore();
const isLoggedIn = ref(false); // 登录状态
const username = ref(''); // 用户名
const unreadCount = computed(() => userStore.unreadMessageCount); // 使用 computed 实时获取未读消息数量

function goTarget(url) {
  window.open(url, '__blank')
}

const openLoginModal = () => {
  router.push({ name: 'login' }); // 跳转到登录页面
};

const logout = () => {
  // 添加确认弹窗
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    isLoggedIn.value = false;
    username.value = '';
    userStore.unreadMessageCount = 0;
    // 这里可以添加用户退出的逻辑，例如调用用户存储的退出方法
    userStore.logOut().then(() => {
      location.href = '/Homepage'; // 跳转到首页
    });
  }).catch(() => { });
};

// 检查用户登录状态
const checkLoginStatus = () => {
  if (getToken()) {
    // 如果有 token，尝试获取用户信息
    const userInfo = userStore;
    if (userInfo && userInfo.name) {
      isLoggedIn.value = true;
      username.value = userInfo.name;
      userStore.getUnreadMessageCount();
    } else {
      // 如果有 token 但没有用户信息，可能需要重新获取用户信息
      userStore.getInfo().then(res => {
        isLoggedIn.value = true;
        username.value = userStore.name;
        userStore.getUnreadMessageCount();
      }).catch(() => {
        isLoggedIn.value = false;
        username.value = '';
        userStore.unreadMessageCount = 0;
      });
    }
  } else {
    isLoggedIn.value = false;
    username.value = '';
    userStore.unreadMessageCount = 0;
  }
};

// 监听登录状态变化，更新未读消息数量
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    // 获取未读消息数量，无需手动赋值
    userStore.getUnreadMessageCount();
  } else {
    userStore.unreadMessageCount = 0;
  }
});
const handleMessageCenterClick = () => { 
  if (!isLoggedIn.value) { 
    // 修改提示逻辑
    ElMessage.warning('您尚未登录，登录后可查看消息。'); 
  } else { 
    router.push('/notification'); 
  } 
}; 
checkLoginStatus(); // 检查登录状态
</script>

<style scoped lang="scss">
.top-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10%;
  height: 70px;
  background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  
  .logo-container {
    display: flex;
    align-items: center;
    
    .logo {
      height: 40px;
      margin-right: 15px;
    }
    
    h3 {
      margin: 0;
      font-size: 20px;
      color: white;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
  }
  
  .button-group {
    display: flex;
    align-items: center;
    
    .nav-button {
      margin-left: 15px;
      height: 36px;
      font-weight: 500;
      background-color: #fff;
      color: #1a73e8;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
    .right-menu-item .avatar-wrapper .el-icon { 
      margin-top: 20px;
      color: #ffffff; 
      font-size: 22px; 
  }
    
    .login-button {
      margin-left: 15px;
      height: 36px;
      font-weight: 500;
      background-color: #fff;
      color: #1a73e8;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      margin-left: 20px;
      
      .welcome-text {
        color: white;
        font-size: 14px;
        margin-right: 10px;
      }
      
      .logout-button {
        color: white;
        padding: 0;
        
        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}
</style>
