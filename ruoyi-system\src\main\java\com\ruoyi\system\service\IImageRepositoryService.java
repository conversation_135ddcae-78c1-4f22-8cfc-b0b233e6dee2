package com.ruoyi.system.service;

import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.dto.response.ImageRepositoryVO;
import com.ruoyi.system.domain.dto.AnnotationDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 图片仓库Service接口
 * 职责: 作为最底层的服务，负责所有与图片相关的原子操作，如按条件查找、统计和更新标注
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IImageRepositoryService {
    
    /**
     * 创建图片记录
     * 
     * @param imageRepository 图片信息
     * @return 创建的图片记录
     */
    ImageRepository createImage(ImageRepository imageRepository);
    
    /**
     * 根据图片ID获取图片信息
     * 
     * @param imageId 图片ID
     * @return 图片信息VO
     */
    ImageRepositoryVO getImageById(String imageId);
    
    /**
     * 根据MongoDB主键获取图片信息
     * 
     * @param id MongoDB主键
     * @return 图片实体
     */
    ImageRepository getImageByMongoId(String id);
    
    /**
     * 根据文件夹ID获取图片列表
     * 
     * @param folderId 文件夹ID
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByFolderId(String folderId);
    
    /**
     * 根据版本ID获取图片列表
     * 
     * @param versionId 版本ID
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByVersionId(String versionId);
    
    /**
     * 根据文件夹ID统计图片数量
     * 
     * @param folderId 文件夹ID
     * @return 图片数量
     */
    long countImagesByFolderId(String folderId);
    
    /**
     * 根据任务ID获取图片列表
     * 
     * @param taskId 任务ID
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByTaskId(String taskId);
    
    /**
     * 根据光照类型获取图片列表
     * 
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByLightType(String lightType);
    
    /**
     * 根据是否为主图获取图片列表
     * 
     * @param isMainImage 是否为主图
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByMainImageFlag(boolean isMainImage);
    
    /**
     * 根据处理状态获取图片列表
     * 
     * @param processStatus 处理状态
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByProcessStatus(String processStatus);
    
    /**
     * 根据部门ID获取图片列表
     * 
     * @param deptId 部门ID
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByDeptId(Long deptId);
    
    /**
     * 根据文件夹ID和光照类型获取图片列表
     * 
     * @param folderId 文件夹ID
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByFolderIdAndLightType(String folderId, String lightType);
    
    /**
     * 根据文件夹ID和是否为主图获取图片列表
     * 
     * @param folderId 文件夹ID
     * @param isMainImage 是否为主图
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByFolderIdAndMainImageFlag(String folderId, boolean isMainImage);
    
    /**
     * 根据版本ID和光照类型获取图片列表
     * 
     * @param versionId 版本ID
     * @param lightType 光照类型
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByVersionIdAndLightType(String versionId, String lightType);
    
    /**
     * 根据创建时间范围获取图片列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 图片列表
     */
    List<ImageRepositoryVO> getImagesByCreateTimeRange(Date startTime, Date endTime);
    
    /**
     * 根据文件夹ID获取主图列表
     * 
     * @param folderId 文件夹ID
     * @return 主图列表
     */
    List<ImageRepositoryVO> getMainImagesByFolderId(String folderId);
    
    /**
     * 根据文件夹ID统计已处理图片数量
     * 
     * @param folderId 文件夹ID
     * @return 已处理图片数量
     */
    long countProcessedImagesByFolderId(String folderId);
    
    /**
     * 根据版本ID统计图片数量
     * 
     * @param versionId 版本ID
     * @return 图片数量
     */
    long countImagesByVersionId(String versionId);
    
    /**
     * 更新图片信息
     * 
     * @param imageRepository 图片信息
     * @return 更新后的图片信息
     */
    ImageRepository updateImage(ImageRepository imageRepository);
    
    /**
     * 更新图片处理状态
     * 
     * @param imageId 图片ID
     * @param processStatus 处理状态
     * @return 是否更新成功
     */
    boolean updateImageProcessStatus(String imageId, String processStatus);
    
    /**
     * 更新图片标注信息
     *
     * @param imageId 图片ID
     * @param annotations 标注信息
     * @return 是否更新成功
     */
    boolean updateImageAnnotations(String imageId, Map<String, Object> annotations);

    /**
     * 更新图片标注信息 (使用DTO列表)
     *
     * @param imageId 图片ID
     * @param annotations 标注DTO列表
     * @return 是否更新成功
     */
    boolean updateImageAnnotations(String imageId, List<AnnotationDTO> annotations);
    
    /**
     * 删除图片记录
     * 
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteImage(String imageId);
    
    /**
     * 根据文件夹ID删除所有图片
     *
     * @param folderId 文件夹ID
     * @return 删除的图片数量
     */
    int deleteImagesByFolderId(String folderId);

    /**
     * 获取文件夹下所有图片的MinIO路径
     *
     * @param folderId 文件夹ID
     * @return MinIO路径列表
     */
    List<String> getMinioPathsByFolderId(String folderId);

    /**
     * 获取任务下所有图片的MinIO路径
     *
     * @param taskId 任务ID
     * @return MinIO路径列表
     */
    List<String> getMinioPathsByTaskId(String taskId);
    
    /**
     * 根据任务ID删除所有图片
     * 
     * @param taskId 任务ID
     * @return 删除的图片数量
     */
    int deleteImagesByTaskId(String taskId);
    
    /**
     * 批量创建图片记录
     * 
     * @param imageRepositories 图片信息列表
     * @return 创建的图片列表
     */
    List<ImageRepository> batchCreateImages(List<ImageRepository> imageRepositories);
    
    /**
     * 批量更新图片处理状态
     * 
     * @param imageIds 图片ID列表
     * @param processStatus 处理状态
     * @return 更新的图片数量
     */
    int batchUpdateImageProcessStatus(List<String> imageIds, String processStatus);
    
    /**
     * 获取图片统计信息
     * 
     * @param folderId 文件夹ID
     * @return 统计信息Map
     */
    Map<String, Object> getImageStatistics(String folderId);
    
    /**
     * 获取任务的图片统计
     * 
     * @param taskId 任务ID
     * @return 统计信息Map
     */
    Map<String, Object> getTaskImageStatistics(String taskId);
    
    /**
     * 检查图片是否存在
     * 
     * @param imageId 图片ID
     * @return 是否存在
     */
    boolean existsById(String imageId);
    
    /**
     * 设置图片为主图
     * 
     * @param imageId 图片ID
     * @return 是否设置成功
     */
    boolean setAsMainImage(String imageId);
    
    /**
     * 取消图片的主图状态
     *
     * @param imageId 图片ID
     * @return 是否取消成功
     */
    boolean unsetMainImage(String imageId);

    /**
     * 批量删除图片
     *
     * @param imageIds 图片ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteImages(List<String> imageIds);

    /**
     * 更新图片状态
     *
     * @param imageId 图片ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateImageStatus(String imageId, String status);

    /**
     * 更新图片信息
     *
     * @param imageId 图片ID
     * @param updateData 更新数据
     * @return 是否成功
     */
    boolean updateImageInfo(String imageId, Map<String, Object> updateData);

    /**
     * 获取文件夹图片统计
     *
     * @param folderId 文件夹ID
     * @return 统计信息Map
     */
    Map<String, Object> getFolderImageStatistics(String folderId);

    // ==================== 标注相关方法 ====================

    /**
     * 自动识别图片类型
     */
    String detectImageType(String fileName);

    /**
     * 批量更新图片类型
     */
    void updateImageTypes(String folderId);

    /**
     * 获取图片标注信息
     */
    ImageAnnotationInfo getImageAnnotations(String imageId);

    /**
     * 保存图片标注（仅标准样本可调用）
     */
    boolean saveImageAnnotations(String imageId, List<AnnotationItem> annotations);

    /**
     * 批量更新文件夹图片的可标注类型
     */
    void updateFolderImagesAnnotatableType(String folderId);

    /**
     * 获取可标注的图片列表
     */
    List<ImageRepositoryVO> getAnnotatableImages(String folderId);

    /**
     * 获取指定类型的图片列表
     */
    List<ImageRepositoryVO> getImagesByType(String folderId, String imageType);

    /**
     * 标注项类
     */
    class AnnotationItem {
        private String annotationId;
        private String annotationType;
        private String annotationName;
        private String annotationValue;
        private AnnotationCoordinate coordinate;
        private Boolean required;
        private Integer displayOrder;

        // 构造函数
        public AnnotationItem() {}

        // getters and setters
        public String getAnnotationId() { return annotationId; }
        public void setAnnotationId(String annotationId) { this.annotationId = annotationId; }

        public String getAnnotationType() { return annotationType; }
        public void setAnnotationType(String annotationType) { this.annotationType = annotationType; }

        public String getAnnotationName() { return annotationName; }
        public void setAnnotationName(String annotationName) { this.annotationName = annotationName; }

        public String getAnnotationValue() { return annotationValue; }
        public void setAnnotationValue(String annotationValue) { this.annotationValue = annotationValue; }

        public AnnotationCoordinate getCoordinate() { return coordinate; }
        public void setCoordinate(AnnotationCoordinate coordinate) { this.coordinate = coordinate; }

        public Boolean getRequired() { return required; }
        public void setRequired(Boolean required) { this.required = required; }

        public Integer getDisplayOrder() { return displayOrder; }
        public void setDisplayOrder(Integer displayOrder) { this.displayOrder = displayOrder; }
    }

    /**
     * 标注坐标类
     */
    class AnnotationCoordinate {
        private Double x;
        private Double y;
        private Double width;
        private Double height;

        // 构造函数
        public AnnotationCoordinate() {}

        // getters and setters
        public Double getX() { return x; }
        public void setX(Double x) { this.x = x; }

        public Double getY() { return y; }
        public void setY(Double y) { this.y = y; }

        public Double getWidth() { return width; }
        public void setWidth(Double width) { this.width = width; }

        public Double getHeight() { return height; }
        public void setHeight(Double height) { this.height = height; }
    }

    /**
     * 图片标注信息DTO
     */
    class ImageAnnotationInfo {
        private String imageId;
        private String imageType;
        private boolean canEdit;
        private boolean canView;
        private List<AnnotationItem> annotations;
        private String templateSource;
        private String reason;

        // 构造函数
        public ImageAnnotationInfo() {}

        // getters and setters
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }

        public String getImageType() { return imageType; }
        public void setImageType(String imageType) { this.imageType = imageType; }

        public boolean isCanEdit() { return canEdit; }
        public void setCanEdit(boolean canEdit) { this.canEdit = canEdit; }

        // 为了兼容性，同时提供get方法
        public boolean getCanEdit() { return canEdit; }

        public boolean isCanView() { return canView; }
        public void setCanView(boolean canView) { this.canView = canView; }

        // 为了兼容性，同时提供get方法
        public boolean getCanView() { return canView; }

        public List<AnnotationItem> getAnnotations() { return annotations; }
        public void setAnnotations(List<AnnotationItem> annotations) { this.annotations = annotations; }

        public String getTemplateSource() { return templateSource; }
        public void setTemplateSource(String templateSource) { this.templateSource = templateSource; }

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
