// MongoDB 数据迁移脚本 - 版本标注模板系统
// 执行日期: 2025-01-15
// 目的: 为版本标注模板系统添加必要的集合、索引和字段

print("=== 开始执行版本标注模板系统数据迁移 ===");

// 1. 创建版本标注模板集合
print("1. 创建版本标注模板集合...");
db.createCollection("version_annotation_template");

// 2. 创建版本标注模板的索引
print("2. 创建版本标注模板索引...");

// 版本ID和图片类型的唯一复合索引
db.version_annotation_template.createIndex(
    { "versionId": 1, "imageType": 1 },
    { unique: true, name: "version_image_type_idx" }
);

// 标准文件夹ID索引
db.version_annotation_template.createIndex(
    { "standardFolderId": 1 },
    { name: "standard_folder_idx" }
);

// 模板ID索引
db.version_annotation_template.createIndex(
    { "templateId": 1 },
    { unique: true, name: "template_id_idx" }
);

// 证件类型索引
db.version_annotation_template.createIndex(
    { "certType": 1 },
    { name: "cert_type_idx" }
);

// 国家代码索引
db.version_annotation_template.createIndex(
    { "countryCode": 1 },
    { name: "country_code_idx" }
);

// 创建人索引
db.version_annotation_template.createIndex(
    { "createdBy": 1 },
    { name: "created_by_idx" }
);

print("版本标注模板索引创建完成");

// 3. 为现有文件夹添加标准样本标志字段
print("3. 为现有文件夹添加标准样本标志字段...");

var folderUpdateResult = db.folder_info.updateMany(
    { "isStandardSample": { $exists: false } },
    { $set: { "isStandardSample": false } }
);

print("文件夹标准样本标志字段更新完成，影响文档数: " + folderUpdateResult.modifiedCount);

// 4. 为现有图片添加可标注类型字段
print("4. 为现有图片添加可标注类型字段...");

var imageUpdateResult = db.image_repository.updateMany(
    { "isAnnotatableType": { $exists: false } },
    { $set: { "isAnnotatableType": false } }
);

print("图片可标注类型字段更新完成，影响文档数: " + imageUpdateResult.modifiedCount);

// 5. 根据文件名自动识别图片类型并设置可标注标志
print("5. 自动识别图片类型并设置可标注标志...");

// 定义图片类型识别规则
var imageTypeRules = [
    {
        pattern: /.*[_\-\s](visible|常光|可见光|data[_\-\s]?page).*\.(jpg|jpeg|png|bmp|tiff)$/i,
        type: "VISIBLE_DATA_PAGE",
        annotatable: true
    },
    {
        pattern: /.*[_\-\s](infrared|红外|ir|data[_\-\s]?page).*\.(jpg|jpeg|png|bmp|tiff)$/i,
        type: "INFRARED_DATA_PAGE",
        annotatable: true
    },
    {
        pattern: /.*[_\-\s](ultraviolet|紫外|uv|data[_\-\s]?page).*\.(jpg|jpeg|png|bmp|tiff)$/i,
        type: "ULTRAVIOLET_DATA_PAGE",
        annotatable: true
    }
];

var typeUpdateCount = 0;

// 遍历所有图片，根据文件名识别类型
db.image_repository.find({}).forEach(function(image) {
    var fileName = image.originalFileName || image.fileName || "";
    var newType = "OTHER";
    var isAnnotatable = false;
    
    // 检查文件名是否匹配任何规则
    for (var i = 0; i < imageTypeRules.length; i++) {
        if (imageTypeRules[i].pattern.test(fileName)) {
            newType = imageTypeRules[i].type;
            isAnnotatable = imageTypeRules[i].annotatable;
            break;
        }
    }
    
    // 更新图片类型和可标注标志
    if (image.imageType !== newType || image.isAnnotatableType !== isAnnotatable) {
        db.image_repository.updateOne(
            { "_id": image._id },
            { 
                $set: { 
                    "imageType": newType,
                    "isAnnotatableType": isAnnotatable
                } 
            }
        );
        typeUpdateCount++;
    }
});

print("图片类型自动识别完成，更新图片数: " + typeUpdateCount);

// 6. 创建文件夹信息的标准样本索引
print("6. 创建文件夹信息的标准样本索引...");

db.folder_info.createIndex(
    { "versionId": 1, "isStandardSample": 1 },
    { name: "version_standard_sample_idx" }
);

db.folder_info.createIndex(
    { "isStandardSample": 1 },
    { name: "is_standard_sample_idx" }
);

print("文件夹标准样本索引创建完成");

// 7. 创建图片仓库的可标注类型索引
print("7. 创建图片仓库的可标注类型索引...");

// 检查并创建imageType索引（如果不存在或名称不同）
try {
    var existingIndexes = db.image_repository.getIndexes();
    var hasImageTypeIndex = false;

    for (var i = 0; i < existingIndexes.length; i++) {
        var index = existingIndexes[i];
        if (index.key && index.key.imageType === 1) {
            hasImageTypeIndex = true;
            print("imageType索引已存在，名称: " + index.name);
            break;
        }
    }

    if (!hasImageTypeIndex) {
        db.image_repository.createIndex(
            { "imageType": 1 },
            { name: "image_type_idx" }
        );
        print("创建imageType索引成功");
    }
} catch (e) {
    print("imageType索引处理: " + e.message);
}

// 创建isAnnotatableType索引
try {
    db.image_repository.createIndex(
        { "isAnnotatableType": 1 },
        { name: "is_annotatable_type_idx" }
    );
    print("创建isAnnotatableType索引成功");
} catch (e) {
    print("isAnnotatableType索引创建失败: " + e.message);
}

// 创建复合索引
try {
    db.image_repository.createIndex(
        { "versionId": 1, "imageType": 1 },
        { name: "version_image_type_idx" }
    );
    print("创建versionId+imageType复合索引成功");
} catch (e) {
    print("复合索引创建失败: " + e.message);
}

print("图片仓库可标注类型索引创建完成");

// 8. 验证迁移结果
print("8. 验证迁移结果...");

var templateCollectionExists = db.getCollectionNames().indexOf("version_annotation_template") !== -1;
var folderStandardCount = db.folder_info.countDocuments({ "isStandardSample": { $exists: true } });
var imageAnnotatableCount = db.image_repository.countDocuments({ "isAnnotatableType": { $exists: true } });
var annotatableImageCount = db.image_repository.countDocuments({ "isAnnotatableType": true });

print("验证结果:");
print("- 版本标注模板集合存在: " + templateCollectionExists);
print("- 具有标准样本标志的文件夹数: " + folderStandardCount);
print("- 具有可标注类型标志的图片数: " + imageAnnotatableCount);
print("- 可标注的图片数: " + annotatableImageCount);

// 9. 输出索引信息
print("9. 输出索引信息...");

print("版本标注模板集合索引:");
db.version_annotation_template.getIndexes().forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("文件夹信息集合新增索引:");
db.folder_info.getIndexes().filter(function(index) {
    return index.name.indexOf("standard") !== -1;
}).forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("图片仓库集合新增索引:");
db.image_repository.getIndexes().filter(function(index) {
    return index.name.indexOf("type") !== -1 || index.name.indexOf("annotatable") !== -1;
}).forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("=== 版本标注模板系统数据迁移完成 ===");
